# 🚀 Advanced DEX Smart Contracts

Bu proje, Ethereum üzerinde çalışan enterprise-level merkezi olmayan bir borsa (DEX) için kapsamlı akıllı kontrat sistemidir. Modern DeFi protokollerinin tüm özelliklerini içeren, güvenlik odaklı ve ölçeklenebilir bir çözüm sunar.

## ✨ Temel Özellikler

### 🔄 Core AMM Protocol
- **Automated Market Maker (AMM)**: Gelişmiş Uniswap V2+ tarzı AMM protokolü
- **Likidite Havuzları**: Optimize edilmiş token çiftleri için likidite sağlama
- **Multi-hop Routing**: Akıllı yönlendirme algoritması ile optimal swap yolları
- **Price Impact Koruması**: Büyük işlemler için fiyat etkisi sınırlaması

### 🛡️ Enterprise Güvenlik
- **Circuit Breaker**: Anormal piyasa koşullarında otomatik koruma
- **MEV Koruması**: Front-running ve sandwich attack koruması
- **Flash Loan Koruması**: Flash loan saldırılarına karşı savunma
- **Emergency Pause**: Acil durum durdurma mekanizması
- **Multi-sig Guardian**: Çoklu imza ile güvenlik yönetimi

### 📊 Gelişmiş Price Oracle
- **TWAP (Time-Weighted Average Price)**: Manipülasyon dirençli fiyat hesaplama
- **Chainlink Entegrasyonu**: Harici fiyat beslemeleri ile doğrulama
- **Price Deviation Detection**: Fiyat sapma tespiti ve uyarı sistemi
- **Multiple Price Sources**: Çoklu kaynak ile güvenilir fiyatlama

### 🎯 Token Registry Sistemi
- **25+ Popüler Token**: Ethereum'daki en popüler tokenların önceden yapılandırılmış listesi
- **Verified Tokens**: Doğrulanmış token sistemi
- **Dynamic Token Management**: Dinamik token ekleme/çıkarma
- **Market Cap Sorting**: Piyasa değerine göre sıralama

### 💰 Liquidity Mining & Governance
- **Yield Farming**: Likidite sağlayıcıları için ödül sistemi
- **Governance Token**: Voting ve protokol yönetimi için native token
- **Boost System**: Kullanıcı seviyelerine göre ödül çarpanları
- **Referral System**: Referans sistemi ile komisyon paylaşımı
- **Vesting Schedules**: Token dağıtımı için vesting programları

## 🏗️ Kontrat Mimarisi

### Core Contracts

#### SwapFactory
- Yeni token çiftleri oluşturur ve yönetir
- Protocol fee ayarlarını kontrol eder
- Pair creation events yayınlar
- Pause/unpause functionality

#### SwapPair
- Likidite havuzu kontratı (ERC20 LP tokens)
- Token swap işlemlerini gerçekleştirir
- Price accumulator ile TWAP desteği
- Flash loan koruması

#### SwapRouter
- Kullanıcı dostu swap interface'i
- Multi-hop routing desteği
- Slippage ve deadline koruması
- Security manager entegrasyonu

### Advanced Contracts

#### SmartRouter
- Optimal path finding algoritması
- Gas-efficient routing
- Liquidity analysis
- Price impact calculation

#### SecurityManager
- Circuit breaker implementation
- MEV protection mechanisms
- Rate limiting
- Emergency controls
- Guardian management

#### PriceOracle
- TWAP calculation engine
- Chainlink price feed integration
- Price manipulation detection
- Multiple price source aggregation

#### TokenRegistry
- Comprehensive token database
- Verification system
- Market cap tracking
- Pagination support

#### LiquidityMining
- Multi-pool staking system
- Flexible reward distribution
- Boost multipliers
- Referral commissions
- Emergency withdrawal

#### GovernanceToken
- ERC20Votes implementation
- Controlled minting with caps
- Vesting schedule management
- Pause functionality

## 🚀 Kurulum ve Kullanım

### Gereksinimler
```bash
Node.js >= 16.0.0
npm >= 8.0.0
```

### Kurulum
```bash
git clone <repository-url>
cd swap-contracts
npm install
```

### Derleme
```bash
npm run compile
```

### Test Suite
```bash
# Tüm testler
npm run test

# Unit testler
npm run test:unit

# Integration testler
npm run test:integration

# Security testler
npm run test:security

# Coverage raporu
npm run test:coverage
```

### Deployment

#### Local Network
```bash
npx hardhat node
npm run deploy
```

#### Testnet (Goerli)
```bash
npm run deploy:goerli
```

#### Mainnet
```bash
npm run deploy:mainnet
```

### Verification
```bash
# Contract verification
npm run verify

# Deployment verification
npx hardhat run scripts/verify-deployment.js
```

## 🔧 Konfigürasyon

### Environment Variables
```bash
# .env dosyası
PRIVATE_KEY=your_private_key
INFURA_API_KEY=your_infura_key
ETHERSCAN_API_KEY=your_etherscan_key
COINMARKETCAP_API_KEY=your_cmc_key
```

### Network Configuration
```javascript
// hardhat.config.js
networks: {
  mainnet: {
    url: `https://mainnet.infura.io/v3/${INFURA_API_KEY}`,
    accounts: [PRIVATE_KEY],
    gasPrice: ***********, // 20 gwei
  },
  goerli: {
    url: `https://goerli.infura.io/v3/${INFURA_API_KEY}`,
    accounts: [PRIVATE_KEY],
  }
}
```

## 📱 Frontend Entegrasyonu

### SDK Kullanımı
```javascript
const { SwapSDK } = require('./scripts/frontend-integration');

const config = {
  router: "0x...",
  factory: "0x...",
  tokenRegistry: "0x...",
  // ... diğer kontrat adresleri
};

const sdk = new SwapSDK(config, provider);

// Token listesi al
const tokens = await sdk.getSupportedTokens();

// Swap fiyatı hesapla
const price = await sdk.getTokenPrice(tokenA, tokenB, amount);

// Optimal yol bul
const path = await sdk.getOptimalPath(tokenA, tokenB, amount);

// Swap işlemi gerçekleştir
const tx = await sdk.executeSwap(tokenA, tokenB, amount, minOut, userAddress, signer);
```

### Event Subscription
```javascript
// Swap eventlerini dinle
sdk.subscribeToEvents('Swap', (event) => {
  console.log('New swap:', event);
});

// Likidite eventlerini dinle
sdk.subscribeToEvents('AddLiquidity', (event) => {
  console.log('Liquidity added:', event);
});
```

**⚠️ Uyarı**: Bu kontratlar eğitim ve geliştirme amaçlıdır. Mainnet'te kullanmadan önce kapsamlı audit yaptırın.