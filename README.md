# Advanced DEX Smart Contracts

This project is a comprehensive smart contract system for an enterprise-level decentralized exchange (DEX) operating on Ethereum. It offers a security-focused and scalable solution that includes all features of modern DeFi protocols.

## Core Features

### Core AMM Protocol
- **Automated Market Maker (AMM)**: Advanced Uniswap V2+ style AMM protocol
- **Liquidity Pools**: Optimized token pairs for liquidity provision
- **Multi-hop Routing**: Smart routing algorithm for optimal swap paths
- **Price Impact Protection**: Price impact limitation for large transactions

### Enterprise Security
- **Circuit Breaker**: Automatic protection during abnormal market conditions
- **MEV Protection**: Protection against front-running and sandwich attacks
- **Flash Loan Protection**: Defense against flash loan attacks
- **Emergency Pause**: Emergency stop mechanism
- **Multi-sig Guardian**: Security management with multiple signatures

### Advanced Price Oracle
- **TWAP (Time-Weighted Average Price)**: Manipulation-resistant price calculation
- **Chainlink Integration**: Verification with external price feeds
- **Price Deviation Detection**: Price deviation detection and alert system
- **Multiple Price Sources**: Reliable pricing with multiple sources

### Token Registry System
- **25+ Popular Tokens**: Pre-configured list of the most popular tokens on Ethereum
- **Verified Tokens**: Verified token system
- **Dynamic Token Management**: Dynamic token addition/removal
- **Market Cap Sorting**: Sorting by market capitalization

### Liquidity Mining & Governance
- **Yield Farming**: Reward system for liquidity providers
- **Governance Token**: Native token for voting and protocol management
- **Boost System**: Reward multipliers based on user levels
- **Referral System**: Commission sharing through referral system
- **Vesting Schedules**: Vesting programs for token distribution

## Contract Architecture

### Core Contracts

#### SwapFactory
- Creates and manages new token pairs
- Controls protocol fee settings
- Publishes pair creation events
- Pause/unpause functionality

#### SwapPair
- Liquidity pool contract (ERC20 LP tokens)
- Executes token swap operations
- TWAP support with price accumulator
- Flash loan protection

#### SwapRouter
- User-friendly swap interface
- Multi-hop routing support
- Slippage and deadline protection
- Security manager integration

### Advanced Contracts

#### SmartRouter
- Optimal path finding algorithm
- Gas-efficient routing
- Liquidity analysis
- Price impact calculation

#### SecurityManager
- Circuit breaker implementation
- MEV protection mechanisms
- Rate limiting
- Emergency controls
- Guardian management

#### PriceOracle
- TWAP calculation engine
- Chainlink price feed integration
- Price manipulation detection
- Multiple price source aggregation

#### TokenRegistry
- Comprehensive token database
- Verification system
- Market cap tracking
- Pagination support

#### LiquidityMining
- Multi-pool staking system
- Flexible reward distribution
- Boost multipliers
- Referral commissions
- Emergency withdrawal

#### GovernanceToken
- ERC20Votes implementation
- Controlled minting with caps
- Vesting schedule management
- Pause functionality

## Installation and Usage

### Requirements