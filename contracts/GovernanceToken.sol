// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Votes.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol"; 

/**
 * @title GovernanceToken
 * @dev ERC20 token with governance features, voting, and controlled minting
 * @notice This is the native governance token for the DEX protocol
 */
contract GovernanceToken is ERC20, ERC20Burnable, ERC20Permit, ERC20Votes, Ownable, Pausable {
    
    // Token Configuration
    uint256 public constant MAX_SUPPLY = 1_000_000_000 * 1e18; // 1 billion tokens
    uint256 public constant INITIAL_SUPPLY = 100_000_000 * 1e18; // 100 million tokens
    
    // Minting Configuration
    mapping(address => bool) public minters;
    uint256 public mintingCap = 50_000_000 * 1e18; // 50 million tokens per year
    uint256 public lastMintingReset;
    uint256 public currentYearMinted;
    uint256 public constant MINTING_RESET_PERIOD = 365 days;
    
    // Vesting Configuration
    struct VestingSchedule {
        uint256 totalAmount;
        uint256 releasedAmount;
        uint256 startTime;
        uint256 duration;
        uint256 cliffDuration;
        bool revocable;
        bool revoked;
    }
    
    mapping(address => VestingSchedule[]) public vestingSchedules;
    mapping(address => uint256) public totalVestedAmount;
    
    // Events
    event MinterAdded(address indexed minter);
    event MinterRemoved(address indexed minter);
    event VestingScheduleCreated(
        address indexed beneficiary,
        uint256 indexed scheduleId,
        uint256 amount,
        uint256 duration
    );
    event VestingScheduleRevoked(address indexed beneficiary, uint256 indexed scheduleId);
    event TokensVested(address indexed beneficiary, uint256 amount);
    event MintingCapUpdated(uint256 newCap);

    modifier onlyMinter() {
        require(minters[msg.sender], "GovernanceToken: NOT_MINTER");
        _;
    }

    constructor()
        ERC20("DEX Governance Token", "DEXGOV")
        ERC20Permit("DEX Governance Token")
    {
        // Mint initial supply to deployer
        _mint(msg.sender, INITIAL_SUPPLY);
        
        // Set initial minting reset time
        lastMintingReset = block.timestamp;
        
        // Add deployer as initial minter
        minters[msg.sender] = true;
        emit MinterAdded(msg.sender);
    }

    // **** MINTING FUNCTIONS ****

    /**
     * @dev Mint tokens with annual cap enforcement
     */
    function mint(address to, uint256 amount) external onlyMinter whenNotPaused {
        require(to != address(0), "GovernanceToken: ZERO_ADDRESS");
        require(amount > 0, "GovernanceToken: ZERO_AMOUNT");
        require(totalSupply() + amount <= MAX_SUPPLY, "GovernanceToken: EXCEEDS_MAX_SUPPLY");

        // Reset minting cap if a year has passed
        if (block.timestamp >= lastMintingReset + MINTING_RESET_PERIOD) {
            lastMintingReset = block.timestamp;
            currentYearMinted = 0;
        }

        // Check annual minting cap
        require(currentYearMinted + amount <= mintingCap, "GovernanceToken: EXCEEDS_MINTING_CAP");
        
        currentYearMinted += amount;
        _mint(to, amount);
    }

    /**
     * @dev Add a new minter
     */
    function addMinter(address minter) external onlyOwner {
        require(minter != address(0), "GovernanceToken: ZERO_ADDRESS");
        require(!minters[minter], "GovernanceToken: ALREADY_MINTER");
        
        minters[minter] = true;
        emit MinterAdded(minter);
    }

    /**
     * @dev Remove a minter
     */
    function removeMinter(address minter) external onlyOwner {
        require(minters[minter], "GovernanceToken: NOT_MINTER");
        
        minters[minter] = false;
        emit MinterRemoved(minter);
    }

    /**
     * @dev Update annual minting cap
     */
    function updateMintingCap(uint256 newCap) external onlyOwner {
        require(newCap <= MAX_SUPPLY / 10, "GovernanceToken: CAP_TOO_HIGH"); // Max 10% of total supply per year
        mintingCap = newCap;
        emit MintingCapUpdated(newCap);
    }

    // **** VESTING FUNCTIONS ****

    /**
     * @dev Create a vesting schedule for a beneficiary
     */
    function createVestingSchedule(
        address beneficiary,
        uint256 amount,
        uint256 startTime,
        uint256 duration,
        uint256 cliffDuration,
        bool revocable
    ) external onlyOwner {
        require(beneficiary != address(0), "GovernanceToken: ZERO_ADDRESS");
        require(amount > 0, "GovernanceToken: ZERO_AMOUNT");
        require(duration > 0, "GovernanceToken: ZERO_DURATION");
        require(cliffDuration <= duration, "GovernanceToken: INVALID_CLIFF");
        require(startTime >= block.timestamp, "GovernanceToken: INVALID_START_TIME");

        // Transfer tokens to contract for vesting
        _transfer(msg.sender, address(this), amount);

        // Create vesting schedule
        vestingSchedules[beneficiary].push(VestingSchedule({
            totalAmount: amount,
            releasedAmount: 0,
            startTime: startTime,
            duration: duration,
            cliffDuration: cliffDuration,
            revocable: revocable,
            revoked: false
        }));

        totalVestedAmount[beneficiary] += amount;

        uint256 scheduleId = vestingSchedules[beneficiary].length - 1;
        emit VestingScheduleCreated(beneficiary, scheduleId, amount, duration);
    }

    /**
     * @dev Release vested tokens for a beneficiary
     */
    function releaseVestedTokens(address beneficiary, uint256 scheduleId) external {
        require(scheduleId < vestingSchedules[beneficiary].length, "GovernanceToken: INVALID_SCHEDULE");
        
        VestingSchedule storage schedule = vestingSchedules[beneficiary][scheduleId];
        require(!schedule.revoked, "GovernanceToken: SCHEDULE_REVOKED");

        uint256 vestedAmount = _computeVestedAmount(schedule);
        uint256 releasableAmount = vestedAmount - schedule.releasedAmount;
        
        require(releasableAmount > 0, "GovernanceToken: NO_TOKENS_TO_RELEASE");

        schedule.releasedAmount += releasableAmount;
        totalVestedAmount[beneficiary] -= releasableAmount;

        _transfer(address(this), beneficiary, releasableAmount);
        emit TokensVested(beneficiary, releasableAmount);
    }

    /**
     * @dev Revoke a vesting schedule (if revocable)
     */
    function revokeVestingSchedule(address beneficiary, uint256 scheduleId) external onlyOwner {
        require(scheduleId < vestingSchedules[beneficiary].length, "GovernanceToken: INVALID_SCHEDULE");
        
        VestingSchedule storage schedule = vestingSchedules[beneficiary][scheduleId];
        require(schedule.revocable, "GovernanceToken: NOT_REVOCABLE");
        require(!schedule.revoked, "GovernanceToken: ALREADY_REVOKED");

        // Calculate vested amount up to now
        uint256 vestedAmount = _computeVestedAmount(schedule);
        uint256 releasableAmount = vestedAmount - schedule.releasedAmount;
        
        // Release any vested tokens
        if (releasableAmount > 0) {
            schedule.releasedAmount += releasableAmount;
            _transfer(address(this), beneficiary, releasableAmount);
            emit TokensVested(beneficiary, releasableAmount);
        }

        // Return unvested tokens to owner
        uint256 unvestedAmount = schedule.totalAmount - vestedAmount;
        if (unvestedAmount > 0) {
            _transfer(address(this), owner(), unvestedAmount);
        }

        // Mark as revoked
        schedule.revoked = true;
        totalVestedAmount[beneficiary] -= unvestedAmount;

        emit VestingScheduleRevoked(beneficiary, scheduleId);
    }

    /**
     * @dev Compute vested amount for a schedule
     */
    function _computeVestedAmount(VestingSchedule memory schedule) private view returns (uint256) {
        if (block.timestamp < schedule.startTime + schedule.cliffDuration) {
            return 0;
        }

        if (block.timestamp >= schedule.startTime + schedule.duration) {
            return schedule.totalAmount;
        }

        uint256 timeFromStart = block.timestamp - schedule.startTime;
        return (schedule.totalAmount * timeFromStart) / schedule.duration;
    }

    // **** VIEW FUNCTIONS ****

    /**
     * @dev Get vested amount for a beneficiary and schedule
     */
    function getVestedAmount(address beneficiary, uint256 scheduleId) external view returns (uint256) {
        require(scheduleId < vestingSchedules[beneficiary].length, "GovernanceToken: INVALID_SCHEDULE");
        
        VestingSchedule memory schedule = vestingSchedules[beneficiary][scheduleId];
        if (schedule.revoked) {
            return schedule.releasedAmount;
        }
        
        return _computeVestedAmount(schedule);
    }

    /**
     * @dev Get releasable amount for a beneficiary and schedule
     */
    function getReleasableAmount(address beneficiary, uint256 scheduleId) external view returns (uint256) {
        require(scheduleId < vestingSchedules[beneficiary].length, "GovernanceToken: INVALID_SCHEDULE");
        
        VestingSchedule memory schedule = vestingSchedules[beneficiary][scheduleId];
        if (schedule.revoked) {
            return 0;
        }
        
        uint256 vestedAmount = _computeVestedAmount(schedule);
        return vestedAmount - schedule.releasedAmount;
    }

    /**
     * @dev Get number of vesting schedules for a beneficiary
     */
    function getVestingScheduleCount(address beneficiary) external view returns (uint256) {
        return vestingSchedules[beneficiary].length;
    }

    /**
     * @dev Get remaining minting capacity for current year
     */
    function getRemainingMintingCapacity() external view returns (uint256) {
        // Check if we need to reset
        if (block.timestamp >= lastMintingReset + MINTING_RESET_PERIOD) {
            return mintingCap;
        }
        
        return mintingCap - currentYearMinted;
    }

    // **** ADMIN FUNCTIONS ****

    /**
     * @dev Pause token transfers
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause token transfers
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    // **** OVERRIDES ****

    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal override whenNotPaused {
        super._beforeTokenTransfer(from, to, amount);
    }

    function _afterTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal override(ERC20, ERC20Votes) {
        super._afterTokenTransfer(from, to, amount);
    }

    function _mint(address to, uint256 amount) internal override(ERC20, ERC20Votes) {
        super._mint(to, amount);
    }

    function _burn(address account, uint256 amount) internal override(ERC20, ERC20Votes) {
        super._burn(account, amount);
    }
}
