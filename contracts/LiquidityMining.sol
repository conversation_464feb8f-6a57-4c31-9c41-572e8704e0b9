// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";
import "./interfaces/ISwapPair.sol";
import "./GovernanceToken.sol";

/**
 * @title LiquidityMining
 * @dev Advanced liquidity mining contract with multiple reward pools and governance integration
 * @notice Stake LP tokens to earn governance tokens and other rewards
 */
contract LiquidityMining is Ownable, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;
    using SafeMath for uint256;

    // Pool Information
    struct PoolInfo {
        IERC20 lpToken;                 // LP token contract
        uint256 allocPoint;             // Allocation points for this pool
        uint256 lastRewardBlock;        // Last block number that rewards were distributed
        uint256 accRewardPerShare;      // Accumulated rewards per share, times 1e18
        uint256 totalStaked;            // Total amount of LP tokens staked
        uint256 minStakingPeriod;       // Minimum staking period in blocks
        uint256 withdrawalFee;          // Withdrawal fee in basis points (100 = 1%)
        bool isActive;                  // Pool is active
        string name;                    // Pool name for UI
    }

    // User Information
    struct UserInfo {
        uint256 amount;                 // Amount of LP tokens staked
        uint256 rewardDebt;             // Reward debt for primary token
        uint256 stakingStartBlock;      // Block when user started staking
        uint256 lastClaimBlock;         // Last block when user claimed rewards
        mapping(address => uint256) bonusRewardDebt; // Bonus reward debts
    }

    // Bonus Reward Information
    struct BonusReward {
        IERC20 token;                   // Bonus reward token
        uint256 rewardPerBlock;         // Bonus rewards per block
        uint256 startBlock;             // Start block for bonus rewards
        uint256 endBlock;               // End block for bonus rewards
        uint256 accRewardPerShare;      // Accumulated bonus rewards per share
        bool isActive;                  // Bonus reward is active
    }

    // State Variables
    GovernanceToken public immutable rewardToken;
    uint256 public rewardPerBlock;
    uint256 public startBlock;
    uint256 public endBlock;
    uint256 public totalAllocPoint;
    uint256 public constant BONUS_MULTIPLIER = 1;

    // Pool and user mappings
    PoolInfo[] public poolInfo;
    mapping(uint256 => mapping(address => UserInfo)) public userInfo;
    mapping(uint256 => mapping(address => BonusReward)) public bonusRewards;
    mapping(uint256 => address[]) public poolBonusTokens;

    // Referral system
    mapping(address => address) public referrers;
    mapping(address => uint256) public referralRewards;
    uint256 public referralCommission = 100; // 1%

    // Boost system
    mapping(address => uint256) public userBoostLevel;
    mapping(uint256 => uint256) public boostMultipliers; // boost level => multiplier (basis points)

    // Events
    event PoolAdded(uint256 indexed pid, address indexed lpToken, uint256 allocPoint);
    event PoolUpdated(uint256 indexed pid, uint256 allocPoint);
    event Deposit(address indexed user, uint256 indexed pid, uint256 amount);
    event Withdraw(address indexed user, uint256 indexed pid, uint256 amount);
    event EmergencyWithdraw(address indexed user, uint256 indexed pid, uint256 amount);
    event RewardClaimed(address indexed user, uint256 indexed pid, uint256 amount);
    event BonusRewardAdded(uint256 indexed pid, address indexed token, uint256 rewardPerBlock);
    event ReferralCommissionPaid(address indexed referrer, address indexed user, uint256 amount);
    event BoostLevelUpdated(address indexed user, uint256 level);

    constructor(
        GovernanceToken _rewardToken,
        uint256 _rewardPerBlock,
        uint256 _startBlock,
        uint256 _endBlock
    ) {
        rewardToken = _rewardToken;
        rewardPerBlock = _rewardPerBlock;
        startBlock = _startBlock;
        endBlock = _endBlock;

        // Initialize boost multipliers
        boostMultipliers[0] = 10000; // 1x (no boost)
        boostMultipliers[1] = 11000; // 1.1x
        boostMultipliers[2] = 12500; // 1.25x
        boostMultipliers[3] = 15000; // 1.5x
        boostMultipliers[4] = 20000; // 2x
    }

    // **** POOL MANAGEMENT ****

    /**
     * @dev Add a new LP token pool
     */
    function addPool(
        uint256 _allocPoint,
        IERC20 _lpToken,
        uint256 _minStakingPeriod,
        uint256 _withdrawalFee,
        string memory _name,
        bool _withUpdate
    ) external onlyOwner {
        require(address(_lpToken) != address(0), "LiquidityMining: ZERO_ADDRESS");
        require(_withdrawalFee <= 1000, "LiquidityMining: INVALID_FEE"); // Max 10%

        if (_withUpdate) {
            massUpdatePools();
        }

        uint256 lastRewardBlock = block.number > startBlock ? block.number : startBlock;
        totalAllocPoint = totalAllocPoint.add(_allocPoint);

        poolInfo.push(PoolInfo({
            lpToken: _lpToken,
            allocPoint: _allocPoint,
            lastRewardBlock: lastRewardBlock,
            accRewardPerShare: 0,
            totalStaked: 0,
            minStakingPeriod: _minStakingPeriod,
            withdrawalFee: _withdrawalFee,
            isActive: true,
            name: _name
        }));

        emit PoolAdded(poolInfo.length - 1, address(_lpToken), _allocPoint);
    }

    /**
     * @dev Update pool allocation points
     */
    function updatePool(uint256 _pid, uint256 _allocPoint, bool _withUpdate) external onlyOwner {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");

        if (_withUpdate) {
            massUpdatePools();
        }

        totalAllocPoint = totalAllocPoint.sub(poolInfo[_pid].allocPoint).add(_allocPoint);
        poolInfo[_pid].allocPoint = _allocPoint;

        emit PoolUpdated(_pid, _allocPoint);
    }

    /**
     * @dev Set pool active status
     */
    function setPoolActive(uint256 _pid, bool _isActive) external onlyOwner {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");
        poolInfo[_pid].isActive = _isActive;
    }

    // **** BONUS REWARDS ****

    /**
     * @dev Add bonus reward token to a pool
     */
    function addBonusReward(
        uint256 _pid,
        IERC20 _bonusToken,
        uint256 _rewardPerBlock,
        uint256 _startBlock,
        uint256 _endBlock
    ) external onlyOwner {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");
        require(address(_bonusToken) != address(0), "LiquidityMining: ZERO_ADDRESS");
        require(_startBlock < _endBlock, "LiquidityMining: INVALID_BLOCKS");

        updatePool(_pid);

        bonusRewards[_pid][address(_bonusToken)] = BonusReward({
            token: _bonusToken,
            rewardPerBlock: _rewardPerBlock,
            startBlock: _startBlock,
            endBlock: _endBlock,
            accRewardPerShare: 0,
            isActive: true
        });

        poolBonusTokens[_pid].push(address(_bonusToken));

        emit BonusRewardAdded(_pid, address(_bonusToken), _rewardPerBlock);
    }

    /**
     * @dev Update bonus reward parameters
     */
    function updateBonusReward(
        uint256 _pid,
        address _bonusToken,
        uint256 _rewardPerBlock,
        uint256 _endBlock
    ) external onlyOwner {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");
        
        BonusReward storage bonus = bonusRewards[_pid][_bonusToken];
        require(bonus.isActive, "LiquidityMining: BONUS_NOT_ACTIVE");

        updatePool(_pid);
        _updateBonusReward(_pid, _bonusToken);

        bonus.rewardPerBlock = _rewardPerBlock;
        bonus.endBlock = _endBlock;
    }

    // **** STAKING FUNCTIONS ****

    /**
     * @dev Stake LP tokens
     */
    function deposit(uint256 _pid, uint256 _amount, address _referrer) external nonReentrant whenNotPaused {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");
        require(_amount > 0, "LiquidityMining: ZERO_AMOUNT");

        PoolInfo storage pool = poolInfo[_pid];
        require(pool.isActive, "LiquidityMining: POOL_INACTIVE");

        UserInfo storage user = userInfo[_pid][msg.sender];

        // Set referrer if not set and valid
        if (referrers[msg.sender] == address(0) && _referrer != address(0) && _referrer != msg.sender) {
            referrers[msg.sender] = _referrer;
        }

        updatePool(_pid);
        _updateAllBonusRewards(_pid);

        // Claim pending rewards
        if (user.amount > 0) {
            _claimRewards(_pid, msg.sender);
        }

        // Transfer LP tokens
        pool.lpToken.safeTransferFrom(msg.sender, address(this), _amount);

        // Update user info
        user.amount = user.amount.add(_amount);
        user.stakingStartBlock = block.number;
        user.rewardDebt = user.amount.mul(pool.accRewardPerShare).div(1e18);

        // Update bonus reward debts
        address[] memory bonusTokens = poolBonusTokens[_pid];
        for (uint256 i = 0; i < bonusTokens.length; i++) {
            address bonusToken = bonusTokens[i];
            BonusReward storage bonus = bonusRewards[_pid][bonusToken];
            if (bonus.isActive) {
                user.bonusRewardDebt[bonusToken] = user.amount.mul(bonus.accRewardPerShare).div(1e18);
            }
        }

        // Update pool total
        pool.totalStaked = pool.totalStaked.add(_amount);

        emit Deposit(msg.sender, _pid, _amount);
    }

    /**
     * @dev Withdraw LP tokens
     */
    function withdraw(uint256 _pid, uint256 _amount) external nonReentrant {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");
        require(_amount > 0, "LiquidityMining: ZERO_AMOUNT");

        PoolInfo storage pool = poolInfo[_pid];
        UserInfo storage user = userInfo[_pid][msg.sender];
        require(user.amount >= _amount, "LiquidityMining: INSUFFICIENT_BALANCE");

        // Check minimum staking period
        require(
            block.number >= user.stakingStartBlock.add(pool.minStakingPeriod),
            "LiquidityMining: MIN_STAKING_PERIOD"
        );

        updatePool(_pid);
        _updateAllBonusRewards(_pid);

        // Claim pending rewards
        _claimRewards(_pid, msg.sender);

        // Update user info
        user.amount = user.amount.sub(_amount);
        user.rewardDebt = user.amount.mul(pool.accRewardPerShare).div(1e18);

        // Update bonus reward debts
        address[] memory bonusTokens = poolBonusTokens[_pid];
        for (uint256 i = 0; i < bonusTokens.length; i++) {
            address bonusToken = bonusTokens[i];
            BonusReward storage bonus = bonusRewards[_pid][bonusToken];
            if (bonus.isActive) {
                user.bonusRewardDebt[bonusToken] = user.amount.mul(bonus.accRewardPerShare).div(1e18);
            }
        }

        // Calculate withdrawal fee
        uint256 withdrawAmount = _amount;
        if (pool.withdrawalFee > 0) {
            uint256 fee = _amount.mul(pool.withdrawalFee).div(10000);
            withdrawAmount = _amount.sub(fee);
            // Fee stays in contract for protocol treasury
        }

        // Update pool total
        pool.totalStaked = pool.totalStaked.sub(_amount);

        // Transfer LP tokens
        pool.lpToken.safeTransfer(msg.sender, withdrawAmount);

        emit Withdraw(msg.sender, _pid, _amount);
    }

    /**
     * @dev Emergency withdraw without caring about rewards
     */
    function emergencyWithdraw(uint256 _pid) external nonReentrant {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");

        PoolInfo storage pool = poolInfo[_pid];
        UserInfo storage user = userInfo[_pid][msg.sender];
        uint256 amount = user.amount;

        require(amount > 0, "LiquidityMining: NO_STAKE");

        // Reset user info
        user.amount = 0;
        user.rewardDebt = 0;

        // Reset bonus reward debts
        address[] memory bonusTokens = poolBonusTokens[_pid];
        for (uint256 i = 0; i < bonusTokens.length; i++) {
            user.bonusRewardDebt[bonusTokens[i]] = 0;
        }

        // Update pool total
        pool.totalStaked = pool.totalStaked.sub(amount);

        // Transfer LP tokens (with emergency fee)
        uint256 emergencyFee = amount.mul(500).div(10000); // 5% emergency fee
        uint256 withdrawAmount = amount.sub(emergencyFee);
        pool.lpToken.safeTransfer(msg.sender, withdrawAmount);

        emit EmergencyWithdraw(msg.sender, _pid, amount);
    }

    /**
     * @dev Claim rewards for a specific pool
     */
    function claimRewards(uint256 _pid) external nonReentrant {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");

        updatePool(_pid);
        _updateAllBonusRewards(_pid);
        _claimRewards(_pid, msg.sender);
    }

    /**
     * @dev Claim rewards for all pools
     */
    function claimAllRewards() external nonReentrant {
        for (uint256 pid = 0; pid < poolInfo.length; pid++) {
            UserInfo storage user = userInfo[pid][msg.sender];
            if (user.amount > 0) {
                updatePool(pid);
                _updateAllBonusRewards(pid);
                _claimRewards(pid, msg.sender);
            }
        }
    }

    // **** INTERNAL FUNCTIONS ****

    /**
     * @dev Update reward variables for a pool
     */
    function updatePool(uint256 _pid) public {
        PoolInfo storage pool = poolInfo[_pid];

        if (block.number <= pool.lastRewardBlock) {
            return;
        }

        if (pool.totalStaked == 0 || pool.allocPoint == 0) {
            pool.lastRewardBlock = block.number;
            return;
        }

        uint256 multiplier = getMultiplier(pool.lastRewardBlock, block.number);
        uint256 reward = multiplier.mul(rewardPerBlock).mul(pool.allocPoint).div(totalAllocPoint);

        // Mint rewards to contract
        rewardToken.mint(address(this), reward);

        pool.accRewardPerShare = pool.accRewardPerShare.add(reward.mul(1e18).div(pool.totalStaked));
        pool.lastRewardBlock = block.number;
    }

    /**
     * @dev Update all pools
     */
    function massUpdatePools() public {
        for (uint256 pid = 0; pid < poolInfo.length; pid++) {
            updatePool(pid);
        }
    }

    /**
     * @dev Internal function to claim rewards
     */
    function _claimRewards(uint256 _pid, address _user) internal {
        PoolInfo storage pool = poolInfo[_pid];
        UserInfo storage user = userInfo[_pid][_user];

        if (user.amount == 0) return;

        // Calculate primary reward
        uint256 pending = user.amount.mul(pool.accRewardPerShare).div(1e18).sub(user.rewardDebt);

        if (pending > 0) {
            // Apply boost multiplier
            uint256 boostLevel = userBoostLevel[_user];
            uint256 multiplier = boostMultipliers[boostLevel];
            pending = pending.mul(multiplier).div(10000);

            // Pay referral commission
            address referrer = referrers[_user];
            if (referrer != address(0)) {
                uint256 commission = pending.mul(referralCommission).div(10000);
                rewardToken.mint(referrer, commission);
                referralRewards[referrer] = referralRewards[referrer].add(commission);
                emit ReferralCommissionPaid(referrer, _user, commission);
            }

            // Transfer primary reward
            rewardToken.mint(_user, pending);
            user.lastClaimBlock = block.number;
            emit RewardClaimed(_user, _pid, pending);
        }

        // Claim bonus rewards
        address[] memory bonusTokens = poolBonusTokens[_pid];
        for (uint256 i = 0; i < bonusTokens.length; i++) {
            address bonusToken = bonusTokens[i];
            BonusReward storage bonus = bonusRewards[_pid][bonusToken];

            if (bonus.isActive) {
                uint256 bonusPending = user.amount.mul(bonus.accRewardPerShare).div(1e18).sub(user.bonusRewardDebt[bonusToken]);
                if (bonusPending > 0) {
                    bonus.token.safeTransfer(_user, bonusPending);
                }
            }
        }

        // Update reward debt
        user.rewardDebt = user.amount.mul(pool.accRewardPerShare).div(1e18);

        // Update bonus reward debts
        for (uint256 i = 0; i < bonusTokens.length; i++) {
            address bonusToken = bonusTokens[i];
            BonusReward storage bonus = bonusRewards[_pid][bonusToken];
            if (bonus.isActive) {
                user.bonusRewardDebt[bonusToken] = user.amount.mul(bonus.accRewardPerShare).div(1e18);
            }
        }
    }

    /**
     * @dev Update bonus reward for a specific token
     */
    function _updateBonusReward(uint256 _pid, address _bonusToken) internal {
        BonusReward storage bonus = bonusRewards[_pid][_bonusToken];
        PoolInfo storage pool = poolInfo[_pid];

        if (block.number <= pool.lastRewardBlock || pool.totalStaked == 0) {
            return;
        }

        uint256 bonusEndBlock = bonus.endBlock > block.number ? block.number : bonus.endBlock;
        uint256 bonusStartBlock = pool.lastRewardBlock > bonus.startBlock ? pool.lastRewardBlock : bonus.startBlock;

        if (bonusEndBlock <= bonusStartBlock) {
            return;
        }

        uint256 multiplier = bonusEndBlock.sub(bonusStartBlock);
        uint256 bonusReward = multiplier.mul(bonus.rewardPerBlock);

        bonus.accRewardPerShare = bonus.accRewardPerShare.add(bonusReward.mul(1e18).div(pool.totalStaked));
    }

    /**
     * @dev Update all bonus rewards for a pool
     */
    function _updateAllBonusRewards(uint256 _pid) internal {
        address[] memory bonusTokens = poolBonusTokens[_pid];
        for (uint256 i = 0; i < bonusTokens.length; i++) {
            _updateBonusReward(_pid, bonusTokens[i]);
        }
    }

    /**
     * @dev Get multiplier for reward calculation
     */
    function getMultiplier(uint256 _from, uint256 _to) public view returns (uint256) {
        if (_to <= endBlock) {
            return _to.sub(_from).mul(BONUS_MULTIPLIER);
        } else if (_from >= endBlock) {
            return 0;
        } else {
            return endBlock.sub(_from).mul(BONUS_MULTIPLIER);
        }
    }

    // **** VIEW FUNCTIONS ****

    /**
     * @dev Get pending rewards for a user in a pool
     */
    function pendingRewards(uint256 _pid, address _user)
        external
        view
        returns (uint256 primaryReward, uint256[] memory bonusRewardAmounts)
    {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");

        PoolInfo storage pool = poolInfo[_pid];
        UserInfo storage user = userInfo[_pid][_user];
        uint256 accRewardPerShare = pool.accRewardPerShare;

        if (block.number > pool.lastRewardBlock && pool.totalStaked != 0) {
            uint256 blockMultiplier = getMultiplier(pool.lastRewardBlock, block.number);
            uint256 reward = blockMultiplier.mul(rewardPerBlock).mul(pool.allocPoint).div(totalAllocPoint);
            accRewardPerShare = accRewardPerShare.add(reward.mul(1e18).div(pool.totalStaked));
        }

        primaryReward = user.amount.mul(accRewardPerShare).div(1e18).sub(user.rewardDebt);

        // Apply boost multiplier
        uint256 boostLevel = userBoostLevel[_user];
        uint256 multiplier = boostMultipliers[boostLevel];
        primaryReward = primaryReward.mul(multiplier).div(10000);

        // Calculate bonus rewards
        address[] memory bonusTokens = poolBonusTokens[_pid];
        bonusRewardAmounts = new uint256[](bonusTokens.length);

        for (uint256 i = 0; i < bonusTokens.length; i++) {
            address bonusToken = bonusTokens[i];
            BonusReward storage bonus = bonusRewards[_pid][bonusToken];

            if (bonus.isActive) {
                uint256 bonusAccRewardPerShare = bonus.accRewardPerShare;

                if (block.number > pool.lastRewardBlock && pool.totalStaked != 0) {
                    uint256 bonusEndBlock2 = bonus.endBlock > block.number ? block.number : bonus.endBlock;
                    uint256 bonusStartBlock2 = pool.lastRewardBlock > bonus.startBlock ? pool.lastRewardBlock : bonus.startBlock;

                    if (bonusEndBlock2 > bonusStartBlock2) {
                        uint256 bonusMultiplier = bonusEndBlock2.sub(bonusStartBlock2);
                        uint256 bonusReward = bonusMultiplier.mul(bonus.rewardPerBlock);
                        bonusAccRewardPerShare = bonusAccRewardPerShare.add(bonusReward.mul(1e18).div(pool.totalStaked));
                    }
                }

                bonusRewardAmounts[i] = user.amount.mul(bonusAccRewardPerShare).div(1e18).sub(user.bonusRewardDebt[bonusToken]);
            }
        }
    }

    /**
     * @dev Get pool information
     */
    function getPoolInfo(uint256 _pid)
        external
        view
        returns (
            address lpToken,
            uint256 allocPoint,
            uint256 totalStaked,
            uint256 accRewardPerShare,
            bool isActive,
            string memory name
        )
    {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");

        PoolInfo storage pool = poolInfo[_pid];
        return (
            address(pool.lpToken),
            pool.allocPoint,
            pool.totalStaked,
            pool.accRewardPerShare,
            pool.isActive,
            pool.name
        );
    }

    /**
     * @dev Get user information for a pool
     */
    function getUserInfo(uint256 _pid, address _user)
        external
        view
        returns (
            uint256 amount,
            uint256 rewardDebt,
            uint256 stakingStartBlock,
            uint256 lastClaimBlock
        )
    {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");

        UserInfo storage user = userInfo[_pid][_user];
        return (
            user.amount,
            user.rewardDebt,
            user.stakingStartBlock,
            user.lastClaimBlock
        );
    }

    /**
     * @dev Get total number of pools
     */
    function poolLength() external view returns (uint256) {
        return poolInfo.length;
    }

    /**
     * @dev Get bonus tokens for a pool
     */
    function getPoolBonusTokens(uint256 _pid) external view returns (address[] memory) {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");
        return poolBonusTokens[_pid];
    }

    /**
     * @dev Get user's total staked amount across all pools
     */
    function getUserTotalStaked(address _user) external view returns (uint256 totalStaked) {
        for (uint256 pid = 0; pid < poolInfo.length; pid++) {
            totalStaked = totalStaked.add(userInfo[pid][_user].amount);
        }
    }

    /**
     * @dev Get APR for a pool (approximate)
     */
    function getPoolAPR(uint256 _pid) external view returns (uint256) {
        require(_pid < poolInfo.length, "LiquidityMining: INVALID_POOL");

        PoolInfo storage pool = poolInfo[_pid];
        if (pool.totalStaked == 0 || totalAllocPoint == 0) {
            return 0;
        }

        // Approximate blocks per year (assuming 12 second block time)
        uint256 blocksPerYear = 365 * 24 * 60 * 60 / 12;
        uint256 yearlyRewards = rewardPerBlock.mul(blocksPerYear).mul(pool.allocPoint).div(totalAllocPoint);

        // Return APR in basis points (10000 = 100%)
        return yearlyRewards.mul(10000).div(pool.totalStaked);
    }

    // **** ADMIN FUNCTIONS ****

    /**
     * @dev Update reward per block
     */
    function updateRewardPerBlock(uint256 _rewardPerBlock) external onlyOwner {
        massUpdatePools();
        rewardPerBlock = _rewardPerBlock;
    }

    /**
     * @dev Update end block
     */
    function updateEndBlock(uint256 _endBlock) external onlyOwner {
        require(_endBlock > block.number, "LiquidityMining: INVALID_END_BLOCK");
        endBlock = _endBlock;
    }

    /**
     * @dev Set user boost level
     */
    function setUserBoostLevel(address _user, uint256 _level) external onlyOwner {
        require(_level <= 4, "LiquidityMining: INVALID_BOOST_LEVEL");
        userBoostLevel[_user] = _level;
        emit BoostLevelUpdated(_user, _level);
    }

    /**
     * @dev Update boost multiplier
     */
    function updateBoostMultiplier(uint256 _level, uint256 _multiplier) external onlyOwner {
        require(_level <= 4, "LiquidityMining: INVALID_BOOST_LEVEL");
        require(_multiplier >= 10000 && _multiplier <= 50000, "LiquidityMining: INVALID_MULTIPLIER");
        boostMultipliers[_level] = _multiplier;
    }

    /**
     * @dev Update referral commission
     */
    function updateReferralCommission(uint256 _commission) external onlyOwner {
        require(_commission <= 1000, "LiquidityMining: INVALID_COMMISSION"); // Max 10%
        referralCommission = _commission;
    }

    /**
     * @dev Emergency pause
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Emergency token recovery
     */
    function emergencyTokenRecovery(IERC20 _token, uint256 _amount) external onlyOwner {
        require(address(_token) != address(rewardToken), "LiquidityMining: CANNOT_RECOVER_REWARD_TOKEN");

        // Check if token is not a staked LP token
        for (uint256 pid = 0; pid < poolInfo.length; pid++) {
            require(address(_token) != address(poolInfo[pid].lpToken), "LiquidityMining: CANNOT_RECOVER_LP_TOKEN");
        }

        _token.safeTransfer(owner(), _amount);
    }
}
