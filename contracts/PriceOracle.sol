// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "./interfaces/ISwapPair.sol";
import "./interfaces/ISwapFactory.sol";
import "./libraries/UQ112x112.sol";
import "./ChainlinkPriceFeeds.sol";

/**
 * @title PriceOracle
 * @dev Comprehensive price oracle with TWAP and Chainlink integration
 * @notice Provides reliable price feeds for the DEX with manipulation resistance
 */
contract PriceOracle is Ownable, ReentrancyGuard {
    using UQ112x112 for uint224;

    // TWAP Configuration
    struct TWAPConfig {
        uint32 period;              // TWAP period in seconds
        uint32 granularity;         // Number of observations stored
        bool enabled;               // TWAP enabled for this pair
    }

    // Price Observation
    struct Observation {
        uint32 timestamp;
        uint224 price0Cumulative;
        uint224 price1Cumulative;
    }

    // Chainlink Price Feed Interface
    interface AggregatorV3Interface {
        function latestRoundData() external view returns (
            uint80 roundId,
            int256 price,
            uint256 startedAt,
            uint256 updatedAt,
            uint80 answeredInRound
        );
        function decimals() external view returns (uint8);
    }

    // State Variables
    mapping(address => TWAPConfig) public twapConfigs;
    mapping(address => Observation[]) public observations;
    mapping(address => uint256) public observationIndex;
    mapping(address => AggregatorV3Interface) public chainlinkFeeds;
    mapping(address => bool) public useChainlink;
    
    address public immutable factory;
    address public immutable WETH;
    
    // Default TWAP configuration
    uint32 public constant DEFAULT_PERIOD = 1800; // 30 minutes
    uint32 public constant DEFAULT_GRANULARITY = 8;
    uint256 public constant PRICE_PRECISION = 1e18;
    uint256 public constant MAX_PRICE_DEVIATION = 1000; // 10%

    // Events
    event TWAPConfigUpdated(address indexed pair, uint32 period, uint32 granularity, bool enabled);
    event ChainlinkFeedUpdated(address indexed token, address indexed feed, bool useChainlink);
    event PriceUpdated(address indexed pair, uint256 price0, uint256 price1);
    event PriceDeviation(address indexed pair, uint256 twapPrice, uint256 chainlinkPrice, uint256 deviation);

    constructor(address _factory, address _WETH) {
        factory = _factory;
        WETH = _WETH;

        // Otomatik olarak Chainlink feed'lerini ayarla
        _initializeChainlinkFeeds();
    }

    /**
     * @dev Initialize Chainlink price feeds for supported tokens
     */
    function _initializeChainlinkFeeds() private {
        (address[] memory tokens, address[] memory feeds) = ChainlinkPriceFeeds.getSupportedTokens();

        for (uint256 i = 0; i < tokens.length; i++) {
            if (tokens[i] != address(0) && feeds[i] != address(0)) {
                chainlinkFeeds[tokens[i]] = AggregatorV3Interface(feeds[i]);
                useChainlink[tokens[i]] = true;
            }
        }
    }

    // **** TWAP FUNCTIONS ****
    
    /**
     * @dev Update TWAP observations for a pair
     */
    function update(address pair) external nonReentrant {
        require(pair != address(0), "PriceOracle: ZERO_ADDRESS");
        
        TWAPConfig memory config = twapConfigs[pair];
        if (!config.enabled) return;

        (uint112 reserve0, uint112 reserve1, uint32 blockTimestamp) = ISwapPair(pair).getReserves();
        if (reserve0 == 0 || reserve1 == 0) return;

        uint224 price0Cumulative = ISwapPair(pair).price0CumulativeLast();
        uint224 price1Cumulative = ISwapPair(pair).price1CumulativeLast();

        Observation[] storage pairObservations = observations[pair];
        uint256 index = observationIndex[pair];

        // Initialize observations array if needed
        if (pairObservations.length == 0) {
            for (uint i = 0; i < config.granularity; i++) {
                pairObservations.push(Observation({
                    timestamp: blockTimestamp,
                    price0Cumulative: price0Cumulative,
                    price1Cumulative: price1Cumulative
                }));
            }
            return;
        }

        // Check if enough time has passed
        uint32 timeElapsed = blockTimestamp - pairObservations[index].timestamp;
        if (timeElapsed < config.period / config.granularity) return;

        // Update observation
        index = (index + 1) % config.granularity;
        pairObservations[index] = Observation({
            timestamp: blockTimestamp,
            price0Cumulative: price0Cumulative,
            price1Cumulative: price1Cumulative
        });
        observationIndex[pair] = index;

        emit PriceUpdated(pair, uint256(price0Cumulative), uint256(price1Cumulative));
    }

    /**
     * @dev Get TWAP price for a pair
     */
    function getTWAPPrice(address pair, uint32 period) 
        external 
        view 
        returns (uint256 price0, uint256 price1) 
    {
        require(pair != address(0), "PriceOracle: ZERO_ADDRESS");
        require(period > 0, "PriceOracle: INVALID_PERIOD");

        TWAPConfig memory config = twapConfigs[pair];
        require(config.enabled, "PriceOracle: TWAP_NOT_ENABLED");

        Observation[] storage pairObservations = observations[pair];
        require(pairObservations.length > 0, "PriceOracle: NO_OBSERVATIONS");

        uint256 currentIndex = observationIndex[pair];
        Observation memory currentObservation = pairObservations[currentIndex];
        
        // Find the observation closest to the desired period
        uint32 targetTimestamp = currentObservation.timestamp - period;
        Observation memory targetObservation;
        
        for (uint i = 0; i < config.granularity; i++) {
            uint256 index = (currentIndex + config.granularity - i) % config.granularity;
            if (pairObservations[index].timestamp <= targetTimestamp) {
                targetObservation = pairObservations[index];
                break;
            }
        }

        uint32 timeElapsed = currentObservation.timestamp - targetObservation.timestamp;
        require(timeElapsed > 0, "PriceOracle: INVALID_TIME_ELAPSED");

        // Calculate TWAP
        uint224 price0Average = (currentObservation.price0Cumulative - targetObservation.price0Cumulative) / timeElapsed;
        uint224 price1Average = (currentObservation.price1Cumulative - targetObservation.price1Cumulative) / timeElapsed;

        price0 = uint256(price0Average) * PRICE_PRECISION >> 112;
        price1 = uint256(price1Average) * PRICE_PRECISION >> 112;
    }

    /**
     * @dev Get current spot price from pair reserves
     */
    function getSpotPrice(address pair) 
        external 
        view 
        returns (uint256 price0, uint256 price1) 
    {
        require(pair != address(0), "PriceOracle: ZERO_ADDRESS");
        
        (uint112 reserve0, uint112 reserve1,) = ISwapPair(pair).getReserves();
        require(reserve0 > 0 && reserve1 > 0, "PriceOracle: INSUFFICIENT_LIQUIDITY");

        price0 = (uint256(reserve1) * PRICE_PRECISION) / reserve0;
        price1 = (uint256(reserve0) * PRICE_PRECISION) / reserve1;
    }

    // **** CHAINLINK INTEGRATION ****
    
    /**
     * @dev Get price from Chainlink feed
     */
    function getChainlinkPrice(address token) 
        external 
        view 
        returns (uint256 price, uint256 updatedAt) 
    {
        require(useChainlink[token], "PriceOracle: CHAINLINK_NOT_ENABLED");
        
        AggregatorV3Interface feed = chainlinkFeeds[token];
        require(address(feed) != address(0), "PriceOracle: NO_CHAINLINK_FEED");

        (, int256 rawPrice,, uint256 timestamp,) = feed.latestRoundData();
        require(rawPrice > 0, "PriceOracle: INVALID_CHAINLINK_PRICE");
        require(timestamp > 0, "PriceOracle: STALE_CHAINLINK_PRICE");

        uint8 decimals = feed.decimals();
        price = uint256(rawPrice) * PRICE_PRECISION / (10 ** decimals);
        updatedAt = timestamp;
    }

    /**
     * @dev Get combined price with deviation check
     */
    function getCombinedPrice(address tokenA, address tokenB) 
        external 
        view 
        returns (uint256 price, bool reliable) 
    {
        address pair = ISwapFactory(factory).getPair(tokenA, tokenB);
        require(pair != address(0), "PriceOracle: PAIR_NOT_EXISTS");

        // Get TWAP price if available
        uint256 twapPrice;
        if (twapConfigs[pair].enabled) {
            (uint256 price0, uint256 price1) = this.getTWAPPrice(pair, DEFAULT_PERIOD);
            twapPrice = tokenA < tokenB ? price0 : price1;
        }

        // Get Chainlink price if available
        uint256 chainlinkPrice;
        bool hasChainlink = false;
        if (useChainlink[tokenA] && useChainlink[tokenB]) {
            (uint256 priceA,) = this.getChainlinkPrice(tokenA);
            (uint256 priceB,) = this.getChainlinkPrice(tokenB);
            chainlinkPrice = (priceA * PRICE_PRECISION) / priceB;
            hasChainlink = true;
        }

        // Determine final price and reliability
        if (twapPrice > 0 && hasChainlink) {
            uint256 deviation = twapPrice > chainlinkPrice ? 
                ((twapPrice - chainlinkPrice) * 10000) / chainlinkPrice :
                ((chainlinkPrice - twapPrice) * 10000) / twapPrice;
            
            if (deviation > MAX_PRICE_DEVIATION) {
                emit PriceDeviation(pair, twapPrice, chainlinkPrice, deviation);
                reliable = false;
            } else {
                reliable = true;
            }
            
            // Use average of both prices
            price = (twapPrice + chainlinkPrice) / 2;
        } else if (twapPrice > 0) {
            price = twapPrice;
            reliable = true;
        } else if (hasChainlink) {
            price = chainlinkPrice;
            reliable = true;
        } else {
            // Fallback to spot price
            (uint256 price0, uint256 price1) = this.getSpotPrice(pair);
            price = tokenA < tokenB ? price0 : price1;
            reliable = false;
        }
    }

    // **** ADMIN FUNCTIONS ****

    /**
     * @dev Configure TWAP for a pair
     */
    function configureTWAP(
        address pair,
        uint32 period,
        uint32 granularity,
        bool enabled
    ) external onlyOwner {
        require(pair != address(0), "PriceOracle: ZERO_ADDRESS");
        require(period > 0, "PriceOracle: INVALID_PERIOD");
        require(granularity > 0 && granularity <= 255, "PriceOracle: INVALID_GRANULARITY");

        twapConfigs[pair] = TWAPConfig({
            period: period,
            granularity: granularity,
            enabled: enabled
        });

        // Clear existing observations when reconfiguring
        if (enabled) {
            delete observations[pair];
            observationIndex[pair] = 0;
        }

        emit TWAPConfigUpdated(pair, period, granularity, enabled);
    }

    /**
     * @dev Set Chainlink price feed for a token
     */
    function setChainlinkFeed(
        address token,
        address feed,
        bool enabled
    ) external onlyOwner {
        require(token != address(0), "PriceOracle: ZERO_ADDRESS");

        if (enabled) {
            require(feed != address(0), "PriceOracle: ZERO_FEED_ADDRESS");
            chainlinkFeeds[token] = AggregatorV3Interface(feed);
        }

        useChainlink[token] = enabled;
        emit ChainlinkFeedUpdated(token, feed, enabled);
    }

    /**
     * @dev Batch update multiple pairs
     */
    function batchUpdate(address[] calldata pairs) external {
        for (uint i = 0; i < pairs.length; i++) {
            this.update(pairs[i]);
        }
    }

    // **** VIEW FUNCTIONS ****

    /**
     * @dev Check if TWAP is available for a pair
     */
    function isTWAPAvailable(address pair) external view returns (bool) {
        return twapConfigs[pair].enabled && observations[pair].length > 0;
    }

    /**
     * @dev Get TWAP configuration for a pair
     */
    function getTWAPConfig(address pair)
        external
        view
        returns (uint32 period, uint32 granularity, bool enabled)
    {
        TWAPConfig memory config = twapConfigs[pair];
        return (config.period, config.granularity, config.enabled);
    }

    /**
     * @dev Get latest observation for a pair
     */
    function getLatestObservation(address pair)
        external
        view
        returns (uint32 timestamp, uint224 price0Cumulative, uint224 price1Cumulative)
    {
        require(observations[pair].length > 0, "PriceOracle: NO_OBSERVATIONS");

        uint256 index = observationIndex[pair];
        Observation memory obs = observations[pair][index];
        return (obs.timestamp, obs.price0Cumulative, obs.price1Cumulative);
    }

    /**
     * @dev Get observation count for a pair
     */
    function getObservationCount(address pair) external view returns (uint256) {
        return observations[pair].length;
    }

    /**
     * @dev Check if Chainlink feed is available for a token
     */
    function isChainlinkAvailable(address token) external view returns (bool) {
        return useChainlink[token] && address(chainlinkFeeds[token]) != address(0);
    }

    /**
     * @dev Get price with safety checks
     */
    function getSafePrice(address tokenA, address tokenB)
        external
        view
        returns (uint256 price, bool safe)
    {
        (price, safe) = this.getCombinedPrice(tokenA, tokenB);

        // Additional safety checks
        if (price == 0) {
            safe = false;
        }

        // Check for extreme prices (potential manipulation)
        if (price > 1e30 || price < 1e6) {
            safe = false;
        }
    }

    /**
     * @dev Emergency function to disable TWAP for a pair
     */
    function emergencyDisableTWAP(address pair) external onlyOwner {
        twapConfigs[pair].enabled = false;
        emit TWAPConfigUpdated(pair, 0, 0, false);
    }

    /**
     * @dev Emergency function to disable Chainlink for a token
     */
    function emergencyDisableChainlink(address token) external onlyOwner {
        useChainlink[token] = false;
        emit ChainlinkFeedUpdated(token, address(0), false);
    }
}
