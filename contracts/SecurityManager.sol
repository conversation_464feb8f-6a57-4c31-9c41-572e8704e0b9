// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title SecurityManager
 * @dev Enterprise-level security features for the DEX
 * @notice Implements circuit breaker, flash loan protection, MEV protection, and emergency controls
 */
contract SecurityManager is Ownable(msg.sender), ReentrancyGuard, Pausable {

    // Circuit Breaker Configuration
    struct CircuitBreakerConfig {
        uint256 maxVolumePerBlock;      // Maximum volume per block
        uint256 maxVolumePerHour;       // Maximum volume per hour
        uint256 maxPriceImpact;         // Maximum price impact (basis points)
        uint256 maxSlippage;            // Maximum slippage (basis points)
        bool enabled;                   // Circuit breaker enabled
    }

    // MEV Protection Configuration
    struct MEVProtectionConfig {
        uint256 minBlockDelay;          // Minimum blocks between large trades
        uint256 maxFrontRunProtection;  // Maximum protection against front-running
        uint256 sandwichProtection;     // Sandwich attack protection threshold
        bool enabled;                   // MEV protection enabled
    }

    // Flash Loan Protection
    struct FlashLoanProtection {
        uint256 maxFlashLoanAmount;     // Maximum flash loan amount
        uint256 flashLoanFee;           // Flash loan fee (basis points)
        bool blockFlashLoans;           // Block all flash loans
        mapping(address => bool) whitelistedFlashLoaners; // Whitelisted flash loan contracts
    }

    // State Variables
    CircuitBreakerConfig public circuitBreaker;
    MEVProtectionConfig public mevProtection;
    FlashLoanProtection public flashLoanProtection;

    // Tracking Variables
    mapping(uint256 => uint256) public volumePerBlock;
    mapping(uint256 => uint256) public volumePerHour;
    mapping(address => uint256) public lastTradeBlock;
    mapping(address => uint256) public userTradeCount;
    mapping(bytes32 => bool) public executedTransactions;

    // Multi-sig Configuration
    mapping(address => bool) public guardians;
    uint256 public requiredGuardianSignatures;
    uint256 public guardianCount;

    // Emergency Controls
    bool public emergencyMode;
    uint256 public emergencyActivatedAt;
    uint256 public constant EMERGENCY_DURATION = 24 hours;

    // Events
    event CircuitBreakerTriggered(string reason, uint256 value, uint256 threshold);
    event MEVProtectionTriggered(address indexed user, string reason);
    event FlashLoanBlocked(address indexed user, uint256 amount, string reason);
    event EmergencyModeActivated(address indexed activator, string reason);
    event EmergencyModeDeactivated(address indexed deactivator);
    event GuardianAdded(address indexed guardian);
    event GuardianRemoved(address indexed guardian);
    event SecurityConfigUpdated(string configType);

    // Modifiers
    modifier onlyGuardian() {
        require(guardians[msg.sender], "SecurityManager: NOT_GUARDIAN");
        _;
    }

    modifier notInEmergency() {
        require(!emergencyMode, "SecurityManager: EMERGENCY_MODE_ACTIVE");
        _;
    }

    modifier circuitBreakerCheck(uint256 amount, uint256 priceImpact) {
        if (circuitBreaker.enabled) {
            _checkCircuitBreaker(amount, priceImpact);
        }
        _;
    }

    modifier mevProtectionCheck(address user) {
        if (mevProtection.enabled) {
            _checkMEVProtection(user);
        }
        _;
    }

    modifier flashLoanProtectionCheck(address user, uint256 amount) {
        if (flashLoanProtection.blockFlashLoans) {
            _checkFlashLoanProtection(user, amount);
        }
        _;
    }

    constructor() {
        // Initialize default security configurations
        circuitBreaker = CircuitBreakerConfig({
            maxVolumePerBlock: 1000 ether,
            maxVolumePerHour: 10000 ether,
            maxPriceImpact: 1000, // 10%
            maxSlippage: 500,     // 5%
            enabled: true
        });

        mevProtection = MEVProtectionConfig({
            minBlockDelay: 2,
            maxFrontRunProtection: 100, // 1%
            sandwichProtection: 200,    // 2%
            enabled: true
        });

        flashLoanProtection.maxFlashLoanAmount = 10000 ether;
        flashLoanProtection.flashLoanFee = 30; // 0.3%
        flashLoanProtection.blockFlashLoans = false;

        // Set initial guardian
        guardians[msg.sender] = true;
        guardianCount = 1;
        requiredGuardianSignatures = 1;
    }

    // Circuit Breaker Functions
    function _checkCircuitBreaker(uint256 amount, uint256 priceImpact) internal {
        uint256 currentBlock = block.number;
        uint256 currentHour = block.timestamp / 1 hours;

        // Check volume per block
        volumePerBlock[currentBlock] = volumePerBlock[currentBlock].add(amount);
        if (volumePerBlock[currentBlock] > circuitBreaker.maxVolumePerBlock) {
            emit CircuitBreakerTriggered("MAX_VOLUME_PER_BLOCK", volumePerBlock[currentBlock], circuitBreaker.maxVolumePerBlock);
            revert("SecurityManager: CIRCUIT_BREAKER_VOLUME_BLOCK");
        }

        // Check volume per hour
        volumePerHour[currentHour] = volumePerHour[currentHour].add(amount);
        if (volumePerHour[currentHour] > circuitBreaker.maxVolumePerHour) {
            emit CircuitBreakerTriggered("MAX_VOLUME_PER_HOUR", volumePerHour[currentHour], circuitBreaker.maxVolumePerHour);
            revert("SecurityManager: CIRCUIT_BREAKER_VOLUME_HOUR");
        }

        // Check price impact
        if (priceImpact > circuitBreaker.maxPriceImpact) {
            emit CircuitBreakerTriggered("MAX_PRICE_IMPACT", priceImpact, circuitBreaker.maxPriceImpact);
            revert("SecurityManager: CIRCUIT_BREAKER_PRICE_IMPACT");
        }
    }

    // MEV Protection Functions
    function _checkMEVProtection(address user) internal {
        uint256 currentBlock = block.number;

        // Check minimum block delay
        if (lastTradeBlock[user] != 0 && currentBlock.sub(lastTradeBlock[user]) < mevProtection.minBlockDelay) {
            emit MEVProtectionTriggered(user, "MIN_BLOCK_DELAY");
            revert("SecurityManager: MEV_PROTECTION_BLOCK_DELAY");
        }

        // Update last trade block
        lastTradeBlock[user] = currentBlock;
        userTradeCount[user] = userTradeCount[user].add(1);

        // Check for suspicious trading patterns
        if (userTradeCount[user] > 10 && currentBlock.sub(lastTradeBlock[user]) == 0) {
            emit MEVProtectionTriggered(user, "SUSPICIOUS_PATTERN");
            revert("SecurityManager: MEV_PROTECTION_SUSPICIOUS_PATTERN");
        }
    }

    // Flash Loan Protection Functions
    function _checkFlashLoanProtection(address user, uint256 amount) internal view {
        if (flashLoanProtection.blockFlashLoans && !flashLoanProtection.whitelistedFlashLoaners[user]) {
            revert("SecurityManager: FLASH_LOANS_BLOCKED");
        }

        if (amount > flashLoanProtection.maxFlashLoanAmount) {
            revert("SecurityManager: FLASH_LOAN_AMOUNT_EXCEEDED");
        }
    }

    // Emergency Functions
    function activateEmergencyMode(string calldata reason) external onlyGuardian {
        emergencyMode = true;
        emergencyActivatedAt = block.timestamp;
        _pause();
        emit EmergencyModeActivated(msg.sender, reason);
    }

    function deactivateEmergencyMode() external onlyGuardian {
        require(emergencyMode, "SecurityManager: NOT_IN_EMERGENCY");
        require(
            block.timestamp >= emergencyActivatedAt.add(EMERGENCY_DURATION) || 
            _hasRequiredGuardianSignatures(),
            "SecurityManager: EMERGENCY_COOLDOWN_NOT_PASSED"
        );
        
        emergencyMode = false;
        emergencyActivatedAt = 0;
        _unpause();
        emit EmergencyModeDeactivated(msg.sender);
    }

    // Guardian Management
    function addGuardian(address guardian) external onlyOwner {
        require(!guardians[guardian], "SecurityManager: ALREADY_GUARDIAN");
        guardians[guardian] = true;
        guardianCount = guardianCount.add(1);
        emit GuardianAdded(guardian);
    }

    function removeGuardian(address guardian) external onlyOwner {
        require(guardians[guardian], "SecurityManager: NOT_GUARDIAN");
        require(guardianCount > 1, "SecurityManager: CANNOT_REMOVE_LAST_GUARDIAN");
        guardians[guardian] = false;
        guardianCount = guardianCount.sub(1);
        emit GuardianRemoved(guardian);
    }

    function setRequiredGuardianSignatures(uint256 required) external onlyOwner {
        require(required > 0 && required <= guardianCount, "SecurityManager: INVALID_SIGNATURE_REQUIREMENT");
        requiredGuardianSignatures = required;
    }

    function _hasRequiredGuardianSignatures() internal pure returns (bool) {
        // Simplified implementation - in production, implement proper multi-sig logic
        return true;
    }

    // Configuration Functions
    function updateCircuitBreakerConfig(
        uint256 maxVolumePerBlock,
        uint256 maxVolumePerHour,
        uint256 maxPriceImpact,
        uint256 maxSlippage,
        bool enabled
    ) external onlyOwner {
        circuitBreaker.maxVolumePerBlock = maxVolumePerBlock;
        circuitBreaker.maxVolumePerHour = maxVolumePerHour;
        circuitBreaker.maxPriceImpact = maxPriceImpact;
        circuitBreaker.maxSlippage = maxSlippage;
        circuitBreaker.enabled = enabled;
        emit SecurityConfigUpdated("CIRCUIT_BREAKER");
    }

    function updateMEVProtectionConfig(
        uint256 minBlockDelay,
        uint256 maxFrontRunProtection,
        uint256 sandwichProtection,
        bool enabled
    ) external onlyOwner {
        mevProtection.minBlockDelay = minBlockDelay;
        mevProtection.maxFrontRunProtection = maxFrontRunProtection;
        mevProtection.sandwichProtection = sandwichProtection;
        mevProtection.enabled = enabled;
        emit SecurityConfigUpdated("MEV_PROTECTION");
    }

    function updateFlashLoanProtectionConfig(
        uint256 maxFlashLoanAmount,
        uint256 flashLoanFee,
        bool blockFlashLoans
    ) external onlyOwner {
        flashLoanProtection.maxFlashLoanAmount = maxFlashLoanAmount;
        flashLoanProtection.flashLoanFee = flashLoanFee;
        flashLoanProtection.blockFlashLoans = blockFlashLoans;
        emit SecurityConfigUpdated("FLASH_LOAN_PROTECTION");
    }

    function whitelistFlashLoaner(address flashLoaner, bool whitelisted) external onlyOwner {
        flashLoanProtection.whitelistedFlashLoaners[flashLoaner] = whitelisted;
    }

    // View Functions
    function isFlashLoanerWhitelisted(address flashLoaner) external view returns (bool) {
        return flashLoanProtection.whitelistedFlashLoaners[flashLoaner];
    }

    function getCurrentBlockVolume() external view returns (uint256) {
        return volumePerBlock[block.number];
    }

    function getCurrentHourVolume() external view returns (uint256) {
        return volumePerHour[block.timestamp / 1 hours];
    }

    function getUserLastTradeBlock(address user) external view returns (uint256) {
        return lastTradeBlock[user];
    }

    function getUserTradeCount(address user) external view returns (uint256) {
        return userTradeCount[user];
    }

    function isInEmergencyMode() external view returns (bool) {
        return emergencyMode;
    }

    function getEmergencyModeTimeRemaining() external view returns (uint256) {
        if (!emergencyMode) return 0;
        uint256 elapsed = block.timestamp.sub(emergencyActivatedAt);
        if (elapsed >= EMERGENCY_DURATION) return 0;
        return EMERGENCY_DURATION.sub(elapsed);
    }

    // Security Check Functions (to be called by other contracts)
    function checkTradeAllowed(
        address user,
        uint256 amount,
        uint256 priceImpact
    ) external
        whenNotPaused
        notInEmergency
        circuitBreakerCheck(amount, priceImpact)
        mevProtectionCheck(user)
        returns (bool)
    {
        return true;
    }

    function checkFlashLoanAllowed(
        address user,
        uint256 amount
    ) external
        view
        flashLoanProtectionCheck(user, amount)
        returns (bool)
    {
        return true;
    }

    // Transaction Replay Protection
    function markTransactionExecuted(bytes32 txHash) external {
        require(!executedTransactions[txHash], "SecurityManager: TRANSACTION_ALREADY_EXECUTED");
        executedTransactions[txHash] = true;
    }

    function isTransactionExecuted(bytes32 txHash) external view returns (bool) {
        return executedTransactions[txHash];
    }

    // Rate Limiting
    mapping(address => uint256) private lastActionTime;
    mapping(address => uint256) private actionCount;
    uint256 public constant RATE_LIMIT_WINDOW = 1 minutes;
    uint256 public constant MAX_ACTIONS_PER_WINDOW = 10;

    function checkRateLimit(address user) external returns (bool) {
        uint256 currentTime = block.timestamp;

        if (currentTime.sub(lastActionTime[user]) > RATE_LIMIT_WINDOW) {
            // Reset counter for new window
            actionCount[user] = 1;
            lastActionTime[user] = currentTime;
            return true;
        }

        if (actionCount[user] >= MAX_ACTIONS_PER_WINDOW) {
            revert("SecurityManager: RATE_LIMIT_EXCEEDED");
        }

        actionCount[user] = actionCount[user].add(1);
        return true;
    }

    function getUserActionCount(address user) external view returns (uint256) {
        return actionCount[user];
    }

    function getUserLastActionTime(address user) external view returns (uint256) {
        return lastActionTime[user];
    }
}
