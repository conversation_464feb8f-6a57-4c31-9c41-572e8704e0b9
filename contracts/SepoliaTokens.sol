// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title SepoliaTokens
 * @dev Sepolia testnet token addresses and Chainlink price feeds
 * @notice This contract contains Sepolia testnet token addresses for testing
 */
library SepoliaTokens {
    
    // Sepolia Testnet Token Addresses
    
    // WETH on Sepolia (Real)
    address public constant WETH = ******************************************;

    // Test tokens (will be deployed dynamically)
    // These addresses will be set after deployment
    
    // Chainlink Price Feeds on Sepolia
    address public constant ETH_USD_FEED = ******************************************;
    address public constant BTC_USD_FEED = ******************************************;
    address public constant USDC_USD_FEED = ******************************************;
    
    /**
     * @dev Get basic Sepolia tokens (only WETH initially)
     * Test tokens will be added dynamically after deployment
     */
    function getSepoliaTokens() internal pure returns (
        address[] memory tokens,
        string[] memory symbols,
        string[] memory names,
        uint8[] memory decimals,
        address[] memory priceFeeds
    ) {
        tokens = new address[](1);
        symbols = new string[](1);
        names = new string[](1);
        decimals = new uint8[](1);
        priceFeeds = new address[](1);

        // WETH (Real Sepolia WETH)
        tokens[0] = WETH;
        symbols[0] = "WETH";
        names[0] = "Wrapped Ether";
        decimals[0] = 18;
        priceFeeds[0] = ETH_USD_FEED;
    }
    
    /**
     * @dev Check if running on Sepolia testnet
     */
    function isSepoliaNetwork() internal view returns (bool) {
        return block.chainid == 11155111;
    }
    
    /**
     * @dev Get price feed for a token on Sepolia
     */
    function getSepoliaPriceFeed(address token) internal pure returns (address) {
        if (token == WETH) return ETH_USD_FEED;
        // Test tokens don't have price feeds initially
        return address(0);
    }
}
