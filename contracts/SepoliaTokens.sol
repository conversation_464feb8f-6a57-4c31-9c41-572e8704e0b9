// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title SepoliaTokens
 * @dev Sepolia testnet token addresses and Chainlink price feeds
 * @notice This contract contains Sepolia testnet token addresses for testing
 */
library SepoliaTokens {
    
    // Sepolia Testnet Token Addresses
    
    // WETH on Sepolia
    address public constant WETH = ******************************************;
    
    // Mock tokens for testing (these would be deployed)
    address public constant USDC = ******************************************; // Mock USDC
    address public constant USDT = ******************************************; // Mock USDT  
    address public constant DAI = ******************************************; // Mock DAI
    
    // Chainlink Price Feeds on Sepolia
    address public constant ETH_USD_FEED = ******************************************;
    address public constant BTC_USD_FEED = ******************************************;
    address public constant USDC_USD_FEED = ******************************************;
    
    /**
     * @dev Get Sepolia testnet tokens for initialization
     */
    function getSepoliaTokens() internal pure returns (
        address[] memory tokens,
        string[] memory symbols,
        string[] memory names,
        uint8[] memory decimals,
        address[] memory priceFeeds
    ) {
        tokens = new address[](4);
        symbols = new string[](4);
        names = new string[](4);
        decimals = new uint8[](4);
        priceFeeds = new address[](4);
        
        // WETH
        tokens[0] = WETH;
        symbols[0] = "WETH";
        names[0] = "Wrapped Ether";
        decimals[0] = 18;
        priceFeeds[0] = ETH_USD_FEED;
        
        // USDC
        tokens[1] = USDC;
        symbols[1] = "USDC";
        names[1] = "USD Coin";
        decimals[1] = 6;
        priceFeeds[1] = USDC_USD_FEED;
        
        // USDT
        tokens[2] = USDT;
        symbols[2] = "USDT";
        names[2] = "Tether USD";
        decimals[2] = 6;
        priceFeeds[2] = address(0); // No feed available
        
        // DAI
        tokens[3] = DAI;
        symbols[3] = "DAI";
        names[3] = "Dai Stablecoin";
        decimals[3] = 18;
        priceFeeds[3] = address(0); // No feed available
    }
    
    /**
     * @dev Check if running on Sepolia testnet
     */
    function isSepoliaNetwork() internal view returns (bool) {
        return block.chainid == 11155111;
    }
    
    /**
     * @dev Get price feed for a token on Sepolia
     */
    function getSepoliaPriceFeed(address token) internal pure returns (address) {
        if (token == WETH) return ETH_USD_FEED;
        if (token == USDC) return USDC_USD_FEED;
        return address(0);
    }
}
