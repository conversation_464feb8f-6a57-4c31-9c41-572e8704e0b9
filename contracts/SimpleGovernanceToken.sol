// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

/**
 * @title SimpleGovernanceToken
 * @dev Simplified ERC20 token with governance features and controlled minting
 * @notice This is the native governance token for the DEX protocol
 */
contract SimpleGovernanceToken is ERC20, ERC20Burnable, Ownable, Pausable {
    
    // Token Configuration
    uint256 public constant MAX_SUPPLY = 1_000_000_000 * 1e18; // 1 billion tokens
    uint256 public constant INITIAL_SUPPLY = 100_000_000 * 1e18; // 100 million tokens
    
    // Minting controls
    mapping(address => bool) public minters;
    uint256 public constant DAILY_MINT_LIMIT = 1_000_000 * 1e18; // 1 million tokens per day
    uint256 public lastMintingReset;
    uint256 public dailyMintedAmount;
    
    // Events
    event MinterAdded(address indexed minter);
    event MinterRemoved(address indexed minter);
    event TokensMinted(address indexed to, uint256 amount);
    event TokensBurned(address indexed from, uint256 amount);

    modifier onlyMinter() {
        require(minters[msg.sender], "SimpleGovernanceToken: NOT_MINTER");
        _;
    }

    constructor() 
        ERC20("DEX Governance Token", "DEXGOV") 
    {
        // Mint initial supply to deployer
        _mint(msg.sender, INITIAL_SUPPLY);
        
        // Set initial minting reset time
        lastMintingReset = block.timestamp;
        
        // Add deployer as initial minter
        minters[msg.sender] = true;
        emit MinterAdded(msg.sender);
    }

    /**
     * @dev Add a new minter (only owner)
     */
    function addMinter(address _minter) external onlyOwner {
        require(_minter != address(0), "SimpleGovernanceToken: ZERO_ADDRESS");
        require(!minters[_minter], "SimpleGovernanceToken: ALREADY_MINTER");
        
        minters[_minter] = true;
        emit MinterAdded(_minter);
    }

    /**
     * @dev Remove a minter (only owner)
     */
    function removeMinter(address _minter) external onlyOwner {
        require(minters[_minter], "SimpleGovernanceToken: NOT_MINTER");
        
        minters[_minter] = false;
        emit MinterRemoved(_minter);
    }

    /**
     * @dev Mint new tokens (only minters)
     */
    function mint(address to, uint256 amount) external onlyMinter whenNotPaused {
        require(to != address(0), "SimpleGovernanceToken: ZERO_ADDRESS");
        require(amount > 0, "SimpleGovernanceToken: ZERO_AMOUNT");
        
        // Check max supply
        require(totalSupply() + amount <= MAX_SUPPLY, "SimpleGovernanceToken: EXCEEDS_MAX_SUPPLY");
        
        // Reset daily limit if needed
        if (block.timestamp >= lastMintingReset + 1 days) {
            lastMintingReset = block.timestamp;
            dailyMintedAmount = 0;
        }
        
        // Check daily limit
        require(dailyMintedAmount + amount <= DAILY_MINT_LIMIT, "SimpleGovernanceToken: EXCEEDS_DAILY_LIMIT");
        
        dailyMintedAmount += amount;
        _mint(to, amount);
        
        emit TokensMinted(to, amount);
    }

    /**
     * @dev Burn tokens from caller
     */
    function burn(uint256 amount) public override whenNotPaused {
        super.burn(amount);
        emit TokensBurned(msg.sender, amount);
    }

    /**
     * @dev Burn tokens from account (with allowance)
     */
    function burnFrom(address account, uint256 amount) public override whenNotPaused {
        super.burnFrom(account, amount);
        emit TokensBurned(account, amount);
    }

    /**
     * @dev Emergency pause (only owner)
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause (only owner)
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Override transfer to add pause functionality
     */
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal virtual override {
        super._beforeTokenTransfer(from, to, amount);
        require(!paused(), "SimpleGovernanceToken: TOKEN_TRANSFER_WHILE_PAUSED");
    }

    /**
     * @dev Get remaining daily mint limit
     */
    function getRemainingDailyMintLimit() external view returns (uint256) {
        if (block.timestamp >= lastMintingReset + 1 days) {
            return DAILY_MINT_LIMIT;
        }
        return DAILY_MINT_LIMIT - dailyMintedAmount;
    }

    /**
     * @dev Check if address is a minter
     */
    function isMinter(address account) external view returns (bool) {
        return minters[account];
    }

    /**
     * @dev Get token info
     */
    function getTokenInfo() external view returns (
        string memory tokenName,
        string memory tokenSymbol,
        uint8 tokenDecimals,
        uint256 tokenTotalSupply,
        uint256 tokenMaxSupply,
        uint256 remainingMintLimit
    ) {
        tokenName = name();
        tokenSymbol = symbol();
        tokenDecimals = decimals();
        tokenTotalSupply = totalSupply();
        tokenMaxSupply = MAX_SUPPLY;
        
        if (block.timestamp >= lastMintingReset + 1 days) {
            remainingMintLimit = DAILY_MINT_LIMIT;
        } else {
            remainingMintLimit = DAILY_MINT_LIMIT - dailyMintedAmount;
        }
    }

    /**
     * @dev Emergency recovery function (only owner)
     */
    function emergencyRecoverToken(address token, uint256 amount) external onlyOwner {
        require(token != address(this), "SimpleGovernanceToken: CANNOT_RECOVER_SELF");
        IERC20(token).transfer(owner(), amount);
    }

    /**
     * @dev Emergency recovery ETH (only owner)
     */
    function emergencyRecoverETH() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
}
