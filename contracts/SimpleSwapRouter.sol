// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "./interfaces/ISwapFactory.sol";
import "./interfaces/ISwapPair.sol";
import "./interfaces/IWETH.sol";
import "./libraries/SwapLibrary.sol";
import "./TokenRegistry.sol";

contract SimpleSwapRouter is ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;

    address public immutable factory;
    address public immutable WETH;
    TokenRegistry public immutable tokenRegistry;

    address public constant PLATFORM_FEE_RECIPIENT = ******************************************;
    uint256 public platformFeeRate = 30; // 0.3% platform fee (30 basis points)
    uint256 public constant MAX_PLATFORM_FEE = 100; // Maximum 1% platform fee

    event Swap(
        address indexed sender,
        uint256 amountIn,
        uint256 amountOut,
        address[] path,
        address indexed to
    );

    event PlatformFeeCollected(
        address indexed token,
        uint256 amount,
        address indexed recipient
    );

    modifier ensure(uint deadline) {
        require(deadline >= block.timestamp, "SimpleSwapRouter: EXPIRED");
        _;
    }

    constructor(
        address _factory,
        address _WETH,
        address _tokenRegistry
    ) {
        factory = _factory;
        WETH = _WETH;
        tokenRegistry = TokenRegistry(_tokenRegistry);
    }

    receive() external payable {
        assert(msg.sender == WETH); // only accept ETH via fallback from the WETH contract
    }

    // **** SWAP ****
    function _swap(uint[] memory amounts, address[] memory path, address _to) internal virtual {
        for (uint i; i < path.length - 1; i++) {
            (address input, address output) = (path[i], path[i + 1]);
            (address token0,) = SwapLibrary.sortTokens(input, output);
            uint amountOut = amounts[i + 1];
            (uint amount0Out, uint amount1Out) = input == token0 ? (uint(0), amountOut) : (amountOut, uint(0));
            address to = i < path.length - 2 ? SwapLibrary.pairFor(factory, output, path[i + 2]) : _to;
            ISwapPair(SwapLibrary.pairFor(factory, input, output)).swap(
                amount0Out, amount1Out, to, new bytes(0)
            );
        }
    }

    function _collectPlatformFee(address token, uint256 amount) internal returns (uint256) {
        if (platformFeeRate == 0) return amount;
        
        uint256 feeAmount = (amount * platformFeeRate) / 10000;
        if (feeAmount > 0) {
            IERC20(token).safeTransfer(PLATFORM_FEE_RECIPIENT, feeAmount);
            emit PlatformFeeCollected(token, feeAmount, PLATFORM_FEE_RECIPIENT);
        }
        
        return amount - feeAmount;
    }

    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external virtual ensure(deadline) nonReentrant returns (uint[] memory amounts) {
        require(path.length >= 2, "SimpleSwapRouter: INVALID_PATH");
        
        // Collect platform fee from input
        uint256 amountInAfterFee = _collectPlatformFee(path[0], amountIn);
        
        amounts = SwapLibrary.getAmountsOut(factory, amountInAfterFee, path);
        require(amounts[amounts.length - 1] >= amountOutMin, 'SimpleSwapRouter: INSUFFICIENT_OUTPUT_AMOUNT');
        
        IERC20(path[0]).safeTransferFrom(
            msg.sender, SwapLibrary.pairFor(factory, path[0], path[1]), amountInAfterFee
        );
        _swap(amounts, path, to);
        
        emit Swap(msg.sender, amountIn, amounts[amounts.length - 1], path, to);
    }

    function swapTokensForExactTokens(
        uint amountOut,
        uint amountInMax,
        address[] calldata path,
        address to,
        uint deadline
    ) external virtual ensure(deadline) nonReentrant returns (uint[] memory amounts) {
        require(path.length >= 2, "SimpleSwapRouter: INVALID_PATH");
        
        amounts = SwapLibrary.getAmountsIn(factory, amountOut, path);
        require(amounts[0] <= amountInMax, 'SimpleSwapRouter: EXCESSIVE_INPUT_AMOUNT');
        
        // Collect platform fee from input
        uint256 amountInAfterFee = _collectPlatformFee(path[0], amounts[0]);
        
        IERC20(path[0]).safeTransferFrom(
            msg.sender, SwapLibrary.pairFor(factory, path[0], path[1]), amountInAfterFee
        );
        _swap(amounts, path, to);
        
        emit Swap(msg.sender, amounts[0], amountOut, path, to);
    }

    function swapExactETHForTokens(uint amountOutMin, address[] calldata path, address to, uint deadline)
        external
        virtual
        payable
        ensure(deadline)
        nonReentrant
        returns (uint[] memory amounts)
    {
        require(path[0] == WETH, 'SimpleSwapRouter: INVALID_PATH');
        
        // Collect platform fee from ETH
        uint256 amountInAfterFee = _collectPlatformFee(WETH, msg.value);
        
        amounts = SwapLibrary.getAmountsOut(factory, amountInAfterFee, path);
        require(amounts[amounts.length - 1] >= amountOutMin, 'SimpleSwapRouter: INSUFFICIENT_OUTPUT_AMOUNT');
        
        IWETH(WETH).deposit{value: amountInAfterFee}();
        assert(IWETH(WETH).transfer(SwapLibrary.pairFor(factory, path[0], path[1]), amountInAfterFee));
        _swap(amounts, path, to);
        
        emit Swap(msg.sender, msg.value, amounts[amounts.length - 1], path, to);
    }

    function swapTokensForExactETH(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline)
        external
        virtual
        ensure(deadline)
        nonReentrant
        returns (uint[] memory amounts)
    {
        require(path[path.length - 1] == WETH, 'SimpleSwapRouter: INVALID_PATH');
        amounts = SwapLibrary.getAmountsIn(factory, amountOut, path);
        require(amounts[0] <= amountInMax, 'SimpleSwapRouter: EXCESSIVE_INPUT_AMOUNT');
        
        // Collect platform fee from input
        uint256 amountInAfterFee = _collectPlatformFee(path[0], amounts[0]);
        
        IERC20(path[0]).safeTransferFrom(
            msg.sender, SwapLibrary.pairFor(factory, path[0], path[1]), amountInAfterFee
        );
        _swap(amounts, path, address(this));
        IWETH(WETH).withdraw(amounts[amounts.length - 1]);
        _safeTransferETH(to, amounts[amounts.length - 1]);
        
        emit Swap(msg.sender, amounts[0], amountOut, path, to);
    }

    function swapExactTokensForETH(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline)
        external
        virtual
        ensure(deadline)
        nonReentrant
        returns (uint[] memory amounts)
    {
        require(path[path.length - 1] == WETH, 'SimpleSwapRouter: INVALID_PATH');
        
        // Collect platform fee from input
        uint256 amountInAfterFee = _collectPlatformFee(path[0], amountIn);
        
        amounts = SwapLibrary.getAmountsOut(factory, amountInAfterFee, path);
        require(amounts[amounts.length - 1] >= amountOutMin, 'SimpleSwapRouter: INSUFFICIENT_OUTPUT_AMOUNT');
        
        IERC20(path[0]).safeTransferFrom(
            msg.sender, SwapLibrary.pairFor(factory, path[0], path[1]), amountInAfterFee
        );
        _swap(amounts, path, address(this));
        IWETH(WETH).withdraw(amounts[amounts.length - 1]);
        _safeTransferETH(to, amounts[amounts.length - 1]);
        
        emit Swap(msg.sender, amountIn, amounts[amounts.length - 1], path, to);
    }

    function swapETHForExactTokens(uint amountOut, address[] calldata path, address to, uint deadline)
        external
        virtual
        payable
        ensure(deadline)
        nonReentrant
        returns (uint[] memory amounts)
    {
        require(path[0] == WETH, 'SimpleSwapRouter: INVALID_PATH');
        amounts = SwapLibrary.getAmountsIn(factory, amountOut, path);
        require(amounts[0] <= msg.value, 'SimpleSwapRouter: EXCESSIVE_INPUT_AMOUNT');
        
        // Collect platform fee from ETH
        uint256 amountInAfterFee = _collectPlatformFee(WETH, amounts[0]);
        
        IWETH(WETH).deposit{value: amountInAfterFee}();
        assert(IWETH(WETH).transfer(SwapLibrary.pairFor(factory, path[0], path[1]), amountInAfterFee));
        _swap(amounts, path, to);
        
        // refund dust eth, if any
        if (msg.value > amounts[0]) _safeTransferETH(msg.sender, msg.value - amounts[0]);
        
        emit Swap(msg.sender, amounts[0], amountOut, path, to);
    }

    // **** LIBRARY FUNCTIONS ****
    function quote(uint amountA, uint reserveA, uint reserveB) public pure virtual returns (uint amountB) {
        return SwapLibrary.quote(amountA, reserveA, reserveB);
    }

    function getAmountOut(uint amountIn, uint reserveIn, uint reserveOut)
        public
        pure
        virtual
        returns (uint amountOut)
    {
        return SwapLibrary.getAmountOut(amountIn, reserveIn, reserveOut);
    }

    function getAmountIn(uint amountOut, uint reserveIn, uint reserveOut)
        public
        pure
        virtual
        returns (uint amountIn)
    {
        return SwapLibrary.getAmountIn(amountOut, reserveIn, reserveOut);
    }

    function getAmountsOut(uint amountIn, address[] memory path)
        public
        view
        virtual
        returns (uint[] memory amounts)
    {
        return SwapLibrary.getAmountsOut(factory, amountIn, path);
    }

    function getAmountsIn(uint amountOut, address[] memory path)
        public
        view
        virtual
        returns (uint[] memory amounts)
    {
        return SwapLibrary.getAmountsIn(factory, amountOut, path);
    }

    function _safeTransferETH(address to, uint value) internal {
        (bool success,) = to.call{value:value}(new bytes(0));
        require(success, 'SimpleSwapRouter: ETH_TRANSFER_FAILED');
    }

    // Platform fee management
    function setPlatformFeeRate(uint256 _feeRate) external onlyOwner {
        require(_feeRate <= MAX_PLATFORM_FEE, "SimpleSwapRouter: FEE_TOO_HIGH");
        platformFeeRate = _feeRate;
    }

    function getPlatformFeeRate() external view returns (uint256) {
        return platformFeeRate;
    }

    function getPlatformFeeRecipient() external pure returns (address) {
        return PLATFORM_FEE_RECIPIENT;
    }
}
