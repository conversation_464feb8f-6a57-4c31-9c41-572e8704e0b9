// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "./interfaces/ISwapFactory.sol";
import "./interfaces/ISwapPair.sol";
import "./libraries/SwapLibrary.sol";
import "./TokenRegistry.sol";
import "./PriceOracle.sol";

/**
 * @title SmartRouter
 * @dev Advanced multi-hop routing with optimal path finding
 * @notice Finds the best swap paths considering liquidity, fees, and gas costs
 */
contract SmartRouter is Ownable, ReentrancyGuard {
    
    struct RouteStep {
        address tokenIn;
        address tokenOut;
        address pair;
        uint256 amountIn;
        uint256 amountOut;
        uint256 fee;
    }

    struct Route {
        RouteStep[] steps;
        uint256 totalAmountIn;
        uint256 totalAmountOut;
        uint256 totalFee;
        uint256 gasEstimate;
        uint256 priceImpact;
    }

    struct PathInfo {
        address[] tokens;
        uint256 expectedOutput;
        uint256 priceImpact;
        uint256 gasEstimate;
        bool isValid;
    }

    // State Variables
    address public immutable factory;
    address public immutable WETH;
    TokenRegistry public immutable tokenRegistry;
    PriceOracle public immutable priceOracle;

    // Routing Configuration
    uint256 public constant MAX_HOPS = 4;
    uint256 public constant MIN_LIQUIDITY = 1000; // Minimum liquidity threshold
    uint256 public constant MAX_PRICE_IMPACT = 1000; // 10% max price impact
    uint256 public constant GAS_PER_HOP = 60000; // Estimated gas per hop

    // Common intermediate tokens for routing
    address[] public intermediateTokens;
    mapping(address => bool) public isIntermediateToken;

    // Events
    event RouteFound(address indexed tokenIn, address indexed tokenOut, uint256 amountIn, uint256 amountOut, uint256 hops);
    event IntermediateTokenAdded(address indexed token);
    event IntermediateTokenRemoved(address indexed token);

    constructor(
        address _factory,
        address _WETH,
        address _tokenRegistry,
        address _priceOracle
    ) {
        factory = _factory;
        WETH = _WETH;
        tokenRegistry = TokenRegistry(_tokenRegistry);
        priceOracle = PriceOracle(_priceOracle);

        // Initialize common intermediate tokens
        intermediateTokens.push(_WETH);
        isIntermediateToken[_WETH] = true;
    }

    /**
     * @dev Find the optimal swap path between two tokens
     */
    function findOptimalPath(
        address tokenIn,
        address tokenOut,
        uint256 amountIn
    ) external view returns (PathInfo memory bestPath) {
        require(tokenIn != tokenOut, "SmartRouter: IDENTICAL_TOKENS");
        require(amountIn > 0, "SmartRouter: INVALID_AMOUNT");

        PathInfo[] memory allPaths = new PathInfo[](10); // Max 10 paths to consider
        uint256 pathCount = 0;

        // 1. Direct path
        PathInfo memory directPath = _evaluateDirectPath(tokenIn, tokenOut, amountIn);
        if (directPath.isValid) {
            allPaths[pathCount] = directPath;
            pathCount++;
        }

        // 2. Single-hop paths through intermediate tokens
        for (uint256 i = 0; i < intermediateTokens.length && pathCount < 10; i++) {
            address intermediate = intermediateTokens[i];
            if (intermediate != tokenIn && intermediate != tokenOut) {
                PathInfo memory singleHopPath = _evaluateSingleHopPath(tokenIn, tokenOut, intermediate, amountIn);
                if (singleHopPath.isValid) {
                    allPaths[pathCount] = singleHopPath;
                    pathCount++;
                }
            }
        }

        // 3. Multi-hop paths (2 intermediates)
        for (uint256 i = 0; i < intermediateTokens.length && pathCount < 10; i++) {
            for (uint256 j = i + 1; j < intermediateTokens.length && pathCount < 10; j++) {
                address intermediate1 = intermediateTokens[i];
                address intermediate2 = intermediateTokens[j];
                
                if (intermediate1 != tokenIn && intermediate1 != tokenOut &&
                    intermediate2 != tokenIn && intermediate2 != tokenOut &&
                    intermediate1 != intermediate2) {
                    
                    PathInfo memory multiHopPath = _evaluateMultiHopPath(
                        tokenIn, tokenOut, intermediate1, intermediate2, amountIn
                    );
                    if (multiHopPath.isValid) {
                        allPaths[pathCount] = multiHopPath;
                        pathCount++;
                    }
                }
            }
        }

        // Find the best path
        bestPath = _selectBestPath(allPaths, pathCount);
    }

    /**
     * @dev Evaluate direct path between two tokens
     */
    function _evaluateDirectPath(
        address tokenIn,
        address tokenOut,
        uint256 amountIn
    ) internal view returns (PathInfo memory path) {
        address pair = ISwapFactory(factory).getPair(tokenIn, tokenOut);
        if (pair == address(0)) {
            path.isValid = false;
            return path;
        }

        try this._getAmountOut(amountIn, tokenIn, tokenOut) returns (uint256 amountOut) {
            if (amountOut > 0) {
                path.tokens = new address[](2);
                path.tokens[0] = tokenIn;
                path.tokens[1] = tokenOut;
                path.expectedOutput = amountOut;
                path.priceImpact = _calculatePriceImpact(pair, amountIn, true);
                path.gasEstimate = GAS_PER_HOP;
                path.isValid = path.priceImpact <= MAX_PRICE_IMPACT;
            }
        } catch {
            path.isValid = false;
        }
    }

    /**
     * @dev Evaluate single-hop path through an intermediate token
     */
    function _evaluateSingleHopPath(
        address tokenIn,
        address tokenOut,
        address intermediate,
        uint256 amountIn
    ) internal view returns (PathInfo memory path) {
        address pair1 = ISwapFactory(factory).getPair(tokenIn, intermediate);
        address pair2 = ISwapFactory(factory).getPair(intermediate, tokenOut);
        
        if (pair1 == address(0) || pair2 == address(0)) {
            path.isValid = false;
            return path;
        }

        try this._getAmountOut(amountIn, tokenIn, intermediate) returns (uint256 intermediateAmount) {
            if (intermediateAmount > 0) {
                try this._getAmountOut(intermediateAmount, intermediate, tokenOut) returns (uint256 finalAmount) {
                    if (finalAmount > 0) {
                        path.tokens = new address[](3);
                        path.tokens[0] = tokenIn;
                        path.tokens[1] = intermediate;
                        path.tokens[2] = tokenOut;
                        path.expectedOutput = finalAmount;
                        
                        uint256 impact1 = _calculatePriceImpact(pair1, amountIn, true);
                        uint256 impact2 = _calculatePriceImpact(pair2, intermediateAmount, true);
                        path.priceImpact = impact1 + impact2;
                        
                        path.gasEstimate = GAS_PER_HOP * 2;
                        path.isValid = path.priceImpact <= MAX_PRICE_IMPACT;
                    }
                } catch {
                    path.isValid = false;
                }
            }
        } catch {
            path.isValid = false;
        }
    }

    /**
     * @dev Evaluate multi-hop path through two intermediate tokens
     */
    function _evaluateMultiHopPath(
        address tokenIn,
        address tokenOut,
        address intermediate1,
        address intermediate2,
        uint256 amountIn
    ) internal view returns (PathInfo memory path) {
        address pair1 = ISwapFactory(factory).getPair(tokenIn, intermediate1);
        address pair2 = ISwapFactory(factory).getPair(intermediate1, intermediate2);
        address pair3 = ISwapFactory(factory).getPair(intermediate2, tokenOut);
        
        if (pair1 == address(0) || pair2 == address(0) || pair3 == address(0)) {
            path.isValid = false;
            return path;
        }

        try this._getAmountOut(amountIn, tokenIn, intermediate1) returns (uint256 amount1) {
            if (amount1 > 0) {
                try this._getAmountOut(amount1, intermediate1, intermediate2) returns (uint256 amount2) {
                    if (amount2 > 0) {
                        try this._getAmountOut(amount2, intermediate2, tokenOut) returns (uint256 finalAmount) {
                            if (finalAmount > 0) {
                                path.tokens = new address[](4);
                                path.tokens[0] = tokenIn;
                                path.tokens[1] = intermediate1;
                                path.tokens[2] = intermediate2;
                                path.tokens[3] = tokenOut;
                                path.expectedOutput = finalAmount;
                                
                                uint256 impact1 = _calculatePriceImpact(pair1, amountIn, true);
                                uint256 impact2 = _calculatePriceImpact(pair2, amount1, true);
                                uint256 impact3 = _calculatePriceImpact(pair3, amount2, true);
                                path.priceImpact = impact1 + impact2 + impact3;
                                
                                path.gasEstimate = GAS_PER_HOP * 3;
                                path.isValid = path.priceImpact <= MAX_PRICE_IMPACT;
                            }
                        } catch {
                            path.isValid = false;
                        }
                    }
                } catch {
                    path.isValid = false;
                }
            }
        } catch {
            path.isValid = false;
        }
    }

    /**
     * @dev Select the best path from available options
     */
    function _selectBestPath(PathInfo[] memory paths, uint256 pathCount) 
        internal 
        pure 
        returns (PathInfo memory bestPath) 
    {
        if (pathCount == 0) {
            bestPath.isValid = false;
            return bestPath;
        }

        bestPath = paths[0];
        
        for (uint256 i = 1; i < pathCount; i++) {
            PathInfo memory currentPath = paths[i];
            
            // Calculate efficiency score (output per gas)
            uint256 currentEfficiency = (currentPath.expectedOutput * 1e18) / currentPath.gasEstimate;
            uint256 bestEfficiency = (bestPath.expectedOutput * 1e18) / bestPath.gasEstimate;
            
            // Prefer path with higher efficiency and lower price impact
            if (currentEfficiency > bestEfficiency || 
                (currentEfficiency == bestEfficiency && currentPath.priceImpact < bestPath.priceImpact)) {
                bestPath = currentPath;
            }
        }
    }

    /**
     * @dev Calculate price impact for a trade
     */
    function _calculatePriceImpact(address pair, uint256 amountIn, bool isToken0) 
        internal 
        view 
        returns (uint256 priceImpact) 
    {
        (uint112 reserve0, uint112 reserve1,) = ISwapPair(pair).getReserves();
        
        if (reserve0 == 0 || reserve1 == 0) return type(uint256).max;
        
        uint256 reserveIn = isToken0 ? reserve0 : reserve1;
        // uint256 reserveOut = isToken0 ? reserve1 : reserve0; // unused for now

        if (amountIn >= reserveIn) return type(uint256).max;
        
        // Price impact = (amountIn / reserveIn) * 10000 (in basis points)
        priceImpact = (amountIn * 10000) / reserveIn;
    }

    /**
     * @dev External wrapper for getAmountOut (for try-catch)
     */
    function _getAmountOut(uint256 amountIn, address tokenIn, address tokenOut) 
        external 
        view 
        returns (uint256 amountOut) 
    {
        address pair = ISwapFactory(factory).getPair(tokenIn, tokenOut);
        require(pair != address(0), "SmartRouter: PAIR_NOT_EXISTS");
        
        (uint112 reserve0, uint112 reserve1,) = ISwapPair(pair).getReserves();
        (uint112 reserveIn, uint112 reserveOut) = tokenIn < tokenOut ? (reserve0, reserve1) : (reserve1, reserve0);
        
        amountOut = SwapLibrary.getAmountOut(amountIn, reserveIn, reserveOut);
    }

    // **** ADMIN FUNCTIONS ****

    /**
     * @dev Add intermediate token for routing
     */
    function addIntermediateToken(address token) external onlyOwner {
        require(token != address(0), "SmartRouter: ZERO_ADDRESS");
        require(!isIntermediateToken[token], "SmartRouter: ALREADY_INTERMEDIATE");
        require(tokenRegistry.isTokenSupported(token), "SmartRouter: UNSUPPORTED_TOKEN");

        intermediateTokens.push(token);
        isIntermediateToken[token] = true;
        emit IntermediateTokenAdded(token);
    }

    /**
     * @dev Remove intermediate token from routing
     */
    function removeIntermediateToken(address token) external onlyOwner {
        require(isIntermediateToken[token], "SmartRouter: NOT_INTERMEDIATE");
        require(token != WETH, "SmartRouter: CANNOT_REMOVE_WETH");

        isIntermediateToken[token] = false;

        // Remove from array
        for (uint256 i = 0; i < intermediateTokens.length; i++) {
            if (intermediateTokens[i] == token) {
                intermediateTokens[i] = intermediateTokens[intermediateTokens.length - 1];
                intermediateTokens.pop();
                break;
            }
        }

        emit IntermediateTokenRemoved(token);
    }

    // **** VIEW FUNCTIONS ****

    /**
     * @dev Get all intermediate tokens
     */
    function getIntermediateTokens() external view returns (address[] memory) {
        return intermediateTokens;
    }

    /**
     * @dev Get intermediate token count
     */
    function getIntermediateTokenCount() external view returns (uint256) {
        return intermediateTokens.length;
    }

    /**
     * @dev Check if a path exists between two tokens
     */
    function pathExists(address tokenIn, address tokenOut) external view returns (bool) {
        PathInfo memory path = this.findOptimalPath(tokenIn, tokenOut, 1 ether);
        return path.isValid;
    }

    /**
     * @dev Get multiple path options for comparison
     */
    function getPathOptions(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 maxPaths
    ) external view returns (PathInfo[] memory validPaths) {
        require(maxPaths > 0 && maxPaths <= 10, "SmartRouter: INVALID_MAX_PATHS");

        PathInfo[] memory allPaths = new PathInfo[](10);
        uint256 pathCount = 0;

        // Direct path
        PathInfo memory directPath = _evaluateDirectPath(tokenIn, tokenOut, amountIn);
        if (directPath.isValid && pathCount < maxPaths) {
            allPaths[pathCount] = directPath;
            pathCount++;
        }

        // Single-hop paths
        for (uint256 i = 0; i < intermediateTokens.length && pathCount < maxPaths; i++) {
            address intermediate = intermediateTokens[i];
            if (intermediate != tokenIn && intermediate != tokenOut) {
                PathInfo memory singleHopPath = _evaluateSingleHopPath(tokenIn, tokenOut, intermediate, amountIn);
                if (singleHopPath.isValid) {
                    allPaths[pathCount] = singleHopPath;
                    pathCount++;
                }
            }
        }

        // Multi-hop paths
        for (uint256 i = 0; i < intermediateTokens.length && pathCount < maxPaths; i++) {
            for (uint256 j = i + 1; j < intermediateTokens.length && pathCount < maxPaths; j++) {
                address intermediate1 = intermediateTokens[i];
                address intermediate2 = intermediateTokens[j];

                if (intermediate1 != tokenIn && intermediate1 != tokenOut &&
                    intermediate2 != tokenIn && intermediate2 != tokenOut &&
                    intermediate1 != intermediate2) {

                    PathInfo memory multiHopPath = _evaluateMultiHopPath(
                        tokenIn, tokenOut, intermediate1, intermediate2, amountIn
                    );
                    if (multiHopPath.isValid) {
                        allPaths[pathCount] = multiHopPath;
                        pathCount++;
                    }
                }
            }
        }

        // Return only valid paths
        validPaths = new PathInfo[](pathCount);
        for (uint256 i = 0; i < pathCount; i++) {
            validPaths[i] = allPaths[i];
        }
    }

    /**
     * @dev Estimate gas cost for a specific path
     */
    function estimateGasCost(address[] calldata path) external pure returns (uint256) {
        require(path.length >= 2, "SmartRouter: INVALID_PATH");
        return GAS_PER_HOP * (path.length - 1);
    }

    /**
     * @dev Get detailed route information
     */
    function getRouteDetails(
        address tokenIn,
        address tokenOut,
        uint256 amountIn
    ) external view returns (Route memory route) {
        PathInfo memory bestPath = this.findOptimalPath(tokenIn, tokenOut, amountIn);
        require(bestPath.isValid, "SmartRouter: NO_VALID_PATH");

        route.steps = new RouteStep[](bestPath.tokens.length - 1);
        route.totalAmountIn = amountIn;
        route.totalAmountOut = bestPath.expectedOutput;
        route.gasEstimate = bestPath.gasEstimate;
        route.priceImpact = bestPath.priceImpact;

        uint256 currentAmountIn = amountIn;

        for (uint256 i = 0; i < bestPath.tokens.length - 1; i++) {
            address tokenA = bestPath.tokens[i];
            address tokenB = bestPath.tokens[i + 1];
            address pair = ISwapFactory(factory).getPair(tokenA, tokenB);

            uint256 amountOut = this._getAmountOut(currentAmountIn, tokenA, tokenB);

            route.steps[i] = RouteStep({
                tokenIn: tokenA,
                tokenOut: tokenB,
                pair: pair,
                amountIn: currentAmountIn,
                amountOut: amountOut,
                fee: (currentAmountIn * 3) / 1000 // 0.3% fee
            });

            route.totalFee += route.steps[i].fee;
            currentAmountIn = amountOut;
        }
    }

    /**
     * @dev Check if routing is profitable after gas costs
     */
    function isProfitable(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 gasPrice
    ) external view returns (bool profitable, uint256 netOutput) {
        PathInfo memory bestPath = this.findOptimalPath(tokenIn, tokenOut, amountIn);

        if (!bestPath.isValid) {
            return (false, 0);
        }

        uint256 gasCost = bestPath.gasEstimate * gasPrice;

        // Convert gas cost to output token value (simplified)
        // In production, this should use proper price conversion
        uint256 gasCostInOutputToken = gasCost; // Simplified assumption

        if (bestPath.expectedOutput > gasCostInOutputToken) {
            profitable = true;
            netOutput = bestPath.expectedOutput - gasCostInOutputToken;
        } else {
            profitable = false;
            netOutput = 0;
        }
    }
}
