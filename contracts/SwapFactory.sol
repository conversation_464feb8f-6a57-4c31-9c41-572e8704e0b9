// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./interfaces/ISwapFactory.sol";
import "./SwapPair.sol";

contract SwapFactory is ISwapFactory, Ownable(msg.sender), ReentrancyGuard {
    address public override feeTo;
    address public override feeToSetter;
    address public override migrator;
    uint8 public override protocolFeeDenominator = 5; // 0.05% protocol fee

    mapping(address => mapping(address => address)) public override getPair;
    address[] public override allPairs;

    // Security features
    bool public paused = false;
    mapping(address => bool) public authorizedCallers;

    // Event is inherited from ISwapFactory interface
    event ProtocolFeeUpdated(uint8 oldFee, uint8 newFee);
    event PauseStateChanged(bool paused);
    event AuthorizedCallerUpdated(address caller, bool authorized);

    modifier whenNotPaused() {
        require(!paused, "SwapFactory: PAUSED");
        _;
    }

    modifier onlyAuthorized() {
        require(authorizedCallers[msg.sender] || msg.sender == owner(), "SwapFactory: UNAUTHORIZED");
        _;
    }

    constructor(address _feeToSetter) {
        feeToSetter = _feeToSetter;
        authorizedCallers[msg.sender] = true;
    }

    function allPairsLength() external view override returns (uint) {
        return allPairs.length;
    }

    function createPair(address tokenA, address tokenB) 
        external 
        override 
        whenNotPaused 
        nonReentrant 
        returns (address pair) 
    {
        require(tokenA != tokenB, "SwapFactory: IDENTICAL_ADDRESSES");
        (address token0, address token1) = tokenA < tokenB ? (tokenA, tokenB) : (tokenB, tokenA);
        require(token0 != address(0), "SwapFactory: ZERO_ADDRESS");
        require(getPair[token0][token1] == address(0), "SwapFactory: PAIR_EXISTS");
        
        bytes memory bytecode = type(SwapPair).creationCode;
        bytes32 salt = keccak256(abi.encodePacked(token0, token1));
        
        assembly {
            pair := create2(0, add(bytecode, 32), mload(bytecode), salt)
        }
        
        require(pair != address(0), "SwapFactory: PAIR_CREATION_FAILED");
        
        ISwapPair(pair).initialize(token0, token1);
        getPair[token0][token1] = pair;
        getPair[token1][token0] = pair;
        allPairs.push(pair);
        
        emit PairCreated(token0, token1, pair, allPairs.length);
    }

    function setFeeTo(address _feeTo) external override {
        require(msg.sender == feeToSetter, "SwapFactory: FORBIDDEN");
        feeTo = _feeTo;
    }

    function setFeeToSetter(address _feeToSetter) external override {
        require(msg.sender == feeToSetter, "SwapFactory: FORBIDDEN");
        feeToSetter = _feeToSetter;
    }

    function setMigrator(address _migrator) external override onlyOwner {
        migrator = _migrator;
    }

    function setProtocolFeeDenominator(uint8 _protocolFeeDenominator) external override onlyOwner {
        require(_protocolFeeDenominator >= 2 && _protocolFeeDenominator <= 25, "SwapFactory: INVALID_FEE");
        uint8 oldFee = protocolFeeDenominator;
        protocolFeeDenominator = _protocolFeeDenominator;
        emit ProtocolFeeUpdated(oldFee, _protocolFeeDenominator);
    }

    // Emergency functions
    function setPaused(bool _paused) external onlyOwner {
        paused = _paused;
        emit PauseStateChanged(_paused);
    }

    function setAuthorizedCaller(address _caller, bool _authorized) external onlyOwner {
        authorizedCallers[_caller] = _authorized;
        emit AuthorizedCallerUpdated(_caller, _authorized);
    }

    // View functions for frontend integration
    function getPairInfo(address tokenA, address tokenB) 
        external 
        view 
        returns (
            address pair,
            address token0,
            address token1,
            uint112 reserve0,
            uint112 reserve1,
            uint32 blockTimestampLast
        ) 
    {
        pair = getPair[tokenA][tokenB];
        if (pair != address(0)) {
            token0 = ISwapPair(pair).token0();
            token1 = ISwapPair(pair).token1();
            (reserve0, reserve1, blockTimestampLast) = ISwapPair(pair).getReserves();
        }
    }

    function getAllPairs() external view returns (address[] memory) {
        return allPairs;
    }
}
