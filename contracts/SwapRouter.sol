// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "./interfaces/ISwapFactory.sol";
import "./interfaces/ISwapPair.sol";
import "./interfaces/IWETH.sol";
import "./interfaces/ISecurityManager.sol";
import "./libraries/SwapLibrary.sol";
import "./TokenRegistry.sol";

contract SwapRouter is ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;

    address public immutable factory;
    address public immutable WETH;
    TokenRegistry public immutable tokenRegistry;
    ISecurityManager public immutable securityManager;


    address public constant PLATFORM_FEE_RECIPIENT = ******************************************;
    uint256 public platformFeeRate = 30; // 0.3% platform fee (30 basis points)
    uint256 public constant MAX_PLATFORM_FEE = 100; // Maximum 1% platform fee

    modifier ensure(uint deadline) {
        require(deadline >= block.timestamp, "SwapRouter: EXPIRED");
        _;
    }

    modifier validTokenPath(address[] calldata path) {
        require(path.length >= 2, "SwapRouter: INVALID_PATH_LENGTH");
        for (uint i = 0; i < path.length; i++) {
            require(tokenRegistry.isTokenSupported(path[i]), "SwapRouter: UNSUPPORTED_TOKEN");
        }
        _;
    }

    modifier securityCheck(uint256 amount) {
        // Rate limiting
        securityManager.checkRateLimit(msg.sender);

        // Calculate approximate price impact (simplified)
        uint256 priceImpact = _calculatePriceImpact(amount);

        // Security checks
        securityManager.checkTradeAllowed(msg.sender, amount, priceImpact);
        _;
    }

    // Events
    event Swap(
        address indexed sender,
        uint256 amount0In,
        uint256 amount1In,
        uint256 amount0Out,
        uint256 amount1Out,
        address indexed to
    );

    event PlatformFeeCollected(
        address indexed token,
        uint256 amount,
        address indexed recipient
    );

    event PlatformFeeRateUpdated(uint256 oldRate, uint256 newRate);

    constructor(address _factory, address _WETH, address _tokenRegistry, address _securityManager) {
        factory = _factory;
        WETH = _WETH;
        tokenRegistry = TokenRegistry(_tokenRegistry);
        securityManager = ISecurityManager(_securityManager);
    }

    receive() external payable {
        assert(msg.sender == WETH);
    }

    // **** PLATFORM FEE FUNCTIONS ****

    /**
     * @dev Calculate and collect platform fee
     */
    function _collectPlatformFee(address token, uint256 amount) internal returns (uint256 feeAmount) {
        if (platformFeeRate == 0) return 0;

        feeAmount = (amount * platformFeeRate) / 10000;
        if (feeAmount > 0) {
            IERC20(token).safeTransfer(PLATFORM_FEE_RECIPIENT, feeAmount);
            emit PlatformFeeCollected(token, feeAmount, PLATFORM_FEE_RECIPIENT);
        }
    }

    /**
     * @dev Update platform fee rate (only owner)
     */
    function updatePlatformFeeRate(uint256 newRate) external onlyOwner {
        require(newRate <= MAX_PLATFORM_FEE, "SwapRouter: FEE_TOO_HIGH");
        uint256 oldRate = platformFeeRate;
        platformFeeRate = newRate;
        emit PlatformFeeRateUpdated(oldRate, newRate);
    }

    // **** INTERNAL FUNCTIONS ****
    function _calculatePriceImpact(uint256 amount) internal pure returns (uint256) {
        // Simplified price impact calculation
        // In production, this should be more sophisticated
        if (amount < 1 ether) return 10; // 0.1%
        if (amount < 10 ether) return 50; // 0.5%
        if (amount < 100 ether) return 100; // 1%
        return 200; // 2%
    }

    // **** ADD LIQUIDITY ****
    function _addLiquidity(
        address tokenA,
        address tokenB,
        uint amountADesired,
        uint amountBDesired,
        uint amountAMin,
        uint amountBMin
    ) internal virtual returns (uint amountA, uint amountB) {
        if (ISwapFactory(factory).getPair(tokenA, tokenB) == address(0)) {
            ISwapFactory(factory).createPair(tokenA, tokenB);
        }
        (uint reserveA, uint reserveB) = SwapLibrary.getReserves(factory, tokenA, tokenB);
        if (reserveA == 0 && reserveB == 0) {
            (amountA, amountB) = (amountADesired, amountBDesired);
        } else {
            uint amountBOptimal = SwapLibrary.quote(amountADesired, reserveA, reserveB);
            if (amountBOptimal <= amountBDesired) {
                require(amountBOptimal >= amountBMin, "SwapRouter: INSUFFICIENT_B_AMOUNT");
                (amountA, amountB) = (amountADesired, amountBOptimal);
            } else {
                uint amountAOptimal = SwapLibrary.quote(amountBDesired, reserveB, reserveA);
                assert(amountAOptimal <= amountADesired);
                require(amountAOptimal >= amountAMin, "SwapRouter: INSUFFICIENT_A_AMOUNT");
                (amountA, amountB) = (amountAOptimal, amountBDesired);
            }
        }
    }

    function addLiquidity(
        address tokenA,
        address tokenB,
        uint amountADesired,
        uint amountBDesired,
        uint amountAMin,
        uint amountBMin,
        address to,
        uint deadline
    ) external virtual ensure(deadline) nonReentrant returns (uint amountA, uint amountB, uint liquidity) {
        (amountA, amountB) = _addLiquidity(tokenA, tokenB, amountADesired, amountBDesired, amountAMin, amountBMin);
        address pair = SwapLibrary.pairFor(factory, tokenA, tokenB);
        IERC20(tokenA).safeTransferFrom(msg.sender, pair, amountA);
        IERC20(tokenB).safeTransferFrom(msg.sender, pair, amountB);
        liquidity = ISwapPair(pair).mint(to);
    }

    function addLiquidityETH(
        address token,
        uint amountTokenDesired,
        uint amountTokenMin,
        uint amountETHMin,
        address to,
        uint deadline
    ) external virtual payable ensure(deadline) nonReentrant returns (uint amountToken, uint amountETH, uint liquidity) {
        (amountToken, amountETH) = _addLiquidity(
            token,
            WETH,
            amountTokenDesired,
            msg.value,
            amountTokenMin,
            amountETHMin
        );
        address pair = SwapLibrary.pairFor(factory, token, WETH);
        IERC20(token).safeTransferFrom(msg.sender, pair, amountToken);
        IWETH(WETH).deposit{value: amountETH}();
        assert(IWETH(WETH).transfer(pair, amountETH));
        liquidity = ISwapPair(pair).mint(to);
        if (msg.value > amountETH) payable(msg.sender).transfer(msg.value - amountETH);
    }

    // **** REMOVE LIQUIDITY ****
    function removeLiquidity(
        address tokenA,
        address tokenB,
        uint liquidity,
        uint amountAMin,
        uint amountBMin,
        address to,
        uint deadline
    ) public virtual ensure(deadline) nonReentrant returns (uint amountA, uint amountB) {
        address pair = SwapLibrary.pairFor(factory, tokenA, tokenB);
        ISwapPair(pair).transferFrom(msg.sender, pair, liquidity);
        (uint amount0, uint amount1) = ISwapPair(pair).burn(to);
        (address token0,) = SwapLibrary.sortTokens(tokenA, tokenB);
        (amountA, amountB) = tokenA == token0 ? (amount0, amount1) : (amount1, amount0);
        require(amountA >= amountAMin, "SwapRouter: INSUFFICIENT_A_AMOUNT");
        require(amountB >= amountBMin, "SwapRouter: INSUFFICIENT_B_AMOUNT");
    }

    function removeLiquidityETH(
        address token,
        uint liquidity,
        uint amountTokenMin,
        uint amountETHMin,
        address to,
        uint deadline
    ) public virtual ensure(deadline) nonReentrant returns (uint amountToken, uint amountETH) {
        (amountToken, amountETH) = removeLiquidity(
            token,
            WETH,
            liquidity,
            amountTokenMin,
            amountETHMin,
            address(this),
            deadline
        );
        IERC20(token).safeTransfer(to, amountToken);
        IWETH(WETH).withdraw(amountETH);
        payable(to).transfer(amountETH);
    }

    // **** SWAP ****
    function _swap(uint[] memory amounts, address[] memory path, address _to) internal virtual {
        for (uint i; i < path.length - 1; i++) {
            (address input, address output) = (path[i], path[i + 1]);
            (address token0,) = SwapLibrary.sortTokens(input, output);
            uint amountOut = amounts[i + 1];
            (uint amount0Out, uint amount1Out) = input == token0 ? (uint(0), amountOut) : (amountOut, uint(0));
            address to = i < path.length - 2 ? SwapLibrary.pairFor(factory, output, path[i + 2]) : _to;
            ISwapPair(SwapLibrary.pairFor(factory, input, output)).swap(
                amount0Out, amount1Out, to, new bytes(0)
            );
        }
    }

    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external virtual ensure(deadline) validTokenPath(path) securityCheck(amountIn) nonReentrant returns (uint[] memory amounts) {
        amounts = SwapLibrary.getAmountsOut(factory, amountIn, path);
        require(amounts[amounts.length - 1] >= amountOutMin, "SwapRouter: INSUFFICIENT_OUTPUT_AMOUNT");

        // Collect platform fee from input token
        uint256 platformFee = _collectPlatformFee(path[0], amountIn);
        uint256 amountAfterFee = amountIn - platformFee;

        // Recalculate amounts with fee deducted
        amounts = SwapLibrary.getAmountsOut(factory, amountAfterFee, path);
        require(amounts[amounts.length - 1] >= amountOutMin, "SwapRouter: INSUFFICIENT_OUTPUT_AMOUNT");

        IERC20(path[0]).safeTransferFrom(msg.sender, address(this), amountIn);
        if (platformFee > 0) {
            IERC20(path[0]).safeTransfer(PLATFORM_FEE_RECIPIENT, platformFee);
        }
        IERC20(path[0]).safeTransfer(SwapLibrary.pairFor(factory, path[0], path[1]), amountAfterFee);

        _swap(amounts, path, to);
    }

    function swapTokensForExactTokens(
        uint amountOut,
        uint amountInMax,
        address[] calldata path,
        address to,
        uint deadline
    ) external virtual ensure(deadline) validTokenPath(path) securityCheck(amountInMax) nonReentrant returns (uint[] memory amounts) {
        amounts = SwapLibrary.getAmountsIn(factory, amountOut, path);
        require(amounts[0] <= amountInMax, "SwapRouter: EXCESSIVE_INPUT_AMOUNT");
        IERC20(path[0]).safeTransferFrom(
            msg.sender, SwapLibrary.pairFor(factory, path[0], path[1]), amounts[0]
        );
        _swap(amounts, path, to);
    }

    function swapExactETHForTokens(uint amountOutMin, address[] calldata path, address to, uint deadline)
        external
        virtual
        payable
        ensure(deadline)
        nonReentrant
        returns (uint[] memory amounts)
    {
        require(path[0] == WETH, "SwapRouter: INVALID_PATH");
        amounts = SwapLibrary.getAmountsOut(factory, msg.value, path);
        require(amounts[amounts.length - 1] >= amountOutMin, "SwapRouter: INSUFFICIENT_OUTPUT_AMOUNT");
        IWETH(WETH).deposit{value: amounts[0]}();
        assert(IWETH(WETH).transfer(SwapLibrary.pairFor(factory, path[0], path[1]), amounts[0]));
        _swap(amounts, path, to);
    }

    function swapTokensForExactETH(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline)
        external
        virtual
        ensure(deadline)
        nonReentrant
        returns (uint[] memory amounts)
    {
        require(path[path.length - 1] == WETH, "SwapRouter: INVALID_PATH");
        amounts = SwapLibrary.getAmountsIn(factory, amountOut, path);
        require(amounts[0] <= amountInMax, "SwapRouter: EXCESSIVE_INPUT_AMOUNT");
        IERC20(path[0]).safeTransferFrom(
            msg.sender, SwapLibrary.pairFor(factory, path[0], path[1]), amounts[0]
        );
        _swap(amounts, path, address(this));
        IWETH(WETH).withdraw(amounts[amounts.length - 1]);
        payable(to).transfer(amounts[amounts.length - 1]);
    }

    function swapExactTokensForETH(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline)
        external
        virtual
        ensure(deadline)
        nonReentrant
        returns (uint[] memory amounts)
    {
        require(path[path.length - 1] == WETH, "SwapRouter: INVALID_PATH");
        amounts = SwapLibrary.getAmountsOut(factory, amountIn, path);
        require(amounts[amounts.length - 1] >= amountOutMin, "SwapRouter: INSUFFICIENT_OUTPUT_AMOUNT");
        IERC20(path[0]).safeTransferFrom(
            msg.sender, SwapLibrary.pairFor(factory, path[0], path[1]), amounts[0]
        );
        _swap(amounts, path, address(this));
        IWETH(WETH).withdraw(amounts[amounts.length - 1]);
        payable(to).transfer(amounts[amounts.length - 1]);
    }

    function swapETHForExactTokens(uint amountOut, address[] calldata path, address to, uint deadline)
        external
        virtual
        payable
        ensure(deadline)
        nonReentrant
        returns (uint[] memory amounts)
    {
        require(path[0] == WETH, "SwapRouter: INVALID_PATH");
        amounts = SwapLibrary.getAmountsIn(factory, amountOut, path);
        require(amounts[0] <= msg.value, "SwapRouter: EXCESSIVE_INPUT_AMOUNT");
        IWETH(WETH).deposit{value: amounts[0]}();
        assert(IWETH(WETH).transfer(SwapLibrary.pairFor(factory, path[0], path[1]), amounts[0]));
        _swap(amounts, path, to);
        if (msg.value > amounts[0]) payable(msg.sender).transfer(msg.value - amounts[0]);
    }

    // **** LIBRARY FUNCTIONS ****
    function quote(uint amountA, uint reserveA, uint reserveB) public pure virtual returns (uint amountB) {
        return SwapLibrary.quote(amountA, reserveA, reserveB);
    }

    function getAmountOut(uint amountIn, uint reserveIn, uint reserveOut)
        public
        pure
        virtual
        returns (uint amountOut)
    {
        return SwapLibrary.getAmountOut(amountIn, reserveIn, reserveOut);
    }

    function getAmountIn(uint amountOut, uint reserveIn, uint reserveOut)
        public
        pure
        virtual
        returns (uint amountIn)
    {
        return SwapLibrary.getAmountIn(amountOut, reserveIn, reserveOut);
    }

    function getAmountsOut(uint amountIn, address[] memory path)
        public
        view
        virtual
        returns (uint[] memory amounts)
    {
        return SwapLibrary.getAmountsOut(factory, amountIn, path);
    }

    function getAmountsIn(uint amountOut, address[] memory path)
        public
        view
        virtual
        returns (uint[] memory amounts)
    {
        return SwapLibrary.getAmountsIn(factory, amountOut, path);
    }

    // **** ETH-WETH SWAP FUNCTIONS ****

    /**
     * @dev Swap ETH for WETH
     */
    function swapETHForWETH() external payable nonReentrant {
        require(msg.value > 0, "SwapRouter: ZERO_ETH");

        // Collect platform fee
        uint256 platformFee = (msg.value * platformFeeRate) / 10000;
        uint256 amountAfterFee = msg.value - platformFee;

        if (platformFee > 0) {
            payable(PLATFORM_FEE_RECIPIENT).transfer(platformFee);
            emit PlatformFeeCollected(address(0), platformFee, PLATFORM_FEE_RECIPIENT);
        }

        // Wrap ETH to WETH
        IWETH(WETH).deposit{value: amountAfterFee}();
        IWETH(WETH).transfer(msg.sender, amountAfterFee);

        emit Swap(msg.sender, msg.value, 0, 0, amountAfterFee, msg.sender);
    }

    /**
     * @dev Swap WETH for ETH
     */
    function swapWETHForETH(uint256 amountIn) external nonReentrant {
        require(amountIn > 0, "SwapRouter: ZERO_AMOUNT");

        // Transfer WETH from user
        IERC20(WETH).safeTransferFrom(msg.sender, address(this), amountIn);

        // Collect platform fee
        uint256 platformFee = _collectPlatformFee(WETH, amountIn);
        uint256 amountAfterFee = amountIn - platformFee;

        // Unwrap WETH to ETH
        IWETH(WETH).withdraw(amountAfterFee);
        payable(msg.sender).transfer(amountAfterFee);

        emit Swap(msg.sender, 0, amountIn, amountAfterFee, 0, msg.sender);
    }

    // **** UTILITY FUNCTIONS ****
    function getSupportedTokens() external view returns (address[] memory) {
        return tokenRegistry.getActiveTokens();
    }

    function getVerifiedTokens() external view returns (address[] memory) {
        return tokenRegistry.getVerifiedTokens();
    }

    function getTokenInfo(address token) external view returns (TokenRegistry.TokenInfo memory) {
        return tokenRegistry.getTokenInfo(token);
    }

    function isTokenSupported(address token) external view returns (bool) {
        return tokenRegistry.isTokenSupported(token);
    }

    function getPlatformFeeRate() external view returns (uint256) {
        return platformFeeRate;
    }

    function getPlatformFeeRecipient() external pure returns (address) {
        return PLATFORM_FEE_RECIPIENT;
    }

    function getOptimalSwapPath(address tokenIn, address tokenOut)
        external
        view
        returns (address[] memory path)
    {
        // Direct path
        if (ISwapFactory(factory).getPair(tokenIn, tokenOut) != address(0)) {
            path = new address[](2);
            path[0] = tokenIn;
            path[1] = tokenOut;
            return path;
        }

        // Path through WETH
        if (tokenIn != WETH && tokenOut != WETH) {
            if (ISwapFactory(factory).getPair(tokenIn, WETH) != address(0) &&
                ISwapFactory(factory).getPair(WETH, tokenOut) != address(0)) {
                path = new address[](3);
                path[0] = tokenIn;
                path[1] = WETH;
                path[2] = tokenOut;
                return path;
            }
        }

        // No path found
        path = new address[](0);
    }

    function getAmountsOutWithPath(uint amountIn, address tokenIn, address tokenOut)
        external
        view
        returns (uint[] memory amounts, address[] memory path)
    {
        path = this.getOptimalSwapPath(tokenIn, tokenOut);
        require(path.length > 0, "SwapRouter: NO_PATH_FOUND");
        amounts = SwapLibrary.getAmountsOut(factory, amountIn, path);
    }

    function getAmountsInWithPath(uint amountOut, address tokenIn, address tokenOut)
        external
        view
        returns (uint[] memory amounts, address[] memory path)
    {
        path = this.getOptimalSwapPath(tokenIn, tokenOut);
        require(path.length > 0, "SwapRouter: NO_PATH_FOUND");
        amounts = SwapLibrary.getAmountsIn(factory, amountOut, path);
    }
}