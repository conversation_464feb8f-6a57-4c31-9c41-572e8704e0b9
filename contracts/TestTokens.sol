// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title TestToken
 * @dev ERC20 token for testing on Sepolia testnet
 */
contract TestToken is ERC20, Ownable {
    uint8 private _decimals;
    
    constructor(
        string memory name,
        string memory symbol,
        uint8 decimals_,
        uint256 initialSupply
    ) ERC20(name, symbol) {
        _decimals = decimals_;
        _mint(msg.sender, initialSupply);
    }
    
    function decimals() public view virtual override returns (uint8) {
        return _decimals;
    }
    
    function mint(address to, uint256 amount) public onlyOwner {
        _mint(to, amount);
    }
    
    function faucet(uint256 amount) public {
        require(amount <= 1000 * 10**_decimals, "TestToken: AMOUNT_TOO_HIGH");
        _mint(msg.sender, amount);
    }
}

/**
 * @title TestUSDC
 * @dev USDC test token with 6 decimals
 */
contract TestUSDC is TestToken {
    constructor() TestToken(
        "Test USD Coin",
        "TUSDC",
        6,
        1000000 * 10**6 // 1M TUSDC
    ) {}
}

/**
 * @title TestUSDT  
 * @dev USDT test token with 6 decimals
 */
contract TestUSDT is TestToken {
    constructor() TestToken(
        "Test Tether USD",
        "TUSDT", 
        6,
        1000000 * 10**6 // 1M TUSDT
    ) {}
}

/**
 * @title TestDAI
 * @dev DAI test token with 18 decimals
 */
contract TestDAI is TestToken {
    constructor() TestToken(
        "Test Dai Stablecoin",
        "TDAI",
        18,
        1000000 * 10**18 // 1M TDAI
    ) {}
}

/**
 * @title TestUNI
 * @dev UNI test token with 18 decimals
 */
contract TestUNI is TestToken {
    constructor() TestToken(
        "Test Uniswap",
        "TUNI",
        18,
        10000000 * 10**18 // 10M TUNI
    ) {}
}

/**
 * @title TestAAVE
 * @dev AAVE test token with 18 decimals
 */
contract TestAAVE is TestToken {
    constructor() TestToken(
        "Test Aave Token",
        "TAAVE",
        18,
        16000000 * 10**18 // 16M TAAVE
    ) {}
}

/**
 * @title TestLINK
 * @dev LINK test token with 18 decimals
 */
contract TestLINK is TestToken {
    constructor() TestToken(
        "Test Chainlink Token",
        "TLINK",
        18,
        1000000000 * 10**18 // 1B TLINK
    ) {}
}

/**
 * @title TestWBTC
 * @dev WBTC test token with 8 decimals
 */
contract TestWBTC is TestToken {
    constructor() TestToken(
        "Test Wrapped BTC",
        "TWBTC",
        8,
        21000000 * 10**8 // 21M TWBTC
    ) {}
}

/**
 * @title TestTokenDeployer
 * @dev Factory contract to deploy all test tokens at once
 */
contract TestTokenDeployer {
    struct TokenInfo {
        address tokenAddress;
        string name;
        string symbol;
        uint8 decimals;
    }
    
    TokenInfo[] public deployedTokens;
    mapping(string => address) public tokenBySymbol;
    
    event TokenDeployed(string symbol, address tokenAddress);
    
    function deployAllTokens() external returns (address[] memory) {
        address[] memory tokens = new address[](7);
        
        // Deploy TUSDC
        TestUSDC tusdc = new TestUSDC();
        tokens[0] = address(tusdc);
        _addToken("TUSDC", address(tusdc), "Test USD Coin", 6);
        
        // Deploy TUSDT
        TestUSDT tusdt = new TestUSDT();
        tokens[1] = address(tusdt);
        _addToken("TUSDT", address(tusdt), "Test Tether USD", 6);
        
        // Deploy TDAI
        TestDAI tdai = new TestDAI();
        tokens[2] = address(tdai);
        _addToken("TDAI", address(tdai), "Test Dai Stablecoin", 18);
        
        // Deploy TUNI
        TestUNI tuni = new TestUNI();
        tokens[3] = address(tuni);
        _addToken("TUNI", address(tuni), "Test Uniswap", 18);
        
        // Deploy TAAVE
        TestAAVE taave = new TestAAVE();
        tokens[4] = address(taave);
        _addToken("TAAVE", address(taave), "Test Aave Token", 18);
        
        // Deploy TLINK
        TestLINK tlink = new TestLINK();
        tokens[5] = address(tlink);
        _addToken("TLINK", address(tlink), "Test Chainlink Token", 18);
        
        // Deploy TWBTC
        TestWBTC twbtc = new TestWBTC();
        tokens[6] = address(twbtc);
        _addToken("TWBTC", address(twbtc), "Test Wrapped BTC", 8);
        
        return tokens;
    }
    
    function _addToken(string memory symbol, address tokenAddress, string memory name, uint8 decimals) private {
        deployedTokens.push(TokenInfo({
            tokenAddress: tokenAddress,
            name: name,
            symbol: symbol,
            decimals: decimals
        }));
        
        tokenBySymbol[symbol] = tokenAddress;
        emit TokenDeployed(symbol, tokenAddress);
    }
    
    function getDeployedTokens() external view returns (TokenInfo[] memory) {
        return deployedTokens;
    }
    
    function getTokenCount() external view returns (uint256) {
        return deployedTokens.length;
    }
}
