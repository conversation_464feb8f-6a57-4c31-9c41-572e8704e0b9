// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "./SepoliaTokens.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title TokenRegistry
 * @dev Comprehensive registry for popular Ethereum tokens with real mainnet addresses
 * @notice This contract maintains a curated list of verified tokens for the DEX
 */
contract TokenRegistry is Ownable, ReentrancyGuard {
    
    struct TokenInfo {
        address tokenAddress;
        string name;
        string symbol;
        uint8 decimals;
        bool isActive;
        bool isVerified;
        uint256 addedTimestamp;
        string logoURI;
        string website;
        uint256 marketCap; // For sorting/filtering
    }
    
    // Mappings
    mapping(address => TokenInfo) public tokens;
    mapping(string => address) public symbolToAddress;
    address[] public tokenList;
    
    // Events
    event TokenAdded(address indexed tokenAddress, string symbol, string name);
    event TokenUpdated(address indexed tokenAddress, string symbol, string name);
    event TokenStatusChanged(address indexed tokenAddress, bool isActive, bool isVerified);
    
    // Modifiers
    modifier validToken(address _token) {
        require(_token != address(0), "TokenRegistry: ZERO_ADDRESS");
        require(tokens[_token].tokenAddress != address(0), "TokenRegistry: TOKEN_NOT_FOUND");
        _;
    }
    
    constructor() {
        // Initialize tokens based on network
        if (SepoliaTokens.isSepoliaNetwork()) {
            _initializeSepoliaTokens();
        } else {
            _initializeMainnetTokens();
        }
    }

    /**
     * @dev Initialize Sepolia testnet tokens
     */
    function _initializeSepoliaTokens() private {
        (
            address[] memory sepoliaTokens,
            string[] memory sepoliaSymbols,
            string[] memory sepoliaNames,
            uint8[] memory sepoliaDecimals,
            // address[] memory sepoliaFeeds - unused for now
        ) = SepoliaTokens.getSepoliaTokens();

        for (uint256 i = 0; i < sepoliaTokens.length; i++) {
            _addToken(
                sepoliaTokens[i],
                sepoliaSymbols[i],
                sepoliaNames[i],
                sepoliaDecimals[i],
                true, // isActive
                true, // isVerified
                "", // logoURI (empty for testnet)
                "", // website (empty for testnet)
                1000000 // mock marketCap
            );
        }
    }
    
    /**
     * @dev Initialize with popular mainnet tokens
     */
    function _initializeMainnetTokens() private {
        // Major Stablecoins - GERÇEK ADRESLER
        _addToken(******************************************, "USDT", "Tether USD", 6, true, true, "https://assets.coingecko.com/coins/images/325/small/Tether-logo.png", "https://tether.to", 83000000000);
        _addToken(******************************************, "USDC", "USD Coin", 6, true, true, "https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png", "https://centre.io", 52000000000);
        _addToken(******************************************, "DAI", "Dai Stablecoin", 18, true, true, "https://assets.coingecko.com/coins/images/9956/small/4943.png", "https://makerdao.com", 5000000000);
        _addToken(******************************************, "BUSD", "Binance USD", 18, true, true, "https://assets.coingecko.com/coins/images/9576/small/BUSD.png", "https://www.binance.com", 16000000000);

        // Wrapped ETH - GERÇEK ADRES
        _addToken(******************************************, "WETH", "Wrapped Ether", 18, true, true, "https://assets.coingecko.com/coins/images/2518/small/weth.png", "https://weth.io", 20000000000);

        // DeFi Blue Chips - GERÇEK ADRESLER
        _addToken(******************************************, "UNI", "Uniswap", 18, true, true, "https://assets.coingecko.com/coins/images/12504/small/uniswap-uni.png", "https://uniswap.org", 4000000000);
        _addToken(******************************************, "AAVE", "Aave Token", 18, true, true, "https://assets.coingecko.com/coins/images/12645/small/AAVE.png", "https://aave.com", 1500000000);
        _addToken(******************************************, "SNX", "Synthetix", 18, true, true, "https://assets.coingecko.com/coins/images/3406/small/SNX.png", "https://synthetix.io", 500000000);
        _addToken(******************************************, "MKR", "Maker", 18, true, true, "https://assets.coingecko.com/coins/images/1364/small/Mark_Maker.png", "https://makerdao.com", 1200000000);
        _addToken(******************************************, "SUSHI", "SushiToken", 18, true, true, "https://assets.coingecko.com/coins/images/12271/small/512x512_Logo_no_chop.png", "https://sushi.com", 300000000);
        
        // Layer 2 & Scaling
        _addToken(0x7D1AfA7B718fb893dB30A3aBc0Cfc608AaCfeBB0, "MATIC", "Matic Token", 18, true, true, "https://polygon.technology/images/matic-logo.png", "https://polygon.technology", 8000000000);
        _addToken(0x4E15361FD6b4BB609Fa63C81A2be19d873717870, "FTM", "Fantom Token", 18, true, true, "https://fantom.foundation/images/ftm-logo.png", "https://fantom.foundation", 2000000000);

        // Oracle & Infrastructure
        _addToken(0x514910771AF9Ca656af840dff83E8264EcF986CA, "LINK", "ChainLink Token", 18, true, true, "https://chain.link/images/link-logo.png", "https://chain.link", 7000000000);
        _addToken(0x0bc529c00C6401aEF6D220BE8C6Ea1667F6Ad93e, "YFI", "yearn.finance", 18, true, true, "https://yearn.finance/images/yfi-logo.png", "https://yearn.finance", 800000000);

        // Exchange Tokens
        _addToken(0xBB0E17EF65F82Ab018d8EDd776e8DD940327B28b, "AXS", "Axie Infinity Shard", 18, true, true, "https://axieinfinity.com/images/axs-logo.png", "https://axieinfinity.com", 1000000000);
        _addToken(0x4d224452801ACEd8B2F0aebE155379bb5D594381, "APE", "ApeCoin", 18, true, true, "https://apecoin.com/images/ape-logo.png", "https://apecoin.com", 1500000000);

        // Gaming & Metaverse
        _addToken(0x85Eee30c52B0b379b046Fb0F85F4f3Dc3009aFEC, "KEEP", "Keep Token", 18, true, true, "https://keep.network/images/keep-logo.png", "https://keep.network", 200000000);
        _addToken(0x6f259637dcD74C767781E37Bc6133cd6A68aa161, "HT", "HuobiToken", 18, true, true, "https://huobi.com/images/ht-logo.png", "https://huobi.com", 800000000);

        // Newer DeFi Protocols
        _addToken(******************************************, "FXS", "Frax Share", 18, true, true, "https://frax.finance/images/fxs-logo.png", "https://frax.finance", 400000000);
        _addToken(******************************************, "FRAX", "Frax", 18, true, true, "https://frax.finance/images/frax-logo.png", "https://frax.finance", 1000000000);

        // Additional Popular Tokens
        _addToken(******************************************, "WBTC", "Wrapped BTC", 8, true, true, "https://wbtc.network/images/wbtc-logo.png", "https://wbtc.network", 15000000000);
        _addToken(******************************************, "SHIB", "SHIBA INU", 18, true, true, "https://shibatoken.com/images/shib-logo.png", "https://shibatoken.com", 6000000000);

        // Additional DeFi
        _addToken(******************************************, "DPI", "DeFi Pulse Index", 18, true, true, "https://indexcoop.com/images/dpi-logo.png", "https://indexcoop.com", 100000000);
        _addToken(******************************************, "COMP", "Compound", 18, true, true, "https://compound.finance/images/comp-logo.png", "https://compound.finance", 600000000);

        // Additional Popular Tokens (Real Addresses)
        _addToken(******************************************, "CRV", "Curve DAO Token", 18, true, true, "https://curve.fi/images/crv-logo.png", "https://curve.fi", 400000000);
        _addToken(******************************************, "MEME", "Memecoin", 18, true, true, "https://memecoin.org/images/meme-logo.png", "https://memecoin.org", 300000000);
        _addToken(0x6982508145454Ce325dDbE47a25d4ec3d2311933, "PEPE", "Pepe", 18, true, true, "https://pepe.vip/images/pepe-logo.png", "https://pepe.vip", 2000000000);
        _addToken(0x3845badAde8e6dFF049820680d1F14bD3903a5d0, "SAND", "The Sandbox", 18, true, true, "https://sandbox.game/images/sand-logo.png", "https://sandbox.game", 1000000000);
        _addToken(0xF629cBd94d3791C9250152BD8dfBDF380E2a3B9c, "ENJ", "Enjin Coin", 18, true, true, "https://enjin.io/images/enj-logo.png", "https://enjin.io", 500000000);
        _addToken(0x0000000000085d4780B73119b644AE5ecd22b376, "TUSD", "TrueUSD", 18, true, true, "https://trueusd.com/images/tusd-logo.png", "https://trueusd.com", 500000000);
    }
    
    /**
     * @dev Internal function to add a token
     */
    function _addToken(
        address _tokenAddress,
        string memory _symbol,
        string memory _name,
        uint8 _decimals,
        bool _isActive,
        bool _isVerified,
        string memory _logoURI,
        string memory _website,
        uint256 _marketCap
    ) private {
        require(_tokenAddress != address(0), "TokenRegistry: ZERO_ADDRESS");
        require(bytes(_symbol).length > 0, "TokenRegistry: EMPTY_SYMBOL");
        require(bytes(_name).length > 0, "TokenRegistry: EMPTY_NAME");
        
        tokens[_tokenAddress] = TokenInfo({
            tokenAddress: _tokenAddress,
            name: _name,
            symbol: _symbol,
            decimals: _decimals,
            isActive: _isActive,
            isVerified: _isVerified,
            addedTimestamp: block.timestamp,
            logoURI: _logoURI,
            website: _website,
            marketCap: _marketCap
        });
        
        symbolToAddress[_symbol] = _tokenAddress;
        tokenList.push(_tokenAddress);
        
        emit TokenAdded(_tokenAddress, _symbol, _name);
    }

    /**
     * @dev Add a new token (only owner)
     */
    function addToken(
        address _tokenAddress,
        string memory _symbol,
        string memory _name,
        uint8 _decimals,
        string memory _logoURI,
        string memory _website,
        uint256 _marketCap
    ) external onlyOwner {
        require(tokens[_tokenAddress].tokenAddress == address(0), "TokenRegistry: TOKEN_EXISTS");
        _addToken(_tokenAddress, _symbol, _name, _decimals, true, false, _logoURI, _website, _marketCap);
    }

    /**
     * @dev Update token information
     */
    function updateToken(
        address _tokenAddress,
        string memory _logoURI,
        string memory _website,
        uint256 _marketCap
    ) external onlyOwner validToken(_tokenAddress) {
        TokenInfo storage token = tokens[_tokenAddress];
        token.logoURI = _logoURI;
        token.website = _website;
        token.marketCap = _marketCap;

        emit TokenUpdated(_tokenAddress, token.symbol, token.name);
    }

    /**
     * @dev Set token status (active/verified)
     */
    function setTokenStatus(
        address _tokenAddress,
        bool _isActive,
        bool _isVerified
    ) external onlyOwner validToken(_tokenAddress) {
        TokenInfo storage token = tokens[_tokenAddress];
        token.isActive = _isActive;
        token.isVerified = _isVerified;

        emit TokenStatusChanged(_tokenAddress, _isActive, _isVerified);
    }

    /**
     * @dev Get token info by address
     */
    function getTokenInfo(address _tokenAddress)
        external
        view
        returns (TokenInfo memory)
    {
        return tokens[_tokenAddress];
    }

    /**
     * @dev Get token address by symbol
     */
    function getTokenBySymbol(string memory _symbol)
        external
        view
        returns (address)
    {
        return symbolToAddress[_symbol];
    }

    /**
     * @dev Get all active tokens
     */
    function getActiveTokens() external view returns (address[] memory) {
        uint256 activeCount = 0;

        // Count active tokens
        for (uint256 i = 0; i < tokenList.length; i++) {
            if (tokens[tokenList[i]].isActive) {
                activeCount++;
            }
        }

        // Create array of active tokens
        address[] memory activeTokens = new address[](activeCount);
        uint256 index = 0;

        for (uint256 i = 0; i < tokenList.length; i++) {
            if (tokens[tokenList[i]].isActive) {
                activeTokens[index] = tokenList[i];
                index++;
            }
        }

        return activeTokens;
    }

    /**
     * @dev Get verified tokens only
     */
    function getVerifiedTokens() external view returns (address[] memory) {
        uint256 verifiedCount = 0;

        // Count verified tokens
        for (uint256 i = 0; i < tokenList.length; i++) {
            if (tokens[tokenList[i]].isVerified && tokens[tokenList[i]].isActive) {
                verifiedCount++;
            }
        }

        // Create array of verified tokens
        address[] memory verifiedTokens = new address[](verifiedCount);
        uint256 index = 0;

        for (uint256 i = 0; i < tokenList.length; i++) {
            if (tokens[tokenList[i]].isVerified && tokens[tokenList[i]].isActive) {
                verifiedTokens[index] = tokenList[i];
                index++;
            }
        }

        return verifiedTokens;
    }

    /**
     * @dev Get tokens sorted by market cap (descending)
     */
    function getTokensByMarketCap(uint256 _limit)
        external
        view
        returns (address[] memory)
    {
        require(_limit > 0 && _limit <= tokenList.length, "TokenRegistry: INVALID_LIMIT");

        // Create a copy of tokenList for sorting
        address[] memory sortedTokens = new address[](tokenList.length);
        for (uint256 i = 0; i < tokenList.length; i++) {
            sortedTokens[i] = tokenList[i];
        }

        // Simple bubble sort by market cap (descending)
        for (uint256 i = 0; i < sortedTokens.length - 1; i++) {
            for (uint256 j = 0; j < sortedTokens.length - i - 1; j++) {
                if (tokens[sortedTokens[j]].marketCap < tokens[sortedTokens[j + 1]].marketCap) {
                    address temp = sortedTokens[j];
                    sortedTokens[j] = sortedTokens[j + 1];
                    sortedTokens[j + 1] = temp;
                }
            }
        }

        // Return only the requested limit
        address[] memory result = new address[](_limit);
        for (uint256 i = 0; i < _limit; i++) {
            result[i] = sortedTokens[i];
        }

        return result;
    }

    /**
     * @dev Check if token is supported and active
     */
    function isTokenSupported(address _tokenAddress) external view returns (bool) {
        return tokens[_tokenAddress].tokenAddress != address(0) && tokens[_tokenAddress].isActive;
    }

    /**
     * @dev Get total number of tokens
     */
    function getTotalTokens() external view returns (uint256) {
        return tokenList.length;
    }

    /**
     * @dev Get all tokens (paginated)
     */
    function getTokensPaginated(uint256 _offset, uint256 _limit)
        external
        view
        returns (address[] memory)
    {
        require(_offset < tokenList.length, "TokenRegistry: OFFSET_OUT_OF_BOUNDS");

        uint256 end = _offset + _limit;
        if (end > tokenList.length) {
            end = tokenList.length;
        }

        address[] memory result = new address[](end - _offset);
        for (uint256 i = _offset; i < end; i++) {
            result[i - _offset] = tokenList[i];
        }

        return result;
    }
}
