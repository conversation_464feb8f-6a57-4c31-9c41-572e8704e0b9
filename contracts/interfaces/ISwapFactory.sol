// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

interface ISwapFactory {
    event PairCreated(address indexed token0, address indexed token1, address pair, uint);

    function feeTo() external view returns (address);
    function feeToSetter() external view returns (address);

    function getPair(address tokenA, address tokenB) external view returns (address pair);
    function allPairs(uint) external view returns (address pair);
    function allPairsLength() external view returns (uint);

    function createPair(address tokenA, address tokenB) external returns (address pair);

    function setFeeTo(address) external;
    function setFeeToSetter(address) external;
    
    // Additional functions for enhanced functionality
    function migrator() external view returns (address);
    function setMigrator(address) external;
    
    // Fee management
    function protocolFeeDenominator() external view returns (uint8);
    function setProtocolFeeDenominator(uint8) external;
}
