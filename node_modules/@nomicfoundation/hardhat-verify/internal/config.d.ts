import type { HardhatConfig, HardhatUserConfig } from "hardhat/types";
export declare function etherscanConfigExtender(config: HardhatConfig, userConfig: Readonly<HardhatUserConfig>): void;
export declare function sourcifyConfigExtender(config: HardhatConfig, userConfig: Readonly<HardhatUserConfig>): void;
export declare function blockscoutConfigExtender(config: HardhatConfig, userConfig: Readonly<HardhatUserConfig>): void;
//# sourceMappingURL=config.d.ts.map