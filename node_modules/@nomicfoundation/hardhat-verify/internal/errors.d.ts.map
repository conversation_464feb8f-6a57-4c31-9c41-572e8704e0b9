{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../src/internal/errors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,2BAA2B,EAAE,MAAM,iBAAiB,CAAC;AAC9D,OAAO,EACL,0BAA0B,EAC1B,4BAA4B,EAC5B,wBAAwB,EACzB,MAAM,yBAAyB,CAAC;AAGjC,qBAAa,kBAAmB,SAAQ,2BAA2B;gBACrD,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK;CAI5C;AAED,qBAAa,mBAAoB,SAAQ,kBAAkB;;CAM1D;AAED,qBAAa,mBAAoB,SAAQ,kBAAkB;gBAC7C,OAAO,EAAE,MAAM;CAG5B;AAED,qBAAa,wBAAyB,SAAQ,kBAAkB;gBAClD,YAAY,EAAE,MAAM;CAIjC;AAED,qBAAa,kBAAmB,SAAQ,kBAAkB;gBAC5C,OAAO,EAAE,MAAM;CAc5B;AAED,qBAAa,gCAAiC,SAAQ,kBAAkB;;CAUvE;AAED,qBAAa,kCAAmC,SAAQ,kBAAkB;;CAMzE;AAED,qBAAa,sCAAuC,SAAQ,kBAAkB;gBAChE,yBAAyB,EAAE,MAAM;CAK9C;AAED,qBAAa,qBAAsB,SAAQ,kBAAkB;;CAU5D;AAED,qBAAa,2BAA4B,SAAQ,kBAAkB;gBACrD,mBAAmB,EAAE,MAAM;CAKxC;AAED,qBAAa,oBAAqB,SAAQ,kBAAkB;gBAC9C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;CAO1C;AAED,qBAAa,+BAAgC,SAAQ,kBAAkB;;CAUtE;AAED,qBAAa,wBAAyB,SAAQ,kBAAkB;gBAClD,OAAO,EAAE,MAAM;CAS5B;AAED,qBAAa,0CAA2C,SAAQ,kBAAkB;gBACpE,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM;CAKlE;AAED,qBAAa,wCAAyC,SAAQ,kBAAkB;gBAClE,GAAG,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM;CAQjD;AAED,qBAAa,2CAA4C,SAAQ,kBAAkB;gBACrE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM;CAKrD;AAED,qBAAa,uCAAwC,SAAQ,kBAAkB;gBACjE,OAAO,EAAE,MAAM;CAK5B;AAED,qBAAa,iCAAkC,SAAQ,kBAAkB;;CAKxE;AAED,qBAAa,6BAA8B,SAAQ,kBAAkB;gBACvD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;CAI7C;AAED,qBAAa,6BAA8B,SAAQ,kBAAkB;gBAEjE,sBAAsB,EAAE,MAAM,EAAE,EAChC,uBAAuB,EAAE,MAAM,EAC/B,OAAO,EAAE,MAAM;CAelB;AAED,qBAAa,qBAAsB,SAAQ,kBAAkB;gBAC/C,WAAW,EAAE,MAAM;CAGhC;AAED,qBAAa,sBAAuB,SAAQ,kBAAkB;gBAChD,WAAW,EAAE,MAAM;CAIhC;AAED,qBAAa,qCAAsC,SAAQ,kBAAkB;gBAEzE,WAAW,EAAE,MAAM,EACnB,eAAe,EAAE,MAAM,EACvB,cAAc,EAAE,OAAO,EACvB,wBAAwB,EAAE,MAAM,EAChC,OAAO,EAAE,MAAM;CAclB;AAED,qBAAa,6BAA8B,SAAQ,kBAAkB;gBACvD,OAAO,EAAE,MAAM,EAAE,WAAW,CAAC,EAAE,MAAM;CAclD;AAED,qBAAa,oCAAqC,SAAQ,kBAAkB;gBAC9D,UAAU,EAAE,MAAM,EAAE;CAgBjC;AAED,qBAAa,0BAA2B,SAAQ,kBAAkB;gBAE9D,YAAY,EAAE,MAAM,EACpB,WAAW,EAAE,MAAM,EACnB,cAAc,EAAE,MAAM;CAMzB;AAED,qBAAa,sBAAuB,SAAQ,kBAAkB;gBAChD,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM;CAMpD;AAED,qBAAa,oBAAqB,SAAQ,kBAAkB;gBAExD,YAAY,EAAE,MAAM,EACpB,WAAW,EAAE,MAAM,EACnB,YAAY,EAAE,MAAM,EAAE,EACtB,mBAAmB,EAAE,MAAM,EAAE,EAC7B,qBAAqB,EAAE,MAAM,EAAE;CAkBlC;AAED,qBAAa,2BAA4B,SAAQ,kBAAkB;gBACrD,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE;CAO5E;AAED,qBAAa,qBAAsB,SAAQ,kBAAkB;gBAEzD,YAAY,EAAE,MAAM,EACpB,YAAY,EAAE,MAAM,EAAE,EACtB,eAAe,EAAE,MAAM,EAAE,EACzB,qBAAqB,EAAE,MAAM,EAAE;CAgBlC;AAED,qBAAa,6BAA8B,SAAQ,kBAAkB;gBAEjE,SAAS,EAAE,KAAK,CAAC;QACf,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,CAAC;QACxB,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;CAcL;AAED,qBAAa,4BAA6B,SAAQ,kBAAkB;;CAMnE;AAED,qBAAa,sBAAuB,SAAQ,kBAAkB;gBAE1D,UAAU,EAAE,MAAM,EAClB,YAAY,EAAE,MAAM,EACpB,KAAK,EAAE,0BAA0B;CASpC;AAED,qBAAa,oBAAqB,SAAQ,kBAAkB;gBAC9C,KAAK,EAAE,wBAAwB;CAQ5C;AAED,qBAAa,wBAAyB,SAAQ,kBAAkB;gBAClD,KAAK,EAAE,4BAA4B;CAShD;AAED;;;GAGG;AACH,qBAAa,qCAAsC,SAAQ,kBAAkB;gBAC/D,OAAO,EAAE,MAAM;CAK5B;AAED,qBAAa,mBAAoB,SAAQ,kBAAkB;gBAC7C,CAAC,EAAE,KAAK;CAKrB;AAED,qBAAa,+BAAgC,SAAQ,kBAAkB;gBACzD,OAAO,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE;CAa7D;AAED,qBAAa,4BAA6B,SAAQ,kBAAkB;gBACtD,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM;CAKzD"}