{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../src/internal/errors.ts"], "names": [], "mappings": ";;;AAAA,6CAA8D;AAM9D,6CAAkD;AAElD,MAAa,kBAAmB,SAAQ,qCAA2B;IACjE,YAAY,OAAe,EAAE,MAAc;QACzC,KAAK,CAAC,iCAAiC,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1D,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IAC1D,CAAC;CACF;AALD,gDAKC;AAED,MAAa,mBAAoB,SAAQ,kBAAkB;IACzD;QACE,KAAK,CACH,sHAAsH,CACvH,CAAC;IACJ,CAAC;CACF;AAND,kDAMC;AAED,MAAa,mBAAoB,SAAQ,kBAAkB;IACzD,YAAY,OAAe;QACzB,KAAK,CAAC,GAAG,OAAO,yBAAyB,CAAC,CAAC;IAC7C,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,wBAAyB,SAAQ,kBAAkB;IAC9D,YAAY,YAAoB;QAC9B,KAAK,CAAC;mCACyB,YAAY,EAAE,CAAC,CAAC;IACjD,CAAC;CACF;AALD,4DAKC;AAED,MAAa,kBAAmB,SAAQ,kBAAkB;IACxD,YAAY,OAAe;QACzB,KAAK,CAAC,2CAA2C,OAAO;;;;;;QAMpD,OAAO;;;;;8BAKe,CAAC,CAAC;IAC9B,CAAC;CACF;AAfD,gDAeC;AAED,MAAa,gCAAiC,SAAQ,kBAAkB;IACtE;QACE,KAAK,CAAC;;;eAGK,+BAAkB;;;KAG5B,CAAC,CAAC;IACL,CAAC;CACF;AAVD,4EAUC;AAED,MAAa,kCAAmC,SAAQ,kBAAkB;IACxE;QACE,KAAK,CACH,gHAAgH,CACjH,CAAC;IACJ,CAAC;CACF;AAND,gFAMC;AAED,MAAa,sCAAuC,SAAQ,kBAAkB;IAC5E,YAAY,yBAAiC;QAC3C,KAAK,CAAC,cAAc,yBAAyB;;sCAEX,CAAC,CAAC;IACtC,CAAC;CACF;AAND,wFAMC;AAED,MAAa,qBAAsB,SAAQ,kBAAkB;IAC3D;QACE,KAAK,CAAC;;;eAGK,+BAAkB;;;KAG5B,CAAC,CAAC;IACL,CAAC;CACF;AAVD,sDAUC;AAED,MAAa,2BAA4B,SAAQ,kBAAkB;IACjE,YAAY,mBAA2B;QACrC,KAAK,CAAC,cAAc,mBAAmB;;wDAEa,CAAC,CAAC;IACxD,CAAC;CACF;AAND,kEAMC;AAED,MAAa,oBAAqB,SAAQ,kBAAkB;IAC1D,YAAY,MAAc,EAAE,MAAa;QACvC,KAAK,CACH,gCAAgC,MAAM;UAClC,MAAM,CAAC,OAAO,EAAE,EACpB,MAAM,CACP,CAAC;IACJ,CAAC;CACF;AARD,oDAQC;AAED,MAAa,+BAAgC,SAAQ,kBAAkB;IACrE;QACE,KAAK,CACH;;;;yDAImD,CACpD,CAAC;IACJ,CAAC;CACF;AAVD,0EAUC;AAED,MAAa,wBAAyB,SAAQ,kBAAkB;IAC9D,YAAY,OAAe;QACzB,KAAK,CAAC,0DAA0D,OAAO;;;;;;qCAMtC,CAAC,CAAC;IACrC,CAAC;CACF;AAVD,4DAUC;AAED,MAAa,0CAA2C,SAAQ,kBAAkB;IAChF,YAAY,GAAW,EAAE,UAAkB,EAAE,YAAoB;QAC/D,KAAK,CAAC;gBACM,GAAG;mDACgC,UAAU,mBAAmB,YAAY,EAAE,CAAC,CAAC;IAC9F,CAAC;CACF;AAND,gGAMC;AAED,MAAa,wCAAyC,SAAQ,kBAAkB;IAC9E,YAAY,GAAW,EAAE,eAAuB;QAC9C,KAAK,CAAC;gBACM,GAAG;uDACoC,eAAe;;;oHAG8C,CAAC,CAAC;IACpH,CAAC;CACF;AATD,4FASC;AAED,MAAa,2CAA4C,SAAQ,kBAAkB;IACjF,YAAY,UAAkB,EAAE,YAAoB;QAClD,KAAK,CACH,oDAAoD,UAAU,mBAAmB,YAAY,EAAE,CAChG,CAAC;IACJ,CAAC;CACF;AAND,kGAMC;AAED,MAAa,uCAAwC,SAAQ,kBAAkB;IAC7E,YAAY,OAAe;QACzB,KAAK,CAAC;;UAEA,OAAO,EAAE,CAAC,CAAC;IACnB,CAAC;CACF;AAND,0FAMC;AAED,MAAa,iCAAkC,SAAQ,kBAAkB;IACvE;QACE,KAAK,CAAC;4DACkD,CAAC,CAAC;IAC5D,CAAC;CACF;AALD,8EAKC;AAED,MAAa,6BAA8B,SAAQ,kBAAkB;IACnE,YAAY,OAAe,EAAE,OAAe;QAC1C,KAAK,CAAC,eAAe,OAAO;0BACN,OAAO,GAAG,CAAC,CAAC;IACpC,CAAC;CACF;AALD,sEAKC;AAED,MAAa,6BAA8B,SAAQ,kBAAkB;IACnE,YACE,sBAAgC,EAChC,uBAA+B,EAC/B,OAAe;QAEf,MAAM,cAAc,GAClB,sBAAsB,CAAC,MAAM,GAAG,CAAC;YAC/B,CAAC,CAAC,iBAAiB,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACtD,CAAC,CAAC,eAAe,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;QAEjD,KAAK,CAAC,8DAA8D,uBAAuB,kCAAkC,cAAc;;;;;;0BAMrH,OAAO,aAAa,CAAC,CAAC;IAC9C,CAAC;CACF;AAnBD,sEAmBC;AAED,MAAa,qBAAsB,SAAQ,kBAAkB;IAC3D,YAAY,WAAmB;QAC7B,KAAK,CAAC,gBAAgB,WAAW,kCAAkC,CAAC,CAAC;IACvE,CAAC;CACF;AAJD,sDAIC;AAED,MAAa,sBAAuB,SAAQ,kBAAkB;IAC5D,YAAY,WAAmB;QAC7B,KAAK,CAAC,gBAAgB,WAAW;0FACqD,CAAC,CAAC;IAC1F,CAAC;CACF;AALD,wDAKC;AAED,MAAa,qCAAsC,SAAQ,kBAAkB;IAC3E,YACE,WAAmB,EACnB,eAAuB,EACvB,cAAuB,EACvB,wBAAgC,EAChC,OAAe;QAEf,MAAM,cAAc,GAAG,cAAc;YACnC,CAAC,CAAC,mCAAmC,eAAe,EAAE;YACtD,CAAC,CAAC,wBAAwB,eAAe,EAAE,CAAC;QAE9C,KAAK,CAAC,gBAAgB,WAAW,2BAA2B,wBAAwB;+FACO,cAAc;;;;;0BAKnF,OAAO,aAAa,CAAC,CAAC;IAC9C,CAAC;CACF;AApBD,sFAoBC;AAED,MAAa,6BAA8B,SAAQ,kBAAkB;IACnE,YAAY,OAAe,EAAE,WAAoB;QAC/C,MAAM,eAAe,GACnB,OAAO,WAAW,KAAK,QAAQ;YAC7B,CAAC,CAAC,gBAAgB,WAAW,GAAG;YAChC,CAAC,CAAC,8BAA8B,CAAC;QACrC,KAAK,CAAC,wFAAwF,eAAe;;;;;;;4BAOrF,OAAO,aAAa,CAAC,CAAC;IAChD,CAAC;CACF;AAfD,sEAeC;AAED,MAAa,oCAAqC,SAAQ,kBAAkB;IAC1E,YAAY,UAAoB;QAC9B,KAAK,CAAC;;EAER,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;aAQjC,+BAAkB;;;GAG5B,CAAC,CAAC;IACH,CAAC;CACF;AAjBD,oFAiBC;AAED,MAAa,0BAA2B,SAAQ,kBAAkB;IAChE,YACE,YAAoB,EACpB,WAAmB,EACnB,cAAsB;QAEtB,KAAK,CACH,oCAAoC,YAAY,qBAAqB,WAAW,wCAAwC,cAAc,EAAE,CACzI,CAAC;IACJ,CAAC;CACF;AAVD,gEAUC;AAED,MAAa,sBAAuB,SAAQ,kBAAkB;IAC5D,YAAY,WAAmB,EAAE,UAAkB;QACjD,KAAK,CACH,qBAAqB,WAAW,QAAQ,UAAU;2EACmB,CACtE,CAAC;IACJ,CAAC;CACF;AAPD,wDAOC;AAED,MAAa,oBAAqB,SAAQ,kBAAkB;IAC1D,YACE,YAAoB,EACpB,WAAmB,EACnB,YAAsB,EACtB,mBAA6B,EAC7B,qBAA+B;QAE/B,MAAM,wBAAwB,GAAG;EACnC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;EACvD,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;EAEhE,mBAAmB,CAAC,MAAM,GAAG,CAAC;YAC5B,CAAC,CAAC,+GAA+G;YACjH,CAAC,CAAC,EACN,EAAE,CAAC;QAEC,KAAK,CAAC,uCAAuC,WAAW,+EAA+E,YAAY;EAErJ,YAAY,CAAC,MAAM,GAAG,CAAC;YACrB,CAAC,CAAC,wBAAwB;YAC1B,CAAC,CAAC,mDACN,EAAE,CAAC,CAAC;IACF,CAAC;CACF;AAxBD,oDAwBC;AAED,MAAa,2BAA4B,SAAQ,kBAAkB;IACjE,YAAY,YAAoB,EAAE,WAAmB,EAAE,UAAoB;QACzE,KAAK,CAAC,oBAAoB,WAAW,kCAAkC,YAAY;;EAErF,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;4GAE8D,CAAC,CAAC;IAC5G,CAAC;CACF;AARD,kEAQC;AAED,MAAa,qBAAsB,SAAQ,kBAAkB;IAC3D,YACE,YAAoB,EACpB,YAAsB,EACtB,eAAyB,EACzB,qBAA+B;QAE/B,MAAM,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAC1C,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC,CACjE,CAAC;QAEF,KAAK,CAAC,gBAAgB,YAAY;;EAEpC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;EAGlD,gBAAgB,CAAC,MAAM,KAAK,qBAAqB,CAAC,MAAM;YACtD,CAAC,CAAC,mJAAmJ;YACrJ,CAAC,CAAC,oGACN,EAAE,CAAC,CAAC;IACF,CAAC;CACF;AArBD,sDAqBC;AAED,MAAa,6BAA8B,SAAQ,kBAAkB;IACnE,YACE,SAIE;QAEF,KAAK,CAAC;EACR,SAAS;aACR,GAAG,CACF,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,EAAE,EAAE,CAC7C,OAAO,OAAO;iBACH,YAAY;oBACT,eAAe,EAAE,CAClC;aACA,IAAI,CAAC,IAAI,CAAC;;yHAE4G,CAAC,CAAC;IACzH,CAAC;CACF;AApBD,sEAoBC;AAED,MAAa,4BAA6B,SAAQ,kBAAkB;IAClE;QACE,KAAK,CACH,iHAAiH,CAClH,CAAC;IACJ,CAAC;CACF;AAND,oEAMC;AAED,MAAa,sBAAuB,SAAQ,kBAAkB;IAC5D,YACE,UAAkB,EAClB,YAAoB,EACpB,KAAiC;QAEjC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC;QAClE,KAAK,CACH,uBAAuB,UAAU,IAAI,YAAY,QAAQ,YAAY;MACrE,YAAY,mCAAmC,EAC/C,KAAK,CACN,CAAC;IACJ,CAAC;CACF;AAbD,wDAaC;AAED,MAAa,oBAAqB,SAAQ,kBAAkB;IAC1D,YAAY,KAA+B;QACzC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;QAC7D,KAAK,CACH,SAAS,QAAQ,wCAAwC,OAAO;wBAC9C,MAAM,EAAE,EAC1B,KAAK,CACN,CAAC;IACJ,CAAC;CACF;AATD,oDASC;AAED,MAAa,wBAAyB,SAAQ,kBAAkB;IAC9D,YAAY,KAAmC;QAC7C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QAC5D,KAAK,CACH,SAAS,QAAQ;;wBAEC,MAAM,aAAa,SAAS,EAAE,EAChD,KAAK,CACN,CAAC;IACJ,CAAC;CACF;AAVD,4DAUC;AAED;;;GAGG;AACH,MAAa,qCAAsC,SAAQ,kBAAkB;IAC3E,YAAY,OAAe;QACzB,KAAK,CAAC;;WAEC,OAAO,EAAE,CAAC,CAAC;IACpB,CAAC;CACF;AAND,sFAMC;AAED,MAAa,mBAAoB,SAAQ,kBAAkB;IACzD,YAAY,CAAQ;QAClB,KAAK,CACH,2FAA2F,CAAC,CAAC,OAAO,EAAE,CACvG,CAAC;IACJ,CAAC;CACF;AAND,kDAMC;AAED,MAAa,+BAAgC,SAAQ,kBAAkB;IACrE,YAAY,OAAe,EAAE,qBAA+B;QAC1D,KAAK,CAAC;UACA,OAAO;EAEf,qBAAqB,CAAC,MAAM,GAAG,CAAC;YAC9B,CAAC,CAAC;;;;EAIJ,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACvD,CAAC,CAAC,EACN,EAAE,CAAC,CAAC;IACF,CAAC;CACF;AAdD,0EAcC;AAED,MAAa,4BAA6B,SAAQ,kBAAkB;IAClE,YAAY,WAAmB,EAAE,eAAuB;QACtD,KAAK,CAAC,wDAAwD,WAAW,OAAO,eAAe;;qGAEE,CAAC,CAAC;IACrG,CAAC;CACF;AAND,oEAMC"}