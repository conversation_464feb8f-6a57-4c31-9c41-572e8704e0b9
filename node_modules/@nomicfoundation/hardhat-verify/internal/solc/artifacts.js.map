{"version": 3, "file": "artifacts.js", "sourceRoot": "", "sources": ["../../src/internal/solc/artifacts.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AASA,iEAAuE;AACvE,sCASmB;AA6BnB,SAAgB,iBAAiB,CAC/B,iBAA2D,EAAE;IAE7D,MAAM,OAAO,GAAiB,EAAE,CAAC;IACjC,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE;QACrD,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YACpD,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;SAChC;KACF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAVD,8CAUC;AAED,SAAgB,mBAAmB,CACjC,sBAAqE,EAAE;IAEvE,MAAM,OAAO,GAAiB,EAAE,CAAC;IACjC,KAAK,MAAM,eAAe,IAAI,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE;QAChE,OAAO,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;KAClC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AARD,kDAQC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB,CACtC,QAAgB,EAChB,iBAAyB;IAEzB,MAAM,OAAO,GAAiB,EAAE,CAAC;IACjC,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,MAAM,eAAe,GAAG,IAAI,CAAC;IAC7B,MAAM,eAAe,GAAG,eAAe,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IACtE,IACE,iBAAiB,CAAC,UAAU,CAAC,eAAe,CAAC;QAC7C,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,EACpC;QACA,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;KACjD;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAfD,4DAeC;AAED;;;;;GAKG;AACH,SAAgB,kCAAkC,CAChD,WAAmB,EACnB,SAAoB,EACpB,QAAkB;IAElB,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,IAAA,wCAAuB,EAAC,WAAW,CAAC,CAAC;IAC1E,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,CAAC;IAC5E,uDAAuD;IACvD,MAAM,8BAA8B,GAAG,cAAc,CAAC,GAAG,CAAC,gBAAgB,CAAC;IAE3E,IAAI,QAAQ,CAAC,OAAO,CAAC,8BAA8B,CAAC,EAAE;QACpD,OAAO;YACL,aAAa,EAAE,SAAS,CAAC,KAAK;YAC9B,eAAe,EAAE,SAAS,CAAC,eAAe;YAC1C,UAAU;YACV,YAAY;YACZ,cAAc;YACd,gBAAgB,EAAE,QAAQ,CAAC,SAAS,EAAE;SACvC,CAAC;KACH;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAtBD,gFAsBC;AAED;;;;GAIG;AACI,KAAK,UAAU,kCAAkC,CACtD,SAAoB,EACpB,OAAgB,EAChB,wBAAkC,EAClC,QAAkB;IAElB,MAAM,eAAe,GAAG,MAAM,sBAAsB,CAClD,SAAS,EACT,wBAAwB,EACxB,QAAQ,CACT,CAAC;IAEF,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE;QAChC,MAAM,IAAI,sCAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACvD;IAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,CACpC,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,GAAG,UAAU,IAAI,YAAY,EAAE,CAClE,CAAC;QACF,MAAM,IAAI,6CAAoC,CAAC,UAAU,CAAC,CAAC;KAC5D;IAED,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AAxBD,gFAwBC;AAED;;;;;GAKG;AACI,KAAK,UAAU,qBAAqB,CACzC,mBAAwC,EACxC,aAA+B;IAE/B,MAAM,YAAY,GAAG,iBAAiB,CACpC,mBAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAC/D,CAAC;IACF,MAAM,mBAAmB,GAAG,iBAAiB,CAC3C,mBAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,CACvE,CAAC;IACF,MAAM,qBAAqB,GAAG,YAAY,CAAC,MAAM,CAC/C,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,GAAG,CAAC,CAC/D,CAAC;IAEF,oDAAoD;IACpD,MAAM,mBAAmB,GAAG,MAAM,kBAAkB,CAClD,YAAY,EACZ,mBAAmB,EACnB,qBAAqB,EACrB,aAAa,EACb,mBAAmB,CAAC,YAAY,CACjC,CAAC;IAEF,MAAM,gBAAgB,GAAG,+BAA+B,CACtD,mBAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,EACtE,mBAAmB,CAAC,gBAAgB,CACrC,CAAC;IAEF,MAAM,kBAAkB,GAAG,cAAc,CACvC,mBAAmB,EACnB,gBAAgB,CACjB,CAAC;IAEF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;IAC9D,IAAI,eAAe,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,EAAE;QAChD,MAAM,IAAI,8BAAqB,CAC7B,GAAG,mBAAmB,CAAC,UAAU,IAAI,mBAAmB,CAAC,YAAY,EAAE,EACvE,YAAY,EACZ,eAAe,EACf,qBAAqB,CACtB,CAAC;KACH;IAED,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,CAAC;AAClE,CAAC;AA5CD,sDA4CC;AAED,KAAK,UAAU,sBAAsB,CACnC,SAAoB,EACpB,wBAAkC,EAClC,QAAkB;IAElB,MAAM,eAAe,GAA0B,EAAE,CAAC;IAClD,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,yBAAyB,EAAE,CAAC;IAE5D,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,SAAS;SACV;QAED,IACE,CAAC,wBAAwB,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC;YACzD,2HAA2H;YAC3H,CAAC,QAAQ,CAAC,KAAK,EAAE,EACjB;YACA,SAAS;SACV;QAED,MAAM,mBAAmB,GAAG,kCAAkC,CAC5D,MAAM,EACN,SAAS,EACT,QAAQ,CACT,CAAC;QACF,IAAI,mBAAmB,KAAK,IAAI,EAAE;YAChC,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC3C;KACF;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,iBAAiB,CACxB,SAA8E;IAE9E,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,KAAK,MAAM,CAAC,UAAU,EAAE,eAAe,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACrE,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;YACtD,YAAY,CAAC,IAAI,CAAC,GAAG,UAAU,IAAI,WAAW,EAAE,CAAC,CAAC;SACnD;KACF;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,KAAK,UAAU,kBAAkB,CAC/B,YAAsB,EACtB,mBAA6B,EAC7B,qBAA+B,EAC/B,aAA+B,EAC/B,YAAoB;IAEpB,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,wBAAwB,GAAC,CAAC;IAE7D,MAAM,WAAW,GAAgB,IAAI,GAAG,EAAE,CAAC;IAC3C,MAAM,mBAAmB,GAA6B,EAAE,CAAC;IACzD,KAAK,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QACzE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;YAC9B,MAAM,IAAI,mCAA0B,CAClC,YAAY,EACZ,WAAW,EACX,cAAc,CACf,CAAC;SACH;QAED,MAAM,eAAe,GAAG,aAAa,CACnC,YAAY,EACZ,mBAAmB,EACnB,qBAAqB,EACrB,WAAW,EACX,YAAY,CACb,CAAC;QACF,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,GAC9D,IAAA,wCAAuB,EAAC,eAAe,CAAC,CAAC;QAE3C,wDAAwD;QACxD,wDAAwD;QACxD,wEAAwE;QACxE,IAAI,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE;YACpC,MAAM,IAAI,+BAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;SACjE;QAED,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACjC,IAAI,mBAAmB,CAAC,cAAc,CAAC,KAAK,SAAS,EAAE;YACrD,mBAAmB,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;SAC1C;QACD,mBAAmB,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;KACpE;IAED,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED,SAAS,aAAa,CACpB,YAAsB,EACtB,mBAA6B,EAC7B,qBAA+B,EAC/B,eAAuB,EACvB,YAAoB;IAEpB,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAC3C,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,eAAe,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,eAAe,CAC1E,CAAC;IAEF,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,6BAAoB,CAC5B,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,mBAAmB,EACnB,qBAAqB,CACtB,CAAC;KACH;IAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAChC,MAAM,IAAI,oCAA2B,CACnC,YAAY,EACZ,eAAe,EACf,iBAAiB,CAClB,CAAC;KACH;IAED,MAAM,CAAC,eAAe,CAAC,GAAG,iBAAiB,CAAC;IAC5C,OAAO,eAAe,CAAC;AACzB,CAAC;AAED,SAAS,+BAA+B,CACtC,iBAA2D,EAAE,EAC7D,QAAgB;IAEhB,MAAM,wBAAwB,GAA6B,EAAE,CAAC;IAC9D,KAAK,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QAC/D,IAAI,wBAAwB,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;YACtD,wBAAwB,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;SAC3C;QACD,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACrE,wBAAwB,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,GAAG,KAAK,QAAQ,CAAC,KAAK,CACrE,KAAK,GAAG,CAAC,EACT,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CACrB,EAAE,CAAC;SACL;KACF;IACD,OAAO,wBAAwB,CAAC;AAClC,CAAC;AAED,SAAS,cAAc,CACrB,mBAA6C,EAC7C,iBAA2C;IAE3C,MAAM,SAAS,GAIV,EAAE,CAAC;IACR,KAAK,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;QACzE,KAAK,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACrE,IACE,UAAU,IAAI,iBAAiB;gBAC/B,WAAW,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAC5C;gBACA,MAAM,eAAe,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,CAAC;gBACnE,wDAAwD;gBACxD,IAAI,cAAc,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW,EAAE,EAAE;oBAClE,SAAS,CAAC,IAAI,CAAC;wBACb,OAAO,EAAE,GAAG,UAAU,IAAI,WAAW,EAAE;wBACvC,eAAe;wBACf,YAAY,EAAE,cAAc;qBAC7B,CAAC,CAAC;iBACJ;aACF;SACF;KACF;IAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI,sCAA6B,CAAC,SAAS,CAAC,CAAC;KACpD;IAED,yCAAyC;IACzC,MAAM,KAAK,GAAG,CACZ,eAAyC,EACzC,YAAsC,EACtC,EAAE;QACF,KAAK,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;YAClE,IAAI,eAAe,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE;gBAC7C,eAAe,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;aAClC;YACD,KAAK,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACrE,eAAe,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,GAAG,cAAc,CAAC;aAC3D;SACF;IACH,CAAC,CAAC;IACF,MAAM,eAAe,GAA6B,EAAE,CAAC;IACrD,KAAK,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC;IAC5C,KAAK,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;IAE1C,OAAO,eAAe,CAAC;AACzB,CAAC"}