{"version": 3, "file": "bytecode.js", "sourceRoot": "", "sources": ["../../src/internal/solc/bytecode.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,sCAA0D;AAC1D,yCAMoB;AACpB,2CAKqB;AAErB,kHAAkH;AAClH,0HAA0H;AAC1H,iHAAiH;AACjH,yGAAyG;AACzG,iHAAiH;AACjH,kHAAkH;AAClH,+BAA+B;AAC/B,yJAAyJ;AACzJ,MAAM,gBAAgB,GACpB,4EAA4E,CAAC;AAC/E,MAAM,mBAAmB,GACvB,4EAA4E,CAAC;AAE/E,MAAa,QAAQ;IAMnB,YAAY,QAAgB;QAC1B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAE1B,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,IAAA,+BAAoB,EAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,kBAAkB,GAAG;YACxB,KAAK,EAAE,CAAC;YACR,MAAM,EAAE,QAAQ,CAAC,MAAM,GAAG,IAAA,mCAAwB,EAAC,cAAc,CAAC,GAAG,CAAC;SACvE,CAAC;QAEF,kGAAkG;QAClG,wLAAwL;QACxL,sSAAsS;QACtS,iGAAiG;QACjG,kGAAkG;QAClG,8BAA8B;QAC9B,+FAA+F;QAC/F,+FAA+F;QAC/F,uBAAuB;QACvB,iFAAiF;QACjF,wGAAwG;QACxG,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IACvD,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAC7C,OAAe,EACf,QAA0B,EAC1B,OAAe;QAEf,MAAM,QAAQ,GAAW,MAAM,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE;YAC1D,OAAO;YACP,QAAQ;SACT,CAAC,CAAC;QACH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAErD,IAAI,gBAAgB,KAAK,EAAE,EAAE;YAC3B,MAAM,IAAI,sCAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IACxC,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEM,KAAK;QACV,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEM,eAAe;QACpB,OAAO,CACL,IAAI,CAAC,QAAQ,KAAK,yCAA8B;YAChD,IAAI,CAAC,QAAQ,KAAK,mDAAwC,CAC3D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,QAAkB;QACjD,MAAM,MAAM,GAAG,wDAAa,QAAQ,GAAC,CAAC;QAEtC,MAAM,wBAAwB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAC3D,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CACzC,CAAC;QAEF,OAAO,wBAAwB,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,OAAO,CACZ,8BAAsD;QAEtD,4DAA4D;QAC5D,oFAAoF;QACpF,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACvD,IAAI,0BAA0B,GAAG,sBAAsB,CACrD,8BAA8B,CAAC,MAAM,CACtC,CAAC;QAEF,+FAA+F;QAC/F,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,0BAA0B,GAAG,0BAA0B;iBACpD,KAAK,CAAC,gBAAgB,CAAC;iBACvB,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC9B;QAED,IACE,iBAAiB,CAAC,MAAM,KAAK,0BAA0B,CAAC,MAAM;YAC9D,yFAAyF;YACzF,CAAC,IAAI,CAAC,MAAM,EACZ;YACA,OAAO,KAAK,CAAC;SACd;QAED,MAAM,kBAAkB,GAAG,sBAAsB,CAC/C,iBAAiB,EACjB,8BAA8B,CAC/B,CAAC;QAEF,gGAAgG;QAChG,4CAA4C;QAC5C,MAAM,2BAA2B,GAAG,sBAAsB,CACxD,0BAA0B,EAC1B,8BAA8B,CAC/B,CAAC;QAEF,IAAI,kBAAkB,KAAK,2BAA2B,EAAE;YACtD,OAAO,IAAI,CAAC;SACb;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,qBAAqB;QAC3B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAClD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;CACF;AAhID,4BAgIC;AAED,SAAS,sBAAsB,CAC7B,QAAgB,EAChB,EACE,MAAM,EAAE,iBAAiB,EACzB,cAAc,EACd,mBAAmB,GACI;IAEzB,MAAM,OAAO,GAAG;QACd,GAAG,IAAA,6BAAiB,EAAC,cAAc,CAAC;QACpC,GAAG,IAAA,+BAAmB,EAAC,mBAAmB,CAAC;QAC3C,GAAG,IAAA,oCAAwB,EAAC,QAAQ,EAAE,iBAAiB,CAAC;KACzD,CAAC;IAEF,KAAK,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,OAAO,EAAE;QACvC,QAAQ,GAAG;YACT,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;YAC5B,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YACtB,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;SACrC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACZ;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;GAMG;AACH,SAAS,sBAAsB,CAAC,QAAgB;IAC9C,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QAC7B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KAC9B;IAED,2FAA2F;IAC3F,0EAA0E;IAC1E,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CACrC,QAAQ,CAAC,KAAK,CAAC,CAAC,0BAAe,GAAG,CAAC,CAAC,EACpC,KAAK,CACN,CAAC;IAEF,8FAA8F;IAC9F,0CAA0C;IAC1C,IAAI,mBAAmB,CAAC,MAAM,KAAK,0BAAe,EAAE;QAClD,OAAO,QAAQ,CAAC;KACjB;IAED,MAAM,qBAAqB,GAAG,IAAA,mCAAwB,EAAC,mBAAmB,CAAC,CAAC;IAE5E,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,qBAAqB,GAAG,CAAC,CAAC,CAAC;AACxE,CAAC"}