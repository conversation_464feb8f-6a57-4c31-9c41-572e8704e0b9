/// <reference types="node" />
/// <reference types="node" />
export declare const METADATA_LENGTH = 2;
export declare const SOLC_NOT_FOUND_IN_METADATA_VERSION_RANGE = "0.4.7 - 0.5.8";
export declare const MISSING_METADATA_VERSION_RANGE = "<0.4.7";
/**
 * Try to infer the Solidity compiler version from the bytecode metadata.
 *
 * Not all compiler releases produce the same bytecode:
 * Solc v0.4.7 was the first compiler to introduce metadata into the generated bytecode.
 * See https://docs.soliditylang.org/en/v0.4.7/miscellaneous.html#contract-metadata
 * Solc v0.4.26, the last release for the v0.4 series, does not feature the compiler version in its emitted metadata.
 * See https://docs.soliditylang.org/en/v0.4.26/metadata.html#encoding-of-the-metadata-hash-in-the-bytecode
 * Solc v0.5.9 was the first compiler to introduce its version into the metadata.
 * See https://docs.soliditylang.org/en/v0.5.9/metadata.html#encoding-of-the-metadata-hash-in-the-bytecode
 * Solc v0.6.0 features compiler version metadata.
 * See https://docs.soliditylang.org/en/v0.6.0/metadata.html#encoding-of-the-metadata-hash-in-the-bytecode
 */
export declare function inferCompilerVersion(bytecode: Buffer): string;
export declare function getMetadataSectionLength(bytecode: Buffer): number;
//# sourceMappingURL=metadata.d.ts.map