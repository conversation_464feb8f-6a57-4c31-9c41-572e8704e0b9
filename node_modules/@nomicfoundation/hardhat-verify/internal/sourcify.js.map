{"version": 3, "file": "sourcify.js", "sourceRoot": "", "sources": ["../src/internal/sourcify.ts"], "names": [], "mappings": ";;;AAMA,qCAIkB;AAClB,qCAAgF;AAChF,qDAAkD;AAGlD,MAAa,QAAQ;IACnB,YACS,OAAe,EACf,MAAc,EACd,UAAkB;QAFlB,YAAO,GAAP,OAAO,CAAQ;QACf,WAAM,GAAN,MAAM,CAAQ;QACd,eAAU,GAAV,UAAU,CAAQ;IACxB,CAAC;IAEJ,+EAA+E;IACxE,KAAK,CAAC,UAAU,CAAC,OAAe;QACrC,MAAM,UAAU,GAAG,IAAI,eAAe,CAAC;YACrC,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE;SAC5B,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,yBAAyB,CAAC,CAAC;QAC7D,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QAEnC,IAAI,QAA6C,CAAC;QAClD,IAAI,IAA8C,CAAC;QACnD,IAAI;YACF,QAAQ,GAAG,MAAM,IAAA,uBAAc,EAAC,GAAG,CAAC,CAAC;YACrC,IAAI,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAiC,CAAC;SACrE;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,IAAI,4BAAmB,CAAC,CAAC,CAAC,CAAC;SAClC;QAED,IAAI,CAAC,IAAA,4BAAmB,EAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC7C,MAAM,IAAI,mDAA0C,CAClD,GAAG,CAAC,QAAQ,EAAE,EACd,QAAQ,CAAC,UAAU,EACnB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CACrB,CAAC;SACH;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,MAAM,IAAI,8CAAqC,CAC7C,6BAA6B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CACpD,CAAC;SACH;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CACxB,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,OAAO,CAAC,WAAW,EAAE,CACjE,CAAC;QACF,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,KAAK,CAAC;SACd;QAED,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,+BAAc,CAAC,SAAS,EAAE;YACxE,OAAO,KAAK,CAAC;SACd;QAED,IAAI,UAAU,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5D,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACxC,IACE,MAAM,KAAK,+BAAc,CAAC,OAAO;gBACjC,MAAM,KAAK,+BAAc,CAAC,OAAO,EACjC;gBACA,OAAO,MAAM,CAAC;aACf;SACF;QAED,MAAM,IAAI,8CAAqC,CAC7C,6BAA6B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CACpD,CAAC;IACJ,CAAC;IAED,8EAA8E;IACvE,KAAK,CAAC,MAAM,CACjB,OAAe,EACf,KAA6B,EAC7B,cAAuB;QAEvB,MAAM,UAAU,GAAQ;YACtB,OAAO;YACP,KAAK;YACL,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE;SACzB,CAAC;QAEF,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,UAAU,CAAC,cAAc,GAAG,GAAG,cAAc,EAAE,CAAC;SACjD;QAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEjC,IAAI,QAA6C,CAAC;QAClD,IAAI,IAAwC,CAAC;QAC7C,IAAI;YACF,QAAQ,GAAG,MAAM,IAAA,wBAAe,EAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;gBAChE,cAAc,EAAE,kBAAkB;aACnC,CAAC,CAAC;YACH,IAAI,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAA2B,CAAC;SAC/D;QAAC,OAAO,CAAM,EAAE;YACf,MAAM,IAAI,4BAAmB,CAAC,CAAC,CAAC,CAAC;SAClC;QAED,IAAI,CAAC,IAAA,4BAAmB,EAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC7C,MAAM,IAAI,mDAA0C,CAClD,GAAG,CAAC,QAAQ,EAAE,EACd,QAAQ,CAAC,UAAU,EACnB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CACrB,CAAC;SACH;QAED,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAEpD,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAE;YAC5B,MAAM,IAAI,8CAAqC,CAC7C,8BAA8B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CACrD,CAAC;SACH;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEM,cAAc,CACnB,OAAe,EACf,cAA+D;QAE/D,MAAM,SAAS,GACb,cAAc,KAAK,+BAAc,CAAC,OAAO;YACvC,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,eAAe,CAAC;QACtB,OAAO,GAAG,IAAI,CAAC,UAAU,cAAc,SAAS,IAAI,IAAI,CAAC,OAAO,IAAI,OAAO,GAAG,CAAC;IACjF,CAAC;CACF;AA5HD,4BA4HC;AAED,MAAM,gBAAgB;IAOpB,YAAY,QAAgC;QAC1C,IAAI,OAAO,IAAI,QAAQ,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;SAC7B;aAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,+BAAc,CAAC,OAAO,EAAE;YAC/D,IAAI,CAAC,MAAM,GAAG,+BAAc,CAAC,OAAO,CAAC;SACtC;aAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,+BAAc,CAAC,OAAO,EAAE;YAC/D,IAAI,CAAC,MAAM,GAAG,+BAAc,CAAC,OAAO,CAAC;SACtC;IACH,CAAC;IAEM,SAAS;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC;IAClC,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC;IAClC,CAAC;IAEM,IAAI;QAGT,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,OAAO;YACtC,IAAI,CAAC,MAAM,KAAK,+BAAc,CAAC,OAAO,CACvC,CAAC;IACJ,CAAC;CACF"}