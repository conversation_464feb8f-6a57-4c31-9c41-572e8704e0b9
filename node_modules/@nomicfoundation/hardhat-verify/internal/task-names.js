"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TASK_VERIFY_BLOCKSCOUT_ATTEMPT_VERIFICATION = exports.TASK_VERIFY_BLOCKSCOUT_RESOLVE_ARGUMENTS = exports.TASK_VERIFY_BLOCKSCOUT = exports.TASK_VERIFY_SOURCIFY_DISABLED_WARNING = exports.TASK_VERIFY_SOURCIFY_ATTEMPT_VERIFICATION = exports.TASK_VERIFY_SOURCIFY_RESOLVE_ARGUMENTS = exports.TASK_VERIFY_SOURCIFY = exports.TASK_VERIFY_ETHERSCAN_ATTEMPT_VERIFICATION = exports.TASK_VERIFY_ETHERSCAN_GET_MINIMAL_INPUT = exports.TASK_VERIFY_ETHERSCAN_RESOLVE_ARGUMENTS = exports.TASK_VERIFY_ETHERSCAN = exports.TASK_VERIFY_GET_CONTRACT_INFORMATION = exports.TASK_VERIFY_PRINT_SUPPORTED_NETWORKS = exports.TASK_VERIFY_VERIFY = exports.TASK_VERIFY_GET_VERIFICATION_SUBTASKS = exports.TASK_VERIFY = void 0;
exports.TASK_VERIFY = "verify";
exports.TASK_VERIFY_GET_VERIFICATION_SUBTASKS = "verify:get-verification-subtasks";
exports.TASK_VERIFY_VERIFY = "verify:verify";
exports.TASK_VERIFY_PRINT_SUPPORTED_NETWORKS = "verify:print-supported-networks";
exports.TASK_VERIFY_GET_CONTRACT_INFORMATION = "verify:get-contract-information";
// Etherscan
exports.TASK_VERIFY_ETHERSCAN = "verify:etherscan";
exports.TASK_VERIFY_ETHERSCAN_RESOLVE_ARGUMENTS = "verify:etherscan-resolve-arguments";
exports.TASK_VERIFY_ETHERSCAN_GET_MINIMAL_INPUT = "verify:etherscan-get-minimal-input";
exports.TASK_VERIFY_ETHERSCAN_ATTEMPT_VERIFICATION = "verify:etherscan-attempt-verification";
// Sourcify
exports.TASK_VERIFY_SOURCIFY = "verify:sourcify";
exports.TASK_VERIFY_SOURCIFY_RESOLVE_ARGUMENTS = "verify:sourcify-resolve-arguments";
exports.TASK_VERIFY_SOURCIFY_ATTEMPT_VERIFICATION = "verify:sourcify-attempt-verification";
exports.TASK_VERIFY_SOURCIFY_DISABLED_WARNING = "verify:sourcify-disabled-warning";
// Blockscout
exports.TASK_VERIFY_BLOCKSCOUT = "verify:blockscout";
exports.TASK_VERIFY_BLOCKSCOUT_RESOLVE_ARGUMENTS = "verify:blockscout-resolve-arguments";
exports.TASK_VERIFY_BLOCKSCOUT_ATTEMPT_VERIFICATION = "verify:blockscout-attempt-verification";
//# sourceMappingURL=task-names.js.map