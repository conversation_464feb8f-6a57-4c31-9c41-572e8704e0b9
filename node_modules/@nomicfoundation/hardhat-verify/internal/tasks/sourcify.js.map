{"version": 3, "file": "sourcify.js", "sourceRoot": "", "sources": ["../../src/internal/tasks/sourcify.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,4DAAoC;AACpC,2CAAgD;AAChD,iEAAoE;AACpE,6CAAuD;AAEvD,0CAAuC;AACvC,sCAQmB;AACnB,8CAMuB;AACvB,4CAAqE;AACrE,+CAA4C;AAe5C;;;;;GAKG;AACH,IAAA,gBAAO,EAAC,iCAAoB,CAAC;KAC1B,QAAQ,CAAC,SAAS,CAAC;KACnB,gBAAgB,CAAC,UAAU,CAAC;KAC5B,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,cAAK,CAAC,GAAG,CAAC;KAC9D,SAAS,CAAC,KAAK,EAAE,QAAwB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE;IACtE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,GAAqB,MAAM,GAAG,CACrE,mDAAsC,EACtC,QAAQ,CACT,CAAC;IAEF,IAAI,OAAO,CAAC,IAAI,KAAK,8BAAoB,EAAE;QACzC,MAAM,IAAI,wCAA+B,EAAE,CAAC;KAC7C;IAED,MAAM,cAAc,GAAG,QAAQ,CAC7B,MAAM,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,EAC1C,EAAE,CACH,CAAC;IAEF,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC;IAE/C,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,MAAM,IAAI,2BAAkB,CAAC,kCAAkC,CAAC,CAAC;KAClE;IAED,IAAI,UAAU,KAAK,SAAS,EAAE;QAC5B,MAAM,IAAI,2BAAkB,CAAC,sCAAsC,CAAC,CAAC;KACtE;IAED,MAAM,QAAQ,GAAG,IAAI,mBAAQ,CAAC,cAAc,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAElE,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAClD,IAAI,MAAM,KAAK,KAAK,EAAE;QACpB,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO;EACvC,WAAW;CACZ,CAAC,CAAC;QACG,OAAO;KACR;IAED,MAAM,sBAAsB,GAAG,MAAM,IAAA,+BAAmB,EAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAE1E,MAAM,gBAAgB,GAAG,MAAM,mBAAQ,CAAC,2BAA2B,CACjE,OAAO,EACP,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,IAAI,CACb,CAAC;IAEF,MAAM,wBAAwB,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,CACzE,sBAAsB,CACvB,CAAC;IACF,+HAA+H;IAC/H,IAAI,wBAAwB,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,EAAE;QACtE,MAAM,IAAI,sCAA6B,CACrC,sBAAsB,EACtB,gBAAgB,CAAC,UAAU,EAAE,EAC7B,OAAO,CAAC,IAAI,CACb,CAAC;KACH;IAED,MAAM,mBAAmB,GAAgC,MAAM,GAAG,CAChE,iDAAoC,EACpC;QACE,WAAW;QACX,gBAAgB;QAChB,wBAAwB;QACxB,SAAS;KACV,CACF,CAAC;IAEF,MAAM,EACJ,OAAO,EAAE,mBAAmB,EAC5B,OAAO,EAAE,mBAAmB,GAC7B,GAAyB,MAAM,GAAG,CACjC,sDAAyC,EACzC;QACE,OAAO;QACP,qBAAqB,EAAE,QAAQ;QAC/B,mBAAmB;KACpB,CACF,CAAC;IAEF,IAAI,mBAAmB,EAAE;QACvB,OAAO;KACR;IAED,MAAM,IAAI,wCAA+B,CACvC,mBAAmB,EACnB,mBAAmB,CAAC,qBAAqB,CAC1C,CAAC;AACJ,CAAC,CAAC,CAAC;AAEL,IAAA,gBAAO,EAAC,mDAAsC,CAAC;KAC5C,gBAAgB,CAAC,SAAS,CAAC;KAC3B,gBAAgB,CAAC,UAAU,CAAC;KAC5B,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,cAAK,CAAC,GAAG,CAAC;KAC9D,SAAS,CACR,KAAK,EAAE,EACL,OAAO,EACP,QAAQ,EACR,SAAS,EAAE,eAAe,GACX,EAA6B,EAAE;IAC9C,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,MAAM,IAAI,4BAAmB,EAAE,CAAC;KACjC;IAED,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,wBAAwB,GAAC,CAAC;IAC7D,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;QACvB,MAAM,IAAI,4BAAmB,CAAC,OAAO,CAAC,CAAC;KACxC;IAED,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,IAAA,qCAAoB,EAAC,QAAQ,CAAC,EAAE;QAC7D,MAAM,IAAI,iCAAwB,CAAC,QAAQ,CAAC,CAAC;KAC9C;IAED,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;QACvC,SAAS,GAAG,eAAe,CAAC;KAC7B;SAAM;QACL,SAAS,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,CAAC,CAAC;KACrD;IAED,OAAO;QACL,OAAO;QACP,SAAS;QACT,WAAW,EAAE,QAAQ;KACtB,CAAC;AACJ,CAAC,CACF,CAAC;AAEJ,IAAA,gBAAO,EAAC,sDAAyC,CAAC;KAC/C,QAAQ,CAAC,SAAS,CAAC;KACnB,QAAQ,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,cAAK,CAAC,GAAG,CAAC;KAChE,QAAQ,CAAC,uBAAuB,EAAE,SAAS,EAAE,SAAS,EAAE,cAAK,CAAC,GAAG,CAAC;KAClE,SAAS,CACR,KAAK,EAAE,EACL,OAAO,EACP,qBAAqB,EACrB,mBAAmB,GACK,EAAiC,EAAE;IAC3D,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,GAC/D,mBAAmB,CAAC;IAEtB,MAAM,uBAAuB,GAAG,MAAM,CAAC,IAAI,CACzC,mBAAmB,CAAC,SAAS,CAC9B,CAAC,MAAM,CAAC,CAAC,GAA2B,EAAE,aAAa,EAAE,EAAE;QACtD,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;QAChE,GAAG,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC;QAChC,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,QAAQ,GAAG,MAAM,qBAAqB,CAAC,MAAM,CAAC,OAAO,EAAE;QAC3D,eAAe,EAAG,cAAsB,CAAC,QAAQ;QACjD,CAAC,UAAU,CAAC,EAAE,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO;QACvD,GAAG,uBAAuB;KAC3B,CAAC,CAAC;IAEH,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE;QACnB,MAAM,WAAW,GAAG,qBAAqB,CAAC,cAAc,CACtD,OAAO,EACP,QAAQ,CAAC,MAAM,CAChB,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,kCAAkC,YAAY;EAChE,WAAW;CACZ,CAAC,CAAC;KACI;IAED,OAAO;QACL,OAAO,EAAE,QAAQ,CAAC,SAAS,EAAE;QAC7B,OAAO,EAAE,4CAA4C;KACtD,CAAC;AACJ,CAAC,CACF,CAAC;AAEJ,IAAA,gBAAO,EAAC,kDAAqC,EAAE,KAAK,IAAI,EAAE;IACxD,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,IAAI,CACb;;;;;;;;4HAQsH,CACvH,CACF,CAAC;AACJ,CAAC,CAAC,CAAC"}