{"version": 3, "file": "utilities.js", "sourceRoot": "", "sources": ["../src/internal/utilities.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,4DAAoC;AACpC,gDAAwB;AACxB,iDAA+C;AAC/C,qCAUkB;AAGlB,mEAKiC;AAE1B,KAAK,UAAU,KAAK,CAAC,EAAU;IACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3D,CAAC;AAFD,sBAEC;AAED;;;GAGG;AACI,KAAK,UAAU,sBAAsB,CAC1C,YAA2B;IAE3B,MAAM,EAAE,KAAK,EAAE,GAAG,wDAAa,OAAO,GAAC,CAAC;IAExC,qBAAqB;IACrB,MAAM,iBAAiB,GAAG,4BAAa,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QACpE,OAAO;QACP,OAAO;KACR,CAAC,CAAC;IAEH,MAAM,sBAAsB,GAAG,KAAK,CAAC;QACnC,CAAC,oBAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,oBAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACzD,GAAG,iBAAiB;KACrB,CAAC,CAAC;IAEH,kBAAkB;IAClB,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QAChE,OAAO;QACP,OAAO;KACR,CAAC,CAAC;IAEH,MAAM,mBAAmB,GACvB,cAAc,CAAC,MAAM,GAAG,CAAC;QACvB,CAAC,CAAC,KAAK,CAAC;YACJ,CAAC,oBAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,oBAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzD,GAAG,cAAc;SAClB,CAAC;QACJ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;IAEjD,gBAAgB;IAChB,OAAO,CAAC,GAAG,CACT;;;EAGF,sBAAsB;;;;EAItB,mBAAmB;;;CAGpB,CAAC,SAAS,EAAE,CACV,CAAC;AACJ,CAAC;AA5CD,wDA4CC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,SAAgB,uBAAuB,CACrC,MAA0C;IAE1C,IAAI,YAAY,GACd,8EAA8E,CAAC;IAEjF,KAAK,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QAC1D,YAAY,IAAI,GAAG,YAAY,MAAM,KAAK,CAAC,OAAO,MAAM,CAAC;KAC1D;IAED,OAAO,CAAC,KAAK,CAAC,oBAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;AAC9C,CAAC;AAXD,0DAWC;AAED;;;GAGG;AACI,KAAK,UAAU,2BAA2B,CAC/C,qBAA+B,EAC/B,qBAA8B;IAE9B,IAAI,qBAAqB,KAAK,SAAS,EAAE;QACvC,OAAO,qBAAqB,CAAC;KAC9B;IAED,IAAI,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE;QACpC,MAAM,IAAI,2CAAkC,EAAE,CAAC;KAChD;IAED,MAAM,yBAAyB,GAAG,cAAI,CAAC,OAAO,CAC5C,OAAO,CAAC,GAAG,EAAE,EACb,qBAAqB,CACtB,CAAC;IAEF,IAAI;QACF,MAAM,oBAAoB,GAAG,CAAC,yBAAa,yBAAyB,uCAAC,CAAC;aACnE,OAAO,CAAC;QAEX,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE;YACxC,MAAM,IAAI,+CAAsC,CAC9C,yBAAyB,CAC1B,CAAC;SACH;QAED,OAAO,oBAAoB,CAAC;KAC7B;IAAC,OAAO,KAAU,EAAE;QACnB,MAAM,IAAI,6BAAoB,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;KACrE;AACH,CAAC;AA/BD,kEA+BC;AAED;;;GAGG;AACI,KAAK,UAAU,gBAAgB,CACpC,eAAwB;IAExB,IAAI,eAAe,KAAK,SAAS,EAAE;QACjC,OAAO,EAAE,CAAC;KACX;IAED,MAAM,mBAAmB,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,eAAe,CAAC,CAAC;IAEzE,IAAI;QACF,MAAM,SAAS,GAAG,CAAC,yBAAa,mBAAmB,uCAAC,CAAC,CAAC,OAAO,CAAC;QAE9D,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC7D,MAAM,IAAI,oCAA2B,CAAC,mBAAmB,CAAC,CAAC;SAC5D;QAED,OAAO,SAAS,CAAC;KAClB;IAAC,OAAO,KAAU,EAAE;QACnB,MAAM,IAAI,6BAAoB,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;KAC/D;AACH,CAAC;AApBD,4CAoBC;AAED;;;;;GAKG;AACI,KAAK,UAAU,mBAAmB,CAAC,EACxC,SAAS,EACT,SAAS,GACM;IACf;QACE,MAAM,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QACjE,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,KAAK,MAAM,EAAE,OAAO,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;gBAClD,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAChC;SACF;QAED,6EAA6E;QAC7E,wCAAwC;QACxC,MAAM,yBAAyB,GAAG,UAAU,CAAC;QAC7C,MAAM,MAAM,GAAG,wDAAa,QAAQ,GAAC,CAAC;QACtC,IACE,gBAAgB,CAAC,IAAI,CACnB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,yBAAyB,CAAC,CACnE,EACD;YACA,MAAM,IAAI,0CAAiC,EAAE,CAAC;SAC/C;QAED,OAAO,gBAAgB,CAAC;KACzB;AACH,CAAC;AA1BD,kDA0BC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CACnC,GAAmB,EACnB,UAAkB,EAClB,YAAoB,EACpB,oBAA2B;IAE3B,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,oBAAoB,GAAC,CAAC;IAEzD,MAAM,iBAAiB,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7C,IAAI,2BAA2B,CAAC;IAChC,IAAI;QACF,sEAAsE;QACtE,qEAAqE;QACrE,qBAAqB;QACrB,MAAM,uBAAuB,GAAG,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC;QAChE,oBAAoB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;YACtC,IACE,uBAAuB,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,QAAQ;gBAC7C,OAAO,GAAG,KAAK,QAAQ,EACvB;gBACA,MAAM,IAAI,6BAAoB,CAAC;oBAC7B,IAAI,EAAE,kBAAkB;oBACxB,QAAQ,EAAE,uBAAuB,CAAC,CAAC,CAAC,CAAC,IAAI;oBACzC,KAAK,EAAE,GAAG;oBACV,MAAM,EAAE,sBAAsB;iBACH,CAAC,CAAC;aAChC;QACH,CAAC,CAAC,CAAC;QAEH,2BAA2B,GAAG,iBAAiB;aAC5C,YAAY,CAAC,oBAAoB,CAAC;aAClC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;KACtB;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,IAAA,gDAAwB,EAAC,KAAK,CAAC,EAAE;YACnC,MAAM,IAAI,+BAAsB,CAAC,UAAU,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;SACnE;QACD,IAAI,IAAA,8CAAsB,EAAC,KAAK,CAAC,EAAE;YACjC,MAAM,IAAI,6BAAoB,CAAC,KAAK,CAAC,CAAC;SACvC;QACD,IAAI,IAAA,kDAA0B,EAAC,KAAK,CAAC,EAAE;YACrC,MAAM,IAAI,iCAAwB,CAAC,KAAK,CAAC,CAAC;SAC3C;QAED,yBAAyB;QACzB,MAAM,KAAK,CAAC;KACb;IAED,OAAO,2BAA2B,CAAC;AACrC,CAAC;AAhDD,0CAgDC"}