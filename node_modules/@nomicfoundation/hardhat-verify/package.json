{"name": "@nomicfoundation/hardhat-verify", "version": "2.1.0", "description": "Hardhat plugin for verifying contracts", "keywords": ["ethereum", "smart-contracts", "hardhat", "hardhat-plugin", "etherscan"], "homepage": "https://github.com/nomicfoundation/hardhat/tree/main/packages/hardhat-verify", "repository": "github:nomicfoundation/hardhat", "license": "MIT", "author": "Nomic Foundation", "main": "index.js", "types": "index.d.ts", "files": ["dist/src/", "src/", "internal/", "*.d.ts", "*.d.ts.map", "*.js", "*.js.map", "LICENSE", "README.md"], "dependencies": {"@ethersproject/abi": "^5.1.2", "@ethersproject/address": "^5.0.2", "cbor": "^8.1.0", "debug": "^4.1.1", "lodash.clonedeep": "^4.5.0", "picocolors": "^1.1.0", "semver": "^6.3.0", "table": "^6.8.0", "undici": "^5.14.0"}, "devDependencies": {"@nomicfoundation/hardhat-ethers": "^3.1.0", "@types/chai": "^4.2.0", "@types/chai-as-promised": "^7.1.3", "@types/debug": "^4.1.4", "@types/lodash.clonedeep": "^4.5.7", "@types/mocha": ">=9.1.0", "@types/node": "^20.0.0", "@types/semver": "^6.0.2", "@types/sinon": "^9.0.8", "@types/sinon-chai": "^3.2.9", "@typescript-eslint/eslint-plugin": "5.61.0", "@typescript-eslint/parser": "5.61.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^8.44.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-mocha": "10.4.1", "eslint-plugin-prettier": "3.4.0", "ethers": "^5.0.0", "hardhat": "^2.26.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "sinon": "^9.0.0", "sinon-chai": "^3.7.0", "ts-node": "^10.8.0", "typescript": "~5.0.0", "@nomicfoundation/eslint-plugin-hardhat-internal-rules": "^1.0.2", "@nomicfoundation/eslint-plugin-slow-imports": "^1.0.0"}, "peerDependencies": {"hardhat": "^2.26.0"}, "scripts": {"build": "tsc --build .", "clean": "rimraf dist internal *.{d.ts,js}{,.map} build-test tsconfig.tsbuildinfo", "coverage": "nyc pnpm test -- --reporter min", "eslint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "lint": "pnpm prettier --check && pnpm eslint", "lint:fix": "pnpm prettier --write && pnpm eslint --fix", "prettier": "prettier \"**/*.{js,md,json}\"", "pretest": "cd ../.. && pnpm build", "test": "mocha --recursive \"test/**/*.ts\" --exit"}}