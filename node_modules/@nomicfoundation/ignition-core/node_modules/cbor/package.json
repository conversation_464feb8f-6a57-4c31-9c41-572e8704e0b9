{"name": "cbor", "version": "9.0.2", "description": "Encode and parse data in the Concise Binary Object Representation (CBOR) data format (RFC8949).", "main": "./lib/cbor.js", "repository": {"type": "git", "url": "http://github.com/hildjj/node-cbor.git", "directory": "packages/cbor"}, "homepage": "https://github.com/hildjj/node-cbor/tree/main/packages/cbor", "directories": {"lib": "lib"}, "browser": {"fs": false}, "scripts": {"clean": "rimraf coverage .nyc_output/ docs", "lint": "eslint lib/*.js test/*.js", "coverage": "nyc -r lcov npm test", "test": "ava test/*.ava.js", "release": "npm version patch && git push --follow-tags && npm publish", "predev": "npm run coverage", "dev": "light-server -q -s. -w 'lib/*.js,test/*.js # npm run coverage' -o /coverage/lcov-report/index.html", "types": "tsc && npm run copy-types", "copy-types": "copyfiles \"./types/**\" \"../cbor-web/\""}, "keywords": ["coap", "cbor", "json", "rfc7049", "rfc8949"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>> (http://paroga.com/)", "<PERSON><PERSON><PERSON> <<EMAIL>> (http://scorpi.org/)", "<PERSON> <<EMAIL>> (http://lapaev.me/)", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<PERSON>_<PERSON><EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://jakubarbet.me/)", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/rouzwelt)"], "types": "./types/lib/cbor.d.ts", "dependencies": {"nofilter": "^3.1.0"}, "devDependencies": {"@types/node": "^20", "bignumber.js": "^9.1.1", "garbage": "~0.0.0", "p-event": "^4.2.0", "rimraf": "^5.0.0"}, "license": "MIT", "readmeFilename": "README.md", "engines": {"node": ">=16"}, "gitHead": "1967cd4540e9aab60093fcd711cd97edee542043"}