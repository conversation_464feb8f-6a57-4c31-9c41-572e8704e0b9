{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AACA,iCAAyD;AAAhD,0CAAA,uBAAuB,CAAA;AAAE,wBAAA,KAAK,CAAA;AACvC,qCAAoC;AAA3B,4BAAA,OAAO,CAAA;AAChB,6BAQe;AAPb,gCAAA,eAAe,CAAA;AACf,8BAAA,aAAa,CAAA;AACb,kCAAA,iBAAiB,CAAA;AACjB,+BAAA,cAAc,CAAA;AACd,oBAAA,GAAG,CAAA;AACH,yBAAA,QAAQ,CAAA;AACR,gCAAA,eAAe,CAAA", "sourcesContent": ["export { Carrier, DomainAsCarrier, Layer } from './interfaces';\nexport { addGlobalEventProcessor, Scope } from './scope';\nexport { Session } from './session';\nexport {\n  getActiveDomain,\n  getCurrentHub,\n  getHubFromCarrier,\n  getMainCarrier,\n  Hub,\n  makeMain,\n  setHubOnCarrier,\n} from './hub';\n"]}