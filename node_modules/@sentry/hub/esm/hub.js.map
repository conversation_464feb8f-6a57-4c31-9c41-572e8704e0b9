{"version": 3, "file": "hub.js", "sourceRoot": "", "sources": ["../src/hub.ts"], "names": [], "mappings": ";AAsBA,OAAO,EAAE,cAAc,EAAE,sBAAsB,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAGlH,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEpC;;;;;;;GAOG;AACH,MAAM,CAAC,IAAM,WAAW,GAAG,CAAC,CAAC;AAE7B;;;GAGG;AACH,IAAM,mBAAmB,GAAG,GAAG,CAAC;AAEhC;;;GAGG;AACH,IAAM,eAAe,GAAG,GAAG,CAAC;AAE5B;;GAEG;AACH;IAOE;;;;;;;OAOG;IACH,aAAmB,MAAe,EAAE,KAA0B,EAAmB,QAA8B;QAA3E,sBAAA,EAAA,YAAmB,KAAK,EAAE;QAAmB,yBAAA,EAAA,sBAA8B;QAA9B,aAAQ,GAAR,QAAQ,CAAsB;QAd/G,2DAA2D;QAC1C,WAAM,GAAY,CAAC,EAAE,CAAC,CAAC;QActC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,yBAAW,GAAlB,UAAmB,OAAe;QAChC,OAAO,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,wBAAU,GAAjB,UAAkB,MAAe;QAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;QACpB,IAAI,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE;YACtC,MAAM,CAAC,iBAAiB,EAAE,CAAC;SAC5B;IACH,CAAC;IAED;;OAEG;IACI,uBAAS,GAAhB;QACE,6CAA6C;QAC7C,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC;YACnB,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;YACxB,KAAK,OAAA;SACN,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,sBAAQ,GAAf;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QAC9C,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,uBAAS,GAAhB,UAAiB,QAAgC;QAC/C,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC/B,IAAI;YACF,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjB;gBAAS;YACR,IAAI,CAAC,QAAQ,EAAE,CAAC;SACjB;IACH,CAAC;IAED;;OAEG;IACI,uBAAS,GAAhB;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAW,CAAC;IACxC,CAAC;IAED,0CAA0C;IACnC,sBAAQ,GAAf;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC;IAClC,CAAC;IAED,0DAA0D;IACnD,sBAAQ,GAAf;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,6EAA6E;IACtE,yBAAW,GAAlB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,iHAAiH;IAC1G,8BAAgB,GAAvB,UAAwB,SAAc,EAAE,IAAgB;QACtD,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,EAAE,CAAC,CAAC;QAC9C,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,gFAAgF;QAChF,yDAAyD;QACzD,gFAAgF;QAChF,iEAAiE;QACjE,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,kBAAkB,SAAO,CAAC;YAC9B,IAAI;gBACF,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;aAC9C;YAAC,OAAO,SAAS,EAAE;gBAClB,kBAAkB,GAAG,SAAkB,CAAC;aACzC;YACD,SAAS,GAAG;gBACV,iBAAiB,EAAE,SAAS;gBAC5B,kBAAkB,oBAAA;aACnB,CAAC;SACH;QAED,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,SAAS,wBAC3C,SAAS,KACZ,QAAQ,EAAE,OAAO,IACjB,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,4BAAc,GAArB,UAAsB,OAAe,EAAE,KAAgB,EAAE,IAAgB;QACvE,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,EAAE,CAAC,CAAC;QAC9C,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,gFAAgF;QAChF,yDAAyD;QACzD,gFAAgF;QAChF,iEAAiE;QACjE,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,kBAAkB,SAAO,CAAC;YAC9B,IAAI;gBACF,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;aAC1B;YAAC,OAAO,SAAS,EAAE;gBAClB,kBAAkB,GAAG,SAAkB,CAAC;aACzC;YACD,SAAS,GAAG;gBACV,iBAAiB,EAAE,OAAO;gBAC1B,kBAAkB,oBAAA;aACnB,CAAC;SACH;QAED,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,OAAO,EAAE,KAAK,wBAC9C,SAAS,KACZ,QAAQ,EAAE,OAAO,IACjB,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,0BAAY,GAAnB,UAAoB,KAAY,EAAE,IAAgB;QAChD,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,EAAE,CAAC,CAAC;QAC9C,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,KAAK,wBACnC,IAAI,KACP,QAAQ,EAAE,OAAO,IACjB,CAAC;QACH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,yBAAW,GAAlB;QACE,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,2BAAa,GAApB,UAAqB,UAAsB,EAAE,IAAqB;QAC1D,IAAA,uBAAsC,EAApC,gBAAK,EAAE,kBAA6B,CAAC;QAE7C,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM;YAAE,OAAO;QAE9B,6DAA6D;QACvD,IAAA,qDAC4C,EAD1C,wBAAuB,EAAvB,4CAAuB,EAAE,sBAAoC,EAApC,yDACiB,CAAC;QAEnD,IAAI,cAAc,IAAI,CAAC;YAAE,OAAO;QAEhC,IAAM,SAAS,GAAG,sBAAsB,EAAE,CAAC;QAC3C,IAAM,gBAAgB,cAAK,SAAS,WAAA,IAAK,UAAU,CAAE,CAAC;QACtD,IAAM,eAAe,GAAG,gBAAgB;YACtC,CAAC,CAAE,cAAc,CAAC,cAAM,OAAA,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAAxC,CAAwC,CAAuB;YACvF,CAAC,CAAC,gBAAgB,CAAC;QAErB,IAAI,eAAe,KAAK,IAAI;YAAE,OAAO;QAErC,KAAK,CAAC,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC;IAClF,CAAC;IAED;;OAEG;IACI,qBAAO,GAAd,UAAe,IAAiB;QAC9B,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK;YAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,qBAAO,GAAd,UAAe,IAAkC;QAC/C,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK;YAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,uBAAS,GAAhB,UAAiB,MAAc;QAC7B,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK;YAAE,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,oBAAM,GAAb,UAAc,GAAW,EAAE,KAAgB;QACzC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK;YAAE,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,sBAAQ,GAAf,UAAgB,GAAW,EAAE,KAAY;QACvC,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK;YAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,8DAA8D;IACvD,wBAAU,GAAjB,UAAkB,IAAY,EAAE,OAAsC;QACpE,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK;YAAE,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,4BAAc,GAArB,UAAsB,QAAgC;QAC9C,IAAA,uBAAsC,EAApC,gBAAK,EAAE,kBAA6B,CAAC;QAC7C,IAAI,KAAK,IAAI,MAAM,EAAE;YACnB,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjB;IACH,CAAC;IAED;;OAEG;IACI,iBAAG,GAAV,UAAW,QAA4B;QACrC,IAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI;YACF,QAAQ,CAAC,IAAI,CAAC,CAAC;SAChB;gBAAS;YACR,QAAQ,CAAC,MAAM,CAAC,CAAC;SAClB;IACH,CAAC;IAED;;OAEG;IACI,4BAAc,GAArB,UAA6C,WAAgC;QAC3E,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QACzB,IAAI;YACF,OAAO,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;SAC3C;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,iCAA+B,WAAW,CAAC,EAAE,0BAAuB,CAAC,CAAC;YAClF,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACI,uBAAS,GAAhB,UAAiB,OAAoB;QACnC,OAAO,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,8BAAgB,GAAvB,UAAwB,OAA2B,EAAE,qBAA6C;QAChG,OAAO,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;IACvF,CAAC;IAED;;OAEG;IACI,0BAAY,GAAnB;QACE,OAAO,IAAI,CAAC,oBAAoB,CAA4B,cAAc,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,0BAAY,GAAnB,UAAoB,OAAwB;QAC1C,sCAAsC;QACtC,IAAI,CAAC,UAAU,EAAE,CAAC;QAEZ,IAAA,uBAAsC,EAApC,gBAAK,EAAE,kBAA6B,CAAC;QACvC,IAAA,0CAAgE,EAA9D,oBAAO,EAAE,4BAAqD,CAAC;QACvE,IAAM,OAAO,GAAG,IAAI,OAAO,qBACzB,OAAO,SAAA;YACP,WAAW,aAAA,IACR,CAAC,KAAK,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,GACpC,OAAO,EACV,CAAC;QACH,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SAC3B;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,wBAAU,GAAjB;QACQ,IAAA,uBAAsC,EAApC,gBAAK,EAAE,kBAA6B,CAAC;QAC7C,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,IAAM,OAAO,GAAG,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;QACvD,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE;gBACnC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;aAChC;YACD,KAAK,CAAC,UAAU,EAAE,CAAC;SACpB;IACH,CAAC;IAED;;;;;OAKG;IACH,8DAA8D;IACtD,2BAAa,GAArB,UAA8C,MAAS;;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAC/D,IAAA,uBAAsC,EAApC,gBAAK,EAAE,kBAA6B,CAAC;QAC7C,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE;YAC5B,0GAA0G;YAC1G,CAAA,KAAC,MAAc,CAAA,CAAC,MAAM,CAAC,oBAAI,IAAI,GAAE,KAAK,IAAE;SACzC;IACH,CAAC;IAED;;OAEG;IACH,2GAA2G;IAC3G,8DAA8D;IACtD,kCAAoB,GAA5B,UAAgC,MAAc;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QAC5D,IAAM,OAAO,GAAG,cAAc,EAAE,CAAC;QACjC,IAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;QAClC,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE;YAClF,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACpD;QACD,MAAM,CAAC,IAAI,CAAC,sBAAoB,MAAM,uCAAoC,CAAC,CAAC;IAC9E,CAAC;IACH,UAAC;AAAD,CAAC,AApXD,IAoXC;;AAED,wCAAwC;AACxC,MAAM,UAAU,cAAc;IAC5B,IAAM,OAAO,GAAG,eAAe,EAAE,CAAC;IAClC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI;QACzC,UAAU,EAAE,EAAE;QACd,GAAG,EAAE,SAAS;KACf,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,QAAQ,CAAC,GAAQ;IAC/B,IAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;IAClC,IAAM,MAAM,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC3C,eAAe,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,aAAa;IAC3B,kDAAkD;IAClD,IAAM,QAAQ,GAAG,cAAc,EAAE,CAAC;IAElC,yDAAyD;IACzD,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,iBAAiB,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;QACtF,eAAe,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;KACtC;IAED,qFAAqF;IACrF,IAAI,SAAS,EAAE,EAAE;QACf,OAAO,sBAAsB,CAAC,QAAQ,CAAC,CAAC;KACzC;IACD,2CAA2C;IAC3C,OAAO,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACrC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe;IAC7B,IAAM,MAAM,GAAG,cAAc,EAAE,CAAC,UAAU,CAAC;IAE3C,OAAO,MAAM,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AACpG,CAAC;AAED;;;GAGG;AACH,SAAS,sBAAsB,CAAC,QAAiB;IAC/C,IAAI;QACF,IAAM,YAAY,GAAG,eAAe,EAAE,CAAC;QAEvC,sDAAsD;QACtD,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,iBAAiB,CAAC,QAAQ,CAAC,CAAC;SACpC;QAED,4EAA4E;QAC5E,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,iBAAiB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;YAC9F,IAAM,mBAAmB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACtE,eAAe,CAAC,YAAY,EAAE,IAAI,GAAG,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAC5G;QAED,oCAAoC;QACpC,OAAO,iBAAiB,CAAC,YAAY,CAAC,CAAC;KACxC;IAAC,OAAO,GAAG,EAAE;QACZ,2CAA2C;QAC3C,OAAO,iBAAiB,CAAC,QAAQ,CAAC,CAAC;KACpC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,eAAe,CAAC,OAAgB;IACvC,OAAO,CAAC,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACrE,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,OAAgB;IAChD,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG;QAAE,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC;IAC3F,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;IAC9C,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;IACnC,OAAO,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC;AAChC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,OAAgB,EAAE,GAAQ;IACxD,IAAI,CAAC,OAAO;QAAE,OAAO,KAAK,CAAC;IAC3B,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;IAC9C,OAAO,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;IAC7B,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["/* eslint-disable max-lines */\nimport {\n  Breadcrumb,\n  BreadcrumbHint,\n  Client,\n  CustomSamplingContext,\n  Event,\n  EventHint,\n  Extra,\n  Extras,\n  Hub as HubInterface,\n  Integration,\n  IntegrationClass,\n  Primitive,\n  SessionContext,\n  Severity,\n  Span,\n  SpanContext,\n  Transaction,\n  TransactionContext,\n  User,\n} from '@sentry/types';\nimport { consoleSandbox, dateTimestampInSeconds, getGlobalObject, isNodeEnv, logger, uuid4 } from '@sentry/utils';\n\nimport { Carrier, DomainAsCarrier, Layer } from './interfaces';\nimport { Scope } from './scope';\nimport { Session } from './session';\n\n/**\n * API compatibility version of this hub.\n *\n * WARNING: This number should only be increased when the global interface\n * changes and new methods are introduced.\n *\n * @hidden\n */\nexport const API_VERSION = 3;\n\n/**\n * Default maximum number of breadcrumbs added to an event. Can be overwritten\n * with {@link Options.maxBreadcrumbs}.\n */\nconst DEFAULT_BREADCRUMBS = 100;\n\n/**\n * Absolute maximum number of breadcrumbs added to an event. The\n * `maxBreadcrumbs` option cannot be higher than this value.\n */\nconst MAX_BREADCRUMBS = 100;\n\n/**\n * @inheritDoc\n */\nexport class Hub implements HubInterface {\n  /** Is a {@link Layer}[] containing the client and scope */\n  private readonly _stack: Layer[] = [{}];\n\n  /** Contains the last event id of a captured event.  */\n  private _lastEventId?: string;\n\n  /**\n   * Creates a new instance of the hub, will push one {@link Layer} into the\n   * internal stack on creation.\n   *\n   * @param client bound to the hub.\n   * @param scope bound to the hub.\n   * @param version number, higher number means higher priority.\n   */\n  public constructor(client?: Client, scope: Scope = new Scope(), private readonly _version: number = API_VERSION) {\n    this.getStackTop().scope = scope;\n    this.bindClient(client);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public isOlderThan(version: number): boolean {\n    return this._version < version;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public bindClient(client?: Client): void {\n    const top = this.getStackTop();\n    top.client = client;\n    if (client && client.setupIntegrations) {\n      client.setupIntegrations();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public pushScope(): Scope {\n    // We want to clone the content of prev scope\n    const scope = Scope.clone(this.getScope());\n    this.getStack().push({\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public popScope(): boolean {\n    if (this.getStack().length <= 1) return false;\n    return !!this.getStack().pop();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public withScope(callback: (scope: Scope) => void): void {\n    const scope = this.pushScope();\n    try {\n      callback(scope);\n    } finally {\n      this.popScope();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this.getStackTop().client as C;\n  }\n\n  /** Returns the scope of the top stack. */\n  public getScope(): Scope | undefined {\n    return this.getStackTop().scope;\n  }\n\n  /** Returns the scope stack for domains or the process. */\n  public getStack(): Layer[] {\n    return this._stack;\n  }\n\n  /** Returns the topmost scope layer in the order domain > local > process. */\n  public getStackTop(): Layer {\n    return this._stack[this._stack.length - 1];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint): string {\n    const eventId = (this._lastEventId = uuid4());\n    let finalHint = hint;\n\n    // If there's no explicit hint provided, mimick the same thing that would happen\n    // in the minimal itself to create a consistent behavior.\n    // We don't do this in the client, as it's the lowest level API, and doing this,\n    // would prevent user from having full control over direct calls.\n    if (!hint) {\n      let syntheticException: Error;\n      try {\n        throw new Error('Sentry syntheticException');\n      } catch (exception) {\n        syntheticException = exception as Error;\n      }\n      finalHint = {\n        originalException: exception,\n        syntheticException,\n      };\n    }\n\n    this._invokeClient('captureException', exception, {\n      ...finalHint,\n      event_id: eventId,\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(message: string, level?: Severity, hint?: EventHint): string {\n    const eventId = (this._lastEventId = uuid4());\n    let finalHint = hint;\n\n    // If there's no explicit hint provided, mimick the same thing that would happen\n    // in the minimal itself to create a consistent behavior.\n    // We don't do this in the client, as it's the lowest level API, and doing this,\n    // would prevent user from having full control over direct calls.\n    if (!hint) {\n      let syntheticException: Error;\n      try {\n        throw new Error(message);\n      } catch (exception) {\n        syntheticException = exception as Error;\n      }\n      finalHint = {\n        originalException: message,\n        syntheticException,\n      };\n    }\n\n    this._invokeClient('captureMessage', message, level, {\n      ...finalHint,\n      event_id: eventId,\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = (this._lastEventId = uuid4());\n    this._invokeClient('captureEvent', event, {\n      ...hint,\n      event_id: eventId,\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void {\n    const { scope, client } = this.getStackTop();\n\n    if (!scope || !client) return;\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const { beforeBreadcrumb = null, maxBreadcrumbs = DEFAULT_BREADCRUMBS } =\n      (client.getOptions && client.getOptions()) || {};\n\n    if (maxBreadcrumbs <= 0) return;\n\n    const timestamp = dateTimestampInSeconds();\n    const mergedBreadcrumb = { timestamp, ...breadcrumb };\n    const finalBreadcrumb = beforeBreadcrumb\n      ? (consoleSandbox(() => beforeBreadcrumb(mergedBreadcrumb, hint)) as Breadcrumb | null)\n      : mergedBreadcrumb;\n\n    if (finalBreadcrumb === null) return;\n\n    scope.addBreadcrumb(finalBreadcrumb, Math.min(maxBreadcrumbs, MAX_BREADCRUMBS));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): void {\n    const scope = this.getScope();\n    if (scope) scope.setUser(user);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): void {\n    const scope = this.getScope();\n    if (scope) scope.setTags(tags);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): void {\n    const scope = this.getScope();\n    if (scope) scope.setExtras(extras);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): void {\n    const scope = this.getScope();\n    if (scope) scope.setTag(key, value);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): void {\n    const scope = this.getScope();\n    if (scope) scope.setExtra(key, extra);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public setContext(name: string, context: { [key: string]: any } | null): void {\n    const scope = this.getScope();\n    if (scope) scope.setContext(name, context);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public configureScope(callback: (scope: Scope) => void): void {\n    const { scope, client } = this.getStackTop();\n    if (scope && client) {\n      callback(scope);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public run(callback: (hub: Hub) => void): void {\n    const oldHub = makeMain(this);\n    try {\n      callback(this);\n    } finally {\n      makeMain(oldHub);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    const client = this.getClient();\n    if (!client) return null;\n    try {\n      return client.getIntegration(integration);\n    } catch (_oO) {\n      logger.warn(`Cannot retrieve integration ${integration.id} from the current Hub`);\n      return null;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startSpan(context: SpanContext): Span {\n    return this._callExtensionMethod('startSpan', context);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startTransaction(context: TransactionContext, customSamplingContext?: CustomSamplingContext): Transaction {\n    return this._callExtensionMethod('startTransaction', context, customSamplingContext);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public traceHeaders(): { [key: string]: string } {\n    return this._callExtensionMethod<{ [key: string]: string }>('traceHeaders');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startSession(context?: SessionContext): Session {\n    // End existing session if there's one\n    this.endSession();\n\n    const { scope, client } = this.getStackTop();\n    const { release, environment } = (client && client.getOptions()) || {};\n    const session = new Session({\n      release,\n      environment,\n      ...(scope && { user: scope.getUser() }),\n      ...context,\n    });\n    if (scope) {\n      scope.setSession(session);\n    }\n    return session;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public endSession(): void {\n    const { scope, client } = this.getStackTop();\n    if (!scope) return;\n\n    const session = scope.getSession && scope.getSession();\n    if (session) {\n      session.close();\n      if (client && client.captureSession) {\n        client.captureSession(session);\n      }\n      scope.setSession();\n    }\n  }\n\n  /**\n   * Internal helper function to call a method on the top client if it exists.\n   *\n   * @param method The method to call on the client.\n   * @param args Arguments to pass to the client function.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _invokeClient<M extends keyof Client>(method: M, ...args: any[]): void {\n    const { scope, client } = this.getStackTop();\n    if (client && client[method]) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n      (client as any)[method](...args, scope);\n    }\n  }\n\n  /**\n   * Calls global extension method and binding current instance to the function call\n   */\n  // @ts-ignore Function lacks ending return statement and return type does not include 'undefined'. ts(2366)\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _callExtensionMethod<T>(method: string, ...args: any[]): T {\n    const carrier = getMainCarrier();\n    const sentry = carrier.__SENTRY__;\n    if (sentry && sentry.extensions && typeof sentry.extensions[method] === 'function') {\n      return sentry.extensions[method].apply(this, args);\n    }\n    logger.warn(`Extension method ${method} couldn't be found, doing nothing.`);\n  }\n}\n\n/** Returns the global shim registry. */\nexport function getMainCarrier(): Carrier {\n  const carrier = getGlobalObject();\n  carrier.__SENTRY__ = carrier.__SENTRY__ || {\n    extensions: {},\n    hub: undefined,\n  };\n  return carrier;\n}\n\n/**\n * Replaces the current main hub with the passed one on the global object\n *\n * @returns The old replaced hub\n */\nexport function makeMain(hub: Hub): Hub {\n  const registry = getMainCarrier();\n  const oldHub = getHubFromCarrier(registry);\n  setHubOnCarrier(registry, hub);\n  return oldHub;\n}\n\n/**\n * Returns the default hub instance.\n *\n * If a hub is already registered in the global carrier but this module\n * contains a more recent version, it replaces the registered version.\n * Otherwise, the currently registered hub will be returned.\n */\nexport function getCurrentHub(): Hub {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n\n  // If there's no hub, or its an old API, assign a new one\n  if (!hasHubOnCarrier(registry) || getHubFromCarrier(registry).isOlderThan(API_VERSION)) {\n    setHubOnCarrier(registry, new Hub());\n  }\n\n  // Prefer domains over global if they are there (applicable only to Node environment)\n  if (isNodeEnv()) {\n    return getHubFromActiveDomain(registry);\n  }\n  // Return hub that lives on a global object\n  return getHubFromCarrier(registry);\n}\n\n/**\n * Returns the active domain, if one exists\n *\n * @returns The domain, or undefined if there is no active domain\n */\nexport function getActiveDomain(): DomainAsCarrier | undefined {\n  const sentry = getMainCarrier().__SENTRY__;\n\n  return sentry && sentry.extensions && sentry.extensions.domain && sentry.extensions.domain.active;\n}\n\n/**\n * Try to read the hub from an active domain, and fallback to the registry if one doesn't exist\n * @returns discovered hub\n */\nfunction getHubFromActiveDomain(registry: Carrier): Hub {\n  try {\n    const activeDomain = getActiveDomain();\n\n    // If there's no active domain, just return global hub\n    if (!activeDomain) {\n      return getHubFromCarrier(registry);\n    }\n\n    // If there's no hub on current domain, or it's an old API, assign a new one\n    if (!hasHubOnCarrier(activeDomain) || getHubFromCarrier(activeDomain).isOlderThan(API_VERSION)) {\n      const registryHubTopStack = getHubFromCarrier(registry).getStackTop();\n      setHubOnCarrier(activeDomain, new Hub(registryHubTopStack.client, Scope.clone(registryHubTopStack.scope)));\n    }\n\n    // Return hub that lives on a domain\n    return getHubFromCarrier(activeDomain);\n  } catch (_Oo) {\n    // Return hub that lives on a global object\n    return getHubFromCarrier(registry);\n  }\n}\n\n/**\n * This will tell whether a carrier has a hub on it or not\n * @param carrier object\n */\nfunction hasHubOnCarrier(carrier: Carrier): boolean {\n  return !!(carrier && carrier.__SENTRY__ && carrier.__SENTRY__.hub);\n}\n\n/**\n * This will create a new {@link Hub} and add to the passed object on\n * __SENTRY__.hub.\n * @param carrier object\n * @hidden\n */\nexport function getHubFromCarrier(carrier: Carrier): Hub {\n  if (carrier && carrier.__SENTRY__ && carrier.__SENTRY__.hub) return carrier.__SENTRY__.hub;\n  carrier.__SENTRY__ = carrier.__SENTRY__ || {};\n  carrier.__SENTRY__.hub = new Hub();\n  return carrier.__SENTRY__.hub;\n}\n\n/**\n * This will set passed {@link Hub} on the passed object's __SENTRY__.hub attribute\n * @param carrier object\n * @param hub Hub\n */\nexport function setHubOnCarrier(carrier: Carrier, hub: Hub): boolean {\n  if (!carrier) return false;\n  carrier.__SENTRY__ = carrier.__SENTRY__ || {};\n  carrier.__SENTRY__.hub = hub;\n  return true;\n}\n"]}