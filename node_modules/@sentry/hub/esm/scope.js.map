{"version": 3, "file": "scope.js", "sourceRoot": "", "sources": ["../src/scope.ts"], "names": [], "mappings": ";AAmBA,OAAO,EAAE,sBAAsB,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAIhH;;;GAGG;AACH;IAAA;QACE,uCAAuC;QAC7B,wBAAmB,GAAY,KAAK,CAAC;QAE/C,oDAAoD;QAC1C,oBAAe,GAAkC,EAAE,CAAC;QAE9D,oEAAoE;QAC1D,qBAAgB,GAAqB,EAAE,CAAC;QAElD,4BAA4B;QAClB,iBAAY,GAAiB,EAAE,CAAC;QAE1C,WAAW;QACD,UAAK,GAAS,EAAE,CAAC;QAE3B,WAAW;QACD,UAAK,GAAiC,EAAE,CAAC;QAEnD,YAAY;QACF,WAAM,GAAW,EAAE,CAAC;QAE9B,eAAe;QACL,cAAS,GAAa,EAAE,CAAC;IAyarC,CAAC;IAxZC;;;OAGG;IACW,WAAK,GAAnB,UAAoB,KAAa;QAC/B,IAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;QAC7B,IAAI,KAAK,EAAE;YACT,QAAQ,CAAC,YAAY,YAAO,KAAK,CAAC,YAAY,CAAC,CAAC;YAChD,QAAQ,CAAC,KAAK,gBAAQ,KAAK,CAAC,KAAK,CAAE,CAAC;YACpC,QAAQ,CAAC,MAAM,gBAAQ,KAAK,CAAC,MAAM,CAAE,CAAC;YACtC,QAAQ,CAAC,SAAS,gBAAQ,KAAK,CAAC,SAAS,CAAE,CAAC;YAC5C,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAC7B,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC/B,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAC7B,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YACnC,QAAQ,CAAC,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAC;YACnD,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;YAC3C,QAAQ,CAAC,gBAAgB,YAAO,KAAK,CAAC,gBAAgB,CAAC,CAAC;SACzD;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,gCAAgB,GAAvB,UAAwB,QAAgC;QACtD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,iCAAiB,GAAxB,UAAyB,QAAwB;QAC/C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,uBAAO,GAAd,UAAe,IAAiB;QAC9B,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACxB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,uBAAO,GAAd;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,uBAAO,GAAd,UAAe,IAAkC;QAC/C,IAAI,CAAC,KAAK,yBACL,IAAI,CAAC,KAAK,GACV,IAAI,CACR,CAAC;QACF,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,sBAAM,GAAb,UAAc,GAAW,EAAE,KAAgB;;QACzC,IAAI,CAAC,KAAK,yBAAQ,IAAI,CAAC,KAAK,gBAAG,GAAG,IAAG,KAAK,MAAE,CAAC;QAC7C,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,yBAAS,GAAhB,UAAiB,MAAc;QAC7B,IAAI,CAAC,MAAM,yBACN,IAAI,CAAC,MAAM,GACX,MAAM,CACV,CAAC;QACF,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,wBAAQ,GAAf,UAAgB,GAAW,EAAE,KAAY;;QACvC,IAAI,CAAC,MAAM,yBAAQ,IAAI,CAAC,MAAM,gBAAG,GAAG,IAAG,KAAK,MAAE,CAAC;QAC/C,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,8BAAc,GAArB,UAAsB,WAAqB;QACzC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,wBAAQ,GAAf,UAAgB,KAAe;QAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,kCAAkB,GAAzB,UAA0B,IAAa;QACrC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACI,8BAAc,GAArB,UAAsB,IAAa;QACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,0BAAU,GAAjB,UAAkB,GAAW,EAAE,OAAuB;;QACpD,IAAI,OAAO,KAAK,IAAI,EAAE;YACpB,gEAAgE;YAChE,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SAC5B;aAAM;YACL,IAAI,CAAC,SAAS,yBAAQ,IAAI,CAAC,SAAS,gBAAG,GAAG,IAAG,OAAO,MAAE,CAAC;SACxD;QAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,uBAAO,GAAd,UAAe,IAAW;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,uBAAO,GAAd;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,8BAAc,GAArB;;QACE,wEAAwE;QACxE,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAA8D,CAAC;QAExF,2BAA2B;QAC3B,UAAI,IAAI,0CAAE,WAAW,EAAE;YACrB,aAAO,IAAI,0CAAE,WAAW,CAAC;SAC1B;QAED,wFAAwF;QACxF,gBAAI,IAAI,0CAAE,YAAY,0CAAE,KAAK,CAAC,CAAC,GAAG;YAChC,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAgB,CAAC;SAClD;QAED,kCAAkC;QAClC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,0BAAU,GAAjB,UAAkB,OAAiB;QACjC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,IAAI,CAAC,QAAQ,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;SACzB;QACD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,0BAAU,GAAjB;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,sBAAM,GAAb,UAAc,cAA+B;QAC3C,IAAI,CAAC,cAAc,EAAE;YACnB,OAAO,IAAI,CAAC;SACb;QAED,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;YACxC,IAAM,YAAY,GAAI,cAAqC,CAAC,IAAI,CAAC,CAAC;YAClE,OAAO,YAAY,YAAY,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;SAC5D;QAED,IAAI,cAAc,YAAY,KAAK,EAAE;YACnC,IAAI,CAAC,KAAK,yBAAQ,IAAI,CAAC,KAAK,GAAK,cAAc,CAAC,KAAK,CAAE,CAAC;YACxD,IAAI,CAAC,MAAM,yBAAQ,IAAI,CAAC,MAAM,GAAK,cAAc,CAAC,MAAM,CAAE,CAAC;YAC3D,IAAI,CAAC,SAAS,yBAAQ,IAAI,CAAC,SAAS,GAAK,cAAc,CAAC,SAAS,CAAE,CAAC;YACpE,IAAI,cAAc,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;gBACpE,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;aACnC;YACD,IAAI,cAAc,CAAC,MAAM,EAAE;gBACzB,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;aACrC;YACD,IAAI,cAAc,CAAC,YAAY,EAAE;gBAC/B,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC;aACjD;SACF;aAAM,IAAI,aAAa,CAAC,cAAc,CAAC,EAAE;YACxC,6CAA6C;YAC7C,cAAc,GAAG,cAA8B,CAAC;YAChD,IAAI,CAAC,KAAK,yBAAQ,IAAI,CAAC,KAAK,GAAK,cAAc,CAAC,IAAI,CAAE,CAAC;YACvD,IAAI,CAAC,MAAM,yBAAQ,IAAI,CAAC,MAAM,GAAK,cAAc,CAAC,KAAK,CAAE,CAAC;YAC1D,IAAI,CAAC,SAAS,yBAAQ,IAAI,CAAC,SAAS,GAAK,cAAc,CAAC,QAAQ,CAAE,CAAC;YACnE,IAAI,cAAc,CAAC,IAAI,EAAE;gBACvB,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC;aAClC;YACD,IAAI,cAAc,CAAC,KAAK,EAAE;gBACxB,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC;aACpC;YACD,IAAI,cAAc,CAAC,WAAW,EAAE;gBAC9B,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,WAAW,CAAC;aAChD;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,qBAAK,GAAZ;QACE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,6BAAa,GAApB,UAAqB,UAAsB,EAAE,cAAuB;QAClE,IAAM,gBAAgB,cACpB,SAAS,EAAE,sBAAsB,EAAE,IAChC,UAAU,CACd,CAAC;QAEF,IAAI,CAAC,YAAY;YACf,cAAc,KAAK,SAAS,IAAI,cAAc,IAAI,CAAC;gBACjD,CAAC,CAAC,SAAI,IAAI,CAAC,YAAY,GAAE,gBAAgB,GAAE,KAAK,CAAC,CAAC,cAAc,CAAC;gBACjE,CAAC,UAAK,IAAI,CAAC,YAAY,GAAE,gBAAgB,EAAC,CAAC;QAC/C,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,gCAAgB,GAAvB;QACE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;OAOG;IACI,4BAAY,GAAnB,UAAoB,KAAY,EAAE,IAAgB;;QAChD,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE;YAClD,KAAK,CAAC,KAAK,yBAAQ,IAAI,CAAC,MAAM,GAAK,KAAK,CAAC,KAAK,CAAE,CAAC;SAClD;QACD,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;YAChD,KAAK,CAAC,IAAI,yBAAQ,IAAI,CAAC,KAAK,GAAK,KAAK,CAAC,IAAI,CAAE,CAAC;SAC/C;QACD,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;YAChD,KAAK,CAAC,IAAI,yBAAQ,IAAI,CAAC,KAAK,GAAK,KAAK,CAAC,IAAI,CAAE,CAAC;SAC/C;QACD,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE;YACxD,KAAK,CAAC,QAAQ,yBAAQ,IAAI,CAAC,SAAS,GAAK,KAAK,CAAC,QAAQ,CAAE,CAAC;SAC3D;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;SAC3B;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC;SAC3C;QACD,iFAAiF;QACjF,kFAAkF;QAClF,gDAAgD;QAChD,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,KAAK,CAAC,QAAQ,cAAK,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,IAAK,KAAK,CAAC,QAAQ,CAAE,CAAC;YAC5E,IAAM,eAAe,SAAG,IAAI,CAAC,KAAK,CAAC,WAAW,0CAAE,IAAI,CAAC;YACrD,IAAI,eAAe,EAAE;gBACnB,KAAK,CAAC,IAAI,cAAK,WAAW,EAAE,eAAe,IAAK,KAAK,CAAC,IAAI,CAAE,CAAC;aAC9D;SACF;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE9B,KAAK,CAAC,WAAW,YAAO,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC,EAAK,IAAI,CAAC,YAAY,CAAC,CAAC;QACzE,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;QAEjF,OAAO,IAAI,CAAC,sBAAsB,UAAK,wBAAwB,EAAE,EAAK,IAAI,CAAC,gBAAgB,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC;IAC7G,CAAC;IAED;;OAEG;IACO,sCAAsB,GAAhC,UACE,UAA4B,EAC5B,KAAmB,EACnB,IAAgB,EAChB,KAAiB;QAJnB,iBAuBC;QAnBC,sBAAA,EAAA,SAAiB;QAEjB,OAAO,IAAI,WAAW,CAAe,UAAC,OAAO,EAAE,MAAM;YACnD,IAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;gBACrD,OAAO,CAAC,KAAK,CAAC,CAAC;aAChB;iBAAM;gBACL,IAAM,MAAM,GAAG,SAAS,cAAM,KAAK,GAAI,IAAI,CAAiB,CAAC;gBAC7D,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE;oBACrB,MAAoC;yBAClC,IAAI,CAAC,UAAA,KAAK,IAAI,OAAA,KAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAA7E,CAA6E,CAAC;yBAC5F,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;iBACvB;qBAAM;oBACL,KAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC;yBAC7D,IAAI,CAAC,OAAO,CAAC;yBACb,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;iBACvB;aACF;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,qCAAqB,GAA/B;QAAA,iBAWC;QAVC,6FAA6F;QAC7F,kGAAkG;QAClG,uBAAuB;QACvB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,UAAA,QAAQ;gBACnC,QAAQ,CAAC,KAAI,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;SAClC;IACH,CAAC;IAED;;;OAGG;IACK,iCAAiB,GAAzB,UAA0B,KAAY;QACpC,wEAAwE;QACxE,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW;YACnC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;gBAChC,CAAC,CAAC,KAAK,CAAC,WAAW;gBACnB,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;YACvB,CAAC,CAAC,EAAE,CAAC;QAEP,8DAA8D;QAC9D,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACjE;QAED,wDAAwD;QACxD,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE;YAClD,OAAO,KAAK,CAAC,WAAW,CAAC;SAC1B;IACH,CAAC;IACH,YAAC;AAAD,CAAC,AAhcD,IAgcC;;AAED;;GAEG;AACH,SAAS,wBAAwB;IAC/B,oGAAoG;IACpG,IAAM,MAAM,GAAG,eAAe,EAAO,CAAC;IACtC,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC;IAC5C,MAAM,CAAC,UAAU,CAAC,qBAAqB,GAAG,MAAM,CAAC,UAAU,CAAC,qBAAqB,IAAI,EAAE,CAAC;IACxF,OAAO,MAAM,CAAC,UAAU,CAAC,qBAAqB,CAAC;IAC/C,kGAAkG;AACpG,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,uBAAuB,CAAC,QAAwB;IAC9D,wBAAwB,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5C,CAAC", "sourcesContent": ["/* eslint-disable max-lines */\nimport {\n  Breadcrumb,\n  CaptureContext,\n  Context,\n  Contexts,\n  Event,\n  EventHint,\n  EventProcessor,\n  Extra,\n  Extras,\n  Primitive,\n  Scope as ScopeInterface,\n  ScopeContext,\n  Severity,\n  Span,\n  Transaction,\n  User,\n} from '@sentry/types';\nimport { dateTimestampInSeconds, getGlobalObject, isPlainObject, isThenable, SyncPromise } from '@sentry/utils';\n\nimport { Session } from './session';\n\n/**\n * Holds additional event information. {@link Scope.applyToEvent} will be\n * called by the client before an event will be sent.\n */\nexport class Scope implements ScopeInterface {\n  /** Flag if notifiying is happening. */\n  protected _notifyingListeners: boolean = false;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void> = [];\n\n  /** Callback list that will be called after {@link applyToEvent}. */\n  protected _eventProcessors: EventProcessor[] = [];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[] = [];\n\n  /** User */\n  protected _user: User = {};\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive } = {};\n\n  /** Extra */\n  protected _extra: Extras = {};\n\n  /** Contexts */\n  protected _contexts: Contexts = {};\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  protected _level?: Severity;\n\n  /** Transaction Name */\n  protected _transactionName?: string;\n\n  /** Span */\n  protected _span?: Span;\n\n  /** Session */\n  protected _session?: Session;\n\n  /**\n   * Inherit values from the parent scope.\n   * @param scope to clone.\n   */\n  public static clone(scope?: Scope): Scope {\n    const newScope = new Scope();\n    if (scope) {\n      newScope._breadcrumbs = [...scope._breadcrumbs];\n      newScope._tags = { ...scope._tags };\n      newScope._extra = { ...scope._extra };\n      newScope._contexts = { ...scope._contexts };\n      newScope._user = scope._user;\n      newScope._level = scope._level;\n      newScope._span = scope._span;\n      newScope._session = scope._session;\n      newScope._transactionName = scope._transactionName;\n      newScope._fingerprint = scope._fingerprint;\n      newScope._eventProcessors = [...scope._eventProcessors];\n    }\n    return newScope;\n  }\n\n  /**\n   * Add internal on change listener. Used for sub SDKs that need to store the scope.\n   * @hidden\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): this {\n    this._user = user || {};\n    if (this._session) {\n      this._session.update({ user });\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setLevel(level: Severity): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Can be removed in major version.\n   * @deprecated in favor of {@link this.setTransactionName}\n   */\n  public setTransaction(name?: string): this {\n    return this.setTransactionName(name);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts = { ...this._contexts, [key]: context };\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSpan(span?: Span): this {\n    this._span = span;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSpan(): Span | undefined {\n    return this._span;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransaction(): Transaction | undefined {\n    // often, this span will be a transaction, but it's not guaranteed to be\n    const span = this.getSpan() as undefined | (Span & { spanRecorder: { spans: Span[] } });\n\n    // try it the new way first\n    if (span?.transaction) {\n      return span?.transaction;\n    }\n\n    // fallback to the old way (known bug: this only finds transactions with sampled = true)\n    if (span?.spanRecorder?.spans[0]) {\n      return span.spanRecorder.spans[0] as Transaction;\n    }\n\n    // neither way found a transaction\n    return undefined;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    if (typeof captureContext === 'function') {\n      const updatedScope = (captureContext as <T>(scope: T) => T)(this);\n      return updatedScope instanceof Scope ? updatedScope : this;\n    }\n\n    if (captureContext instanceof Scope) {\n      this._tags = { ...this._tags, ...captureContext._tags };\n      this._extra = { ...this._extra, ...captureContext._extra };\n      this._contexts = { ...this._contexts, ...captureContext._contexts };\n      if (captureContext._user && Object.keys(captureContext._user).length) {\n        this._user = captureContext._user;\n      }\n      if (captureContext._level) {\n        this._level = captureContext._level;\n      }\n      if (captureContext._fingerprint) {\n        this._fingerprint = captureContext._fingerprint;\n      }\n    } else if (isPlainObject(captureContext)) {\n      // eslint-disable-next-line no-param-reassign\n      captureContext = captureContext as ScopeContext;\n      this._tags = { ...this._tags, ...captureContext.tags };\n      this._extra = { ...this._extra, ...captureContext.extra };\n      this._contexts = { ...this._contexts, ...captureContext.contexts };\n      if (captureContext.user) {\n        this._user = captureContext.user;\n      }\n      if (captureContext.level) {\n        this._level = captureContext.level;\n      }\n      if (captureContext.fingerprint) {\n        this._fingerprint = captureContext.fingerprint;\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clear(): this {\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._span = undefined;\n    this._session = undefined;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const mergedBreadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n    };\n\n    this._breadcrumbs =\n      maxBreadcrumbs !== undefined && maxBreadcrumbs >= 0\n        ? [...this._breadcrumbs, mergedBreadcrumb].slice(-maxBreadcrumbs)\n        : [...this._breadcrumbs, mergedBreadcrumb];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Applies the current context and fingerprint to the event.\n   * Note that breadcrumbs will be added by the client.\n   * Also if the event has already breadcrumbs on it, we do not merge them.\n   * @param event Event\n   * @param hint May contain additional informartion about the original exception.\n   * @hidden\n   */\n  public applyToEvent(event: Event, hint?: EventHint): PromiseLike<Event | null> {\n    if (this._extra && Object.keys(this._extra).length) {\n      event.extra = { ...this._extra, ...event.extra };\n    }\n    if (this._tags && Object.keys(this._tags).length) {\n      event.tags = { ...this._tags, ...event.tags };\n    }\n    if (this._user && Object.keys(this._user).length) {\n      event.user = { ...this._user, ...event.user };\n    }\n    if (this._contexts && Object.keys(this._contexts).length) {\n      event.contexts = { ...this._contexts, ...event.contexts };\n    }\n    if (this._level) {\n      event.level = this._level;\n    }\n    if (this._transactionName) {\n      event.transaction = this._transactionName;\n    }\n    // We want to set the trace context for normal events only if there isn't already\n    // a trace context on the event. There is a product feature in place where we link\n    // errors with transaction and it relys on that.\n    if (this._span) {\n      event.contexts = { trace: this._span.getTraceContext(), ...event.contexts };\n      const transactionName = this._span.transaction?.name;\n      if (transactionName) {\n        event.tags = { transaction: transactionName, ...event.tags };\n      }\n    }\n\n    this._applyFingerprint(event);\n\n    event.breadcrumbs = [...(event.breadcrumbs || []), ...this._breadcrumbs];\n    event.breadcrumbs = event.breadcrumbs.length > 0 ? event.breadcrumbs : undefined;\n\n    return this._notifyEventProcessors([...getGlobalEventProcessors(), ...this._eventProcessors], event, hint);\n  }\n\n  /**\n   * This will be called after {@link applyToEvent} is finished.\n   */\n  protected _notifyEventProcessors(\n    processors: EventProcessor[],\n    event: Event | null,\n    hint?: EventHint,\n    index: number = 0,\n  ): PromiseLike<Event | null> {\n    return new SyncPromise<Event | null>((resolve, reject) => {\n      const processor = processors[index];\n      if (event === null || typeof processor !== 'function') {\n        resolve(event);\n      } else {\n        const result = processor({ ...event }, hint) as Event | null;\n        if (isThenable(result)) {\n          (result as PromiseLike<Event | null>)\n            .then(final => this._notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n            .then(null, reject);\n        } else {\n          this._notifyEventProcessors(processors, result, hint, index + 1)\n            .then(resolve)\n            .then(null, reject);\n        }\n      }\n    });\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n\n  /**\n   * Applies fingerprint from the scope to the event if there's one,\n   * uses message if there's one instead or get rid of empty fingerprint\n   */\n  private _applyFingerprint(event: Event): void {\n    // Make sure it's an array first and we actually have something in place\n    event.fingerprint = event.fingerprint\n      ? Array.isArray(event.fingerprint)\n        ? event.fingerprint\n        : [event.fingerprint]\n      : [];\n\n    // If we have something on the scope, then merge it with event\n    if (this._fingerprint) {\n      event.fingerprint = event.fingerprint.concat(this._fingerprint);\n    }\n\n    // If we have no data at all, remove empty array default\n    if (event.fingerprint && !event.fingerprint.length) {\n      delete event.fingerprint;\n    }\n  }\n}\n\n/**\n * Retruns the global event processors.\n */\nfunction getGlobalEventProcessors(): EventProcessor[] {\n  /* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access  */\n  const global = getGlobalObject<any>();\n  global.__SENTRY__ = global.__SENTRY__ || {};\n  global.__SENTRY__.globalEventProcessors = global.__SENTRY__.globalEventProcessors || [];\n  return global.__SENTRY__.globalEventProcessors;\n  /* eslint-enable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access */\n}\n\n/**\n * Add a EventProcessor to be kept globally.\n * @param callback EventProcessor to add\n */\nexport function addGlobalEventProcessor(callback: EventProcessor): void {\n  getGlobalEventProcessors().push(callback);\n}\n"]}