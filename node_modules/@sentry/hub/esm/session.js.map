{"version": 3, "file": "session.js", "sourceRoot": "", "sources": ["../src/session.ts"], "names": [], "mappings": "AAAA,OAAO,EAA+C,aAAa,EAAE,MAAM,eAAe,CAAC;AAC3F,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEzD;;GAEG;AACH;IAaE,iBAAY,OAAoD;QAXzD,WAAM,GAAW,CAAC,CAAC;QAEnB,QAAG,GAAW,KAAK,EAAE,CAAC;QAEtB,cAAS,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/B,YAAO,GAAW,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,aAAQ,GAAW,CAAC,CAAC;QACrB,WAAM,GAAkB,aAAa,CAAC,EAAE,CAAC;QAK9C,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SACtB;IACH,CAAC;IAED,YAAY;IACZ,sCAAsC;IACtC,wBAAM,GAAN,UAAO,OAA4B;QAA5B,wBAAA,EAAA,YAA4B;QACjC,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC3B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;aAC1C;YAED,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;gBAChB,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC3E;SACF;QAED,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QAEjD,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,uCAAuC;YACvC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;SAC9D;QACD,IAAI,OAAO,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,GAAG,GAAG,KAAG,OAAO,CAAC,GAAK,CAAC;SAC7B;QACD,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;YACvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAChC;QACD,IAAI,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACxC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SAClC;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAChC;QACD,IAAI,OAAO,CAAC,WAAW,EAAE;YACvB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;SACxC;QACD,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;SACpC;QACD,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;SACpC;QACD,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;YACtC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SAC9B;QACD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SAC9B;IACH,CAAC;IAED,YAAY;IACZ,uBAAK,GAAL,UAAM,MAAiD;QACrD,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;SACzB;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;SAC/C;aAAM;YACL,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;IACH,CAAC;IAED,YAAY;IACZ,wBAAM,GAAN;QAgBE,OAAO,iBAAiB,CAAC;YACvB,GAAG,EAAE,KAAG,IAAI,CAAC,GAAK;YAClB,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE;YAC7C,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE;YACjD,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,GAAG,EAAE,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAG,IAAI,CAAC,GAAK,CAAC,CAAC,CAAC,SAAS;YAC7F,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,iBAAiB,CAAC;gBACvB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,UAAU,EAAE,IAAI,CAAC,SAAS;gBAC1B,UAAU,EAAE,IAAI,CAAC,SAAS;aAC3B,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IACH,cAAC;AAAD,CAAC,AAlHD,IAkHC", "sourcesContent": ["import { Session as SessionInterface, SessionContext, SessionStatus } from '@sentry/types';\nimport { dropUndefinedKeys, uuid4 } from '@sentry/utils';\n\n/**\n * @inheritdoc\n */\nexport class Session implements SessionInterface {\n  public userAgent?: string;\n  public errors: number = 0;\n  public release?: string;\n  public sid: string = uuid4();\n  public did?: string;\n  public timestamp: number = Date.now();\n  public started: number = Date.now();\n  public duration: number = 0;\n  public status: SessionStatus = SessionStatus.Ok;\n  public environment?: string;\n  public ipAddress?: string;\n\n  constructor(context?: Omit<SessionContext, 'started' | 'status'>) {\n    if (context) {\n      this.update(context);\n    }\n  }\n\n  /** JSDoc */\n  // eslint-disable-next-line complexity\n  update(context: SessionContext = {}): void {\n    if (context.user) {\n      if (context.user.ip_address) {\n        this.ipAddress = context.user.ip_address;\n      }\n\n      if (!context.did) {\n        this.did = context.user.id || context.user.email || context.user.username;\n      }\n    }\n\n    this.timestamp = context.timestamp || Date.now();\n\n    if (context.sid) {\n      // Good enough uuid validation. — Kamil\n      this.sid = context.sid.length === 32 ? context.sid : uuid4();\n    }\n    if (context.did) {\n      this.did = `${context.did}`;\n    }\n    if (typeof context.started === 'number') {\n      this.started = context.started;\n    }\n    if (typeof context.duration === 'number') {\n      this.duration = context.duration;\n    } else {\n      this.duration = this.timestamp - this.started;\n    }\n    if (context.release) {\n      this.release = context.release;\n    }\n    if (context.environment) {\n      this.environment = context.environment;\n    }\n    if (context.ipAddress) {\n      this.ipAddress = context.ipAddress;\n    }\n    if (context.userAgent) {\n      this.userAgent = context.userAgent;\n    }\n    if (typeof context.errors === 'number') {\n      this.errors = context.errors;\n    }\n    if (context.status) {\n      this.status = context.status;\n    }\n  }\n\n  /** JSDoc */\n  close(status?: Exclude<SessionStatus, SessionStatus.Ok>): void {\n    if (status) {\n      this.update({ status });\n    } else if (this.status === SessionStatus.Ok) {\n      this.update({ status: SessionStatus.Exited });\n    } else {\n      this.update();\n    }\n  }\n\n  /** JSDoc */\n  toJSON(): {\n    init: boolean;\n    sid: string;\n    did?: string;\n    timestamp: string;\n    started: string;\n    duration: number;\n    status: SessionStatus;\n    errors: number;\n    attrs?: {\n      release?: string;\n      environment?: string;\n      user_agent?: string;\n      ip_address?: string;\n    };\n  } {\n    return dropUndefinedKeys({\n      sid: `${this.sid}`,\n      init: true,\n      started: new Date(this.started).toISOString(),\n      timestamp: new Date(this.timestamp).toISOString(),\n      status: this.status,\n      errors: this.errors,\n      did: typeof this.did === 'number' || typeof this.did === 'string' ? `${this.did}` : undefined,\n      duration: this.duration,\n      attrs: dropUndefinedKeys({\n        release: this.release,\n        environment: this.environment,\n        ip_address: this.ipAddress,\n        user_agent: this.userAgent,\n      }),\n    });\n  }\n}\n"]}