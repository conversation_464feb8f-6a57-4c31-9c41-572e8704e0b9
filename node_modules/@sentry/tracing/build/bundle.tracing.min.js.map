{"version": 3, "file": "bundle.tracing.min.js", "sources": ["../../types/src/loglevel.ts", "../../types/src/session.ts", "../../types/src/severity.ts", "../../types/src/status.ts", "../../types/src/transaction.ts", "../../utils/src/is.ts", "../../utils/src/browser.ts", "../../utils/src/polyfill.ts", "../../utils/src/error.ts", "../../utils/src/dsn.ts", "../../utils/src/memo.ts", "../../utils/src/stacktrace.ts", "../../utils/src/string.ts", "../../utils/src/object.ts", "../../utils/src/node.ts", "../../utils/src/misc.ts", "../../utils/src/logger.ts", "../../utils/src/supports.ts", "../../utils/src/instrument.ts", "../../utils/src/syncpromise.ts", "../../utils/src/promisebuffer.ts", "../../utils/src/time.ts", "../../hub/src/scope.ts", "../../hub/src/session.ts", "../../hub/src/hub.ts", "../../minimal/src/index.ts", "../../core/src/api.ts", "../../core/src/integration.ts", "../../core/src/baseclient.ts", "../../core/src/integrations/functiontostring.ts", "../../core/src/transports/noop.ts", "../../core/src/basebackend.ts", "../../core/src/request.ts", "../../core/src/integrations/inboundfilters.ts", "../../browser/src/tracekit.ts", "../../browser/src/parsers.ts", "../../browser/src/eventbuilder.ts", "../../browser/src/transports/base.ts", "../../browser/src/transports/fetch.ts", "../../browser/src/transports/xhr.ts", "../../browser/src/backend.ts", "../../browser/src/helpers.ts", "../../browser/src/integrations/globalhandlers.ts", "../../browser/src/integrations/trycatch.ts", "../../browser/src/integrations/breadcrumbs.ts", "../../browser/src/integrations/linkederrors.ts", "../../browser/src/integrations/useragent.ts", "../../browser/src/version.ts", "../../browser/src/client.ts", "../../browser/src/sdk.ts", "../../browser/src/index.ts", "../src/spanstatus.ts", "../src/utils.ts", "../src/errors.ts", "../src/span.ts", "../src/transaction.ts", "../src/idletransaction.ts", "../src/hubextensions.ts", "../src/browser/backgroundtab.ts", "../src/browser/web-vitals/lib/bindReporter.ts", "../src/browser/web-vitals/lib/getFirstHidden.ts", "../src/browser/web-vitals/lib/whenInput.ts", "../src/browser/web-vitals/lib/initMetric.ts", "../src/browser/web-vitals/lib/generateUniqueID.ts", "../src/browser/web-vitals/lib/observe.ts", "../src/browser/web-vitals/lib/onHidden.ts", "../src/browser/web-vitals/getLCP.ts", "../src/browser/web-vitals/getTTFB.ts", "../src/browser/metrics.ts", "../src/browser/web-vitals/getCLS.ts", "../src/browser/web-vitals/getFID.ts", "../src/browser/request.ts", "../src/browser/router.ts", "../src/browser/browsertracing.ts", "../src/index.bundle.ts", "../../core/src/sdk.ts"], "sourcesContent": ["/** Console logging verbosity for the SDK. */\nexport enum LogLevel {\n  /** No logs will be generated. */\n  None = 0,\n  /** Only SDK internal errors will be logged. */\n  Error = 1,\n  /** Information useful for debugging the SDK will be logged. */\n  Debug = 2,\n  /** All SDK actions will be logged. */\n  Verbose = 3,\n}\n", "import { User } from './user';\n\n/**\n * @inheritdoc\n */\nexport interface Session extends SessionContext {\n  /** JSDoc */\n  update(context?: SessionContext): void;\n\n  /** JSDoc */\n  close(status?: SessionStatus): void;\n\n  /** JSDoc */\n  toJSON(): {\n    init: boolean;\n    sid: string;\n    did?: string;\n    timestamp: string;\n    started: string;\n    duration: number;\n    status: SessionStatus;\n    errors: number;\n    attrs?: {\n      release?: string;\n      environment?: string;\n      user_agent?: string;\n      ip_address?: string;\n    };\n  };\n}\n\n/**\n * Session Context\n */\nexport interface SessionContext {\n  sid?: string;\n  did?: string;\n  timestamp?: number;\n  started?: number;\n  duration?: number;\n  status?: SessionStatus;\n  release?: string;\n  environment?: string;\n  userAgent?: string;\n  ipAddress?: string;\n  errors?: number;\n  user?: User | null;\n}\n\n/**\n * Session Status\n */\nexport enum SessionStatus {\n  /** JSDoc */\n  Ok = 'ok',\n  /** JSDoc */\n  Exited = 'exited',\n  /** JSDoc */\n  Crashed = 'crashed',\n  /** JSDoc */\n  Abnormal = 'abnormal',\n}\n", "/** JSDoc */\n// eslint-disable-next-line import/export\nexport enum Severity {\n  /** JSDoc */\n  Fatal = 'fatal',\n  /** JSDoc */\n  Error = 'error',\n  /** JSDoc */\n  Warning = 'warning',\n  /** JSDoc */\n  Log = 'log',\n  /** JSDoc */\n  Info = 'info',\n  /** JSDoc */\n  Debug = 'debug',\n  /** JSDoc */\n  Critical = 'critical',\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace, import/export\nexport namespace Severity {\n  /**\n   * Converts a string-based level into a {@link Severity}.\n   *\n   * @param level string representation of Severity\n   * @returns Severity\n   */\n  export function fromString(level: string): Severity {\n    switch (level) {\n      case 'debug':\n        return Severity.Debug;\n      case 'info':\n        return Severity.Info;\n      case 'warn':\n      case 'warning':\n        return Severity.Warning;\n      case 'error':\n        return Severity.Error;\n      case 'fatal':\n        return Severity.Fatal;\n      case 'critical':\n        return Severity.Critical;\n      case 'log':\n      default:\n        return Severity.Log;\n    }\n  }\n}\n", "/** The status of an event. */\n// eslint-disable-next-line import/export\nexport enum Status {\n  /** The status could not be determined. */\n  Unknown = 'unknown',\n  /** The event was skipped due to configuration or callbacks. */\n  Skipped = 'skipped',\n  /** The event was sent to Sentry successfully. */\n  Success = 'success',\n  /** The client is currently rate limited and will try again later. */\n  RateLimit = 'rate_limit',\n  /** The event could not be processed. */\n  Invalid = 'invalid',\n  /** A server-side error ocurred during submission. */\n  Failed = 'failed',\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace, import/export\nexport namespace Status {\n  /**\n   * Converts a HTTP status code into a {@link Status}.\n   *\n   * @param code The HTTP response status code.\n   * @returns The send status or {@link Status.Unknown}.\n   */\n  export function fromHttpCode(code: number): Status {\n    if (code >= 200 && code < 300) {\n      return Status.Success;\n    }\n\n    if (code === 429) {\n      return Status.RateLimit;\n    }\n\n    if (code >= 400 && code < 500) {\n      return Status.Invalid;\n    }\n\n    if (code >= 500) {\n      return Status.Failed;\n    }\n\n    return Status.Unknown;\n  }\n}\n", "import { ExtractedNodeRequestData, Primitive, WorkerLocation } from './misc';\nimport { Span, SpanContext } from './span';\n\n/**\n * Interface holding Transaction-specific properties\n */\nexport interface TransactionContext extends SpanContext {\n  /**\n   * Human-readable identifier for the transaction\n   */\n  name: string;\n\n  /**\n   * If true, sets the end timestamp of the transaction to the highest timestamp of child spans, trimming\n   * the duration of the transaction. This is useful to discard extra time in the transaction that is not\n   * accounted for in child spans, like what happens in the idle transaction Tracing integration, where we finish the\n   * transaction after a given \"idle time\" and we don't want this \"idle time\" to be part of the transaction.\n   */\n  trimEnd?: boolean;\n\n  /**\n   * If this transaction has a parent, the parent's sampling decision\n   */\n  parentSampled?: boolean;\n}\n\n/**\n * Data pulled from a `sentry-trace` header\n */\nexport type TraceparentData = Pick<TransactionContext, 'traceId' | 'parentSpanId' | 'parentSampled'>;\n\n/**\n * Transaction \"Class\", inherits Span only has `setName`\n */\nexport interface Transaction extends TransactionContext, Span {\n  /**\n   * @inheritDoc\n   */\n  spanId: string;\n\n  /**\n   * @inheritDoc\n   */\n  traceId: string;\n\n  /**\n   * @inheritDoc\n   */\n  startTimestamp: number;\n\n  /**\n   * @inheritDoc\n   */\n  tags: { [key: string]: Primitive };\n\n  /**\n   * @inheritDoc\n   */\n  data: { [key: string]: any };\n\n  /**\n   * Set the name of the transaction\n   */\n  setName(name: string): void;\n}\n\n/**\n * Context data passed by the user when starting a transaction, to be used by the tracesSampler method.\n */\nexport interface CustomSamplingContext {\n  [key: string]: any;\n}\n\n/**\n * Data passed to the `tracesSampler` function, which forms the basis for whatever decisions it might make.\n *\n * Adds default data to data provided by the user. See {@link Hub.startTransaction}\n */\nexport interface SamplingContext extends CustomSamplingContext {\n  /**\n   * Context data with which transaction being sampled was created\n   */\n  transactionContext: TransactionContext;\n\n  /**\n   * Sampling decision from the parent transaction, if any.\n   */\n  parentSampled?: boolean;\n\n  /**\n   * Object representing the URL of the current page or worker script. Passed by default in a browser or service worker\n   * context.\n   */\n  location?: WorkerLocation;\n\n  /**\n   * Object representing the incoming request to a node server. Passed by default when using the TracingHandler.\n   */\n  request?: ExtractedNodeRequestData;\n}\n\nexport type Measurements = Record<string, { value: number }>;\n\nexport enum TransactionSamplingMethod {\n  Explicit = 'explicitly_set',\n  Sampler = 'client_sampler',\n  Rate = 'client_rate',\n  Inheritance = 'inheritance',\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n\nimport { Primitive } from '@sentry/types';\n/**\n * Checks whether given value's type is one of a few Error or Error-like\n * {@link isError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isError(wat: any): boolean {\n  switch (Object.prototype.toString.call(wat)) {\n    case '[object Error]':\n      return true;\n    case '[object Exception]':\n      return true;\n    case '[object DOMException]':\n      return true;\n    default:\n      return isInstanceOf(wat, Error);\n  }\n}\n\n/**\n * Checks whether given value's type is ErrorEvent\n * {@link isErrorEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isErrorEvent(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object ErrorEvent]';\n}\n\n/**\n * Checks whether given value's type is DOMError\n * {@link isDOMError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMError(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object DOMError]';\n}\n\n/**\n * Checks whether given value's type is DOMException\n * {@link isDOMException}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMException(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object DOMException]';\n}\n\n/**\n * Checks whether given value's type is a string\n * {@link isString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isString(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object String]';\n}\n\n/**\n * Checks whether given value's is a primitive (undefined, null, number, boolean, string, bigint, symbol)\n * {@link isPrimitive}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPrimitive(wat: any): wat is Primitive {\n  return wat === null || (typeof wat !== 'object' && typeof wat !== 'function');\n}\n\n/**\n * Checks whether given value's type is an object literal\n * {@link isPlainObject}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPlainObject(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object Object]';\n}\n\n/**\n * Checks whether given value's type is an Event instance\n * {@link isEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isEvent(wat: any): boolean {\n  return typeof Event !== 'undefined' && isInstanceOf(wat, Event);\n}\n\n/**\n * Checks whether given value's type is an Element instance\n * {@link isElement}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isElement(wat: any): boolean {\n  return typeof Element !== 'undefined' && isInstanceOf(wat, Element);\n}\n\n/**\n * Checks whether given value's type is an regexp\n * {@link isRegExp}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isRegExp(wat: any): boolean {\n  return Object.prototype.toString.call(wat) === '[object RegExp]';\n}\n\n/**\n * Checks whether given value has a then function.\n * @param wat A value to be checked.\n */\nexport function isThenable(wat: any): boolean {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return Boolean(wat && wat.then && typeof wat.then === 'function');\n}\n\n/**\n * Checks whether given value's type is a SyntheticEvent\n * {@link isSyntheticEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isSyntheticEvent(wat: any): boolean {\n  return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;\n}\n/**\n * Checks whether given value's type is an instance of provided constructor.\n * {@link isInstanceOf}.\n *\n * @param wat A value to be checked.\n * @param base A constructor to be used in a check.\n * @returns A boolean representing the result.\n */\nexport function isInstanceOf(wat: any, base: any): boolean {\n  try {\n    return wat instanceof base;\n  } catch (_e) {\n    return false;\n  }\n}\n", "import { isString } from './is';\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nexport function htmlTreeAsString(elem: unknown): string {\n  type SimpleNode = {\n    parentNode: SimpleNode;\n  } | null;\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem as SimpleNode;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const MAX_OUTPUT_LEN = 80;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n\n    // eslint-disable-next-line no-plusplus\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds MAX_OUTPUT_LEN\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= MAX_OUTPUT_LEN)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el: unknown): string {\n  const elem = el as {\n    tagName?: string;\n    id?: string;\n    className?: string;\n    getAttribute(key: string): string;\n  };\n\n  const out = [];\n  let className;\n  let classes;\n  let key;\n  let attr;\n  let i;\n\n  if (!elem || !elem.tagName) {\n    return '';\n  }\n\n  out.push(elem.tagName.toLowerCase());\n  if (elem.id) {\n    out.push(`#${elem.id}`);\n  }\n\n  // eslint-disable-next-line prefer-const\n  className = elem.className;\n  if (className && isString(className)) {\n    classes = className.split(/\\s+/);\n    for (i = 0; i < classes.length; i++) {\n      out.push(`.${classes[i]}`);\n    }\n  }\n  const allowedAttrs = ['type', 'name', 'title', 'alt'];\n  for (i = 0; i < allowedAttrs.length; i++) {\n    key = allowedAttrs[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.push(`[${key}=\"${attr}\"]`);\n    }\n  }\n  return out.join('');\n}\n", "export const setPrototypeOf =\n  Object.setPrototypeOf || ({ __proto__: [] } instanceof Array ? setProtoOf : mixinProperties);\n\n/**\n * setPrototypeOf polyfill using __proto__\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction setProtoOf<TTarget extends object, TProto>(obj: TTarget, proto: TProto): TTarget & TProto {\n  // @ts-ignore __proto__ does not exist on obj\n  obj.__proto__ = proto;\n  return obj as TTarget & TProto;\n}\n\n/**\n * setPrototypeOf polyfill using mixin\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction mixinProperties<TTarget extends object, TProto>(obj: TTarget, proto: TProto): TTarget & TProto {\n  for (const prop in proto) {\n    // eslint-disable-next-line no-prototype-builtins\n    if (!obj.hasOwnProperty(prop)) {\n      // @ts-ignore typescript complains about indexing so we remove\n      obj[prop] = proto[prop];\n    }\n  }\n\n  return obj as TTarget & TProto;\n}\n", "import { setPrototypeOf } from './polyfill';\n\n/** An error emitted by Sentry SDKs and related utilities. */\nexport class SentryError extends Error {\n  /** Display name of this error instance. */\n  public name: string;\n\n  public constructor(public message: string) {\n    super(message);\n\n    this.name = new.target.prototype.constructor.name;\n    setPrototypeOf(this, new.target.prototype);\n  }\n}\n", "import { DsnC<PERSON><PERSON>, DsnLike, DsnProtocol } from '@sentry/types';\n\nimport { SentryError } from './error';\n\n/** Regular expression used to parse a Dsn. */\nconst DSN_REGEX = /^(?:(\\w+):)\\/\\/(?:(\\w+)(?::(\\w+))?@)([\\w.-]+)(?::(\\d+))?\\/(.+)/;\n\n/** Error message */\nconst ERROR_MESSAGE = 'Invalid Dsn';\n\n/** The Sentry Dsn, identifying a Sentry instance and project. */\nexport class Dsn implements DsnComponents {\n  /** Protocol used to connect to Sentry. */\n  public protocol!: DsnProtocol;\n  /** Public authorization key. */\n  public user!: string;\n  /** Private authorization key (deprecated, optional). */\n  public pass!: string;\n  /** Hostname of the Sentry instance. */\n  public host!: string;\n  /** Port of the Sentry instance. */\n  public port!: string;\n  /** Path */\n  public path!: string;\n  /** Project ID */\n  public projectId!: string;\n\n  /** Creates a new Dsn component */\n  public constructor(from: DsnLike) {\n    if (typeof from === 'string') {\n      this._fromString(from);\n    } else {\n      this._fromComponents(from);\n    }\n\n    this._validate();\n  }\n\n  /**\n   * Renders the string representation of this Dsn.\n   *\n   * By default, this will render the public representation without the password\n   * component. To get the deprecated private representation, set `withPassword`\n   * to true.\n   *\n   * @param withPassword When set to true, the password will be included.\n   */\n  public toString(withPassword: boolean = false): string {\n    const { host, path, pass, port, projectId, protocol, user } = this;\n    return (\n      `${protocol}://${user}${withPassword && pass ? `:${pass}` : ''}` +\n      `@${host}${port ? `:${port}` : ''}/${path ? `${path}/` : path}${projectId}`\n    );\n  }\n\n  /** Parses a string into this Dsn. */\n  private _fromString(str: string): void {\n    const match = DSN_REGEX.exec(str);\n\n    if (!match) {\n      throw new SentryError(ERROR_MESSAGE);\n    }\n\n    const [protocol, user, pass = '', host, port = '', lastPath] = match.slice(1);\n    let path = '';\n    let projectId = lastPath;\n\n    const split = projectId.split('/');\n    if (split.length > 1) {\n      path = split.slice(0, -1).join('/');\n      projectId = split.pop() as string;\n    }\n\n    if (projectId) {\n      const projectMatch = projectId.match(/^\\d+/);\n      if (projectMatch) {\n        projectId = projectMatch[0];\n      }\n    }\n\n    this._fromComponents({ host, pass, path, projectId, port, protocol: protocol as DsnProtocol, user });\n  }\n\n  /** Maps Dsn components into this instance. */\n  private _fromComponents(components: DsnComponents): void {\n    this.protocol = components.protocol;\n    this.user = components.user;\n    this.pass = components.pass || '';\n    this.host = components.host;\n    this.port = components.port || '';\n    this.path = components.path || '';\n    this.projectId = components.projectId;\n  }\n\n  /** Validates this Dsn and throws on error. */\n  private _validate(): void {\n    ['protocol', 'user', 'host', 'projectId'].forEach(component => {\n      if (!this[component as keyof DsnComponents]) {\n        throw new SentryError(`${ERROR_MESSAGE}: ${component} missing`);\n      }\n    });\n\n    if (!this.projectId.match(/^\\d+$/)) {\n      throw new SentryError(`${ERROR_MESSAGE}: Invalid projectId ${this.projectId}`);\n    }\n\n    if (this.protocol !== 'http' && this.protocol !== 'https') {\n      throw new SentryError(`${ERROR_MESSAGE}: Invalid protocol ${this.protocol}`);\n    }\n\n    if (this.port && isNaN(parseInt(this.port, 10))) {\n      throw new SentryError(`${ERROR_MESSAGE}: Invalid port ${this.port}`);\n    }\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/**\n * Memo class used for decycle json objects. Uses WeakSet if available otherwise array.\n */\nexport class Memo {\n  /** Determines if WeakSet is available */\n  private readonly _hasWeakSet: boolean;\n  /** Either WeakSet or Array */\n  private readonly _inner: any;\n\n  public constructor() {\n    this._hasWeakSet = typeof WeakSet === 'function';\n    this._inner = this._hasWeakSet ? new WeakSet() : [];\n  }\n\n  /**\n   * Sets obj to remember.\n   * @param obj Object to remember\n   */\n  public memoize(obj: any): boolean {\n    if (this._hasWeakSet) {\n      if (this._inner.has(obj)) {\n        return true;\n      }\n      this._inner.add(obj);\n      return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < this._inner.length; i++) {\n      const value = this._inner[i];\n      if (value === obj) {\n        return true;\n      }\n    }\n    this._inner.push(obj);\n    return false;\n  }\n\n  /**\n   * Removes object from internal storage.\n   * @param obj Object to forget\n   */\n  public unmemoize(obj: any): void {\n    if (this._hasWeakSet) {\n      this._inner.delete(obj);\n    } else {\n      for (let i = 0; i < this._inner.length; i++) {\n        if (this._inner[i] === obj) {\n          this._inner.splice(i, 1);\n          break;\n        }\n      }\n    }\n  }\n}\n", "const defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nexport function getFunctionName(fn: unknown): string {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n", "import { isRegExp, isString } from './is';\n\n/**\n * Truncates given string to the maximum characters count\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string (0 = unlimited)\n * @returns string Encoded\n */\nexport function truncate(str: string, max: number = 0): string {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.substr(0, max)}...`;\n}\n\n/**\n * This is basically just `trim_line` from\n * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string\n * @returns string Encoded\n */\nexport function snipLine(line: string, colno: number): string {\n  let newLine = line;\n  const ll = newLine.length;\n  if (ll <= 150) {\n    return newLine;\n  }\n  if (colno > ll) {\n    // eslint-disable-next-line no-param-reassign\n    colno = ll;\n  }\n\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n\n  let end = Math.min(start + 140, ll);\n  if (end > ll - 5) {\n    end = ll;\n  }\n  if (end === ll) {\n    start = Math.max(end - 140, 0);\n  }\n\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `'{snip} ${newLine}`;\n  }\n  if (end < ll) {\n    newLine += ' {snip}';\n  }\n\n  return newLine;\n}\n\n/**\n * Join values in array\n * @param input array of values to be joined together\n * @param delimiter string to be placed in-between values\n * @returns Joined values\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function safeJoin(input: any[], delimiter?: string): string {\n  if (!Array.isArray(input)) {\n    return '';\n  }\n\n  const output = [];\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < input.length; i++) {\n    const value = input[i];\n    try {\n      output.push(String(value));\n    } catch (e) {\n      output.push('[value cannot be serialized]');\n    }\n  }\n\n  return output.join(delimiter);\n}\n\n/**\n * Checks if the value matches a regex or includes the string\n * @param value The string value to be checked against\n * @param pattern Either a regex or a string that must be contained in value\n */\nexport function isMatchingPattern(value: string, pattern: RegExp | string): boolean {\n  if (!isString(value)) {\n    return false;\n  }\n\n  if (isRegExp(pattern)) {\n    return (pattern as RegExp).test(value);\n  }\n  if (typeof pattern === 'string') {\n    return value.indexOf(pattern) !== -1;\n  }\n  return false;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { ExtendedError, WrappedFunction } from '@sentry/types';\n\nimport { htmlTreeAsString } from './browser';\nimport { isElement, isError, isEvent, isInstanceOf, isPlainObject, isPrimitive, isSyntheticEvent } from './is';\nimport { Memo } from './memo';\nimport { getFunctionName } from './stacktrace';\nimport { truncate } from './string';\n\n/**\n * Wrap a given object method with a higher-order function\n *\n * @param source An object that contains a method to be wrapped.\n * @param name A name of method to be wrapped.\n * @param replacementFactory A function that should be used to wrap a given method, returning the wrapped method which\n * will be substituted in for `source[name]`.\n * @returns void\n */\nexport function fill(source: { [key: string]: any }, name: string, replacementFactory: (...args: any[]) => any): void {\n  if (!(name in source)) {\n    return;\n  }\n\n  const original = source[name] as () => any;\n  const wrapped = replacementFactory(original) as WrappedFunction;\n\n  // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n  // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n  if (typeof wrapped === 'function') {\n    try {\n      wrapped.prototype = wrapped.prototype || {};\n      Object.defineProperties(wrapped, {\n        __sentry_original__: {\n          enumerable: false,\n          value: original,\n        },\n      });\n    } catch (_Oo) {\n      // This can throw if multiple fill happens on a global object like XMLHttpRequest\n      // Fixes https://github.com/getsentry/sentry-javascript/issues/2043\n    }\n  }\n\n  source[name] = wrapped;\n}\n\n/**\n * Encodes given object into url-friendly format\n *\n * @param object An object that contains serializable values\n * @returns string Encoded\n */\nexport function urlEncode(object: { [key: string]: any }): string {\n  return Object.keys(object)\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(object[key])}`)\n    .join('&');\n}\n\n/**\n * Transforms any object into an object literal with all its attributes\n * attached to it.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n */\nfunction getWalkSource(\n  value: any,\n): {\n  [key: string]: any;\n} {\n  if (isError(value)) {\n    const error = value as ExtendedError;\n    const err: {\n      [key: string]: any;\n      stack: string | undefined;\n      message: string;\n      name: string;\n    } = {\n      message: error.message,\n      name: error.name,\n      stack: error.stack,\n    };\n\n    for (const i in error) {\n      if (Object.prototype.hasOwnProperty.call(error, i)) {\n        err[i] = error[i];\n      }\n    }\n\n    return err;\n  }\n\n  if (isEvent(value)) {\n    /**\n     * Event-like interface that's usable in browser and node\n     */\n    interface SimpleEvent {\n      [key: string]: unknown;\n      type: string;\n      target?: unknown;\n      currentTarget?: unknown;\n    }\n\n    const event = value as SimpleEvent;\n\n    const source: {\n      [key: string]: any;\n    } = {};\n\n    source.type = event.type;\n\n    // Accessing event.target can throw (see getsentry/raven-js#838, #768)\n    try {\n      source.target = isElement(event.target)\n        ? htmlTreeAsString(event.target)\n        : Object.prototype.toString.call(event.target);\n    } catch (_oO) {\n      source.target = '<unknown>';\n    }\n\n    try {\n      source.currentTarget = isElement(event.currentTarget)\n        ? htmlTreeAsString(event.currentTarget)\n        : Object.prototype.toString.call(event.currentTarget);\n    } catch (_oO) {\n      source.currentTarget = '<unknown>';\n    }\n\n    if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n      source.detail = event.detail;\n    }\n\n    for (const i in event) {\n      if (Object.prototype.hasOwnProperty.call(event, i)) {\n        source[i] = event;\n      }\n    }\n\n    return source;\n  }\n\n  return value as {\n    [key: string]: any;\n  };\n}\n\n/** Calculates bytes size of input string */\nfunction utf8Length(value: string): number {\n  // eslint-disable-next-line no-bitwise\n  return ~-encodeURI(value).split(/%..|./).length;\n}\n\n/** Calculates bytes size of input object */\nfunction jsonSize(value: any): number {\n  return utf8Length(JSON.stringify(value));\n}\n\n/** JSDoc */\nexport function normalizeToSize<T>(\n  object: { [key: string]: any },\n  // Default Node.js REPL depth\n  depth: number = 3,\n  // 100kB, as 200kB is max payload size, so half sounds reasonable\n  maxSize: number = 100 * 1024,\n): T {\n  const serialized = normalize(object, depth);\n\n  if (jsonSize(serialized) > maxSize) {\n    return normalizeToSize(object, depth - 1, maxSize);\n  }\n\n  return serialized as T;\n}\n\n/**\n * Transform any non-primitive, BigInt, or Symbol-type value into a string. Acts as a no-op on strings, numbers,\n * booleans, null, and undefined.\n *\n * @param value The value to stringify\n * @returns For non-primitive, BigInt, and Symbol-type values, a string denoting the value's type, type and value, or\n *  type and `description` property, respectively. For non-BigInt, non-Symbol primitives, returns the original value,\n *  unchanged.\n */\nfunction serializeValue(value: any): any {\n  const type = Object.prototype.toString.call(value);\n\n  // Node.js REPL notation\n  if (typeof value === 'string') {\n    return value;\n  }\n  if (type === '[object Object]') {\n    return '[Object]';\n  }\n  if (type === '[object Array]') {\n    return '[Array]';\n  }\n\n  const normalized = normalizeValue(value);\n  return isPrimitive(normalized) ? normalized : type;\n}\n\n/**\n * normalizeValue()\n *\n * Takes unserializable input and make it serializable friendly\n *\n * - translates undefined/NaN values to \"[undefined]\"/\"[NaN]\" respectively,\n * - serializes Error objects\n * - filter global objects\n */\nfunction normalizeValue<T>(value: T, key?: any): T | string {\n  if (key === 'domain' && value && typeof value === 'object' && ((value as unknown) as { _events: any })._events) {\n    return '[Domain]';\n  }\n\n  if (key === 'domainEmitter') {\n    return '[DomainEmitter]';\n  }\n\n  if (typeof (global as any) !== 'undefined' && (value as unknown) === global) {\n    return '[Global]';\n  }\n\n  if (typeof (window as any) !== 'undefined' && (value as unknown) === window) {\n    return '[Window]';\n  }\n\n  if (typeof (document as any) !== 'undefined' && (value as unknown) === document) {\n    return '[Document]';\n  }\n\n  // React's SyntheticEvent thingy\n  if (isSyntheticEvent(value)) {\n    return '[SyntheticEvent]';\n  }\n\n  if (typeof value === 'number' && value !== value) {\n    return '[NaN]';\n  }\n\n  if (value === void 0) {\n    return '[undefined]';\n  }\n\n  if (typeof value === 'function') {\n    return `[Function: ${getFunctionName(value)}]`;\n  }\n\n  // symbols and bigints are considered primitives by TS, but aren't natively JSON-serilaizable\n\n  if (typeof value === 'symbol') {\n    return `[${String(value)}]`;\n  }\n\n  if (typeof value === 'bigint') {\n    return `[BigInt: ${String(value)}]`;\n  }\n\n  return value;\n}\n\n/**\n * Walks an object to perform a normalization on it\n *\n * @param key of object that's walked in current iteration\n * @param value object to be walked\n * @param depth Optional number indicating how deep should walking be performed\n * @param memo Optional Memo class handling decycling\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function walk(key: string, value: any, depth: number = +Infinity, memo: Memo = new Memo()): any {\n  // If we reach the maximum depth, serialize whatever has left\n  if (depth === 0) {\n    return serializeValue(value);\n  }\n\n  /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n  // If value implements `toJSON` method, call it and return early\n  if (value !== null && value !== undefined && typeof value.toJSON === 'function') {\n    return value.toJSON();\n  }\n  /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n\n  // If normalized value is a primitive, there are no branches left to walk, so we can just bail out, as theres no point in going down that branch any further\n  const normalized = normalizeValue(value, key);\n  if (isPrimitive(normalized)) {\n    return normalized;\n  }\n\n  // Create source that we will use for next itterations, either objectified error object (Error type with extracted keys:value pairs) or the input itself\n  const source = getWalkSource(value);\n\n  // Create an accumulator that will act as a parent for all future itterations of that branch\n  const acc = Array.isArray(value) ? [] : {};\n\n  // If we already walked that branch, bail out, as it's circular reference\n  if (memo.memoize(value)) {\n    return '[Circular ~]';\n  }\n\n  // Walk all keys of the source\n  for (const innerKey in source) {\n    // Avoid iterating over fields in the prototype if they've somehow been exposed to enumeration.\n    if (!Object.prototype.hasOwnProperty.call(source, innerKey)) {\n      continue;\n    }\n    // Recursively walk through all the child nodes\n    (acc as { [key: string]: any })[innerKey] = walk(innerKey, source[innerKey], depth - 1, memo);\n  }\n\n  // Once walked through all the branches, remove the parent from memo storage\n  memo.unmemoize(value);\n\n  // Return accumulated values\n  return acc;\n}\n\n/**\n * normalize()\n *\n * - Creates a copy to prevent original input mutation\n * - Skip non-enumerablers\n * - Calls `toJSON` if implemented\n * - Removes circular references\n * - Translates non-serializeable values (undefined/NaN/Functions) to serializable format\n * - Translates known global objects/Classes to a string representations\n * - Takes care of Error objects serialization\n * - Optionally limit depth of final output\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function normalize(input: any, depth?: number): any {\n  try {\n    return JSON.parse(JSON.stringify(input, (key: string, value: any) => walk(key, value, depth)));\n  } catch (_oO) {\n    return '**non-serializable**';\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function extractExceptionKeysForMessage(exception: any, maxLength: number = 40): string {\n  const keys = Object.keys(getWalkSource(exception));\n  keys.sort();\n\n  if (!keys.length) {\n    return '[object has no keys]';\n  }\n\n  if (keys[0].length >= maxLength) {\n    return truncate(keys[0], maxLength);\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n\n  return '';\n}\n\n/**\n * Given any object, return the new object with removed keys that value was `undefined`.\n * Works recursively on objects and arrays.\n */\nexport function dropUndefinedKeys<T>(val: T): T {\n  if (isPlainObject(val)) {\n    const obj = val as { [key: string]: any };\n    const rv: { [key: string]: any } = {};\n    for (const key of Object.keys(obj)) {\n      if (typeof obj[key] !== 'undefined') {\n        rv[key] = dropUndefinedKeys(obj[key]);\n      }\n    }\n    return rv as T;\n  }\n\n  if (Array.isArray(val)) {\n    return (val as any[]).map(dropUndefinedKeys) as any;\n  }\n\n  return val;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { ExtractedNodeRequestData } from '@sentry/types';\n\nimport { isString } from './is';\nimport { normalize } from './object';\n\n/**\n * Checks whether we're in the Node.js or Browser environment\n *\n * @returns Answer to given question\n */\nexport function isNodeEnv(): boolean {\n  return Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]';\n}\n\n/**\n * Requires a module which is protected against bundler minification.\n *\n * @param request The module path to resolve\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nexport function dynamicRequire(mod: any, request: string): any {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return mod.require(request);\n}\n\n/** Default request keys that'll be used to extract data from the request */\nconst DEFAULT_REQUEST_KEYS = ['cookies', 'data', 'headers', 'method', 'query_string', 'url'];\n\n/**\n * Normalizes data from the request object, accounting for framework differences.\n *\n * @param req The request object from which to extract data\n * @param keys An optional array of keys to include in the normalized data. Defaults to DEFAULT_REQUEST_KEYS if not\n * provided.\n * @returns An object containing normalized request data\n */\nexport function extractNodeRequestData(\n  req: { [key: string]: any },\n  keys: string[] = DEFAULT_REQUEST_KEYS,\n): ExtractedNodeRequestData {\n  // make sure we can safely use dynamicRequire below\n  if (!isNodeEnv()) {\n    throw new Error(\"Can't get node request data outside of a node environment\");\n  }\n\n  const requestData: { [key: string]: any } = {};\n\n  // headers:\n  //   node, express: req.headers\n  //   koa: req.header\n  const headers = (req.headers || req.header || {}) as {\n    host?: string;\n    cookie?: string;\n  };\n  // method:\n  //   node, express, koa: req.method\n  const method = req.method;\n  // host:\n  //   express: req.hostname in > 4 and req.host in < 4\n  //   koa: req.host\n  //   node: req.headers.host\n  const host = req.hostname || req.host || headers.host || '<no host>';\n  // protocol:\n  //   node: <n/a>\n  //   express, koa: req.protocol\n  const protocol =\n    req.protocol === 'https' || req.secure || ((req.socket || {}) as { encrypted?: boolean }).encrypted\n      ? 'https'\n      : 'http';\n  // url (including path and query string):\n  //   node, express: req.originalUrl\n  //   koa: req.url\n  const originalUrl = (req.originalUrl || req.url || '') as string;\n  // absolute url\n  const absoluteUrl = `${protocol}://${host}${originalUrl}`;\n\n  keys.forEach(key => {\n    switch (key) {\n      case 'headers':\n        requestData.headers = headers;\n        break;\n      case 'method':\n        requestData.method = method;\n        break;\n      case 'url':\n        requestData.url = absoluteUrl;\n        break;\n      case 'cookies':\n        // cookies:\n        //   node, express, koa: req.headers.cookie\n        //   vercel, sails.js, express (w/ cookie middleware): req.cookies\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        requestData.cookies = req.cookies || dynamicRequire(module, 'cookie').parse(headers.cookie || '');\n        break;\n      case 'query_string':\n        // query string:\n        //   node: req.url (raw)\n        //   express, koa: req.query\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        requestData.query_string = dynamicRequire(module, 'url').parse(originalUrl || '', false).query;\n        break;\n      case 'data':\n        if (method === 'GET' || method === 'HEAD') {\n          break;\n        }\n        // body data:\n        //   node, express, koa: req.body\n        if (req.body !== undefined) {\n          requestData.data = isString(req.body) ? req.body : JSON.stringify(normalize(req.body));\n        }\n        break;\n      default:\n        if ({}.hasOwnProperty.call(req, key)) {\n          requestData[key] = (req as { [key: string]: any })[key];\n        }\n    }\n  });\n\n  return requestData;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Event, Integration, StackFrame, WrappedFunction } from '@sentry/types';\n\nimport { isNodeEnv } from './node';\nimport { snipLine } from './string';\n\n/** Internal */\ninterface SentryGlobal {\n  Sentry?: {\n    Integrations?: Integration[];\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  __SENTRY__: {\n    globalEventProcessors: any;\n    hub: any;\n    logger: any;\n  };\n}\n\nconst fallbackGlobalObject = {};\n\n/**\n * Safely get global scope object\n *\n * @returns Global scope object\n */\nexport function getGlobalObject<T>(): T & SentryGlobal {\n  return (isNodeEnv()\n    ? global\n    : typeof window !== 'undefined'\n    ? window\n    : typeof self !== 'undefined'\n    ? self\n    : fallbackGlobalObject) as T & SentryGlobal;\n}\n\n/**\n * Extended Window interface that allows for Crypto API usage in IE browsers\n */\ninterface MsCryptoWindow extends Window {\n  msCrypto?: Crypto;\n}\n\n/**\n * UUID4 generator\n *\n * @returns string Generated UUID4.\n */\nexport function uuid4(): string {\n  const global = getGlobalObject() as MsCryptoWindow;\n  const crypto = global.crypto || global.msCrypto;\n\n  if (!(crypto === void 0) && crypto.getRandomValues) {\n    // Use window.crypto API if available\n    const arr = new Uint16Array(8);\n    crypto.getRandomValues(arr);\n\n    // set 4 in byte 7\n    // eslint-disable-next-line no-bitwise\n    arr[3] = (arr[3] & 0xfff) | 0x4000;\n    // set 2 most significant bits of byte 9 to '10'\n    // eslint-disable-next-line no-bitwise\n    arr[4] = (arr[4] & 0x3fff) | 0x8000;\n\n    const pad = (num: number): string => {\n      let v = num.toString(16);\n      while (v.length < 4) {\n        v = `0${v}`;\n      }\n      return v;\n    };\n\n    return (\n      pad(arr[0]) + pad(arr[1]) + pad(arr[2]) + pad(arr[3]) + pad(arr[4]) + pad(arr[5]) + pad(arr[6]) + pad(arr[7])\n    );\n  }\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n  return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    // eslint-disable-next-line no-bitwise\n    const r = (Math.random() * 16) | 0;\n    // eslint-disable-next-line no-bitwise\n    const v = c === 'x' ? r : (r & 0x3) | 0x8;\n    return v.toString(16);\n  });\n}\n\n/**\n * Parses string form of URL into an object\n * // borrowed from https://tools.ietf.org/html/rfc3986#appendix-B\n * // intentionally using regex and not <a/> href parsing trick because React Native and other\n * // environments where DOM might not be available\n * @returns parsed URL object\n */\nexport function parseUrl(\n  url: string,\n): {\n  host?: string;\n  path?: string;\n  protocol?: string;\n  relative?: string;\n} {\n  if (!url) {\n    return {};\n  }\n\n  const match = url.match(/^(([^:/?#]+):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$/);\n\n  if (!match) {\n    return {};\n  }\n\n  // coerce to undefined values to empty string so we don't get 'undefined'\n  const query = match[6] || '';\n  const fragment = match[8] || '';\n  return {\n    host: match[4],\n    path: match[5],\n    protocol: match[2],\n    relative: match[5] + query + fragment, // everything minus origin\n  };\n}\n\n/**\n * Extracts either message or type+value from an event that can be used for user-facing logs\n * @returns event's description\n */\nexport function getEventDescription(event: Event): string {\n  if (event.message) {\n    return event.message;\n  }\n  if (event.exception && event.exception.values && event.exception.values[0]) {\n    const exception = event.exception.values[0];\n\n    if (exception.type && exception.value) {\n      return `${exception.type}: ${exception.value}`;\n    }\n    return exception.type || exception.value || event.event_id || '<unknown>';\n  }\n  return event.event_id || '<unknown>';\n}\n\n/** JSDoc */\ninterface ExtensibleConsole extends Console {\n  [key: string]: any;\n}\n\n/** JSDoc */\nexport function consoleSandbox(callback: () => any): any {\n  const global = getGlobalObject<Window>();\n  const levels = ['debug', 'info', 'warn', 'error', 'log', 'assert'];\n\n  if (!('console' in global)) {\n    return callback();\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  const originalConsole = (global as any).console as ExtensibleConsole;\n  const wrappedLevels: { [key: string]: any } = {};\n\n  // Restore all wrapped console methods\n  levels.forEach(level => {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    if (level in (global as any).console && (originalConsole[level] as WrappedFunction).__sentry_original__) {\n      wrappedLevels[level] = originalConsole[level] as WrappedFunction;\n      originalConsole[level] = (originalConsole[level] as WrappedFunction).__sentry_original__;\n    }\n  });\n\n  // Perform callback manipulations\n  const result = callback();\n\n  // Revert restoration to wrapped state\n  Object.keys(wrappedLevels).forEach(level => {\n    originalConsole[level] = wrappedLevels[level];\n  });\n\n  return result;\n}\n\n/**\n * Adds exception values, type and value to an synthetic Exception.\n * @param event The event to modify.\n * @param value Value of the exception.\n * @param type Type of the exception.\n * @hidden\n */\nexport function addExceptionTypeValue(event: Event, value?: string, type?: string): void {\n  event.exception = event.exception || {};\n  event.exception.values = event.exception.values || [];\n  event.exception.values[0] = event.exception.values[0] || {};\n  event.exception.values[0].value = event.exception.values[0].value || value || '';\n  event.exception.values[0].type = event.exception.values[0].type || type || 'Error';\n}\n\n/**\n * Adds exception mechanism to a given event.\n * @param event The event to modify.\n * @param mechanism Mechanism of the mechanism.\n * @hidden\n */\nexport function addExceptionMechanism(\n  event: Event,\n  mechanism: {\n    [key: string]: any;\n  } = {},\n): void {\n  // TODO: Use real type with `keyof Mechanism` thingy and maybe make it better?\n  try {\n    // @ts-ignore Type 'Mechanism | {}' is not assignable to type 'Mechanism | undefined'\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    event.exception!.values![0].mechanism = event.exception!.values![0].mechanism || {};\n    Object.keys(mechanism).forEach(key => {\n      // @ts-ignore Mechanism has no index signature\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      event.exception!.values![0].mechanism[key] = mechanism[key];\n    });\n  } catch (_oO) {\n    // no-empty\n  }\n}\n\n/**\n * A safe form of location.href\n */\nexport function getLocationHref(): string {\n  try {\n    return document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n// https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string\nconst SEMVER_REGEXP = /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\n/**\n * Represents Semantic Versioning object\n */\ninterface SemVer {\n  major?: number;\n  minor?: number;\n  patch?: number;\n  prerelease?: string;\n  buildmetadata?: string;\n}\n\n/**\n * Parses input into a SemVer interface\n * @param input string representation of a semver version\n */\nexport function parseSemver(input: string): SemVer {\n  const match = input.match(SEMVER_REGEXP) || [];\n  const major = parseInt(match[1], 10);\n  const minor = parseInt(match[2], 10);\n  const patch = parseInt(match[3], 10);\n  return {\n    buildmetadata: match[5],\n    major: isNaN(major) ? undefined : major,\n    minor: isNaN(minor) ? undefined : minor,\n    patch: isNaN(patch) ? undefined : patch,\n    prerelease: match[4],\n  };\n}\n\nconst defaultRetryAfter = 60 * 1000; // 60 seconds\n\n/**\n * Extracts Retry-After value from the request header or returns default value\n * @param now current unix timestamp\n * @param header string representation of 'Retry-After' header\n */\nexport function parseRetryAfterHeader(now: number, header?: string | number | null): number {\n  if (!header) {\n    return defaultRetryAfter;\n  }\n\n  const headerDelay = parseInt(`${header}`, 10);\n  if (!isNaN(headerDelay)) {\n    return headerDelay * 1000;\n  }\n\n  const headerDate = Date.parse(`${header}`);\n  if (!isNaN(headerDate)) {\n    return headerDate - now;\n  }\n\n  return defaultRetryAfter;\n}\n\n/**\n * This function adds context (pre/post/line) lines to the provided frame\n *\n * @param lines string[] containing all lines\n * @param frame StackFrame that will be mutated\n * @param linesOfContext number of context lines we want to add pre/post\n */\nexport function addContextToFrame(lines: string[], frame: StackFrame, linesOfContext: number = 5): void {\n  const lineno = frame.lineno || 0;\n  const maxLines = lines.length;\n  const sourceLine = Math.max(Math.min(maxLines, lineno - 1), 0);\n\n  frame.pre_context = lines\n    .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)\n    .map((line: string) => snipLine(line, 0));\n\n  frame.context_line = snipLine(lines[Math.min(maxLines - 1, sourceLine)], frame.colno || 0);\n\n  frame.post_context = lines\n    .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)\n    .map((line: string) => snipLine(line, 0));\n}\n\n/**\n * Strip the query string and fragment off of a given URL or path (if present)\n *\n * @param urlPath Full URL or path, including possible query string and/or fragment\n * @returns URL or path without query string or fragment\n */\nexport function stripUrlQueryAndFragment(urlPath: string): string {\n  // eslint-disable-next-line no-useless-escape\n  return urlPath.split(/[\\?#]/, 1)[0];\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { consoleSandbox, getGlobalObject } from './misc';\n\n// TODO: Implement different loggers for different environments\nconst global = getGlobalObject<Window | NodeJS.Global>();\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\n/** JSDoc */\nclass Logger {\n  /** JSDoc */\n  private _enabled: boolean;\n\n  /** JSDoc */\n  public constructor() {\n    this._enabled = false;\n  }\n\n  /** JSDoc */\n  public disable(): void {\n    this._enabled = false;\n  }\n\n  /** JSDoc */\n  public enable(): void {\n    this._enabled = true;\n  }\n\n  /** JSDoc */\n  public log(...args: any[]): void {\n    if (!this._enabled) {\n      return;\n    }\n    consoleSandbox(() => {\n      global.console.log(`${PREFIX}[Log]: ${args.join(' ')}`);\n    });\n  }\n\n  /** JSDoc */\n  public warn(...args: any[]): void {\n    if (!this._enabled) {\n      return;\n    }\n    consoleSandbox(() => {\n      global.console.warn(`${PREFIX}[Warn]: ${args.join(' ')}`);\n    });\n  }\n\n  /** JSDoc */\n  public error(...args: any[]): void {\n    if (!this._enabled) {\n      return;\n    }\n    consoleSandbox(() => {\n      global.console.error(`${PREFIX}[Error]: ${args.join(' ')}`);\n    });\n  }\n}\n\n// Ensure we only have a single logger instance, even if multiple versions of @sentry/utils are being used\nglobal.__SENTRY__ = global.__SENTRY__ || {};\nconst logger = (global.__SENTRY__.logger as Logger) || (global.__SENTRY__.logger = new Logger());\n\nexport { logger };\n", "import { logger } from './logger';\nimport { getGlobalObject } from './misc';\n\n/**\n * Tells whether current environment supports ErrorEvent objects\n * {@link supportsErrorEvent}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsErrorEvent(): boolean {\n  try {\n    new ErrorEvent('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMError objects\n * {@link supportsDOMError}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsDOMError(): boolean {\n  try {\n    // Chrome: VM89:1 Uncaught TypeError: Failed to construct 'DOMError':\n    // 1 argument required, but only 0 present.\n    // @ts-ignore It really needs 1 argument, not 0.\n    new DOMError('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMException objects\n * {@link supportsDOMException}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsDOMException(): boolean {\n  try {\n    new DOMException('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports Fetch API\n * {@link supportsFetch}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsFetch(): boolean {\n  if (!('fetch' in getGlobalObject<Window>())) {\n    return false;\n  }\n\n  try {\n    new Headers();\n    new Request('');\n    new Response();\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * isNativeFetch checks if the given function is a native implementation of fetch()\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isNativeFetch(func: Function): boolean {\n  return func && /^function fetch\\(\\)\\s+\\{\\s+\\[native code\\]\\s+\\}$/.test(func.toString());\n}\n\n/**\n * Tells whether current environment supports Fetch API natively\n * {@link supportsNativeFetch}.\n *\n * @returns true if `window.fetch` is natively implemented, false otherwise\n */\nexport function supportsNativeFetch(): boolean {\n  if (!supportsFetch()) {\n    return false;\n  }\n\n  const global = getGlobalObject<Window>();\n\n  // Fast path to avoid DOM I/O\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  if (isNativeFetch(global.fetch)) {\n    return true;\n  }\n\n  // window.fetch is implemented, but is polyfilled or already wrapped (e.g: by a chrome extension)\n  // so create a \"pure\" iframe to see if that has native fetch\n  let result = false;\n  const doc = global.document;\n  // eslint-disable-next-line deprecation/deprecation\n  if (doc && typeof (doc.createElement as unknown) === `function`) {\n    try {\n      const sandbox = doc.createElement('iframe');\n      sandbox.hidden = true;\n      doc.head.appendChild(sandbox);\n      if (sandbox.contentWindow && sandbox.contentWindow.fetch) {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        result = isNativeFetch(sandbox.contentWindow.fetch);\n      }\n      doc.head.removeChild(sandbox);\n    } catch (err) {\n      logger.warn('Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ', err);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Tells whether current environment supports ReportingObserver API\n * {@link supportsReportingObserver}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsReportingObserver(): boolean {\n  return 'ReportingObserver' in getGlobalObject();\n}\n\n/**\n * Tells whether current environment supports Referrer Policy API\n * {@link supportsReferrerPolicy}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsReferrerPolicy(): boolean {\n  // Despite all stars in the sky saying that Edge supports old draft syntax, aka 'never', 'always', 'origin' and 'default\n  // https://caniuse.com/#feat=referrer-policy\n  // It doesn't. And it throw exception instead of ignoring this parameter...\n  // REF: https://github.com/getsentry/raven-js/issues/1233\n\n  if (!supportsFetch()) {\n    return false;\n  }\n\n  try {\n    new Request('_', {\n      referrerPolicy: 'origin' as ReferrerPolicy,\n    });\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports History API\n * {@link supportsHistory}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsHistory(): boolean {\n  // NOTE: in Chrome App environment, touching history.pushState, *even inside\n  //       a try/catch block*, will cause Chrome to output an error to console.error\n  // borrowed from: https://github.com/angular/angular.js/pull/13945/files\n  const global = getGlobalObject<Window>();\n  /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const chrome = (global as any).chrome;\n  const isChromePackagedApp = chrome && chrome.app && chrome.app.runtime;\n  /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n  const hasHistoryApi = 'history' in global && !!global.history.pushState && !!global.history.replaceState;\n\n  return !isChromePackagedApp && hasHistoryApi;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/ban-types */\nimport { WrappedFunction } from '@sentry/types';\n\nimport { isInstanceOf, isString } from './is';\nimport { logger } from './logger';\nimport { getGlobalObject } from './misc';\nimport { fill } from './object';\nimport { getFunctionName } from './stacktrace';\nimport { supportsHistory, supportsNativeFetch } from './supports';\n\nconst global = getGlobalObject<Window>();\n\n/** Object describing handler that will be triggered for a given `type` of instrumentation */\ninterface InstrumentHandler {\n  type: InstrumentHandlerType;\n  callback: InstrumentHandlerCallback;\n}\ntype InstrumentHandlerType =\n  | 'console'\n  | 'dom'\n  | 'fetch'\n  | 'history'\n  | 'sentry'\n  | 'xhr'\n  | 'error'\n  | 'unhandledrejection';\ntype InstrumentHandlerCallback = (data: any) => void;\n\n/**\n * Instrument native APIs to call handlers that can be used to create breadcrumbs, APM spans etc.\n *  - Console API\n *  - Fetch API\n *  - XHR API\n *  - History API\n *  - DOM API (click/typing)\n *  - Error API\n *  - UnhandledRejection API\n */\n\nconst handlers: { [key in InstrumentHandlerType]?: InstrumentHandlerCallback[] } = {};\nconst instrumented: { [key in InstrumentHandlerType]?: boolean } = {};\n\n/** Instruments given API */\nfunction instrument(type: InstrumentHandlerType): void {\n  if (instrumented[type]) {\n    return;\n  }\n\n  instrumented[type] = true;\n\n  switch (type) {\n    case 'console':\n      instrumentConsole();\n      break;\n    case 'dom':\n      instrumentDOM();\n      break;\n    case 'xhr':\n      instrumentXHR();\n      break;\n    case 'fetch':\n      instrumentFetch();\n      break;\n    case 'history':\n      instrumentHistory();\n      break;\n    case 'error':\n      instrumentError();\n      break;\n    case 'unhandledrejection':\n      instrumentUnhandledRejection();\n      break;\n    default:\n      logger.warn('unknown instrumentation type:', type);\n  }\n}\n\n/**\n * Add handler that will be called when given type of instrumentation triggers.\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addInstrumentationHandler(handler: InstrumentHandler): void {\n  if (!handler || typeof handler.type !== 'string' || typeof handler.callback !== 'function') {\n    return;\n  }\n  handlers[handler.type] = handlers[handler.type] || [];\n  (handlers[handler.type] as InstrumentHandlerCallback[]).push(handler.callback);\n  instrument(handler.type);\n}\n\n/** JSDoc */\nfunction triggerHandlers(type: InstrumentHandlerType, data: any): void {\n  if (!type || !handlers[type]) {\n    return;\n  }\n\n  for (const handler of handlers[type] || []) {\n    try {\n      handler(data);\n    } catch (e) {\n      logger.error(\n        `Error while triggering instrumentation handler.\\nType: ${type}\\nName: ${getFunctionName(\n          handler,\n        )}\\nError: ${e}`,\n      );\n    }\n  }\n}\n\n/** JSDoc */\nfunction instrumentConsole(): void {\n  if (!('console' in global)) {\n    return;\n  }\n\n  ['debug', 'info', 'warn', 'error', 'log', 'assert'].forEach(function(level: string): void {\n    if (!(level in global.console)) {\n      return;\n    }\n\n    fill(global.console, level, function(originalConsoleLevel: () => any): Function {\n      return function(...args: any[]): void {\n        triggerHandlers('console', { args, level });\n\n        // this fails for some browsers. :(\n        if (originalConsoleLevel) {\n          Function.prototype.apply.call(originalConsoleLevel, global.console, args);\n        }\n      };\n    });\n  });\n}\n\n/** JSDoc */\nfunction instrumentFetch(): void {\n  if (!supportsNativeFetch()) {\n    return;\n  }\n\n  fill(global, 'fetch', function(originalFetch: () => void): () => void {\n    return function(...args: any[]): void {\n      const handlerData = {\n        args,\n        fetchData: {\n          method: getFetchMethod(args),\n          url: getFetchUrl(args),\n        },\n        startTimestamp: Date.now(),\n      };\n\n      triggerHandlers('fetch', {\n        ...handlerData,\n      });\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      return originalFetch.apply(global, args).then(\n        (response: Response) => {\n          triggerHandlers('fetch', {\n            ...handlerData,\n            endTimestamp: Date.now(),\n            response,\n          });\n          return response;\n        },\n        (error: Error) => {\n          triggerHandlers('fetch', {\n            ...handlerData,\n            endTimestamp: Date.now(),\n            error,\n          });\n          // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n          //       it means the sentry.javascript SDK caught an error invoking your application code.\n          //       This is expected behavior and NOT indicative of a bug with sentry.javascript.\n          throw error;\n        },\n      );\n    };\n  });\n}\n\ntype XHRSendInput = null | Blob | BufferSource | FormData | URLSearchParams | string;\n\n/** JSDoc */\ninterface SentryWrappedXMLHttpRequest extends XMLHttpRequest {\n  [key: string]: any;\n  __sentry_xhr__?: {\n    method?: string;\n    url?: string;\n    status_code?: number;\n    body?: XHRSendInput;\n  };\n}\n\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/** Extract `method` from fetch call arguments */\nfunction getFetchMethod(fetchArgs: any[] = []): string {\n  if ('Request' in global && isInstanceOf(fetchArgs[0], Request) && fetchArgs[0].method) {\n    return String(fetchArgs[0].method).toUpperCase();\n  }\n  if (fetchArgs[1] && fetchArgs[1].method) {\n    return String(fetchArgs[1].method).toUpperCase();\n  }\n  return 'GET';\n}\n\n/** Extract `url` from fetch call arguments */\nfunction getFetchUrl(fetchArgs: any[] = []): string {\n  if (typeof fetchArgs[0] === 'string') {\n    return fetchArgs[0];\n  }\n  if ('Request' in global && isInstanceOf(fetchArgs[0], Request)) {\n    return fetchArgs[0].url;\n  }\n  return String(fetchArgs[0]);\n}\n/* eslint-enable @typescript-eslint/no-unsafe-member-access */\n\n/** JSDoc */\nfunction instrumentXHR(): void {\n  if (!('XMLHttpRequest' in global)) {\n    return;\n  }\n\n  // Poor man's implementation of ES6 `Map`, tracking and keeping in sync key and value separately.\n  const requestKeys: XMLHttpRequest[] = [];\n  const requestValues: Array<any>[] = [];\n  const xhrproto = XMLHttpRequest.prototype;\n\n  fill(xhrproto, 'open', function(originalOpen: () => void): () => void {\n    return function(this: SentryWrappedXMLHttpRequest, ...args: any[]): void {\n      // eslint-disable-next-line @typescript-eslint/no-this-alias\n      const xhr = this;\n      const url = args[1];\n      xhr.__sentry_xhr__ = {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        method: isString(args[0]) ? args[0].toUpperCase() : args[0],\n        url: args[1],\n      };\n\n      // if Sentry key appears in URL, don't capture it as a request\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      if (isString(url) && xhr.__sentry_xhr__.method === 'POST' && url.match(/sentry_key/)) {\n        xhr.__sentry_own_request__ = true;\n      }\n\n      const onreadystatechangeHandler = function(): void {\n        if (xhr.readyState === 4) {\n          try {\n            // touching statusCode in some platforms throws\n            // an exception\n            if (xhr.__sentry_xhr__) {\n              xhr.__sentry_xhr__.status_code = xhr.status;\n            }\n          } catch (e) {\n            /* do nothing */\n          }\n\n          try {\n            const requestPos = requestKeys.indexOf(xhr);\n            if (requestPos !== -1) {\n              // Make sure to pop both key and value to keep it in sync.\n              requestKeys.splice(requestPos);\n              const args = requestValues.splice(requestPos)[0];\n              if (xhr.__sentry_xhr__ && args[0] !== undefined) {\n                xhr.__sentry_xhr__.body = args[0] as XHRSendInput;\n              }\n            }\n          } catch (e) {\n            /* do nothing */\n          }\n\n          triggerHandlers('xhr', {\n            args,\n            endTimestamp: Date.now(),\n            startTimestamp: Date.now(),\n            xhr,\n          });\n        }\n      };\n\n      if ('onreadystatechange' in xhr && typeof xhr.onreadystatechange === 'function') {\n        fill(xhr, 'onreadystatechange', function(original: WrappedFunction): Function {\n          return function(...readyStateArgs: any[]): void {\n            onreadystatechangeHandler();\n            return original.apply(xhr, readyStateArgs);\n          };\n        });\n      } else {\n        xhr.addEventListener('readystatechange', onreadystatechangeHandler);\n      }\n\n      return originalOpen.apply(xhr, args);\n    };\n  });\n\n  fill(xhrproto, 'send', function(originalSend: () => void): () => void {\n    return function(this: SentryWrappedXMLHttpRequest, ...args: any[]): void {\n      requestKeys.push(this);\n      requestValues.push(args);\n\n      triggerHandlers('xhr', {\n        args,\n        startTimestamp: Date.now(),\n        xhr: this,\n      });\n\n      return originalSend.apply(this, args);\n    };\n  });\n}\n\nlet lastHref: string;\n\n/** JSDoc */\nfunction instrumentHistory(): void {\n  if (!supportsHistory()) {\n    return;\n  }\n\n  const oldOnPopState = global.onpopstate;\n  global.onpopstate = function(this: WindowEventHandlers, ...args: any[]): any {\n    const to = global.location.href;\n    // keep track of the current URL state, as we always receive only the updated state\n    const from = lastHref;\n    lastHref = to;\n    triggerHandlers('history', {\n      from,\n      to,\n    });\n    if (oldOnPopState) {\n      return oldOnPopState.apply(this, args);\n    }\n  };\n\n  /** @hidden */\n  function historyReplacementFunction(originalHistoryFunction: () => void): () => void {\n    return function(this: History, ...args: any[]): void {\n      const url = args.length > 2 ? args[2] : undefined;\n      if (url) {\n        // coerce to string (this is what pushState does)\n        const from = lastHref;\n        const to = String(url);\n        // keep track of the current URL state, as we always receive only the updated state\n        lastHref = to;\n        triggerHandlers('history', {\n          from,\n          to,\n        });\n      }\n      return originalHistoryFunction.apply(this, args);\n    };\n  }\n\n  fill(global.history, 'pushState', historyReplacementFunction);\n  fill(global.history, 'replaceState', historyReplacementFunction);\n}\n\n/** JSDoc */\nfunction instrumentDOM(): void {\n  if (!('document' in global)) {\n    return;\n  }\n\n  // Capture breadcrumbs from any click that is unhandled / bubbled up all the way\n  // to the document. Do this before we instrument addEventListener.\n  global.document.addEventListener('click', domEventHandler('click', triggerHandlers.bind(null, 'dom')), false);\n  global.document.addEventListener('keypress', keypressEventHandler(triggerHandlers.bind(null, 'dom')), false);\n\n  // After hooking into document bubbled up click and keypresses events, we also hook into user handled click & keypresses.\n  ['EventTarget', 'Node'].forEach((target: string) => {\n    /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n    const proto = (global as any)[target] && (global as any)[target].prototype;\n\n    // eslint-disable-next-line no-prototype-builtins\n    if (!proto || !proto.hasOwnProperty || !proto.hasOwnProperty('addEventListener')) {\n      return;\n    }\n    /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n\n    fill(proto, 'addEventListener', function(\n      original: () => void,\n    ): (\n      eventName: string,\n      fn: EventListenerOrEventListenerObject,\n      options?: boolean | AddEventListenerOptions,\n    ) => void {\n      return function(\n        this: any,\n        eventName: string,\n        fn: EventListenerOrEventListenerObject,\n        options?: boolean | AddEventListenerOptions,\n      ): (eventName: string, fn: EventListenerOrEventListenerObject, capture?: boolean, secure?: boolean) => void {\n        if (fn && (fn as EventListenerObject).handleEvent) {\n          if (eventName === 'click') {\n            fill(fn, 'handleEvent', function(innerOriginal: () => void): (caughtEvent: Event) => void {\n              return function(this: any, event: Event): (event: Event) => void {\n                domEventHandler('click', triggerHandlers.bind(null, 'dom'))(event);\n                return innerOriginal.call(this, event);\n              };\n            });\n          }\n          if (eventName === 'keypress') {\n            fill(fn, 'handleEvent', function(innerOriginal: () => void): (caughtEvent: Event) => void {\n              return function(this: any, event: Event): (event: Event) => void {\n                keypressEventHandler(triggerHandlers.bind(null, 'dom'))(event);\n                return innerOriginal.call(this, event);\n              };\n            });\n          }\n        } else {\n          if (eventName === 'click') {\n            domEventHandler('click', triggerHandlers.bind(null, 'dom'), true)(this);\n          }\n          if (eventName === 'keypress') {\n            keypressEventHandler(triggerHandlers.bind(null, 'dom'))(this);\n          }\n        }\n\n        return original.call(this, eventName, fn, options);\n      };\n    });\n\n    fill(proto, 'removeEventListener', function(\n      original: () => void,\n    ): (\n      this: any,\n      eventName: string,\n      fn: EventListenerOrEventListenerObject,\n      options?: boolean | EventListenerOptions,\n    ) => () => void {\n      return function(\n        this: any,\n        eventName: string,\n        fn: EventListenerOrEventListenerObject,\n        options?: boolean | EventListenerOptions,\n      ): () => void {\n        try {\n          original.call(this, eventName, ((fn as unknown) as WrappedFunction).__sentry_wrapped__, options);\n        } catch (e) {\n          // ignore, accessing __sentry_wrapped__ will throw in some Selenium environments\n        }\n        return original.call(this, eventName, fn, options);\n      };\n    });\n  });\n}\n\nconst debounceDuration: number = 1000;\nlet debounceTimer: number = 0;\nlet keypressTimeout: number | undefined;\nlet lastCapturedEvent: Event | undefined;\n\n/**\n * Wraps addEventListener to capture UI breadcrumbs\n * @param name the event name (e.g. \"click\")\n * @param handler function that will be triggered\n * @param debounce decides whether it should wait till another event loop\n * @returns wrapped breadcrumb events handler\n * @hidden\n */\nfunction domEventHandler(name: string, handler: Function, debounce: boolean = false): (event: Event) => void {\n  return (event: Event): void => {\n    // reset keypress timeout; e.g. triggering a 'click' after\n    // a 'keypress' will reset the keypress debounce so that a new\n    // set of keypresses can be recorded\n    keypressTimeout = undefined;\n    // It's possible this handler might trigger multiple times for the same\n    // event (e.g. event propagation through node ancestors). Ignore if we've\n    // already captured the event.\n    if (!event || lastCapturedEvent === event) {\n      return;\n    }\n\n    lastCapturedEvent = event;\n\n    if (debounceTimer) {\n      clearTimeout(debounceTimer);\n    }\n\n    if (debounce) {\n      debounceTimer = setTimeout(() => {\n        handler({ event, name });\n      });\n    } else {\n      handler({ event, name });\n    }\n  };\n}\n\n/**\n * Wraps addEventListener to capture keypress UI events\n * @param handler function that will be triggered\n * @returns wrapped keypress events handler\n * @hidden\n */\nfunction keypressEventHandler(handler: Function): (event: Event) => void {\n  // TODO: if somehow user switches keypress target before\n  //       debounce timeout is triggered, we will only capture\n  //       a single breadcrumb from the FIRST target (acceptable?)\n  return (event: Event): void => {\n    let target;\n\n    try {\n      target = event.target;\n    } catch (e) {\n      // just accessing event properties can throw an exception in some rare circumstances\n      // see: https://github.com/getsentry/raven-js/issues/838\n      return;\n    }\n\n    const tagName = target && (target as HTMLElement).tagName;\n\n    // only consider keypress events on actual input elements\n    // this will disregard keypresses targeting body (e.g. tabbing\n    // through elements, hotkeys, etc)\n    if (!tagName || (tagName !== 'INPUT' && tagName !== 'TEXTAREA' && !(target as HTMLElement).isContentEditable)) {\n      return;\n    }\n\n    // record first keypress in a series, but ignore subsequent\n    // keypresses until debounce clears\n    if (!keypressTimeout) {\n      domEventHandler('input', handler)(event);\n    }\n    clearTimeout(keypressTimeout);\n\n    keypressTimeout = (setTimeout(() => {\n      keypressTimeout = undefined;\n    }, debounceDuration) as any) as number;\n  };\n}\n\nlet _oldOnErrorHandler: OnErrorEventHandler = null;\n/** JSDoc */\nfunction instrumentError(): void {\n  _oldOnErrorHandler = global.onerror;\n\n  global.onerror = function(msg: any, url: any, line: any, column: any, error: any): boolean {\n    triggerHandlers('error', {\n      column,\n      error,\n      line,\n      msg,\n      url,\n    });\n\n    if (_oldOnErrorHandler) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnErrorHandler.apply(this, arguments);\n    }\n\n    return false;\n  };\n}\n\nlet _oldOnUnhandledRejectionHandler: ((e: any) => void) | null = null;\n/** JSDoc */\nfunction instrumentUnhandledRejection(): void {\n  _oldOnUnhandledRejectionHandler = global.onunhandledrejection;\n\n  global.onunhandledrejection = function(e: any): boolean {\n    triggerHandlers('unhandledrejection', e);\n\n    if (_oldOnUnhandledRejectionHandler) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnUnhandledRejectionHandler.apply(this, arguments);\n    }\n\n    return true;\n  };\n}\n", "/* eslint-disable @typescript-eslint/explicit-function-return-type */\n/* eslint-disable @typescript-eslint/typedef */\n/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isThenable } from './is';\n\n/** SyncPromise internal states */\nenum States {\n  /** Pending */\n  PENDING = 'PENDING',\n  /** Resolved / OK */\n  RESOLVED = 'RESOLVED',\n  /** Rejected / Error */\n  REJECTED = 'REJECTED',\n}\n\n/**\n * Thenable class that behaves like a Promise and follows it's interface\n * but is not async internally\n */\nclass SyncPromise<T> implements PromiseLike<T> {\n  private _state: States = States.PENDING;\n  private _handlers: Array<{\n    done: boolean;\n    onfulfilled?: ((value: T) => T | PromiseLike<T>) | null;\n    onrejected?: ((reason: any) => any) | null;\n  }> = [];\n  private _value: any;\n\n  public constructor(\n    executor: (resolve: (value?: T | PromiseLike<T> | null) => void, reject: (reason?: any) => void) => void,\n  ) {\n    try {\n      executor(this._resolve, this._reject);\n    } catch (e) {\n      this._reject(e);\n    }\n  }\n\n  /** JSDoc */\n  public static resolve<T>(value: T | PromiseLike<T>): PromiseLike<T> {\n    return new SyncPromise(resolve => {\n      resolve(value);\n    });\n  }\n\n  /** JSDoc */\n  public static reject<T = never>(reason?: any): PromiseLike<T> {\n    return new SyncPromise((_, reject) => {\n      reject(reason);\n    });\n  }\n\n  /** JSDoc */\n  public static all<U = any>(collection: Array<U | PromiseLike<U>>): PromiseLike<U[]> {\n    return new SyncPromise<U[]>((resolve, reject) => {\n      if (!Array.isArray(collection)) {\n        reject(new TypeError(`Promise.all requires an array as input.`));\n        return;\n      }\n\n      if (collection.length === 0) {\n        resolve([]);\n        return;\n      }\n\n      let counter = collection.length;\n      const resolvedCollection: U[] = [];\n\n      collection.forEach((item, index) => {\n        SyncPromise.resolve(item)\n          .then(value => {\n            resolvedCollection[index] = value;\n            counter -= 1;\n\n            if (counter !== 0) {\n              return;\n            }\n            resolve(resolvedCollection);\n          })\n          .then(null, reject);\n      });\n    });\n  }\n\n  /** JSDoc */\n  public then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,\n  ): PromiseLike<TResult1 | TResult2> {\n    return new SyncPromise((resolve, reject) => {\n      this._attachHandler({\n        done: false,\n        onfulfilled: result => {\n          if (!onfulfilled) {\n            // TODO: ¯\\_(ツ)_/¯\n            // TODO: FIXME\n            resolve(result as any);\n            return;\n          }\n          try {\n            resolve(onfulfilled(result));\n            return;\n          } catch (e) {\n            reject(e);\n            return;\n          }\n        },\n        onrejected: reason => {\n          if (!onrejected) {\n            reject(reason);\n            return;\n          }\n          try {\n            resolve(onrejected(reason));\n            return;\n          } catch (e) {\n            reject(e);\n            return;\n          }\n        },\n      });\n    });\n  }\n\n  /** JSDoc */\n  public catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,\n  ): PromiseLike<T | TResult> {\n    return this.then(val => val, onrejected);\n  }\n\n  /** JSDoc */\n  public finally<TResult>(onfinally?: (() => void) | null): PromiseLike<TResult> {\n    return new SyncPromise<TResult>((resolve, reject) => {\n      let val: TResult | any;\n      let isRejected: boolean;\n\n      return this.then(\n        value => {\n          isRejected = false;\n          val = value;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n        reason => {\n          isRejected = true;\n          val = reason;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n      ).then(() => {\n        if (isRejected) {\n          reject(val);\n          return;\n        }\n\n        resolve((val as unknown) as any);\n      });\n    });\n  }\n\n  /** JSDoc */\n  public toString(): string {\n    return '[object SyncPromise]';\n  }\n\n  /** JSDoc */\n  private readonly _resolve = (value?: T | PromiseLike<T> | null) => {\n    this._setResult(States.RESOLVED, value);\n  };\n\n  /** JSDoc */\n  private readonly _reject = (reason?: any) => {\n    this._setResult(States.REJECTED, reason);\n  };\n\n  /** JSDoc */\n  private readonly _setResult = (state: States, value?: T | PromiseLike<T> | any) => {\n    if (this._state !== States.PENDING) {\n      return;\n    }\n\n    if (isThenable(value)) {\n      (value as PromiseLike<T>).then(this._resolve, this._reject);\n      return;\n    }\n\n    this._state = state;\n    this._value = value;\n\n    this._executeHandlers();\n  };\n\n  // TODO: FIXME\n  /** JSDoc */\n  private readonly _attachHandler = (handler: {\n    /** JSDoc */\n    done: boolean;\n    /** JSDoc */\n    onfulfilled?(value: T): any;\n    /** JSDoc */\n    onrejected?(reason: any): any;\n  }) => {\n    this._handlers = this._handlers.concat(handler);\n    this._executeHandlers();\n  };\n\n  /** JSDoc */\n  private readonly _executeHandlers = () => {\n    if (this._state === States.PENDING) {\n      return;\n    }\n\n    const cachedHandlers = this._handlers.slice();\n    this._handlers = [];\n\n    cachedHandlers.forEach(handler => {\n      if (handler.done) {\n        return;\n      }\n\n      if (this._state === States.RESOLVED) {\n        if (handler.onfulfilled) {\n          // eslint-disable-next-line @typescript-eslint/no-floating-promises\n          handler.onfulfilled((this._value as unknown) as any);\n        }\n      }\n\n      if (this._state === States.REJECTED) {\n        if (handler.onrejected) {\n          handler.onrejected(this._value);\n        }\n      }\n\n      handler.done = true;\n    });\n  };\n}\n\nexport { SyncPromise };\n", "import { SentryError } from './error';\nimport { SyncPromise } from './syncpromise';\n\n/** A simple queue that holds promises. */\nexport class PromiseBuffer<T> {\n  /** Internal set of queued Promises */\n  private readonly _buffer: Array<PromiseLike<T>> = [];\n\n  public constructor(protected _limit?: number) {}\n\n  /**\n   * Says if the buffer is ready to take more requests\n   */\n  public isReady(): boolean {\n    return this._limit === undefined || this.length() < this._limit;\n  }\n\n  /**\n   * Add a promise to the queue.\n   *\n   * @param task Can be any PromiseLike<T>\n   * @returns The original promise.\n   */\n  public add(task: PromiseLike<T>): PromiseLike<T> {\n    if (!this.isReady()) {\n      return SyncPromise.reject(new SentryError('Not adding Promise due to buffer limit reached.'));\n    }\n    if (this._buffer.indexOf(task) === -1) {\n      this._buffer.push(task);\n    }\n    task\n      .then(() => this.remove(task))\n      .then(null, () =>\n        this.remove(task).then(null, () => {\n          // We have to add this catch here otherwise we have an unhandledPromiseRejection\n          // because it's a new Promise chain.\n        }),\n      );\n    return task;\n  }\n\n  /**\n   * Remove a promise to the queue.\n   *\n   * @param task Can be any PromiseLike<T>\n   * @returns Removed promise.\n   */\n  public remove(task: PromiseLike<T>): PromiseLike<T> {\n    const removedTask = this._buffer.splice(this._buffer.indexOf(task), 1)[0];\n    return removedTask;\n  }\n\n  /**\n   * This function returns the number of unresolved promises in the queue.\n   */\n  public length(): number {\n    return this._buffer.length;\n  }\n\n  /**\n   * This will drain the whole queue, returns true if queue is empty or drained.\n   * If timeout is provided and the queue takes longer to drain, the promise still resolves but with false.\n   *\n   * @param timeout Number in ms to wait until it resolves with false.\n   */\n  public drain(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise<boolean>(resolve => {\n      const capturedSetTimeout = setTimeout(() => {\n        if (timeout && timeout > 0) {\n          resolve(false);\n        }\n      }, timeout);\n      SyncPromise.all(this._buffer)\n        .then(() => {\n          clearTimeout(capturedSetTimeout);\n          resolve(true);\n        })\n        .then(null, () => {\n          resolve(true);\n        });\n    });\n  }\n}\n", "import { getGlobalObject } from './misc';\nimport { dynamicRequire, isNodeEnv } from './node';\n\n/**\n * An object that can return the current timestamp in seconds since the UNIX epoch.\n */\ninterface TimestampSource {\n  nowSeconds(): number;\n}\n\n/**\n * A TimestampSource implementation for environments that do not support the Performance Web API natively.\n *\n * Note that this TimestampSource does not use a monotonic clock. A call to `nowSeconds` may return a timestamp earlier\n * than a previously returned value. We do not try to emulate a monotonic behavior in order to facilitate debugging. It\n * is more obvious to explain \"why does my span have negative duration\" than \"why my spans have zero duration\".\n */\nconst dateTimestampSource: TimestampSource = {\n  nowSeconds: () => Date.now() / 1000,\n};\n\n/**\n * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}\n * for accessing a high resolution monotonic clock.\n */\ninterface Performance {\n  /**\n   * The millisecond timestamp at which measurement began, measured in Unix time.\n   */\n  timeOrigin: number;\n  /**\n   * Returns the current millisecond timestamp, where 0 represents the start of measurement.\n   */\n  now(): number;\n}\n\n/**\n * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not\n * support the API.\n *\n * Wrapping the native API works around differences in behavior from different browsers.\n */\nfunction getBrowserPerformance(): Performance | undefined {\n  const { performance } = getGlobalObject<Window>();\n  if (!performance || !performance.now) {\n    return undefined;\n  }\n\n  // Replace performance.timeOrigin with our own timeOrigin based on Date.now().\n  //\n  // This is a partial workaround for browsers reporting performance.timeOrigin such that performance.timeOrigin +\n  // performance.now() gives a date arbitrarily in the past.\n  //\n  // Additionally, computing timeOrigin in this way fills the gap for browsers where performance.timeOrigin is\n  // undefined.\n  //\n  // The assumption that performance.timeOrigin + performance.now() ~= Date.now() is flawed, but we depend on it to\n  // interact with data coming out of performance entries.\n  //\n  // Note that despite recommendations against it in the spec, browsers implement the Performance API with a clock that\n  // might stop when the computer is asleep (and perhaps under other circumstances). Such behavior causes\n  // performance.timeOrigin + performance.now() to have an arbitrary skew over Date.now(). In laptop computers, we have\n  // observed skews that can be as long as days, weeks or months.\n  //\n  // See https://github.com/getsentry/sentry-javascript/issues/2590.\n  //\n  // BUG: despite our best intentions, this workaround has its limitations. It mostly addresses timings of pageload\n  // transactions, but ignores the skew built up over time that can aversely affect timestamps of navigation\n  // transactions of long-lived web pages.\n  const timeOrigin = Date.now() - performance.now();\n\n  return {\n    now: () => performance.now(),\n    timeOrigin,\n  };\n}\n\n/**\n * Returns the native Performance API implementation from Node.js. Returns undefined in old Node.js versions that don't\n * implement the API.\n */\nfunction getNodePerformance(): Performance | undefined {\n  try {\n    const perfHooks = dynamicRequire(module, 'perf_hooks') as { performance: Performance };\n    return perfHooks.performance;\n  } catch (_) {\n    return undefined;\n  }\n}\n\n/**\n * The Performance API implementation for the current platform, if available.\n */\nconst platformPerformance: Performance | undefined = isNodeEnv() ? getNodePerformance() : getBrowserPerformance();\n\nconst timestampSource: TimestampSource =\n  platformPerformance === undefined\n    ? dateTimestampSource\n    : {\n        nowSeconds: () => (platformPerformance.timeOrigin + platformPerformance.now()) / 1000,\n      };\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using the Date API.\n */\nexport const dateTimestampInSeconds = dateTimestampSource.nowSeconds.bind(dateTimestampSource);\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the\n * availability of the Performance API.\n *\n * See `usingPerformanceAPI` to test whether the Performance API is used.\n *\n * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is\n * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The\n * skew can grow to arbitrary amounts like days, weeks or months.\n * See https://github.com/getsentry/sentry-javascript/issues/2590.\n */\nexport const timestampInSeconds = timestampSource.nowSeconds.bind(timestampSource);\n\n// Re-exported with an old name for backwards-compatibility.\nexport const timestampWithMs = timestampInSeconds;\n\n/**\n * A boolean that is true when timestampInSeconds uses the Performance API to produce monotonic timestamps.\n */\nexport const usingPerformanceAPI = platformPerformance !== undefined;\n\n/**\n * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the\n * performance API is available.\n */\nexport const browserPerformanceTimeOrigin = ((): number | undefined => {\n  const { performance } = getGlobalObject<Window>();\n  if (!performance) {\n    return undefined;\n  }\n  if (performance.timeOrigin) {\n    return performance.timeOrigin;\n  }\n  // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin\n  // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.\n  // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always\n  // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the\n  // Date API.\n  // eslint-disable-next-line deprecation/deprecation\n  return (performance.timing && performance.timing.navigationStart) || Date.now();\n})();\n", "/* eslint-disable max-lines */\nimport {\n  Breadcrumb,\n  CaptureContext,\n  Context,\n  Contexts,\n  Event,\n  EventHint,\n  EventProcessor,\n  Extra,\n  Extras,\n  Primitive,\n  Scope as ScopeInterface,\n  ScopeContext,\n  Severity,\n  Span,\n  Transaction,\n  User,\n} from '@sentry/types';\nimport { dateTimestampInSeconds, getGlobalObject, isPlainObject, isThenable, SyncPromise } from '@sentry/utils';\n\nimport { Session } from './session';\n\n/**\n * Holds additional event information. {@link Scope.applyToEvent} will be\n * called by the client before an event will be sent.\n */\nexport class Scope implements ScopeInterface {\n  /** Flag if notifiying is happening. */\n  protected _notifyingListeners: boolean = false;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void> = [];\n\n  /** Callback list that will be called after {@link applyToEvent}. */\n  protected _eventProcessors: EventProcessor[] = [];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[] = [];\n\n  /** User */\n  protected _user: User = {};\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive } = {};\n\n  /** Extra */\n  protected _extra: Extras = {};\n\n  /** Contexts */\n  protected _contexts: Contexts = {};\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  protected _level?: Severity;\n\n  /** Transaction Name */\n  protected _transactionName?: string;\n\n  /** Span */\n  protected _span?: Span;\n\n  /** Session */\n  protected _session?: Session;\n\n  /**\n   * Inherit values from the parent scope.\n   * @param scope to clone.\n   */\n  public static clone(scope?: Scope): Scope {\n    const newScope = new Scope();\n    if (scope) {\n      newScope._breadcrumbs = [...scope._breadcrumbs];\n      newScope._tags = { ...scope._tags };\n      newScope._extra = { ...scope._extra };\n      newScope._contexts = { ...scope._contexts };\n      newScope._user = scope._user;\n      newScope._level = scope._level;\n      newScope._span = scope._span;\n      newScope._session = scope._session;\n      newScope._transactionName = scope._transactionName;\n      newScope._fingerprint = scope._fingerprint;\n      newScope._eventProcessors = [...scope._eventProcessors];\n    }\n    return newScope;\n  }\n\n  /**\n   * Add internal on change listener. Used for sub SDKs that need to store the scope.\n   * @hidden\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): this {\n    this._user = user || {};\n    if (this._session) {\n      this._session.update({ user });\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setLevel(level: Severity): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Can be removed in major version.\n   * @deprecated in favor of {@link this.setTransactionName}\n   */\n  public setTransaction(name?: string): this {\n    return this.setTransactionName(name);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts = { ...this._contexts, [key]: context };\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSpan(span?: Span): this {\n    this._span = span;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSpan(): Span | undefined {\n    return this._span;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransaction(): Transaction | undefined {\n    // often, this span will be a transaction, but it's not guaranteed to be\n    const span = this.getSpan() as undefined | (Span & { spanRecorder: { spans: Span[] } });\n\n    // try it the new way first\n    if (span?.transaction) {\n      return span?.transaction;\n    }\n\n    // fallback to the old way (known bug: this only finds transactions with sampled = true)\n    if (span?.spanRecorder?.spans[0]) {\n      return span.spanRecorder.spans[0] as Transaction;\n    }\n\n    // neither way found a transaction\n    return undefined;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    if (typeof captureContext === 'function') {\n      const updatedScope = (captureContext as <T>(scope: T) => T)(this);\n      return updatedScope instanceof Scope ? updatedScope : this;\n    }\n\n    if (captureContext instanceof Scope) {\n      this._tags = { ...this._tags, ...captureContext._tags };\n      this._extra = { ...this._extra, ...captureContext._extra };\n      this._contexts = { ...this._contexts, ...captureContext._contexts };\n      if (captureContext._user && Object.keys(captureContext._user).length) {\n        this._user = captureContext._user;\n      }\n      if (captureContext._level) {\n        this._level = captureContext._level;\n      }\n      if (captureContext._fingerprint) {\n        this._fingerprint = captureContext._fingerprint;\n      }\n    } else if (isPlainObject(captureContext)) {\n      // eslint-disable-next-line no-param-reassign\n      captureContext = captureContext as ScopeContext;\n      this._tags = { ...this._tags, ...captureContext.tags };\n      this._extra = { ...this._extra, ...captureContext.extra };\n      this._contexts = { ...this._contexts, ...captureContext.contexts };\n      if (captureContext.user) {\n        this._user = captureContext.user;\n      }\n      if (captureContext.level) {\n        this._level = captureContext.level;\n      }\n      if (captureContext.fingerprint) {\n        this._fingerprint = captureContext.fingerprint;\n      }\n    }\n\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clear(): this {\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._span = undefined;\n    this._session = undefined;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const mergedBreadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n    };\n\n    this._breadcrumbs =\n      maxBreadcrumbs !== undefined && maxBreadcrumbs >= 0\n        ? [...this._breadcrumbs, mergedBreadcrumb].slice(-maxBreadcrumbs)\n        : [...this._breadcrumbs, mergedBreadcrumb];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Applies the current context and fingerprint to the event.\n   * Note that breadcrumbs will be added by the client.\n   * Also if the event has already breadcrumbs on it, we do not merge them.\n   * @param event Event\n   * @param hint May contain additional informartion about the original exception.\n   * @hidden\n   */\n  public applyToEvent(event: Event, hint?: EventHint): PromiseLike<Event | null> {\n    if (this._extra && Object.keys(this._extra).length) {\n      event.extra = { ...this._extra, ...event.extra };\n    }\n    if (this._tags && Object.keys(this._tags).length) {\n      event.tags = { ...this._tags, ...event.tags };\n    }\n    if (this._user && Object.keys(this._user).length) {\n      event.user = { ...this._user, ...event.user };\n    }\n    if (this._contexts && Object.keys(this._contexts).length) {\n      event.contexts = { ...this._contexts, ...event.contexts };\n    }\n    if (this._level) {\n      event.level = this._level;\n    }\n    if (this._transactionName) {\n      event.transaction = this._transactionName;\n    }\n    // We want to set the trace context for normal events only if there isn't already\n    // a trace context on the event. There is a product feature in place where we link\n    // errors with transaction and it relys on that.\n    if (this._span) {\n      event.contexts = { trace: this._span.getTraceContext(), ...event.contexts };\n      const transactionName = this._span.transaction?.name;\n      if (transactionName) {\n        event.tags = { transaction: transactionName, ...event.tags };\n      }\n    }\n\n    this._applyFingerprint(event);\n\n    event.breadcrumbs = [...(event.breadcrumbs || []), ...this._breadcrumbs];\n    event.breadcrumbs = event.breadcrumbs.length > 0 ? event.breadcrumbs : undefined;\n\n    return this._notifyEventProcessors([...getGlobalEventProcessors(), ...this._eventProcessors], event, hint);\n  }\n\n  /**\n   * This will be called after {@link applyToEvent} is finished.\n   */\n  protected _notifyEventProcessors(\n    processors: EventProcessor[],\n    event: Event | null,\n    hint?: EventHint,\n    index: number = 0,\n  ): PromiseLike<Event | null> {\n    return new SyncPromise<Event | null>((resolve, reject) => {\n      const processor = processors[index];\n      if (event === null || typeof processor !== 'function') {\n        resolve(event);\n      } else {\n        const result = processor({ ...event }, hint) as Event | null;\n        if (isThenable(result)) {\n          (result as PromiseLike<Event | null>)\n            .then(final => this._notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n            .then(null, reject);\n        } else {\n          this._notifyEventProcessors(processors, result, hint, index + 1)\n            .then(resolve)\n            .then(null, reject);\n        }\n      }\n    });\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n\n  /**\n   * Applies fingerprint from the scope to the event if there's one,\n   * uses message if there's one instead or get rid of empty fingerprint\n   */\n  private _applyFingerprint(event: Event): void {\n    // Make sure it's an array first and we actually have something in place\n    event.fingerprint = event.fingerprint\n      ? Array.isArray(event.fingerprint)\n        ? event.fingerprint\n        : [event.fingerprint]\n      : [];\n\n    // If we have something on the scope, then merge it with event\n    if (this._fingerprint) {\n      event.fingerprint = event.fingerprint.concat(this._fingerprint);\n    }\n\n    // If we have no data at all, remove empty array default\n    if (event.fingerprint && !event.fingerprint.length) {\n      delete event.fingerprint;\n    }\n  }\n}\n\n/**\n * Retruns the global event processors.\n */\nfunction getGlobalEventProcessors(): EventProcessor[] {\n  /* eslint-disable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access  */\n  const global = getGlobalObject<any>();\n  global.__SENTRY__ = global.__SENTRY__ || {};\n  global.__SENTRY__.globalEventProcessors = global.__SENTRY__.globalEventProcessors || [];\n  return global.__SENTRY__.globalEventProcessors;\n  /* eslint-enable @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access */\n}\n\n/**\n * Add a EventProcessor to be kept globally.\n * @param callback EventProcessor to add\n */\nexport function addGlobalEventProcessor(callback: EventProcessor): void {\n  getGlobalEventProcessors().push(callback);\n}\n", "import { Session as SessionInterface, SessionContext, SessionStatus } from '@sentry/types';\nimport { dropUndefinedKeys, uuid4 } from '@sentry/utils';\n\n/**\n * @inheritdoc\n */\nexport class Session implements SessionInterface {\n  public userAgent?: string;\n  public errors: number = 0;\n  public release?: string;\n  public sid: string = uuid4();\n  public did?: string;\n  public timestamp: number = Date.now();\n  public started: number = Date.now();\n  public duration: number = 0;\n  public status: SessionStatus = SessionStatus.Ok;\n  public environment?: string;\n  public ipAddress?: string;\n\n  constructor(context?: Omit<SessionContext, 'started' | 'status'>) {\n    if (context) {\n      this.update(context);\n    }\n  }\n\n  /** JSDoc */\n  // eslint-disable-next-line complexity\n  update(context: SessionContext = {}): void {\n    if (context.user) {\n      if (context.user.ip_address) {\n        this.ipAddress = context.user.ip_address;\n      }\n\n      if (!context.did) {\n        this.did = context.user.id || context.user.email || context.user.username;\n      }\n    }\n\n    this.timestamp = context.timestamp || Date.now();\n\n    if (context.sid) {\n      // Good enough uuid validation. — Kamil\n      this.sid = context.sid.length === 32 ? context.sid : uuid4();\n    }\n    if (context.did) {\n      this.did = `${context.did}`;\n    }\n    if (typeof context.started === 'number') {\n      this.started = context.started;\n    }\n    if (typeof context.duration === 'number') {\n      this.duration = context.duration;\n    } else {\n      this.duration = this.timestamp - this.started;\n    }\n    if (context.release) {\n      this.release = context.release;\n    }\n    if (context.environment) {\n      this.environment = context.environment;\n    }\n    if (context.ipAddress) {\n      this.ipAddress = context.ipAddress;\n    }\n    if (context.userAgent) {\n      this.userAgent = context.userAgent;\n    }\n    if (typeof context.errors === 'number') {\n      this.errors = context.errors;\n    }\n    if (context.status) {\n      this.status = context.status;\n    }\n  }\n\n  /** JSDoc */\n  close(status?: Exclude<SessionStatus, SessionStatus.Ok>): void {\n    if (status) {\n      this.update({ status });\n    } else if (this.status === SessionStatus.Ok) {\n      this.update({ status: SessionStatus.Exited });\n    } else {\n      this.update();\n    }\n  }\n\n  /** JSDoc */\n  toJSON(): {\n    init: boolean;\n    sid: string;\n    did?: string;\n    timestamp: string;\n    started: string;\n    duration: number;\n    status: SessionStatus;\n    errors: number;\n    attrs?: {\n      release?: string;\n      environment?: string;\n      user_agent?: string;\n      ip_address?: string;\n    };\n  } {\n    return dropUndefinedKeys({\n      sid: `${this.sid}`,\n      init: true,\n      started: new Date(this.started).toISOString(),\n      timestamp: new Date(this.timestamp).toISOString(),\n      status: this.status,\n      errors: this.errors,\n      did: typeof this.did === 'number' || typeof this.did === 'string' ? `${this.did}` : undefined,\n      duration: this.duration,\n      attrs: dropUndefinedKeys({\n        release: this.release,\n        environment: this.environment,\n        ip_address: this.ipAddress,\n        user_agent: this.userAgent,\n      }),\n    });\n  }\n}\n", "/* eslint-disable max-lines */\nimport {\n  Breadcrumb,\n  BreadcrumbHint,\n  Client,\n  CustomSamplingContext,\n  Event,\n  EventHint,\n  Extra,\n  Extras,\n  Hub as HubInterface,\n  Integration,\n  IntegrationClass,\n  Primitive,\n  SessionContext,\n  Severity,\n  Span,\n  SpanContext,\n  Transaction,\n  TransactionContext,\n  User,\n} from '@sentry/types';\nimport { consoleSandbox, dateTimestampInSeconds, getGlobalObject, isNodeEnv, logger, uuid4 } from '@sentry/utils';\n\nimport { Carrier, DomainAsCarrier, Layer } from './interfaces';\nimport { Scope } from './scope';\nimport { Session } from './session';\n\n/**\n * API compatibility version of this hub.\n *\n * WARNING: This number should only be increased when the global interface\n * changes and new methods are introduced.\n *\n * @hidden\n */\nexport const API_VERSION = 3;\n\n/**\n * Default maximum number of breadcrumbs added to an event. Can be overwritten\n * with {@link Options.maxBreadcrumbs}.\n */\nconst DEFAULT_BREADCRUMBS = 100;\n\n/**\n * Absolute maximum number of breadcrumbs added to an event. The\n * `maxBreadcrumbs` option cannot be higher than this value.\n */\nconst MAX_BREADCRUMBS = 100;\n\n/**\n * @inheritDoc\n */\nexport class Hub implements HubInterface {\n  /** Is a {@link Layer}[] containing the client and scope */\n  private readonly _stack: Layer[] = [{}];\n\n  /** Contains the last event id of a captured event.  */\n  private _lastEventId?: string;\n\n  /**\n   * Creates a new instance of the hub, will push one {@link Layer} into the\n   * internal stack on creation.\n   *\n   * @param client bound to the hub.\n   * @param scope bound to the hub.\n   * @param version number, higher number means higher priority.\n   */\n  public constructor(client?: Client, scope: Scope = new Scope(), private readonly _version: number = API_VERSION) {\n    this.getStackTop().scope = scope;\n    this.bindClient(client);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public isOlderThan(version: number): boolean {\n    return this._version < version;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public bindClient(client?: Client): void {\n    const top = this.getStackTop();\n    top.client = client;\n    if (client && client.setupIntegrations) {\n      client.setupIntegrations();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public pushScope(): Scope {\n    // We want to clone the content of prev scope\n    const scope = Scope.clone(this.getScope());\n    this.getStack().push({\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public popScope(): boolean {\n    if (this.getStack().length <= 1) return false;\n    return !!this.getStack().pop();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public withScope(callback: (scope: Scope) => void): void {\n    const scope = this.pushScope();\n    try {\n      callback(scope);\n    } finally {\n      this.popScope();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this.getStackTop().client as C;\n  }\n\n  /** Returns the scope of the top stack. */\n  public getScope(): Scope | undefined {\n    return this.getStackTop().scope;\n  }\n\n  /** Returns the scope stack for domains or the process. */\n  public getStack(): Layer[] {\n    return this._stack;\n  }\n\n  /** Returns the topmost scope layer in the order domain > local > process. */\n  public getStackTop(): Layer {\n    return this._stack[this._stack.length - 1];\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint): string {\n    const eventId = (this._lastEventId = uuid4());\n    let finalHint = hint;\n\n    // If there's no explicit hint provided, mimick the same thing that would happen\n    // in the minimal itself to create a consistent behavior.\n    // We don't do this in the client, as it's the lowest level API, and doing this,\n    // would prevent user from having full control over direct calls.\n    if (!hint) {\n      let syntheticException: Error;\n      try {\n        throw new Error('Sentry syntheticException');\n      } catch (exception) {\n        syntheticException = exception as Error;\n      }\n      finalHint = {\n        originalException: exception,\n        syntheticException,\n      };\n    }\n\n    this._invokeClient('captureException', exception, {\n      ...finalHint,\n      event_id: eventId,\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(message: string, level?: Severity, hint?: EventHint): string {\n    const eventId = (this._lastEventId = uuid4());\n    let finalHint = hint;\n\n    // If there's no explicit hint provided, mimick the same thing that would happen\n    // in the minimal itself to create a consistent behavior.\n    // We don't do this in the client, as it's the lowest level API, and doing this,\n    // would prevent user from having full control over direct calls.\n    if (!hint) {\n      let syntheticException: Error;\n      try {\n        throw new Error(message);\n      } catch (exception) {\n        syntheticException = exception as Error;\n      }\n      finalHint = {\n        originalException: message,\n        syntheticException,\n      };\n    }\n\n    this._invokeClient('captureMessage', message, level, {\n      ...finalHint,\n      event_id: eventId,\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = (this._lastEventId = uuid4());\n    this._invokeClient('captureEvent', event, {\n      ...hint,\n      event_id: eventId,\n    });\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void {\n    const { scope, client } = this.getStackTop();\n\n    if (!scope || !client) return;\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const { beforeBreadcrumb = null, maxBreadcrumbs = DEFAULT_BREADCRUMBS } =\n      (client.getOptions && client.getOptions()) || {};\n\n    if (maxBreadcrumbs <= 0) return;\n\n    const timestamp = dateTimestampInSeconds();\n    const mergedBreadcrumb = { timestamp, ...breadcrumb };\n    const finalBreadcrumb = beforeBreadcrumb\n      ? (consoleSandbox(() => beforeBreadcrumb(mergedBreadcrumb, hint)) as Breadcrumb | null)\n      : mergedBreadcrumb;\n\n    if (finalBreadcrumb === null) return;\n\n    scope.addBreadcrumb(finalBreadcrumb, Math.min(maxBreadcrumbs, MAX_BREADCRUMBS));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setUser(user: User | null): void {\n    const scope = this.getScope();\n    if (scope) scope.setUser(user);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTags(tags: { [key: string]: Primitive }): void {\n    const scope = this.getScope();\n    if (scope) scope.setTags(tags);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtras(extras: Extras): void {\n    const scope = this.getScope();\n    if (scope) scope.setExtras(extras);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): void {\n    const scope = this.getScope();\n    if (scope) scope.setTag(key, value);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setExtra(key: string, extra: Extra): void {\n    const scope = this.getScope();\n    if (scope) scope.setExtra(key, extra);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public setContext(name: string, context: { [key: string]: any } | null): void {\n    const scope = this.getScope();\n    if (scope) scope.setContext(name, context);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public configureScope(callback: (scope: Scope) => void): void {\n    const { scope, client } = this.getStackTop();\n    if (scope && client) {\n      callback(scope);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public run(callback: (hub: Hub) => void): void {\n    const oldHub = makeMain(this);\n    try {\n      callback(this);\n    } finally {\n      makeMain(oldHub);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    const client = this.getClient();\n    if (!client) return null;\n    try {\n      return client.getIntegration(integration);\n    } catch (_oO) {\n      logger.warn(`Cannot retrieve integration ${integration.id} from the current Hub`);\n      return null;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startSpan(context: SpanContext): Span {\n    return this._callExtensionMethod('startSpan', context);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startTransaction(context: TransactionContext, customSamplingContext?: CustomSamplingContext): Transaction {\n    return this._callExtensionMethod('startTransaction', context, customSamplingContext);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public traceHeaders(): { [key: string]: string } {\n    return this._callExtensionMethod<{ [key: string]: string }>('traceHeaders');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startSession(context?: SessionContext): Session {\n    // End existing session if there's one\n    this.endSession();\n\n    const { scope, client } = this.getStackTop();\n    const { release, environment } = (client && client.getOptions()) || {};\n    const session = new Session({\n      release,\n      environment,\n      ...(scope && { user: scope.getUser() }),\n      ...context,\n    });\n    if (scope) {\n      scope.setSession(session);\n    }\n    return session;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public endSession(): void {\n    const { scope, client } = this.getStackTop();\n    if (!scope) return;\n\n    const session = scope.getSession && scope.getSession();\n    if (session) {\n      session.close();\n      if (client && client.captureSession) {\n        client.captureSession(session);\n      }\n      scope.setSession();\n    }\n  }\n\n  /**\n   * Internal helper function to call a method on the top client if it exists.\n   *\n   * @param method The method to call on the client.\n   * @param args Arguments to pass to the client function.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _invokeClient<M extends keyof Client>(method: M, ...args: any[]): void {\n    const { scope, client } = this.getStackTop();\n    if (client && client[method]) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n      (client as any)[method](...args, scope);\n    }\n  }\n\n  /**\n   * Calls global extension method and binding current instance to the function call\n   */\n  // @ts-ignore Function lacks ending return statement and return type does not include 'undefined'. ts(2366)\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _callExtensionMethod<T>(method: string, ...args: any[]): T {\n    const carrier = getMainCarrier();\n    const sentry = carrier.__SENTRY__;\n    if (sentry && sentry.extensions && typeof sentry.extensions[method] === 'function') {\n      return sentry.extensions[method].apply(this, args);\n    }\n    logger.warn(`Extension method ${method} couldn't be found, doing nothing.`);\n  }\n}\n\n/** Returns the global shim registry. */\nexport function getMainCarrier(): Carrier {\n  const carrier = getGlobalObject();\n  carrier.__SENTRY__ = carrier.__SENTRY__ || {\n    extensions: {},\n    hub: undefined,\n  };\n  return carrier;\n}\n\n/**\n * Replaces the current main hub with the passed one on the global object\n *\n * @returns The old replaced hub\n */\nexport function makeMain(hub: Hub): Hub {\n  const registry = getMainCarrier();\n  const oldHub = getHubFromCarrier(registry);\n  setHubOnCarrier(registry, hub);\n  return oldHub;\n}\n\n/**\n * Returns the default hub instance.\n *\n * If a hub is already registered in the global carrier but this module\n * contains a more recent version, it replaces the registered version.\n * Otherwise, the currently registered hub will be returned.\n */\nexport function getCurrentHub(): Hub {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n\n  // If there's no hub, or its an old API, assign a new one\n  if (!hasHubOnCarrier(registry) || getHubFromCarrier(registry).isOlderThan(API_VERSION)) {\n    setHubOnCarrier(registry, new Hub());\n  }\n\n  // Prefer domains over global if they are there (applicable only to Node environment)\n  if (isNodeEnv()) {\n    return getHubFromActiveDomain(registry);\n  }\n  // Return hub that lives on a global object\n  return getHubFromCarrier(registry);\n}\n\n/**\n * Returns the active domain, if one exists\n *\n * @returns The domain, or undefined if there is no active domain\n */\nexport function getActiveDomain(): DomainAsCarrier | undefined {\n  const sentry = getMainCarrier().__SENTRY__;\n\n  return sentry && sentry.extensions && sentry.extensions.domain && sentry.extensions.domain.active;\n}\n\n/**\n * Try to read the hub from an active domain, and fallback to the registry if one doesn't exist\n * @returns discovered hub\n */\nfunction getHubFromActiveDomain(registry: Carrier): Hub {\n  try {\n    const activeDomain = getActiveDomain();\n\n    // If there's no active domain, just return global hub\n    if (!activeDomain) {\n      return getHubFromCarrier(registry);\n    }\n\n    // If there's no hub on current domain, or it's an old API, assign a new one\n    if (!hasHubOnCarrier(activeDomain) || getHubFromCarrier(activeDomain).isOlderThan(API_VERSION)) {\n      const registryHubTopStack = getHubFromCarrier(registry).getStackTop();\n      setHubOnCarrier(activeDomain, new Hub(registryHubTopStack.client, Scope.clone(registryHubTopStack.scope)));\n    }\n\n    // Return hub that lives on a domain\n    return getHubFromCarrier(activeDomain);\n  } catch (_Oo) {\n    // Return hub that lives on a global object\n    return getHubFromCarrier(registry);\n  }\n}\n\n/**\n * This will tell whether a carrier has a hub on it or not\n * @param carrier object\n */\nfunction hasHubOnCarrier(carrier: Carrier): boolean {\n  return !!(carrier && carrier.__SENTRY__ && carrier.__SENTRY__.hub);\n}\n\n/**\n * This will create a new {@link Hub} and add to the passed object on\n * __SENTRY__.hub.\n * @param carrier object\n * @hidden\n */\nexport function getHubFromCarrier(carrier: Carrier): Hub {\n  if (carrier && carrier.__SENTRY__ && carrier.__SENTRY__.hub) return carrier.__SENTRY__.hub;\n  carrier.__SENTRY__ = carrier.__SENTRY__ || {};\n  carrier.__SENTRY__.hub = new Hub();\n  return carrier.__SENTRY__.hub;\n}\n\n/**\n * This will set passed {@link Hub} on the passed object's __SENTRY__.hub attribute\n * @param carrier object\n * @param hub Hub\n */\nexport function setHubOnCarrier(carrier: Carrier, hub: Hub): boolean {\n  if (!carrier) return false;\n  carrier.__SENTRY__ = carrier.__SENTRY__ || {};\n  carrier.__SENTRY__.hub = hub;\n  return true;\n}\n", "import { getCur<PERSON>H<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@sentry/hub';\nimport {\n  Breadcrumb,\n  CaptureContext,\n  CustomSamplingContext,\n  Event,\n  Extra,\n  Extras,\n  Primitive,\n  Severity,\n  Transaction,\n  TransactionContext,\n  User,\n} from '@sentry/types';\n\n/**\n * This calls a function on the current hub.\n * @param method function to call on hub.\n * @param args to pass to function.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction callOnHub<T>(method: string, ...args: any[]): T {\n  const hub = getCurrentHub();\n  if (hub && hub[method as keyof Hub]) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return (hub[method as keyof Hub] as any)(...args);\n  }\n  throw new Error(`No hub defined or ${method} was not found on the hub, please open a bug report.`);\n}\n\n/**\n * Captures an exception event and sends it to Sentry.\n *\n * @param exception An exception-like object.\n * @returns The generated eventId.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nexport function captureException(exception: any, captureContext?: CaptureContext): string {\n  let syntheticException: Error;\n  try {\n    throw new Error('Sentry syntheticException');\n  } catch (exception) {\n    syntheticException = exception as Error;\n  }\n  return callOnHub('captureException', exception, {\n    captureContext,\n    originalException: exception,\n    syntheticException,\n  });\n}\n\n/**\n * Captures a message event and sends it to Sentry.\n *\n * @param message The message to send to Sentry.\n * @param level Define the level of the message.\n * @returns The generated eventId.\n */\nexport function captureMessage(message: string, captureContext?: CaptureContext | Severity): string {\n  let syntheticException: Error;\n  try {\n    throw new Error(message);\n  } catch (exception) {\n    syntheticException = exception as Error;\n  }\n\n  // This is necessary to provide explicit scopes upgrade, without changing the original\n  // arity of the `captureMessage(message, level)` method.\n  const level = typeof captureContext === 'string' ? captureContext : undefined;\n  const context = typeof captureContext !== 'string' ? { captureContext } : undefined;\n\n  return callOnHub('captureMessage', message, level, {\n    originalException: message,\n    syntheticException,\n    ...context,\n  });\n}\n\n/**\n * Captures a manually created event and sends it to Sentry.\n *\n * @param event The event to send to Sentry.\n * @returns The generated eventId.\n */\nexport function captureEvent(event: Event): string {\n  return callOnHub('captureEvent', event);\n}\n\n/**\n * Callback to set context information onto the scope.\n * @param callback Callback function that receives Scope.\n */\nexport function configureScope(callback: (scope: Scope) => void): void {\n  callOnHub<void>('configureScope', callback);\n}\n\n/**\n * Records a new breadcrumb which will be attached to future events.\n *\n * Breadcrumbs will be added to subsequent events to provide more context on\n * user's actions prior to an error or crash.\n *\n * @param breadcrumb The breadcrumb to record.\n */\nexport function addBreadcrumb(breadcrumb: Breadcrumb): void {\n  callOnHub<void>('addBreadcrumb', breadcrumb);\n}\n\n/**\n * Sets context data with the given name.\n * @param name of the context\n * @param context Any kind of data. This data will be normalized.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function setContext(name: string, context: { [key: string]: any } | null): void {\n  callOnHub<void>('setContext', name, context);\n}\n\n/**\n * Set an object that will be merged sent as extra data with the event.\n * @param extras Extras object to merge into current context.\n */\nexport function setExtras(extras: Extras): void {\n  callOnHub<void>('setExtras', extras);\n}\n\n/**\n * Set an object that will be merged sent as tags data with the event.\n * @param tags Tags context object to merge into current context.\n */\nexport function setTags(tags: { [key: string]: Primitive }): void {\n  callOnHub<void>('setTags', tags);\n}\n\n/**\n * Set key:value that will be sent as extra data with the event.\n * @param key String of extra\n * @param extra Any kind of data. This data will be normalized.\n */\nexport function setExtra(key: string, extra: Extra): void {\n  callOnHub<void>('setExtra', key, extra);\n}\n\n/**\n * Set key:value that will be sent as tags data with the event.\n *\n * Can also be used to unset a tag, by passing `undefined`.\n *\n * @param key String key of tag\n * @param value Value of tag\n */\nexport function setTag(key: string, value: Primitive): void {\n  callOnHub<void>('setTag', key, value);\n}\n\n/**\n * Updates user context information for future events.\n *\n * @param user User context object to be set in the current context. Pass `null` to unset the user.\n */\nexport function setUser(user: User | null): void {\n  callOnHub<void>('setUser', user);\n}\n\n/**\n * Creates a new scope with and executes the given operation within.\n * The scope is automatically removed once the operation\n * finishes or throws.\n *\n * This is essentially a convenience function for:\n *\n *     pushScope();\n *     callback();\n *     popScope();\n *\n * @param callback that will be enclosed into push/popScope.\n */\nexport function withScope(callback: (scope: Scope) => void): void {\n  callOnHub<void>('withScope', callback);\n}\n\n/**\n * Calls a function on the latest client. Use this with caution, it's meant as\n * in \"internal\" helper so we don't need to expose every possible function in\n * the shim. It is not guaranteed that the client actually implements the\n * function.\n *\n * @param method The method to call on the client/client.\n * @param args Arguments to pass to the client/fontend.\n * @hidden\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function _callOnClient(method: string, ...args: any[]): void {\n  callOnHub<void>('_invokeClient', method, ...args);\n}\n\n/**\n * Starts a new `Transaction` and returns it. This is the entry point to manual tracing instrumentation.\n *\n * A tree structure can be built by adding child spans to the transaction, and child spans to other spans. To start a\n * new child span within the transaction or any span, call the respective `.startChild()` method.\n *\n * Every child span must be finished before the transaction is finished, otherwise the unfinished spans are discarded.\n *\n * The transaction must be finished with a call to its `.finish()` method, at which point the transaction with all its\n * finished child spans will be sent to Sentry.\n *\n * @param context Properties of the new `Transaction`.\n * @param customSamplingContext Information given to the transaction sampling function (along with context-dependent\n * default values). See {@link Options.tracesSampler}.\n *\n * @returns The transaction which was just started\n */\nexport function startTransaction(\n  context: TransactionContext,\n  customSamplingContext?: CustomSamplingContext,\n): Transaction {\n  return callOnHub('startTransaction', { ...context }, customSamplingContext);\n}\n", "import { Dsn<PERSON><PERSON> } from '@sentry/types';\nimport { Dsn, urlEncode } from '@sentry/utils';\n\nconst SENTRY_API_VERSION = '7';\n\n/** Helper class to provide urls to different Sentry endpoints. */\nexport class API {\n  /** The internally used Dsn object. */\n  private readonly _dsnObject: Dsn;\n  /** Create a new instance of API */\n  public constructor(public dsn: DsnLike) {\n    this._dsnObject = new Dsn(dsn);\n  }\n\n  /** Returns the Dsn object. */\n  public getDsn(): Dsn {\n    return this._dsnObject;\n  }\n\n  /** Returns the prefix to construct Sentry ingestion API endpoints. */\n  public getBaseApiEndpoint(): string {\n    const dsn = this._dsnObject;\n    const protocol = dsn.protocol ? `${dsn.protocol}:` : '';\n    const port = dsn.port ? `:${dsn.port}` : '';\n    return `${protocol}//${dsn.host}${port}${dsn.path ? `/${dsn.path}` : ''}/api/`;\n  }\n\n  /** Returns the store endpoint URL. */\n  public getStoreEndpoint(): string {\n    return this._getIngestEndpoint('store');\n  }\n\n  /**\n   * Returns the store endpoint URL with auth in the query string.\n   *\n   * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n   */\n  public getStoreEndpointWithUrlEncodedAuth(): string {\n    return `${this.getStoreEndpoint()}?${this._encodedAuth()}`;\n  }\n\n  /**\n   * Returns the envelope endpoint URL with auth in the query string.\n   *\n   * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n   */\n  public getEnvelopeEndpointWithUrlEncodedAuth(): string {\n    return `${this._getEnvelopeEndpoint()}?${this._encodedAuth()}`;\n  }\n\n  /** Returns only the path component for the store endpoint. */\n  public getStoreEndpointPath(): string {\n    const dsn = this._dsnObject;\n    return `${dsn.path ? `/${dsn.path}` : ''}/api/${dsn.projectId}/store/`;\n  }\n\n  /**\n   * Returns an object that can be used in request headers.\n   * This is needed for node and the old /store endpoint in sentry\n   */\n  public getRequestHeaders(clientName: string, clientVersion: string): { [key: string]: string } {\n    const dsn = this._dsnObject;\n    const header = [`Sentry sentry_version=${SENTRY_API_VERSION}`];\n    header.push(`sentry_client=${clientName}/${clientVersion}`);\n    header.push(`sentry_key=${dsn.user}`);\n    if (dsn.pass) {\n      header.push(`sentry_secret=${dsn.pass}`);\n    }\n    return {\n      'Content-Type': 'application/json',\n      'X-Sentry-Auth': header.join(', '),\n    };\n  }\n\n  /** Returns the url to the report dialog endpoint. */\n  public getReportDialogEndpoint(\n    dialogOptions: {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      [key: string]: any;\n      user?: { name?: string; email?: string };\n    } = {},\n  ): string {\n    const dsn = this._dsnObject;\n    const endpoint = `${this.getBaseApiEndpoint()}embed/error-page/`;\n\n    const encodedOptions = [];\n    encodedOptions.push(`dsn=${dsn.toString()}`);\n    for (const key in dialogOptions) {\n      if (key === 'dsn') {\n        continue;\n      }\n\n      if (key === 'user') {\n        if (!dialogOptions.user) {\n          continue;\n        }\n        if (dialogOptions.user.name) {\n          encodedOptions.push(`name=${encodeURIComponent(dialogOptions.user.name)}`);\n        }\n        if (dialogOptions.user.email) {\n          encodedOptions.push(`email=${encodeURIComponent(dialogOptions.user.email)}`);\n        }\n      } else {\n        encodedOptions.push(`${encodeURIComponent(key)}=${encodeURIComponent(dialogOptions[key] as string)}`);\n      }\n    }\n    if (encodedOptions.length) {\n      return `${endpoint}?${encodedOptions.join('&')}`;\n    }\n\n    return endpoint;\n  }\n\n  /** Returns the envelope endpoint URL. */\n  private _getEnvelopeEndpoint(): string {\n    return this._getIngestEndpoint('envelope');\n  }\n\n  /** Returns the ingest API endpoint for target. */\n  private _getIngestEndpoint(target: 'store' | 'envelope'): string {\n    const base = this.getBaseApiEndpoint();\n    const dsn = this._dsnObject;\n    return `${base}${dsn.projectId}/${target}/`;\n  }\n\n  /** Returns a URL-encoded string with auth config suitable for a query string. */\n  private _encodedAuth(): string {\n    const dsn = this._dsnObject;\n    const auth = {\n      // We send only the minimum set of required information. See\n      // https://github.com/getsentry/sentry-javascript/issues/2572.\n      sentry_key: dsn.user,\n      sentry_version: SENTRY_API_VERSION,\n    };\n    return urlEncode(auth);\n  }\n}\n", "import { addGlobalEventProcessor, getCurrentHub } from '@sentry/hub';\nimport { Integration, Options } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\nexport const installedIntegrations: string[] = [];\n\n/** Map of integrations assigned to a client */\nexport interface IntegrationIndex {\n  [key: string]: Integration;\n}\n\n/** Gets integration to install */\nexport function getIntegrationsToSetup(options: Options): Integration[] {\n  const defaultIntegrations = (options.defaultIntegrations && [...options.defaultIntegrations]) || [];\n  const userIntegrations = options.integrations;\n  let integrations: Integration[] = [];\n  if (Array.isArray(userIntegrations)) {\n    const userIntegrationsNames = userIntegrations.map(i => i.name);\n    const pickedIntegrationsNames: string[] = [];\n\n    // Leave only unique default integrations, that were not overridden with provided user integrations\n    defaultIntegrations.forEach(defaultIntegration => {\n      if (\n        userIntegrationsNames.indexOf(defaultIntegration.name) === -1 &&\n        pickedIntegrationsNames.indexOf(defaultIntegration.name) === -1\n      ) {\n        integrations.push(defaultIntegration);\n        pickedIntegrationsNames.push(defaultIntegration.name);\n      }\n    });\n\n    // Don't add same user integration twice\n    userIntegrations.forEach(userIntegration => {\n      if (pickedIntegrationsNames.indexOf(userIntegration.name) === -1) {\n        integrations.push(userIntegration);\n        pickedIntegrationsNames.push(userIntegration.name);\n      }\n    });\n  } else if (typeof userIntegrations === 'function') {\n    integrations = userIntegrations(defaultIntegrations);\n    integrations = Array.isArray(integrations) ? integrations : [integrations];\n  } else {\n    integrations = [...defaultIntegrations];\n  }\n\n  // Make sure that if present, `Debug` integration will always run last\n  const integrationsNames = integrations.map(i => i.name);\n  const alwaysLastToRun = 'Debug';\n  if (integrationsNames.indexOf(alwaysLastToRun) !== -1) {\n    integrations.push(...integrations.splice(integrationsNames.indexOf(alwaysLastToRun), 1));\n  }\n\n  return integrations;\n}\n\n/** Setup given integration */\nexport function setupIntegration(integration: Integration): void {\n  if (installedIntegrations.indexOf(integration.name) !== -1) {\n    return;\n  }\n  integration.setupOnce(addGlobalEventProcessor, getCurrentHub);\n  installedIntegrations.push(integration.name);\n  logger.log(`Integration installed: ${integration.name}`);\n}\n\n/**\n * Given a list of integration instances this installs them all. When `withDefaults` is set to `true` then all default\n * integrations are added unless they were already provided before.\n * @param integrations array of integration instances\n * @param withDefault should enable default integrations\n */\nexport function setupIntegrations<O extends Options>(options: O): IntegrationIndex {\n  const integrations: IntegrationIndex = {};\n  getIntegrationsToSetup(options).forEach(integration => {\n    integrations[integration.name] = integration;\n    setupIntegration(integration);\n  });\n  return integrations;\n}\n", "/* eslint-disable max-lines */\nimport { Scope, Session } from '@sentry/hub';\nimport {\n  Client,\n  Event,\n  EventHint,\n  Integration,\n  IntegrationClass,\n  Options,\n  SessionStatus,\n  Severity,\n} from '@sentry/types';\nimport {\n  dateTimestampInSeconds,\n  Dsn,\n  isPrimitive,\n  isThenable,\n  logger,\n  normalize,\n  SentryError,\n  SyncPromise,\n  truncate,\n  uuid4,\n} from '@sentry/utils';\n\nimport { Backend, BackendClass } from './basebackend';\nimport { IntegrationIndex, setupIntegrations } from './integration';\n\n/**\n * Base implementation for all JavaScript SDK clients.\n *\n * Call the constructor with the corresponding backend constructor and options\n * specific to the client subclass. To access these options later, use\n * {@link Client.getOptions}. Also, the Backend instance is available via\n * {@link Client.getBackend}.\n *\n * If a Dsn is specified in the options, it will be parsed and stored. Use\n * {@link Client.getDsn} to retrieve the Dsn at any moment. In case the Dsn is\n * invalid, the constructor will throw a {@link SentryException}. Note that\n * without a valid Dsn, the SDK will not send any events to Sentry.\n *\n * Before sending an event via the backend, it is passed through\n * {@link BaseClient.prepareEvent} to add SDK information and scope data\n * (breadcrumbs and context). To add more custom information, override this\n * method and extend the resulting prepared event.\n *\n * To issue automatically created events (e.g. via instrumentation), use\n * {@link Client.captureEvent}. It will prepare the event and pass it through\n * the callback lifecycle. To issue auto-breadcrumbs, use\n * {@link Client.addBreadcrumb}.\n *\n * @example\n * class NodeClient extends BaseClient<NodeBackend, NodeOptions> {\n *   public constructor(options: NodeOptions) {\n *     super(NodeBackend, options);\n *   }\n *\n *   // ...\n * }\n */\nexport abstract class BaseClient<B extends Backend, O extends Options> implements Client<O> {\n  /**\n   * The backend used to physically interact in the environment. Usually, this\n   * will correspond to the client. When composing SDKs, however, the Backend\n   * from the root SDK will be used.\n   */\n  protected readonly _backend: B;\n\n  /** Options passed to the SDK. */\n  protected readonly _options: O;\n\n  /** The client Dsn, if specified in options. Without this Dsn, the SDK will be disabled. */\n  protected readonly _dsn?: Dsn;\n\n  /** Array of used integrations. */\n  protected _integrations: IntegrationIndex = {};\n\n  /** Number of call being processed */\n  protected _processing: number = 0;\n\n  /**\n   * Initializes this client instance.\n   *\n   * @param backendClass A constructor function to create the backend.\n   * @param options Options for the client.\n   */\n  protected constructor(backendClass: BackendClass<B, O>, options: O) {\n    this._backend = new backendClass(options);\n    this._options = options;\n\n    if (options.dsn) {\n      this._dsn = new Dsn(options.dsn);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint, scope?: Scope): string | undefined {\n    let eventId: string | undefined = hint && hint.event_id;\n\n    this._process(\n      this._getBackend()\n        .eventFromException(exception, hint)\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(message: string, level?: Severity, hint?: EventHint, scope?: Scope): string | undefined {\n    let eventId: string | undefined = hint && hint.event_id;\n\n    const promisedEvent = isPrimitive(message)\n      ? this._getBackend().eventFromMessage(String(message), level, hint)\n      : this._getBackend().eventFromException(message, hint);\n\n    this._process(\n      promisedEvent\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string | undefined {\n    let eventId: string | undefined = hint && hint.event_id;\n\n    this._process(\n      this._captureEvent(event, hint, scope).then(result => {\n        eventId = result;\n      }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureSession(session: Session): void {\n    if (!session.release) {\n      logger.warn('Discarded session because of missing release');\n    } else {\n      this._sendSession(session);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getDsn(): Dsn | undefined {\n    return this._dsn;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getOptions(): O {\n    return this._options;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public flush(timeout?: number): PromiseLike<boolean> {\n    return this._isClientProcessing(timeout).then(ready => {\n      return this._getBackend()\n        .getTransport()\n        .close(timeout)\n        .then(transportFlushed => ready && transportFlushed);\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    return this.flush(timeout).then(result => {\n      this.getOptions().enabled = false;\n      return result;\n    });\n  }\n\n  /**\n   * Sets up the integrations\n   */\n  public setupIntegrations(): void {\n    if (this._isEnabled()) {\n      this._integrations = setupIntegrations(this._options);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    try {\n      return (this._integrations[integration.id] as T) || null;\n    } catch (_oO) {\n      logger.warn(`Cannot retrieve integration ${integration.id} from the current Client`);\n      return null;\n    }\n  }\n\n  /** Updates existing session based on the provided event */\n  protected _updateSessionFromEvent(session: Session, event: Event): void {\n    let crashed = false;\n    let errored = false;\n    let userAgent;\n    const exceptions = event.exception && event.exception.values;\n\n    if (exceptions) {\n      errored = true;\n\n      for (const ex of exceptions) {\n        const mechanism = ex.mechanism;\n        if (mechanism && mechanism.handled === false) {\n          crashed = true;\n          break;\n        }\n      }\n    }\n\n    const user = event.user;\n    if (!session.userAgent) {\n      const headers = event.request ? event.request.headers : {};\n      for (const key in headers) {\n        if (key.toLowerCase() === 'user-agent') {\n          userAgent = headers[key];\n          break;\n        }\n      }\n    }\n\n    session.update({\n      ...(crashed && { status: SessionStatus.Crashed }),\n      user,\n      userAgent,\n      errors: session.errors + Number(errored || crashed),\n    });\n  }\n\n  /** Deliver captured session to Sentry */\n  protected _sendSession(session: Session): void {\n    this._getBackend().sendSession(session);\n  }\n\n  /** Waits for the client to be done with processing. */\n  protected _isClientProcessing(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise(resolve => {\n      let ticked: number = 0;\n      const tick: number = 1;\n\n      const interval = setInterval(() => {\n        if (this._processing == 0) {\n          clearInterval(interval);\n          resolve(true);\n        } else {\n          ticked += tick;\n          if (timeout && ticked >= timeout) {\n            clearInterval(interval);\n            resolve(false);\n          }\n        }\n      }, tick);\n    });\n  }\n\n  /** Returns the current backend. */\n  protected _getBackend(): B {\n    return this._backend;\n  }\n\n  /** Determines whether this SDK is enabled and a valid Dsn is present. */\n  protected _isEnabled(): boolean {\n    return this.getOptions().enabled !== false && this._dsn !== undefined;\n  }\n\n  /**\n   * Adds common information to events.\n   *\n   * The information includes release and environment from `options`,\n   * breadcrumbs and context (extra, tags and user) from the scope.\n   *\n   * Information that is already present in the event is never overwritten. For\n   * nested objects, such as the context, keys are merged.\n   *\n   * @param event The original event.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A new event with more information.\n   */\n  protected _prepareEvent(event: Event, scope?: Scope, hint?: EventHint): PromiseLike<Event | null> {\n    const { normalizeDepth = 3 } = this.getOptions();\n    const prepared: Event = {\n      ...event,\n      event_id: event.event_id || (hint && hint.event_id ? hint.event_id : uuid4()),\n      timestamp: event.timestamp || dateTimestampInSeconds(),\n    };\n\n    this._applyClientOptions(prepared);\n    this._applyIntegrationsMetadata(prepared);\n\n    // If we have scope given to us, use it as the base for further modifications.\n    // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n    let finalScope = scope;\n    if (hint && hint.captureContext) {\n      finalScope = Scope.clone(finalScope).update(hint.captureContext);\n    }\n\n    // We prepare the result here with a resolved Event.\n    let result = SyncPromise.resolve<Event | null>(prepared);\n\n    // This should be the last thing called, since we want that\n    // {@link Hub.addEventProcessor} gets the finished prepared event.\n    if (finalScope) {\n      // In case we have a hub we reassign it.\n      result = finalScope.applyToEvent(prepared, hint);\n    }\n\n    return result.then(evt => {\n      if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n        return this._normalizeEvent(evt, normalizeDepth);\n      }\n      return evt;\n    });\n  }\n\n  /**\n   * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n   * Normalized keys:\n   * - `breadcrumbs.data`\n   * - `user`\n   * - `contexts`\n   * - `extra`\n   * @param event Event\n   * @returns Normalized event\n   */\n  protected _normalizeEvent(event: Event | null, depth: number): Event | null {\n    if (!event) {\n      return null;\n    }\n\n    const normalized = {\n      ...event,\n      ...(event.breadcrumbs && {\n        breadcrumbs: event.breadcrumbs.map(b => ({\n          ...b,\n          ...(b.data && {\n            data: normalize(b.data, depth),\n          }),\n        })),\n      }),\n      ...(event.user && {\n        user: normalize(event.user, depth),\n      }),\n      ...(event.contexts && {\n        contexts: normalize(event.contexts, depth),\n      }),\n      ...(event.extra && {\n        extra: normalize(event.extra, depth),\n      }),\n    };\n    // event.contexts.trace stores information about a Transaction. Similarly,\n    // event.spans[] stores information about child Spans. Given that a\n    // Transaction is conceptually a Span, normalization should apply to both\n    // Transactions and Spans consistently.\n    // For now the decision is to skip normalization of Transactions and Spans,\n    // so this block overwrites the normalized event to add back the original\n    // Transaction information prior to normalization.\n    if (event.contexts && event.contexts.trace) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      normalized.contexts.trace = event.contexts.trace;\n    }\n    return normalized;\n  }\n\n  /**\n   *  Enhances event using the client configuration.\n   *  It takes care of all \"static\" values like environment, release and `dist`,\n   *  as well as truncating overly long values.\n   * @param event event instance to be enhanced\n   */\n  protected _applyClientOptions(event: Event): void {\n    const options = this.getOptions();\n    const { environment, release, dist, maxValueLength = 250 } = options;\n\n    if (!('environment' in event)) {\n      event.environment = 'environment' in options ? environment : 'production';\n    }\n\n    if (event.release === undefined && release !== undefined) {\n      event.release = release;\n    }\n\n    if (event.dist === undefined && dist !== undefined) {\n      event.dist = dist;\n    }\n\n    if (event.message) {\n      event.message = truncate(event.message, maxValueLength);\n    }\n\n    const exception = event.exception && event.exception.values && event.exception.values[0];\n    if (exception && exception.value) {\n      exception.value = truncate(exception.value, maxValueLength);\n    }\n\n    const request = event.request;\n    if (request && request.url) {\n      request.url = truncate(request.url, maxValueLength);\n    }\n  }\n\n  /**\n   * This function adds all used integrations to the SDK info in the event.\n   * @param sdkInfo The sdkInfo of the event that will be filled with all integrations.\n   */\n  protected _applyIntegrationsMetadata(event: Event): void {\n    const sdkInfo = event.sdk;\n    const integrationsArray = Object.keys(this._integrations);\n    if (sdkInfo && integrationsArray.length > 0) {\n      sdkInfo.integrations = integrationsArray;\n    }\n  }\n\n  /**\n   * Tells the backend to send this event\n   * @param event The Sentry event to send\n   */\n  protected _sendEvent(event: Event): void {\n    this._getBackend().sendEvent(event);\n  }\n\n  /**\n   * Processes the event and logs an error in case of rejection\n   * @param event\n   * @param hint\n   * @param scope\n   */\n  protected _captureEvent(event: Event, hint?: EventHint, scope?: Scope): PromiseLike<string | undefined> {\n    return this._processEvent(event, hint, scope).then(\n      finalEvent => {\n        return finalEvent.event_id;\n      },\n      reason => {\n        logger.error(reason);\n        return undefined;\n      },\n    );\n  }\n\n  /**\n   * Processes an event (either error or message) and sends it to Sentry.\n   *\n   * This also adds breadcrumbs and context information to the event. However,\n   * platform specific meta data (such as the User's IP address) must be added\n   * by the SDK implementor.\n   *\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A SyncPromise that resolves with the event or rejects in case event was/will not be send.\n   */\n  protected _processEvent(event: Event, hint?: EventHint, scope?: Scope): PromiseLike<Event> {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const { beforeSend, sampleRate } = this.getOptions();\n\n    if (!this._isEnabled()) {\n      return SyncPromise.reject(new SentryError('SDK not enabled, will not send event.'));\n    }\n\n    const isTransaction = event.type === 'transaction';\n    // 1.0 === 100% events are sent\n    // 0.0 === 0% events are sent\n    // Sampling for transaction happens somewhere else\n    if (!isTransaction && typeof sampleRate === 'number' && Math.random() > sampleRate) {\n      return SyncPromise.reject(\n        new SentryError(\n          `Discarding event because it's not included in the random sample (sampling rate = ${sampleRate})`,\n        ),\n      );\n    }\n\n    return this._prepareEvent(event, scope, hint)\n      .then(prepared => {\n        if (prepared === null) {\n          throw new SentryError('An event processor returned null, will not send event.');\n        }\n\n        const isInternalException = hint && hint.data && (hint.data as { __sentry__: boolean }).__sentry__ === true;\n        if (isInternalException || isTransaction || !beforeSend) {\n          return prepared;\n        }\n\n        const beforeSendResult = beforeSend(prepared, hint);\n        if (typeof beforeSendResult === 'undefined') {\n          throw new SentryError('`beforeSend` method has to return `null` or a valid event.');\n        } else if (isThenable(beforeSendResult)) {\n          return (beforeSendResult as PromiseLike<Event | null>).then(\n            event => event,\n            e => {\n              throw new SentryError(`beforeSend rejected with ${e}`);\n            },\n          );\n        }\n        return beforeSendResult;\n      })\n      .then(processedEvent => {\n        if (processedEvent === null) {\n          throw new SentryError('`beforeSend` returned `null`, will not send event.');\n        }\n\n        const session = scope && scope.getSession && scope.getSession();\n        if (!isTransaction && session) {\n          this._updateSessionFromEvent(session, processedEvent);\n        }\n\n        this._sendEvent(processedEvent);\n        return processedEvent;\n      })\n      .then(null, reason => {\n        if (reason instanceof SentryError) {\n          throw reason;\n        }\n\n        this.captureException(reason, {\n          data: {\n            __sentry__: true,\n          },\n          originalException: reason as Error,\n        });\n        throw new SentryError(\n          `Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\\nReason: ${reason}`,\n        );\n      });\n  }\n\n  /**\n   * Occupies the client with processing and event\n   */\n  protected _process<T>(promise: PromiseLike<T>): void {\n    this._processing += 1;\n    promise.then(\n      value => {\n        this._processing -= 1;\n        return value;\n      },\n      reason => {\n        this._processing -= 1;\n        return reason;\n      },\n    );\n  }\n}\n", "import { Integration, WrappedFunction } from '@sentry/types';\n\nlet originalFunctionToString: () => void;\n\n/** Patch toString calls to return proper name for wrapped functions */\nexport class FunctionToString implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'FunctionToString';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = FunctionToString.id;\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    originalFunctionToString = Function.prototype.toString;\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    Function.prototype.toString = function(this: WrappedFunction, ...args: any[]): string {\n      const context = this.__sentry_original__ || this;\n      return originalFunctionToString.apply(context, args);\n    };\n  }\n}\n", "import { Event, Response, Status, Transport } from '@sentry/types';\nimport { SyncPromise } from '@sentry/utils';\n\n/** Noop transport */\nexport class NoopTransport implements Transport {\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(_: Event): PromiseLike<Response> {\n    return SyncPromise.resolve({\n      reason: `NoopTransport: Event has been skipped because no Dsn is configured.`,\n      status: Status.Skipped,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(_?: number): PromiseLike<boolean> {\n    return SyncPromise.resolve(true);\n  }\n}\n", "import { Event, EventHint, Options, Session, Severity, Transport } from '@sentry/types';\nimport { logger, SentryError } from '@sentry/utils';\n\nimport { NoopTransport } from './transports/noop';\n\n/**\n * Internal platform-dependent Sentry SDK Backend.\n *\n * While {@link Client} contains business logic specific to an SDK, the\n * Backend offers platform specific implementations for low-level operations.\n * These are persisting and loading information, sending events, and hooking\n * into the environment.\n *\n * Backends receive a handle to the Client in their constructor. When a\n * Backend automatically generates events, it must pass them to\n * the Client for validation and processing first.\n *\n * Usually, the Client will be of corresponding type, e.g. NodeBackend\n * receives NodeClient. However, higher-level SDKs can choose to instantiate\n * multiple Backends and delegate tasks between them. In this case, an event\n * generated by one backend might very well be sent by another one.\n *\n * The client also provides access to options via {@link Client.getOptions}.\n * @hidden\n */\nexport interface Backend {\n  /** Creates a {@link Event} from an exception. */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  eventFromException(exception: any, hint?: EventHint): PromiseLike<Event>;\n\n  /** Creates a {@link Event} from a plain message. */\n  eventFromMessage(message: string, level?: Severity, hint?: EventHint): PromiseLike<Event>;\n\n  /** Submits the event to Sentry */\n  sendEvent(event: Event): void;\n\n  /** Submits the session to Sentry */\n  sendSession(session: Session): void;\n\n  /**\n   * Returns the transport that is used by the backend.\n   * Please note that the transport gets lazy initialized so it will only be there once the first event has been sent.\n   *\n   * @returns The transport.\n   */\n  getTransport(): Transport;\n}\n\n/**\n * A class object that can instantiate Backend objects.\n * @hidden\n */\nexport type BackendClass<B extends Backend, O extends Options> = new (options: O) => B;\n\n/**\n * This is the base implemention of a Backend.\n * @hidden\n */\nexport abstract class BaseBackend<O extends Options> implements Backend {\n  /** Options passed to the SDK. */\n  protected readonly _options: O;\n\n  /** Cached transport used internally. */\n  protected _transport: Transport;\n\n  /** Creates a new backend instance. */\n  public constructor(options: O) {\n    this._options = options;\n    if (!this._options.dsn) {\n      logger.warn('No DSN provided, backend will not do anything.');\n    }\n    this._transport = this._setupTransport();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public eventFromException(_exception: any, _hint?: EventHint): PromiseLike<Event> {\n    throw new SentryError('Backend has to implement `eventFromException` method');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(_message: string, _level?: Severity, _hint?: EventHint): PromiseLike<Event> {\n    throw new SentryError('Backend has to implement `eventFromMessage` method');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event): void {\n    this._transport.sendEvent(event).then(null, reason => {\n      logger.error(`Error while sending event: ${reason}`);\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendSession(session: Session): void {\n    if (!this._transport.sendSession) {\n      logger.warn(\"Dropping session because custom transport doesn't implement sendSession\");\n      return;\n    }\n\n    this._transport.sendSession(session).then(null, reason => {\n      logger.error(`Error while sending session: ${reason}`);\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransport(): Transport {\n    return this._transport;\n  }\n\n  /**\n   * Sets up the transport so it can be used later to send requests.\n   */\n  protected _setupTransport(): Transport {\n    return new NoopTransport();\n  }\n}\n", "import { Event, SentryRequest, Session } from '@sentry/types';\n\nimport { API } from './api';\n\n/** Creates a SentryRequest from an event. */\nexport function sessionToSentryRequest(session: Session, api: API): SentryRequest {\n  const envelopeHeaders = JSON.stringify({\n    sent_at: new Date().toISOString(),\n  });\n  const itemHeaders = JSON.stringify({\n    type: 'session',\n  });\n\n  return {\n    body: `${envelopeHeaders}\\n${itemHeaders}\\n${JSON.stringify(session)}`,\n    type: 'session',\n    url: api.getEnvelopeEndpointWithUrlEncodedAuth(),\n  };\n}\n\n/** Creates a SentryRequest from an event. */\nexport function eventToSentryRequest(event: Event, api: API): SentryRequest {\n  // since JS has no Object.prototype.pop()\n  const { __sentry_samplingMethod: samplingMethod, __sentry_sampleRate: sampleRate, ...otherTags } = event.tags || {};\n  event.tags = otherTags;\n\n  const useEnvelope = event.type === 'transaction';\n\n  const req: SentryRequest = {\n    body: JSON.stringify(event),\n    type: event.type || 'event',\n    url: useEnvelope ? api.getEnvelopeEndpointWithUrlEncodedAuth() : api.getStoreEndpointWithUrlEncodedAuth(),\n  };\n\n  // https://develop.sentry.dev/sdk/envelopes/\n\n  // Since we don't need to manipulate envelopes nor store them, there is no\n  // exported concept of an Envelope with operations including serialization and\n  // deserialization. Instead, we only implement a minimal subset of the spec to\n  // serialize events inline here.\n  if (useEnvelope) {\n    const envelopeHeaders = JSON.stringify({\n      event_id: event.event_id,\n      sent_at: new Date().toISOString(),\n    });\n    const itemHeaders = JSON.stringify({\n      type: event.type,\n\n      // TODO: Right now, sampleRate may or may not be defined (it won't be in the cases of inheritance and\n      // explicitly-set sampling decisions). Are we good with that?\n      sample_rates: [{ id: samplingMethod, rate: sampleRate }],\n\n      // The content-type is assumed to be 'application/json' and not part of\n      // the current spec for transaction items, so we don't bloat the request\n      // body with it.\n      //\n      // content_type: 'application/json',\n      //\n      // The length is optional. It must be the number of bytes in req.Body\n      // encoded as UTF-8. Since the server can figure this out and would\n      // otherwise refuse events that report the length incorrectly, we decided\n      // not to send the length to avoid problems related to reporting the wrong\n      // size and to reduce request body size.\n      //\n      // length: new TextEncoder().encode(req.body).length,\n    });\n    // The trailing newline is optional. We intentionally don't send it to avoid\n    // sending unnecessary bytes.\n    //\n    // const envelope = `${envelopeHeaders}\\n${itemHeaders}\\n${req.body}\\n`;\n    const envelope = `${envelopeHeaders}\\n${itemHeaders}\\n${req.body}`;\n    req.body = envelope;\n  }\n\n  return req;\n}\n", "import { addGlobalEventProcessor, getCurrentHub } from '@sentry/hub';\nimport { Event, Integration } from '@sentry/types';\nimport { getEventDescription, isMatchingPattern, logger } from '@sentry/utils';\n\n// \"Script error.\" is hard coded into browsers for errors that it can't read.\n// this is the result of a script being pulled in from an external domain and CORS.\nconst DEFAULT_IGNORE_ERRORS = [/^Script error\\.?$/, /^Javascript error: Script error\\.? on line 0$/];\n\n/** JSDoc */\ninterface InboundFiltersOptions {\n  allowUrls: Array<string | RegExp>;\n  denyUrls: Array<string | RegExp>;\n  ignoreErrors: Array<string | RegExp>;\n  ignoreInternal: boolean;\n\n  /** @deprecated use {@link InboundFiltersOptions.allowUrls} instead. */\n  whitelistUrls: Array<string | RegExp>;\n  /** @deprecated use {@link InboundFiltersOptions.denyUrls} instead. */\n  blacklistUrls: Array<string | RegExp>;\n}\n\n/** Inbound filters configurable by the user */\nexport class InboundFilters implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'InboundFilters';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = InboundFilters.id;\n\n  public constructor(private readonly _options: Partial<InboundFiltersOptions> = {}) {}\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    addGlobalEventProcessor((event: Event) => {\n      const hub = getCurrentHub();\n      if (!hub) {\n        return event;\n      }\n      const self = hub.getIntegration(InboundFilters);\n      if (self) {\n        const client = hub.getClient();\n        const clientOptions = client ? client.getOptions() : {};\n        const options = self._mergeOptions(clientOptions);\n        if (self._shouldDropEvent(event, options)) {\n          return null;\n        }\n      }\n      return event;\n    });\n  }\n\n  /** JSDoc */\n  private _shouldDropEvent(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    if (this._isSentryError(event, options)) {\n      logger.warn(`Event dropped due to being internal Sentry Error.\\nEvent: ${getEventDescription(event)}`);\n      return true;\n    }\n    if (this._isIgnoredError(event, options)) {\n      logger.warn(\n        `Event dropped due to being matched by \\`ignoreErrors\\` option.\\nEvent: ${getEventDescription(event)}`,\n      );\n      return true;\n    }\n    if (this._isDeniedUrl(event, options)) {\n      logger.warn(\n        `Event dropped due to being matched by \\`denyUrls\\` option.\\nEvent: ${getEventDescription(\n          event,\n        )}.\\nUrl: ${this._getEventFilterUrl(event)}`,\n      );\n      return true;\n    }\n    if (!this._isAllowedUrl(event, options)) {\n      logger.warn(\n        `Event dropped due to not being matched by \\`allowUrls\\` option.\\nEvent: ${getEventDescription(\n          event,\n        )}.\\nUrl: ${this._getEventFilterUrl(event)}`,\n      );\n      return true;\n    }\n    return false;\n  }\n\n  /** JSDoc */\n  private _isSentryError(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    if (!options.ignoreInternal) {\n      return false;\n    }\n\n    try {\n      return (\n        (event &&\n          event.exception &&\n          event.exception.values &&\n          event.exception.values[0] &&\n          event.exception.values[0].type === 'SentryError') ||\n        false\n      );\n    } catch (_oO) {\n      return false;\n    }\n  }\n\n  /** JSDoc */\n  private _isIgnoredError(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    if (!options.ignoreErrors || !options.ignoreErrors.length) {\n      return false;\n    }\n\n    return this._getPossibleEventMessages(event).some(message =>\n      // Not sure why TypeScript complains here...\n      (options.ignoreErrors as Array<RegExp | string>).some(pattern => isMatchingPattern(message, pattern)),\n    );\n  }\n\n  /** JSDoc */\n  private _isDeniedUrl(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    // TODO: Use Glob instead?\n    if (!options.denyUrls || !options.denyUrls.length) {\n      return false;\n    }\n    const url = this._getEventFilterUrl(event);\n    return !url ? false : options.denyUrls.some(pattern => isMatchingPattern(url, pattern));\n  }\n\n  /** JSDoc */\n  private _isAllowedUrl(event: Event, options: Partial<InboundFiltersOptions>): boolean {\n    // TODO: Use Glob instead?\n    if (!options.allowUrls || !options.allowUrls.length) {\n      return true;\n    }\n    const url = this._getEventFilterUrl(event);\n    return !url ? true : options.allowUrls.some(pattern => isMatchingPattern(url, pattern));\n  }\n\n  /** JSDoc */\n  private _mergeOptions(clientOptions: Partial<InboundFiltersOptions> = {}): Partial<InboundFiltersOptions> {\n    return {\n      allowUrls: [\n        // eslint-disable-next-line deprecation/deprecation\n        ...(this._options.whitelistUrls || []),\n        ...(this._options.allowUrls || []),\n        // eslint-disable-next-line deprecation/deprecation\n        ...(clientOptions.whitelistUrls || []),\n        ...(clientOptions.allowUrls || []),\n      ],\n      denyUrls: [\n        // eslint-disable-next-line deprecation/deprecation\n        ...(this._options.blacklistUrls || []),\n        ...(this._options.denyUrls || []),\n        // eslint-disable-next-line deprecation/deprecation\n        ...(clientOptions.blacklistUrls || []),\n        ...(clientOptions.denyUrls || []),\n      ],\n      ignoreErrors: [\n        ...(this._options.ignoreErrors || []),\n        ...(clientOptions.ignoreErrors || []),\n        ...DEFAULT_IGNORE_ERRORS,\n      ],\n      ignoreInternal: typeof this._options.ignoreInternal !== 'undefined' ? this._options.ignoreInternal : true,\n    };\n  }\n\n  /** JSDoc */\n  private _getPossibleEventMessages(event: Event): string[] {\n    if (event.message) {\n      return [event.message];\n    }\n    if (event.exception) {\n      try {\n        const { type = '', value = '' } = (event.exception.values && event.exception.values[0]) || {};\n        return [`${value}`, `${type}: ${value}`];\n      } catch (oO) {\n        logger.error(`Cannot extract message for event ${getEventDescription(event)}`);\n        return [];\n      }\n    }\n    return [];\n  }\n\n  /** JSDoc */\n  private _getEventFilterUrl(event: Event): string | null {\n    try {\n      if (event.stacktrace) {\n        const frames = event.stacktrace.frames;\n        return (frames && frames[frames.length - 1].filename) || null;\n      }\n      if (event.exception) {\n        const frames =\n          event.exception.values && event.exception.values[0].stacktrace && event.exception.values[0].stacktrace.frames;\n        return (frames && frames[frames.length - 1].filename) || null;\n      }\n      return null;\n    } catch (oO) {\n      logger.error(`Cannot extract url for event ${getEventDescription(event)}`);\n      return null;\n    }\n  }\n}\n", "/**\n * This was originally forked from https://github.com/occ/TraceKit, but has since been\n * largely modified and is now maintained as part of Sentry JS SDK.\n */\n\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n\n/**\n * An object representing a single stack frame.\n * {Object} StackFrame\n * {string} url The JavaScript or HTML file URL.\n * {string} func The function name, or empty for anonymous functions (if guessing did not work).\n * {string[]?} args The arguments passed to the function, if known.\n * {number=} line The line number, if known.\n * {number=} column The column number, if known.\n * {string[]} context An array of source code lines; the middle element corresponds to the correct line#.\n */\nexport interface StackFrame {\n  url: string;\n  func: string;\n  args: string[];\n  line: number | null;\n  column: number | null;\n}\n\n/**\n * An object representing a JavaScript stack trace.\n * {Object} StackTrace\n * {string} name The name of the thrown exception.\n * {string} message The exception error message.\n * {TraceKit.StackFrame[]} stack An array of stack frames.\n */\nexport interface StackTrace {\n  name: string;\n  message: string;\n  mechanism?: string;\n  stack: StackFrame[];\n  failed?: boolean;\n}\n\n// global reference to slice\nconst UNKNOWN_FUNCTION = '?';\n\n// Chromium based browsers: Chrome, Brave, new Opera, new Edge\nconst chrome = /^\\s*at (?:(.*?) ?\\()?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\\/).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\n// gecko regex: `(?:bundle|\\d+\\.js)`: `bundle` is for react native, `\\d+\\.js` also but specifically for ram bundles because it\n// generates filenames without a prefix like `file://` the filenames in the stacktrace are just 42.js\n// We need this specific case for now because we want no other regex to match.\nconst gecko = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|capacitor).*?:\\/.*?|\\[native code\\]|[^@]*(?:bundle|\\d+\\.js)|\\/[\\w\\-. /=]+)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\nconst winjs = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\nconst geckoEval = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\nconst chromeEval = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\n// Based on our own mapping pattern - https://github.com/getsentry/sentry/blob/9f08305e09866c8bd6d0c24f5b0aabdd7dd6c59c/src/sentry/lang/javascript/errormapping.py#L83-L108\nconst reactMinifiedRegexp = /Minified React error #\\d+;/i;\n\n/** JSDoc */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nexport function computeStackTrace(ex: any): StackTrace {\n  let stack = null;\n  let popSize = 0;\n\n  if (ex) {\n    if (typeof ex.framesToPop === 'number') {\n      popSize = ex.framesToPop;\n    } else if (reactMinifiedRegexp.test(ex.message)) {\n      popSize = 1;\n    }\n  }\n\n  try {\n    // This must be tried first because Opera 10 *destroys*\n    // its stacktrace property if you try to access the stack\n    // property first!!\n    stack = computeStackTraceFromStacktraceProp(ex);\n    if (stack) {\n      return popFrames(stack, popSize);\n    }\n  } catch (e) {\n    // no-empty\n  }\n\n  try {\n    stack = computeStackTraceFromStackProp(ex);\n    if (stack) {\n      return popFrames(stack, popSize);\n    }\n  } catch (e) {\n    // no-empty\n  }\n\n  return {\n    message: extractMessage(ex),\n    name: ex && ex.name,\n    stack: [],\n    failed: true,\n  };\n}\n\n/** JSDoc */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, complexity\nfunction computeStackTraceFromStackProp(ex: any): StackTrace | null {\n  if (!ex || !ex.stack) {\n    return null;\n  }\n\n  const stack = [];\n  const lines = ex.stack.split('\\n');\n  let isEval;\n  let submatch;\n  let parts;\n  let element;\n\n  for (let i = 0; i < lines.length; ++i) {\n    if ((parts = chrome.exec(lines[i]))) {\n      const isNative = parts[2] && parts[2].indexOf('native') === 0; // start of line\n      isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n      if (isEval && (submatch = chromeEval.exec(parts[2]))) {\n        // throw out eval line/column and use top-most line/column number\n        parts[2] = submatch[1]; // url\n        parts[3] = submatch[2]; // line\n        parts[4] = submatch[3]; // column\n      }\n      element = {\n        // working with the regexp above is super painful. it is quite a hack, but just stripping the `address at `\n        // prefix here seems like the quickest solution for now.\n        url: parts[2] && parts[2].indexOf('address at ') === 0 ? parts[2].substr('address at '.length) : parts[2],\n        func: parts[1] || UNKNOWN_FUNCTION,\n        args: isNative ? [parts[2]] : [],\n        line: parts[3] ? +parts[3] : null,\n        column: parts[4] ? +parts[4] : null,\n      };\n    } else if ((parts = winjs.exec(lines[i]))) {\n      element = {\n        url: parts[2],\n        func: parts[1] || UNKNOWN_FUNCTION,\n        args: [],\n        line: +parts[3],\n        column: parts[4] ? +parts[4] : null,\n      };\n    } else if ((parts = gecko.exec(lines[i]))) {\n      isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n      if (isEval && (submatch = geckoEval.exec(parts[3]))) {\n        // throw out eval line/column and use top-most line number\n        parts[1] = parts[1] || `eval`;\n        parts[3] = submatch[1];\n        parts[4] = submatch[2];\n        parts[5] = ''; // no column when eval\n      } else if (i === 0 && !parts[5] && ex.columnNumber !== void 0) {\n        // FireFox uses this awesome columnNumber property for its top frame\n        // Also note, Firefox's column number is 0-based and everything else expects 1-based,\n        // so adding 1\n        // NOTE: this hack doesn't work if top-most frame is eval\n        stack[0].column = (ex.columnNumber as number) + 1;\n      }\n      element = {\n        url: parts[3],\n        func: parts[1] || UNKNOWN_FUNCTION,\n        args: parts[2] ? parts[2].split(',') : [],\n        line: parts[4] ? +parts[4] : null,\n        column: parts[5] ? +parts[5] : null,\n      };\n    } else {\n      continue;\n    }\n\n    if (!element.func && element.line) {\n      element.func = UNKNOWN_FUNCTION;\n    }\n\n    stack.push(element);\n  }\n\n  if (!stack.length) {\n    return null;\n  }\n\n  return {\n    message: extractMessage(ex),\n    name: ex.name,\n    stack,\n  };\n}\n\n/** JSDoc */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction computeStackTraceFromStacktraceProp(ex: any): StackTrace | null {\n  if (!ex || !ex.stacktrace) {\n    return null;\n  }\n  // Access and store the stacktrace property before doing ANYTHING\n  // else to it because Opera is not very good at providing it\n  // reliably in other circumstances.\n  const stacktrace = ex.stacktrace;\n  const opera10Regex = / line (\\d+).*script (?:in )?(\\S+)(?:: in function (\\S+))?$/i;\n  const opera11Regex = / line (\\d+), column (\\d+)\\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\\((.*)\\))? in (.*):\\s*$/i;\n  const lines = stacktrace.split('\\n');\n  const stack = [];\n  let parts;\n\n  for (let line = 0; line < lines.length; line += 2) {\n    let element = null;\n    if ((parts = opera10Regex.exec(lines[line]))) {\n      element = {\n        url: parts[2],\n        func: parts[3],\n        args: [],\n        line: +parts[1],\n        column: null,\n      };\n    } else if ((parts = opera11Regex.exec(lines[line]))) {\n      element = {\n        url: parts[6],\n        func: parts[3] || parts[4],\n        args: parts[5] ? parts[5].split(',') : [],\n        line: +parts[1],\n        column: +parts[2],\n      };\n    }\n\n    if (element) {\n      if (!element.func && element.line) {\n        element.func = UNKNOWN_FUNCTION;\n      }\n      stack.push(element);\n    }\n  }\n\n  if (!stack.length) {\n    return null;\n  }\n\n  return {\n    message: extractMessage(ex),\n    name: ex.name,\n    stack,\n  };\n}\n\n/** Remove N number of frames from the stack */\nfunction popFrames(stacktrace: StackTrace, popSize: number): StackTrace {\n  try {\n    return {\n      ...stacktrace,\n      stack: stacktrace.stack.slice(popSize),\n    };\n  } catch (e) {\n    return stacktrace;\n  }\n}\n\n/**\n * There are cases where stacktrace.message is an Event object\n * https://github.com/getsentry/sentry-javascript/issues/1949\n * In this specific case we try to extract stacktrace.message.error.message\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction extractMessage(ex: any): string {\n  const message = ex && ex.message;\n  if (!message) {\n    return 'No error message';\n  }\n  if (message.error && typeof message.error.message === 'string') {\n    return message.error.message;\n  }\n  return message;\n}\n", "import { Event, Exception, StackFrame } from '@sentry/types';\nimport { extractExceptionKeysForMessage, isEvent, normalizeToSize } from '@sentry/utils';\n\nimport { computeStackTrace, StackFrame as TraceKitStackFrame, StackTrace as TraceKitStackTrace } from './tracekit';\n\nconst STACKTRACE_LIMIT = 50;\n\n/**\n * This function creates an exception from an TraceKitStackTrace\n * @param stacktrace TraceKitStackTrace that will be converted to an exception\n * @hidden\n */\nexport function exceptionFromStacktrace(stacktrace: TraceKitStackTrace): Exception {\n  const frames = prepareFramesForEvent(stacktrace.stack);\n\n  const exception: Exception = {\n    type: stacktrace.name,\n    value: stacktrace.message,\n  };\n\n  if (frames && frames.length) {\n    exception.stacktrace = { frames };\n  }\n\n  if (exception.type === undefined && exception.value === '') {\n    exception.value = 'Unrecoverable error caught';\n  }\n\n  return exception;\n}\n\n/**\n * @hidden\n */\nexport function eventFromPlainObject(\n  exception: Record<string, unknown>,\n  syntheticException?: Error,\n  rejection?: boolean,\n): Event {\n  const event: Event = {\n    exception: {\n      values: [\n        {\n          type: isEvent(exception) ? exception.constructor.name : rejection ? 'UnhandledRejection' : 'Error',\n          value: `Non-Error ${\n            rejection ? 'promise rejection' : 'exception'\n          } captured with keys: ${extractExceptionKeysForMessage(exception)}`,\n        },\n      ],\n    },\n    extra: {\n      __serialized__: normalizeToSize(exception),\n    },\n  };\n\n  if (syntheticException) {\n    const stacktrace = computeStackTrace(syntheticException);\n    const frames = prepareFramesForEvent(stacktrace.stack);\n    event.stacktrace = {\n      frames,\n    };\n  }\n\n  return event;\n}\n\n/**\n * @hidden\n */\nexport function eventFromStacktrace(stacktrace: TraceKitStackTrace): Event {\n  const exception = exceptionFromStacktrace(stacktrace);\n\n  return {\n    exception: {\n      values: [exception],\n    },\n  };\n}\n\n/**\n * @hidden\n */\nexport function prepareFramesForEvent(stack: TraceKitStackFrame[]): StackFrame[] {\n  if (!stack || !stack.length) {\n    return [];\n  }\n\n  let localStack = stack;\n\n  const firstFrameFunction = localStack[0].func || '';\n  const lastFrameFunction = localStack[localStack.length - 1].func || '';\n\n  // If stack starts with one of our API calls, remove it (starts, meaning it's the top of the stack - aka last call)\n  if (firstFrameFunction.indexOf('captureMessage') !== -1 || firstFrameFunction.indexOf('captureException') !== -1) {\n    localStack = localStack.slice(1);\n  }\n\n  // If stack ends with one of our internal API calls, remove it (ends, meaning it's the bottom of the stack - aka top-most call)\n  if (lastFrameFunction.indexOf('sentryWrapped') !== -1) {\n    localStack = localStack.slice(0, -1);\n  }\n\n  // The frame where the crash happened, should be the last entry in the array\n  return localStack\n    .slice(0, STACKTRACE_LIMIT)\n    .map(\n      (frame: TraceKitStackFrame): StackFrame => ({\n        colno: frame.column === null ? undefined : frame.column,\n        filename: frame.url || localStack[0].url,\n        function: frame.func || '?',\n        in_app: true,\n        lineno: frame.line === null ? undefined : frame.line,\n      }),\n    )\n    .reverse();\n}\n", "import { Event, EventHint, Options, Severity } from '@sentry/types';\nimport {\n  addExceptionMechanism,\n  addExceptionTypeValue,\n  isDOMError,\n  isDOMException,\n  isError,\n  isErrorEvent,\n  isEvent,\n  isPlainObject,\n  SyncPromise,\n} from '@sentry/utils';\n\nimport { eventFromPlainObject, eventFromStacktrace, prepareFramesForEvent } from './parsers';\nimport { computeStackTrace } from './tracekit';\n\n/**\n * Builds and Event from a Exception\n * @hidden\n */\nexport function eventFromException(options: Options, exception: unknown, hint?: EventHint): PromiseLike<Event> {\n  const syntheticException = (hint && hint.syntheticException) || undefined;\n  const event = eventFromUnknownInput(exception, syntheticException, {\n    attachStacktrace: options.attachStacktrace,\n  });\n  addExceptionMechanism(event, {\n    handled: true,\n    type: 'generic',\n  });\n  event.level = Severity.Error;\n  if (hint && hint.event_id) {\n    event.event_id = hint.event_id;\n  }\n  return SyncPromise.resolve(event);\n}\n\n/**\n * Builds and Event from a Message\n * @hidden\n */\nexport function eventFromMessage(\n  options: Options,\n  message: string,\n  level: Severity = Severity.Info,\n  hint?: EventHint,\n): PromiseLike<Event> {\n  const syntheticException = (hint && hint.syntheticException) || undefined;\n  const event = eventFromString(message, syntheticException, {\n    attachStacktrace: options.attachStacktrace,\n  });\n  event.level = level;\n  if (hint && hint.event_id) {\n    event.event_id = hint.event_id;\n  }\n  return SyncPromise.resolve(event);\n}\n\n/**\n * @hidden\n */\nexport function eventFromUnknownInput(\n  exception: unknown,\n  syntheticException?: Error,\n  options: {\n    rejection?: boolean;\n    attachStacktrace?: boolean;\n  } = {},\n): Event {\n  let event: Event;\n\n  if (isErrorEvent(exception as ErrorEvent) && (exception as ErrorEvent).error) {\n    // If it is an ErrorEvent with `error` property, extract it to get actual Error\n    const errorEvent = exception as ErrorEvent;\n    // eslint-disable-next-line no-param-reassign\n    exception = errorEvent.error;\n    event = eventFromStacktrace(computeStackTrace(exception as Error));\n    return event;\n  }\n  if (isDOMError(exception as DOMError) || isDOMException(exception as DOMException)) {\n    // If it is a DOMError or DOMException (which are legacy APIs, but still supported in some browsers)\n    // then we just extract the name, code, and message, as they don't provide anything else\n    // https://developer.mozilla.org/en-US/docs/Web/API/DOMError\n    // https://developer.mozilla.org/en-US/docs/Web/API/DOMException\n    const domException = exception as DOMException;\n    const name = domException.name || (isDOMError(domException) ? 'DOMError' : 'DOMException');\n    const message = domException.message ? `${name}: ${domException.message}` : name;\n\n    event = eventFromString(message, syntheticException, options);\n    addExceptionTypeValue(event, message);\n    if ('code' in domException) {\n      event.tags = { ...event.tags, 'DOMException.code': `${domException.code}` };\n    }\n\n    return event;\n  }\n  if (isError(exception as Error)) {\n    // we have a real Error object, do nothing\n    event = eventFromStacktrace(computeStackTrace(exception as Error));\n    return event;\n  }\n  if (isPlainObject(exception) || isEvent(exception)) {\n    // If it is plain Object or Event, serialize it manually and extract options\n    // This will allow us to group events based on top-level keys\n    // which is much better than creating new group when any key/value change\n    const objectException = exception as Record<string, unknown>;\n    event = eventFromPlainObject(objectException, syntheticException, options.rejection);\n    addExceptionMechanism(event, {\n      synthetic: true,\n    });\n    return event;\n  }\n\n  // If none of previous checks were valid, then it means that it's not:\n  // - an instance of DOMError\n  // - an instance of DOMException\n  // - an instance of Event\n  // - an instance of Error\n  // - a valid ErrorEvent (one with an error property)\n  // - a plain Object\n  //\n  // So bail out and capture it as a simple message:\n  event = eventFromString(exception as string, syntheticException, options);\n  addExceptionTypeValue(event, `${exception}`, undefined);\n  addExceptionMechanism(event, {\n    synthetic: true,\n  });\n\n  return event;\n}\n\n/**\n * @hidden\n */\nexport function eventFromString(\n  input: string,\n  syntheticException?: Error,\n  options: {\n    attachStacktrace?: boolean;\n  } = {},\n): Event {\n  const event: Event = {\n    message: input,\n  };\n\n  if (options.attachStacktrace && syntheticException) {\n    const stacktrace = computeStackTrace(syntheticException);\n    const frames = prepareFramesForEvent(stacktrace.stack);\n    event.stacktrace = {\n      frames,\n    };\n  }\n\n  return event;\n}\n", "import { API } from '@sentry/core';\nimport {\n  Event,\n  Response as SentryResponse,\n  SentryRequestType,\n  Status,\n  Transport,\n  TransportOptions,\n} from '@sentry/types';\nimport { logger, parseRetryAfterHeader, PromiseBuffer, SentryError } from '@sentry/utils';\n\n/** Base Transport class implementation */\nexport abstract class BaseTransport implements Transport {\n  /**\n   * @deprecated\n   */\n  public url: string;\n\n  /** Helper to get Sentry API endpoints. */\n  protected readonly _api: API;\n\n  /** A simple buffer holding all requests. */\n  protected readonly _buffer: PromiseBuffer<SentryResponse> = new PromiseBuffer(30);\n\n  /** Locks transport after receiving rate limits in a response */\n  protected readonly _rateLimits: Record<string, Date> = {};\n\n  public constructor(public options: TransportOptions) {\n    this._api = new API(this.options.dsn);\n    // eslint-disable-next-line deprecation/deprecation\n    this.url = this._api.getStoreEndpointWithUrlEncodedAuth();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(_: Event): PromiseLike<SentryResponse> {\n    throw new SentryError('Transport Class has to implement `sendEvent` method');\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    return this._buffer.drain(timeout);\n  }\n\n  /**\n   * Handle Sentry repsonse for promise-based transports.\n   */\n  protected _handleResponse({\n    requestType,\n    response,\n    headers,\n    resolve,\n    reject,\n  }: {\n    requestType: SentryRequestType;\n    response: Response | XMLHttpRequest;\n    headers: Record<string, string | null>;\n    resolve: (value?: SentryResponse | PromiseLike<SentryResponse> | null | undefined) => void;\n    reject: (reason?: unknown) => void;\n  }): void {\n    const status = Status.fromHttpCode(response.status);\n    /**\n     * \"The name is case-insensitive.\"\n     * https://developer.mozilla.org/en-US/docs/Web/API/Headers/get\n     */\n    const limited = this._handleRateLimit(headers);\n    if (limited) logger.warn(`Too many requests, backing off until: ${this._disabledUntil(requestType)}`);\n\n    if (status === Status.Success) {\n      resolve({ status });\n      return;\n    }\n\n    reject(response);\n  }\n\n  /**\n   * Gets the time that given category is disabled until for rate limiting\n   */\n  protected _disabledUntil(category: string): Date {\n    return this._rateLimits[category] || this._rateLimits.all;\n  }\n\n  /**\n   * Checks if a category is rate limited\n   */\n  protected _isRateLimited(category: string): boolean {\n    return this._disabledUntil(category) > new Date(Date.now());\n  }\n\n  /**\n   * Sets internal _rateLimits from incoming headers. Returns true if headers contains a non-empty rate limiting header.\n   */\n  protected _handleRateLimit(headers: Record<string, string | null>): boolean {\n    const now = Date.now();\n    const rlHeader = headers['x-sentry-rate-limits'];\n    const raHeader = headers['retry-after'];\n\n    if (rlHeader) {\n      // rate limit headers are of the form\n      //     <header>,<header>,..\n      // where each <header> is of the form\n      //     <retry_after>: <categories>: <scope>: <reason_code>\n      // where\n      //     <retry_after> is a delay in ms\n      //     <categories> is the event type(s) (error, transaction, etc) being rate limited and is of the form\n      //         <category>;<category>;...\n      //     <scope> is what's being limited (org, project, or key) - ignored by SDK\n      //     <reason_code> is an arbitrary string like \"org_quota\" - ignored by SDK\n      for (const limit of rlHeader.trim().split(',')) {\n        const parameters = limit.split(':', 2);\n        const headerDelay = parseInt(parameters[0], 10);\n        const delay = (!isNaN(headerDelay) ? headerDelay : 60) * 1000; // 60sec default\n        for (const category of parameters[1].split(';')) {\n          this._rateLimits[category || 'all'] = new Date(now + delay);\n        }\n      }\n      return true;\n    } else if (raHeader) {\n      this._rateLimits.all = new Date(now + parseRetryAfterHeader(now, raHeader));\n      return true;\n    }\n    return false;\n  }\n}\n", "import { eventToSentryRequest, sessionToSentryRequest } from '@sentry/core';\nimport { Event, Response, SentryRequest, Session } from '@sentry/types';\nimport { getGlobalObject, supportsReferrerPolicy, SyncPromise } from '@sentry/utils';\n\nimport { BaseTransport } from './base';\n\nconst global = getGlobalObject<Window>();\n\n/** `fetch` based transport */\nexport class FetchTransport extends BaseTransport {\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event): PromiseLike<Response> {\n    return this._sendRequest(eventToSentryRequest(event, this._api), event);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendSession(session: Session): PromiseLike<Response> {\n    return this._sendRequest(sessionToSentryRequest(session, this._api), session);\n  }\n\n  /**\n   * @param sentryRequest Prepared SentryRequest to be delivered\n   * @param originalPayload Original payload used to create SentryRequest\n   */\n  private _sendRequest(sentryRequest: SentryRequest, originalPayload: Event | Session): PromiseLike<Response> {\n    if (this._isRateLimited(sentryRequest.type)) {\n      return Promise.reject({\n        event: originalPayload,\n        type: sentryRequest.type,\n        reason: `Transport locked till ${this._disabledUntil(sentryRequest.type)} due to too many requests.`,\n        status: 429,\n      });\n    }\n\n    const options: RequestInit = {\n      body: sentryRequest.body,\n      method: 'POST',\n      // Despite all stars in the sky saying that Edge supports old draft syntax, aka 'never', 'always', 'origin' and 'default\n      // https://caniuse.com/#feat=referrer-policy\n      // It doesn't. And it throw exception instead of ignoring this parameter...\n      // REF: https://github.com/getsentry/raven-js/issues/1233\n      referrerPolicy: (supportsReferrerPolicy() ? 'origin' : '') as ReferrerPolicy,\n    };\n    if (this.options.fetchParameters !== undefined) {\n      Object.assign(options, this.options.fetchParameters);\n    }\n    if (this.options.headers !== undefined) {\n      options.headers = this.options.headers;\n    }\n\n    return this._buffer.add(\n      new SyncPromise<Response>((resolve, reject) => {\n        global\n          .fetch(sentryRequest.url, options)\n          .then(response => {\n            const headers = {\n              'x-sentry-rate-limits': response.headers.get('X-Sentry-Rate-Limits'),\n              'retry-after': response.headers.get('Retry-After'),\n            };\n            this._handleResponse({\n              requestType: sentryRequest.type,\n              response,\n              headers,\n              resolve,\n              reject,\n            });\n          })\n          .catch(reject);\n      }),\n    );\n  }\n}\n", "import { eventToSentryRequest, sessionToSentryRequest } from '@sentry/core';\nimport { Event, Response, SentryRequest, Session } from '@sentry/types';\nimport { SyncPromise } from '@sentry/utils';\n\nimport { BaseTransport } from './base';\n\n/** `XHR` based transport */\nexport class XHRTransport extends BaseTransport {\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event): PromiseLike<Response> {\n    return this._sendRequest(eventToSentryRequest(event, this._api), event);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendSession(session: Session): PromiseLike<Response> {\n    return this._sendRequest(sessionToSentryRequest(session, this._api), session);\n  }\n\n  /**\n   * @param sentryRequest Prepared SentryRequest to be delivered\n   * @param originalPayload Original payload used to create SentryRequest\n   */\n  private _sendRequest(sentryRequest: SentryRequest, originalPayload: Event | Session): PromiseLike<Response> {\n    if (this._isRateLimited(sentryRequest.type)) {\n      return Promise.reject({\n        event: originalPayload,\n        type: sentryRequest.type,\n        reason: `Transport locked till ${this._disabledUntil(sentryRequest.type)} due to too many requests.`,\n        status: 429,\n      });\n    }\n\n    return this._buffer.add(\n      new SyncPromise<Response>((resolve, reject) => {\n        const request = new XMLHttpRequest();\n\n        request.onreadystatechange = (): void => {\n          if (request.readyState === 4) {\n            const headers = {\n              'x-sentry-rate-limits': request.getResponseHeader('X-Sentry-Rate-Limits'),\n              'retry-after': request.getResponseHeader('Retry-After'),\n            };\n            this._handleResponse({ requestType: sentryRequest.type, response: request, headers, resolve, reject });\n          }\n        };\n\n        request.open('POST', sentryRequest.url);\n        for (const header in this.options.headers) {\n          if (this.options.headers.hasOwnProperty(header)) {\n            request.setRequestHeader(header, this.options.headers[header]);\n          }\n        }\n        request.send(sentryRequest.body);\n      }),\n    );\n  }\n}\n", "import { BaseBackend } from '@sentry/core';\nimport { Event, EventHint, Options, Severity, Transport } from '@sentry/types';\nimport { supportsFetch } from '@sentry/utils';\n\nimport { eventFromException, eventFromMessage } from './eventbuilder';\nimport { FetchTransport, XHRTransport } from './transports';\n\n/**\n * Configuration options for the Sentry Browser SDK.\n * @see BrowserClient for more information.\n */\nexport interface BrowserOptions extends Options {\n  /**\n   * A pattern for error URLs which should exclusively be sent to Sentry.\n   * This is the opposite of {@link Options.denyUrls}.\n   * By default, all errors will be sent.\n   */\n  allowUrls?: Array<string | RegExp>;\n\n  /**\n   * A pattern for error URLs which should not be sent to Sentry.\n   * To allow certain errors instead, use {@link Options.allowUrls}.\n   * By default, all errors will be sent.\n   */\n  denyUrls?: Array<string | RegExp>;\n\n  /** @deprecated use {@link Options.allowUrls} instead. */\n  whitelistUrls?: Array<string | RegExp>;\n\n  /** @deprecated use {@link Options.denyUrls} instead. */\n  blacklistUrls?: Array<string | RegExp>;\n\n  /**\n   * A flag enabling Sessions Tracking feature.\n   * By default Sessions Tracking is disabled.\n   */\n  autoSessionTracking?: boolean;\n}\n\n/**\n * The Sentry Browser SDK Backend.\n * @hidden\n */\nexport class BrowserBackend extends BaseBackend<BrowserOptions> {\n  /**\n   * @inheritDoc\n   */\n  public eventFromException(exception: unknown, hint?: EventHint): PromiseLike<Event> {\n    return eventFromException(this._options, exception, hint);\n  }\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(message: string, level: Severity = Severity.Info, hint?: EventHint): PromiseLike<Event> {\n    return eventFromMessage(this._options, message, level, hint);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _setupTransport(): Transport {\n    if (!this._options.dsn) {\n      // We return the noop transport here in case there is no Dsn.\n      return super._setupTransport();\n    }\n\n    const transportOptions = {\n      ...this._options.transportOptions,\n      dsn: this._options.dsn,\n    };\n\n    if (this._options.transport) {\n      return new this._options.transport(transportOptions);\n    }\n    if (supportsFetch()) {\n      return new FetchTransport(transportOptions);\n    }\n    return new XHRTransport(transportOptions);\n  }\n}\n", "import { API, captureException, withScope } from '@sentry/core';\nimport { DsnLike, Event as SentryEvent, Mechanism, Scope, WrappedFunction } from '@sentry/types';\nimport { addExceptionMechanism, addExceptionTypeValue, logger } from '@sentry/utils';\n\nlet ignoreOnError: number = 0;\n\n/**\n * @hidden\n */\nexport function shouldIgnoreOnError(): boolean {\n  return ignoreOnError > 0;\n}\n\n/**\n * @hidden\n */\nexport function ignoreNextOnError(): void {\n  // onerror should trigger before setTimeout\n  ignoreOnError += 1;\n  setTimeout(() => {\n    ignoreOnError -= 1;\n  });\n}\n\n/**\n * Instruments the given function and sends an event to Sentry every time the\n * function throws an exception.\n *\n * @param fn A function to wrap.\n * @returns The wrapped function.\n * @hidden\n */\nexport function wrap(\n  fn: WrappedFunction,\n  options: {\n    mechanism?: Mechanism;\n  } = {},\n  before?: WrappedFunction,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n): any {\n  if (typeof fn !== 'function') {\n    return fn;\n  }\n\n  try {\n    // We don't wanna wrap it twice\n    if (fn.__sentry__) {\n      return fn;\n    }\n\n    // If this has already been wrapped in the past, return that wrapped function\n    if (fn.__sentry_wrapped__) {\n      return fn.__sentry_wrapped__;\n    }\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    // Bail on wrapping and return the function as-is (defers to window.onerror).\n    return fn;\n  }\n\n  /* eslint-disable prefer-rest-params */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const sentryWrapped: WrappedFunction = function(this: any): void {\n    const args = Array.prototype.slice.call(arguments);\n\n    try {\n      if (before && typeof before === 'function') {\n        before.apply(this, arguments);\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access\n      const wrappedArguments = args.map((arg: any) => wrap(arg, options));\n\n      if (fn.handleEvent) {\n        // Attempt to invoke user-land function\n        // NOTE: If you are a Sentry user, and you are seeing this stack frame, it\n        //       means the sentry.javascript SDK caught an error invoking your application code. This\n        //       is expected behavior and NOT indicative of a bug with sentry.javascript.\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        return fn.handleEvent.apply(this, wrappedArguments);\n      }\n      // Attempt to invoke user-land function\n      // NOTE: If you are a Sentry user, and you are seeing this stack frame, it\n      //       means the sentry.javascript SDK caught an error invoking your application code. This\n      //       is expected behavior and NOT indicative of a bug with sentry.javascript.\n      return fn.apply(this, wrappedArguments);\n    } catch (ex) {\n      ignoreNextOnError();\n\n      withScope((scope: Scope) => {\n        scope.addEventProcessor((event: SentryEvent) => {\n          const processedEvent = { ...event };\n\n          if (options.mechanism) {\n            addExceptionTypeValue(processedEvent, undefined, undefined);\n            addExceptionMechanism(processedEvent, options.mechanism);\n          }\n\n          processedEvent.extra = {\n            ...processedEvent.extra,\n            arguments: args,\n          };\n\n          return processedEvent;\n        });\n\n        captureException(ex);\n      });\n\n      throw ex;\n    }\n  };\n  /* eslint-enable prefer-rest-params */\n\n  // Accessing some objects may throw\n  // ref: https://github.com/getsentry/sentry-javascript/issues/1168\n  try {\n    for (const property in fn) {\n      if (Object.prototype.hasOwnProperty.call(fn, property)) {\n        sentryWrapped[property] = fn[property];\n      }\n    }\n  } catch (_oO) {} // eslint-disable-line no-empty\n\n  fn.prototype = fn.prototype || {};\n  sentryWrapped.prototype = fn.prototype;\n\n  Object.defineProperty(fn, '__sentry_wrapped__', {\n    enumerable: false,\n    value: sentryWrapped,\n  });\n\n  // Signal that this function has been wrapped/filled already\n  // for both debugging and to prevent it to being wrapped/filled twice\n  Object.defineProperties(sentryWrapped, {\n    __sentry__: {\n      enumerable: false,\n      value: true,\n    },\n    __sentry_original__: {\n      enumerable: false,\n      value: fn,\n    },\n  });\n\n  // Restore original function name (not all browsers allow that)\n  try {\n    const descriptor = Object.getOwnPropertyDescriptor(sentryWrapped, 'name') as PropertyDescriptor;\n    if (descriptor.configurable) {\n      Object.defineProperty(sentryWrapped, 'name', {\n        get(): string {\n          return fn.name;\n        },\n      });\n    }\n    // eslint-disable-next-line no-empty\n  } catch (_oO) {}\n\n  return sentryWrapped;\n}\n\n/**\n * All properties the report dialog supports\n */\nexport interface ReportDialogOptions {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key: string]: any;\n  eventId?: string;\n  dsn?: DsnLike;\n  user?: {\n    email?: string;\n    name?: string;\n  };\n  lang?: string;\n  title?: string;\n  subtitle?: string;\n  subtitle2?: string;\n  labelName?: string;\n  labelEmail?: string;\n  labelComments?: string;\n  labelClose?: string;\n  labelSubmit?: string;\n  errorGeneric?: string;\n  errorFormEntry?: string;\n  successMessage?: string;\n  /** Callback after reportDialog showed up */\n  onLoad?(): void;\n}\n\n/**\n * Injects the Report Dialog script\n * @hidden\n */\nexport function injectReportDialog(options: ReportDialogOptions = {}): void {\n  if (!options.eventId) {\n    logger.error(`Missing eventId option in showReportDialog call`);\n    return;\n  }\n  if (!options.dsn) {\n    logger.error(`Missing dsn option in showReportDialog call`);\n    return;\n  }\n\n  const script = document.createElement('script');\n  script.async = true;\n  script.src = new API(options.dsn).getReportDialogEndpoint(options);\n\n  if (options.onLoad) {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    script.onload = options.onLoad;\n  }\n\n  (document.head || document.body).appendChild(script);\n}\n", "/* eslint-disable @typescript-eslint/no-unsafe-member-access */\nimport { getCurrentHub } from '@sentry/core';\nimport { Event, Integration, Primitive, Severity } from '@sentry/types';\nimport {\n  addExceptionMechanism,\n  addInstrumentationHandler,\n  getLocationHref,\n  isErrorEvent,\n  isPrimitive,\n  isString,\n  logger,\n} from '@sentry/utils';\n\nimport { eventFromUnknownInput } from '../eventbuilder';\nimport { shouldIgnoreOnError } from '../helpers';\n\n/** JSDoc */\ninterface GlobalHandlersIntegrations {\n  onerror: boolean;\n  onunhandledrejection: boolean;\n}\n\n/** Global handlers */\nexport class GlobalHandlers implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'GlobalHandlers';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = GlobalHandlers.id;\n\n  /** JSDoc */\n  private readonly _options: GlobalHandlersIntegrations;\n\n  /** JSDoc */\n  private _onErrorHandlerInstalled: boolean = false;\n\n  /** JSDoc */\n  private _onUnhandledRejectionHandlerInstalled: boolean = false;\n\n  /** JSDoc */\n  public constructor(options?: GlobalHandlersIntegrations) {\n    this._options = {\n      onerror: true,\n      onunhandledrejection: true,\n      ...options,\n    };\n  }\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    Error.stackTraceLimit = 50;\n\n    if (this._options.onerror) {\n      logger.log('Global Handler attached: onerror');\n      this._installGlobalOnErrorHandler();\n    }\n\n    if (this._options.onunhandledrejection) {\n      logger.log('Global Handler attached: onunhandledrejection');\n      this._installGlobalOnUnhandledRejectionHandler();\n    }\n  }\n\n  /** JSDoc */\n  private _installGlobalOnErrorHandler(): void {\n    if (this._onErrorHandlerInstalled) {\n      return;\n    }\n\n    addInstrumentationHandler({\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      callback: (data: { msg: any; url: any; line: any; column: any; error: any }) => {\n        const error = data.error;\n        const currentHub = getCurrentHub();\n        const hasIntegration = currentHub.getIntegration(GlobalHandlers);\n        const isFailedOwnDelivery = error && error.__sentry_own_request__ === true;\n\n        if (!hasIntegration || shouldIgnoreOnError() || isFailedOwnDelivery) {\n          return;\n        }\n\n        const client = currentHub.getClient();\n        const event = isPrimitive(error)\n          ? this._eventFromIncompleteOnError(data.msg, data.url, data.line, data.column)\n          : this._enhanceEventWithInitialFrame(\n              eventFromUnknownInput(error, undefined, {\n                attachStacktrace: client && client.getOptions().attachStacktrace,\n                rejection: false,\n              }),\n              data.url,\n              data.line,\n              data.column,\n            );\n\n        addExceptionMechanism(event, {\n          handled: false,\n          type: 'onerror',\n        });\n\n        currentHub.captureEvent(event, {\n          originalException: error,\n        });\n      },\n      type: 'error',\n    });\n\n    this._onErrorHandlerInstalled = true;\n  }\n\n  /** JSDoc */\n  private _installGlobalOnUnhandledRejectionHandler(): void {\n    if (this._onUnhandledRejectionHandlerInstalled) {\n      return;\n    }\n\n    addInstrumentationHandler({\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      callback: (e: any) => {\n        let error = e;\n\n        // dig the object of the rejection out of known event types\n        try {\n          // PromiseRejectionEvents store the object of the rejection under 'reason'\n          // see https://developer.mozilla.org/en-US/docs/Web/API/PromiseRejectionEvent\n          if ('reason' in e) {\n            error = e.reason;\n          }\n          // something, somewhere, (likely a browser extension) effectively casts PromiseRejectionEvents\n          // to CustomEvents, moving the `promise` and `reason` attributes of the PRE into\n          // the CustomEvent's `detail` attribute, since they're not part of CustomEvent's spec\n          // see https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent and\n          // https://github.com/getsentry/sentry-javascript/issues/2380\n          else if ('detail' in e && 'reason' in e.detail) {\n            error = e.detail.reason;\n          }\n        } catch (_oO) {\n          // no-empty\n        }\n\n        const currentHub = getCurrentHub();\n        const hasIntegration = currentHub.getIntegration(GlobalHandlers);\n        const isFailedOwnDelivery = error && error.__sentry_own_request__ === true;\n\n        if (!hasIntegration || shouldIgnoreOnError() || isFailedOwnDelivery) {\n          return true;\n        }\n\n        const client = currentHub.getClient();\n        const event = isPrimitive(error)\n          ? this._eventFromRejectionWithPrimitive(error)\n          : eventFromUnknownInput(error, undefined, {\n              attachStacktrace: client && client.getOptions().attachStacktrace,\n              rejection: true,\n            });\n\n        event.level = Severity.Error;\n\n        addExceptionMechanism(event, {\n          handled: false,\n          type: 'onunhandledrejection',\n        });\n\n        currentHub.captureEvent(event, {\n          originalException: error,\n        });\n\n        return;\n      },\n      type: 'unhandledrejection',\n    });\n\n    this._onUnhandledRejectionHandlerInstalled = true;\n  }\n\n  /**\n   * This function creates a stack from an old, error-less onerror handler.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _eventFromIncompleteOnError(msg: any, url: any, line: any, column: any): Event {\n    const ERROR_TYPES_RE = /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;\n\n    // If 'message' is ErrorEvent, get real message from inside\n    let message = isErrorEvent(msg) ? msg.message : msg;\n    let name;\n\n    if (isString(message)) {\n      const groups = message.match(ERROR_TYPES_RE);\n      if (groups) {\n        name = groups[1];\n        message = groups[2];\n      }\n    }\n\n    const event = {\n      exception: {\n        values: [\n          {\n            type: name || 'Error',\n            value: message,\n          },\n        ],\n      },\n    };\n\n    return this._enhanceEventWithInitialFrame(event, url, line, column);\n  }\n\n  /**\n   * Create an event from a promise rejection where the `reason` is a primitive.\n   *\n   * @param reason: The `reason` property of the promise rejection\n   * @returns An Event object with an appropriate `exception` value\n   */\n  private _eventFromRejectionWithPrimitive(reason: Primitive): Event {\n    return {\n      exception: {\n        values: [\n          {\n            type: 'UnhandledRejection',\n            // String() is needed because the Primitive type includes symbols (which can't be automatically stringified)\n            value: `Non-Error promise rejection captured with value: ${String(reason)}`,\n          },\n        ],\n      },\n    };\n  }\n\n  /** JSDoc */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _enhanceEventWithInitialFrame(event: Event, url: any, line: any, column: any): Event {\n    event.exception = event.exception || {};\n    event.exception.values = event.exception.values || [];\n    event.exception.values[0] = event.exception.values[0] || {};\n    event.exception.values[0].stacktrace = event.exception.values[0].stacktrace || {};\n    event.exception.values[0].stacktrace.frames = event.exception.values[0].stacktrace.frames || [];\n\n    const colno = isNaN(parseInt(column, 10)) ? undefined : column;\n    const lineno = isNaN(parseInt(line, 10)) ? undefined : line;\n    const filename = isString(url) && url.length > 0 ? url : getLocationHref();\n\n    if (event.exception.values[0].stacktrace.frames.length === 0) {\n      event.exception.values[0].stacktrace.frames.push({\n        colno,\n        filename,\n        function: '?',\n        in_app: true,\n        lineno,\n      });\n    }\n\n    return event;\n  }\n}\n", "import { Integration, WrappedFunction } from '@sentry/types';\nimport { fill, getFunctionName, getGlobalObject } from '@sentry/utils';\n\nimport { wrap } from '../helpers';\n\nconst DEFAULT_EVENT_TARGET = [\n  'EventTarget',\n  'Window',\n  'Node',\n  'ApplicationCache',\n  'AudioTrackList',\n  'ChannelMergerNode',\n  'CryptoOperation',\n  'EventSource',\n  'FileReader',\n  'HTMLUnknownElement',\n  'IDBDatabase',\n  'IDBRequest',\n  'IDBTransaction',\n  'KeyOperation',\n  'MediaController',\n  'MessagePort',\n  'ModalWindow',\n  'Notification',\n  'SVGElementInstance',\n  'Screen',\n  'TextTrack',\n  'TextTrackCue',\n  'TextTrackList',\n  'WebSocket',\n  'WebSocketWorker',\n  'Worker',\n  'XMLHttpRequest',\n  'XMLHttpRequestEventTarget',\n  'XMLHttpRequestUpload',\n];\n\ntype XMLHttpRequestProp = 'onload' | 'onerror' | 'onprogress' | 'onreadystatechange';\n\n/** JSDoc */\ninterface TryCatchOptions {\n  setTimeout: boolean;\n  setInterval: boolean;\n  requestAnimationFrame: boolean;\n  XMLHttpRequest: boolean;\n  eventTarget: boolean | string[];\n}\n\n/** Wrap timer functions and event targets to catch errors and provide better meta data */\nexport class TryCatch implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'TryCatch';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = TryCatch.id;\n\n  /** JSDoc */\n  private readonly _options: TryCatchOptions;\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options?: Partial<TryCatchOptions>) {\n    this._options = {\n      XMLHttpRequest: true,\n      eventTarget: true,\n      requestAnimationFrame: true,\n      setInterval: true,\n      setTimeout: true,\n      ...options,\n    };\n  }\n\n  /**\n   * Wrap timer functions and event targets to catch errors\n   * and provide better metadata.\n   */\n  public setupOnce(): void {\n    const global = getGlobalObject();\n\n    if (this._options.setTimeout) {\n      fill(global, 'setTimeout', this._wrapTimeFunction.bind(this));\n    }\n\n    if (this._options.setInterval) {\n      fill(global, 'setInterval', this._wrapTimeFunction.bind(this));\n    }\n\n    if (this._options.requestAnimationFrame) {\n      fill(global, 'requestAnimationFrame', this._wrapRAF.bind(this));\n    }\n\n    if (this._options.XMLHttpRequest && 'XMLHttpRequest' in global) {\n      fill(XMLHttpRequest.prototype, 'send', this._wrapXHR.bind(this));\n    }\n\n    if (this._options.eventTarget) {\n      const eventTarget = Array.isArray(this._options.eventTarget) ? this._options.eventTarget : DEFAULT_EVENT_TARGET;\n      eventTarget.forEach(this._wrapEventTarget.bind(this));\n    }\n  }\n\n  /** JSDoc */\n  private _wrapTimeFunction(original: () => void): () => number {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return function(this: any, ...args: any[]): number {\n      const originalCallback = args[0];\n      args[0] = wrap(originalCallback, {\n        mechanism: {\n          data: { function: getFunctionName(original) },\n          handled: true,\n          type: 'instrument',\n        },\n      });\n      return original.apply(this, args);\n    };\n  }\n\n  /** JSDoc */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _wrapRAF(original: any): (callback: () => void) => any {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return function(this: any, callback: () => void): () => void {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      return original.call(\n        this,\n        wrap(callback, {\n          mechanism: {\n            data: {\n              function: 'requestAnimationFrame',\n              handler: getFunctionName(original),\n            },\n            handled: true,\n            type: 'instrument',\n          },\n        }),\n      );\n    };\n  }\n\n  /** JSDoc */\n  private _wrapEventTarget(target: string): void {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const global = getGlobalObject() as { [key: string]: any };\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    const proto = global[target] && global[target].prototype;\n\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    if (!proto || !proto.hasOwnProperty || !proto.hasOwnProperty('addEventListener')) {\n      return;\n    }\n\n    fill(proto, 'addEventListener', function(\n      original: () => void,\n    ): (eventName: string, fn: EventListenerObject, options?: boolean | AddEventListenerOptions) => void {\n      return function(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this: any,\n        eventName: string,\n        fn: EventListenerObject,\n        options?: boolean | AddEventListenerOptions,\n      ): (eventName: string, fn: EventListenerObject, capture?: boolean, secure?: boolean) => void {\n        try {\n          if (typeof fn.handleEvent === 'function') {\n            fn.handleEvent = wrap(fn.handleEvent.bind(fn), {\n              mechanism: {\n                data: {\n                  function: 'handleEvent',\n                  handler: getFunctionName(fn),\n                  target,\n                },\n                handled: true,\n                type: 'instrument',\n              },\n            });\n          }\n        } catch (err) {\n          // can sometimes get 'Permission denied to access property \"handle Event'\n        }\n\n        return original.call(\n          this,\n          eventName,\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          wrap((fn as any) as WrappedFunction, {\n            mechanism: {\n              data: {\n                function: 'addEventListener',\n                handler: getFunctionName(fn),\n                target,\n              },\n              handled: true,\n              type: 'instrument',\n            },\n          }),\n          options,\n        );\n      };\n    });\n\n    fill(proto, 'removeEventListener', function(\n      originalRemoveEventListener: () => void,\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    ): (this: any, eventName: string, fn: EventListenerObject, options?: boolean | EventListenerOptions) => () => void {\n      return function(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this: any,\n        eventName: string,\n        fn: EventListenerObject,\n        options?: boolean | EventListenerOptions,\n      ): () => void {\n        /**\n         * There are 2 possible scenarios here:\n         *\n         * 1. Someone passes a callback, which was attached prior to Sentry initialization, or by using unmodified\n         * method, eg. `document.addEventListener.call(el, name, handler). In this case, we treat this function\n         * as a pass-through, and call original `removeEventListener` with it.\n         *\n         * 2. Someone passes a callback, which was attached after Sentry was initialized, which means that it was using\n         * our wrapped version of `addEventListener`, which internally calls `wrap` helper.\n         * This helper \"wraps\" whole callback inside a try/catch statement, and attached appropriate metadata to it,\n         * in order for us to make a distinction between wrapped/non-wrapped functions possible.\n         * If a function was wrapped, it has additional property of `__sentry_wrapped__`, holding the handler.\n         *\n         * When someone adds a handler prior to initialization, and then do it again, but after,\n         * then we have to detach both of them. Otherwise, if we'd detach only wrapped one, it'd be impossible\n         * to get rid of the initial handler and it'd stick there forever.\n         */\n        const wrappedEventHandler = (fn as unknown) as WrappedFunction;\n        try {\n          const originalEventHandler = wrappedEventHandler?.__sentry_wrapped__;\n          if (originalEventHandler) {\n            originalRemoveEventListener.call(this, eventName, originalEventHandler, options);\n          }\n        } catch (e) {\n          // ignore, accessing __sentry_wrapped__ will throw in some Selenium environments\n        }\n        return originalRemoveEventListener.call(this, eventName, wrappedEventHandler, options);\n      };\n    });\n  }\n\n  /** JSDoc */\n  private _wrapXHR(originalSend: () => void): () => void {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return function(this: XMLHttpRequest, ...args: any[]): void {\n      // eslint-disable-next-line @typescript-eslint/no-this-alias\n      const xhr = this;\n      const xmlHttpRequestProps: XMLHttpRequestProp[] = ['onload', 'onerror', 'onprogress', 'onreadystatechange'];\n\n      xmlHttpRequestProps.forEach(prop => {\n        if (prop in xhr && typeof xhr[prop] === 'function') {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          fill(xhr, prop, function(original: WrappedFunction): () => any {\n            const wrapOptions = {\n              mechanism: {\n                data: {\n                  function: prop,\n                  handler: getFunctionName(original),\n                },\n                handled: true,\n                type: 'instrument',\n              },\n            };\n\n            // If Instrument integration has been called before TryCatch, get the name of original function\n            if (original.__sentry_original__) {\n              wrapOptions.mechanism.data.handler = getFunctionName(original.__sentry_original__);\n            }\n\n            // Otherwise wrap directly\n            return wrap(original, wrapOptions);\n          });\n        }\n      });\n\n      return originalSend.apply(this, args);\n    };\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable max-lines */\nimport { getCurrentHub } from '@sentry/core';\nimport { Event, Integration, Severity } from '@sentry/types';\nimport {\n  addInstrumentationHandler,\n  getEventDescription,\n  getGlobalObject,\n  htmlTreeAsString,\n  parseUrl,\n  safeJoin,\n} from '@sentry/utils';\n\n/** JSDoc */\ninterface BreadcrumbsOptions {\n  console: boolean;\n  dom: boolean;\n  fetch: boolean;\n  history: boolean;\n  sentry: boolean;\n  xhr: boolean;\n}\n\n/**\n * Default Breadcrumbs instrumentations\n * TODO: Deprecated - with v6, this will be renamed to `Instrument`\n */\nexport class Breadcrumbs implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Breadcrumbs';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = Breadcrumbs.id;\n\n  /** JSDoc */\n  private readonly _options: BreadcrumbsOptions;\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options?: Partial<BreadcrumbsOptions>) {\n    this._options = {\n      console: true,\n      dom: true,\n      fetch: true,\n      history: true,\n      sentry: true,\n      xhr: true,\n      ...options,\n    };\n  }\n\n  /**\n   * Create a breadcrumb of `sentry` from the events themselves\n   */\n  public addSentryBreadcrumb(event: Event): void {\n    if (!this._options.sentry) {\n      return;\n    }\n    getCurrentHub().addBreadcrumb(\n      {\n        category: `sentry.${event.type === 'transaction' ? 'transaction' : 'event'}`,\n        event_id: event.event_id,\n        level: event.level,\n        message: getEventDescription(event),\n      },\n      {\n        event,\n      },\n    );\n  }\n\n  /**\n   * Instrument browser built-ins w/ breadcrumb capturing\n   *  - Console API\n   *  - DOM API (click/typing)\n   *  - XMLHttpRequest API\n   *  - Fetch API\n   *  - History API\n   */\n  public setupOnce(): void {\n    if (this._options.console) {\n      addInstrumentationHandler({\n        callback: (...args) => {\n          this._consoleBreadcrumb(...args);\n        },\n        type: 'console',\n      });\n    }\n    if (this._options.dom) {\n      addInstrumentationHandler({\n        callback: (...args) => {\n          this._domBreadcrumb(...args);\n        },\n        type: 'dom',\n      });\n    }\n    if (this._options.xhr) {\n      addInstrumentationHandler({\n        callback: (...args) => {\n          this._xhrBreadcrumb(...args);\n        },\n        type: 'xhr',\n      });\n    }\n    if (this._options.fetch) {\n      addInstrumentationHandler({\n        callback: (...args) => {\n          this._fetchBreadcrumb(...args);\n        },\n        type: 'fetch',\n      });\n    }\n    if (this._options.history) {\n      addInstrumentationHandler({\n        callback: (...args) => {\n          this._historyBreadcrumb(...args);\n        },\n        type: 'history',\n      });\n    }\n  }\n\n  /**\n   * Creates breadcrumbs from console API calls\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _consoleBreadcrumb(handlerData: { [key: string]: any }): void {\n    const breadcrumb = {\n      category: 'console',\n      data: {\n        arguments: handlerData.args,\n        logger: 'console',\n      },\n      level: Severity.fromString(handlerData.level),\n      message: safeJoin(handlerData.args, ' '),\n    };\n\n    if (handlerData.level === 'assert') {\n      if (handlerData.args[0] === false) {\n        breadcrumb.message = `Assertion failed: ${safeJoin(handlerData.args.slice(1), ' ') || 'console.assert'}`;\n        breadcrumb.data.arguments = handlerData.args.slice(1);\n      } else {\n        // Don't capture a breadcrumb for passed assertions\n        return;\n      }\n    }\n\n    getCurrentHub().addBreadcrumb(breadcrumb, {\n      input: handlerData.args,\n      level: handlerData.level,\n    });\n  }\n\n  /**\n   * Creates breadcrumbs from DOM API calls\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _domBreadcrumb(handlerData: { [key: string]: any }): void {\n    let target;\n\n    // Accessing event.target can throw (see getsentry/raven-js#838, #768)\n    try {\n      target = handlerData.event.target\n        ? htmlTreeAsString(handlerData.event.target as Node)\n        : htmlTreeAsString((handlerData.event as unknown) as Node);\n    } catch (e) {\n      target = '<unknown>';\n    }\n\n    if (target.length === 0) {\n      return;\n    }\n\n    getCurrentHub().addBreadcrumb(\n      {\n        category: `ui.${handlerData.name}`,\n        message: target,\n      },\n      {\n        event: handlerData.event,\n        name: handlerData.name,\n      },\n    );\n  }\n\n  /**\n   * Creates breadcrumbs from XHR API calls\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _xhrBreadcrumb(handlerData: { [key: string]: any }): void {\n    if (handlerData.endTimestamp) {\n      // We only capture complete, non-sentry requests\n      if (handlerData.xhr.__sentry_own_request__) {\n        return;\n      }\n\n      const { method, url, status_code, body } = handlerData.xhr.__sentry_xhr__ || {};\n\n      getCurrentHub().addBreadcrumb(\n        {\n          category: 'xhr',\n          data: {\n            method,\n            url,\n            status_code,\n          },\n          type: 'http',\n        },\n        {\n          xhr: handlerData.xhr,\n          input: body,\n        },\n      );\n\n      return;\n    }\n  }\n\n  /**\n   * Creates breadcrumbs from fetch API calls\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _fetchBreadcrumb(handlerData: { [key: string]: any }): void {\n    // We only capture complete fetch requests\n    if (!handlerData.endTimestamp) {\n      return;\n    }\n\n    if (handlerData.fetchData.url.match(/sentry_key/) && handlerData.fetchData.method === 'POST') {\n      // We will not create breadcrumbs for fetch requests that contain `sentry_key` (internal sentry requests)\n      return;\n    }\n\n    if (handlerData.error) {\n      getCurrentHub().addBreadcrumb(\n        {\n          category: 'fetch',\n          data: handlerData.fetchData,\n          level: Severity.Error,\n          type: 'http',\n        },\n        {\n          data: handlerData.error,\n          input: handlerData.args,\n        },\n      );\n    } else {\n      getCurrentHub().addBreadcrumb(\n        {\n          category: 'fetch',\n          data: {\n            ...handlerData.fetchData,\n            status_code: handlerData.response.status,\n          },\n          type: 'http',\n        },\n        {\n          input: handlerData.args,\n          response: handlerData.response,\n        },\n      );\n    }\n  }\n\n  /**\n   * Creates breadcrumbs from history API calls\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _historyBreadcrumb(handlerData: { [key: string]: any }): void {\n    const global = getGlobalObject<Window>();\n    let from = handlerData.from;\n    let to = handlerData.to;\n    const parsedLoc = parseUrl(global.location.href);\n    let parsedFrom = parseUrl(from);\n    const parsedTo = parseUrl(to);\n\n    // Initial pushState doesn't provide `from` information\n    if (!parsedFrom.path) {\n      parsedFrom = parsedLoc;\n    }\n\n    // Use only the path component of the URL if the URL matches the current\n    // document (almost all the time when using pushState)\n    if (parsedLoc.protocol === parsedTo.protocol && parsedLoc.host === parsedTo.host) {\n      to = parsedTo.relative;\n    }\n    if (parsedLoc.protocol === parsedFrom.protocol && parsedLoc.host === parsedFrom.host) {\n      from = parsedFrom.relative;\n    }\n\n    getCurrentHub().addBreadcrumb({\n      category: 'navigation',\n      data: {\n        from,\n        to,\n      },\n    });\n  }\n}\n", "import { addGlobalEventProcessor, getCurrentHub } from '@sentry/core';\nimport { Event, EventHint, Exception, ExtendedError, Integration } from '@sentry/types';\nimport { isInstanceOf } from '@sentry/utils';\n\nimport { exceptionFromStacktrace } from '../parsers';\nimport { computeStackTrace } from '../tracekit';\n\nconst DEFAULT_KEY = 'cause';\nconst DEFAULT_LIMIT = 5;\n\n/** Adds SDK info to an event. */\nexport class LinkedErrors implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'LinkedErrors';\n\n  /**\n   * @inheritDoc\n   */\n  public readonly name: string = LinkedErrors.id;\n\n  /**\n   * @inheritDoc\n   */\n  private readonly _key: string;\n\n  /**\n   * @inheritDoc\n   */\n  private readonly _limit: number;\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options: { key?: string; limit?: number } = {}) {\n    this._key = options.key || DEFAULT_KEY;\n    this._limit = options.limit || DEFAULT_LIMIT;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    addGlobalEventProcessor((event: Event, hint?: EventHint) => {\n      const self = getCurrentHub().getIntegration(LinkedErrors);\n      if (self) {\n        return self._handler(event, hint);\n      }\n      return event;\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  private _handler(event: Event, hint?: EventHint): Event | null {\n    if (!event.exception || !event.exception.values || !hint || !isInstanceOf(hint.originalException, Error)) {\n      return event;\n    }\n    const linkedErrors = this._walkErrorTree(hint.originalException as ExtendedError, this._key);\n    event.exception.values = [...linkedErrors, ...event.exception.values];\n    return event;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  private _walkErrorTree(error: ExtendedError, key: string, stack: Exception[] = []): Exception[] {\n    if (!isInstanceOf(error[key], Error) || stack.length + 1 >= this._limit) {\n      return stack;\n    }\n    const stacktrace = computeStackTrace(error[key]);\n    const exception = exceptionFromStacktrace(stacktrace);\n    return this._walkErrorTree(error[key], key, [exception, ...stack]);\n  }\n}\n", "import { addGlobalEventProcessor, getCurrentHub } from '@sentry/core';\nimport { Event, Integration } from '@sentry/types';\nimport { getGlobalObject } from '@sentry/utils';\n\nconst global = getGlobalObject<Window>();\n\n/** UserAgent */\nexport class UserAgent implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'UserAgent';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = UserAgent.id;\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    addGlobalEventProcessor((event: Event) => {\n      if (getCurrentHub().getIntegration(UserAgent)) {\n        // if none of the information we want exists, don't bother\n        if (!global.navigator && !global.location && !global.document) {\n          return event;\n        }\n\n        // grab as much info as exists and add it to the event\n        const url = event.request?.url || global.location?.href;\n        const { referrer } = global.document || {};\n        const { userAgent } = global.navigator || {};\n\n        const headers = {\n          ...event.request?.headers,\n          ...(referrer && { Referer: referrer }),\n          ...(userAgent && { 'User-Agent': userAgent }),\n        };\n        const request = { ...(url && { url }), headers };\n\n        return { ...event, request };\n      }\n      return event;\n    });\n  }\n}\n", "export const SDK_NAME = 'sentry.javascript.browser';\nexport const SDK_VERSION = '5.30.0';\n", "import { BaseClient, Scope } from '@sentry/core';\nimport { Event, EventHint } from '@sentry/types';\nimport { getGlobalObject, logger } from '@sentry/utils';\n\nimport { BrowserBackend, BrowserOptions } from './backend';\nimport { injectReportDialog, ReportDialogOptions } from './helpers';\nimport { Breadcrumbs } from './integrations';\nimport { SDK_NAME, SDK_VERSION } from './version';\n\n/**\n * The Sentry Browser SDK Client.\n *\n * @see BrowserOptions for documentation on configuration options.\n * @see SentryClient for usage documentation.\n */\nexport class BrowserClient extends BaseClient<BrowserBackend, BrowserOptions> {\n  /**\n   * Creates a new Browser SDK instance.\n   *\n   * @param options Configuration options for this SDK.\n   */\n  public constructor(options: BrowserOptions = {}) {\n    super(BrowserBackend, options);\n  }\n\n  /**\n   * Show a report dialog to the user to send feedback to a specific event.\n   *\n   * @param options Set individual options for the dialog\n   */\n  public showReportDialog(options: ReportDialogOptions = {}): void {\n    // doesn't work without a document (React Native)\n    const document = getGlobalObject<Window>().document;\n    if (!document) {\n      return;\n    }\n\n    if (!this._isEnabled()) {\n      logger.error('Trying to call showReportDialog with Sentry Client disabled');\n      return;\n    }\n\n    injectReportDialog({\n      ...options,\n      dsn: options.dsn || this.getDsn(),\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _prepareEvent(event: Event, scope?: Scope, hint?: EventHint): PromiseLike<Event | null> {\n    event.platform = event.platform || 'javascript';\n    event.sdk = {\n      ...event.sdk,\n      name: SDK_NAME,\n      packages: [\n        ...((event.sdk && event.sdk.packages) || []),\n        {\n          name: 'npm:@sentry/browser',\n          version: SDK_VERSION,\n        },\n      ],\n      version: SDK_VERSION,\n    };\n\n    return super._prepareEvent(event, scope, hint);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _sendEvent(event: Event): void {\n    const integration = this.getIntegration(Breadcrumbs);\n    if (integration) {\n      integration.addSentryBreadcrumb(event);\n    }\n    super._sendEvent(event);\n  }\n}\n", "import { getCurrentHub, initAndBind, Integrations as CoreIntegrations } from '@sentry/core';\nimport { getGlobalObject, SyncPromise } from '@sentry/utils';\n\nimport { BrowserOptions } from './backend';\nimport { BrowserClient } from './client';\nimport { ReportDialogOptions, wrap as internalWrap } from './helpers';\nimport { Breadcrumbs, GlobalHandlers, LinkedErrors, TryCatch, UserAgent } from './integrations';\n\nexport const defaultIntegrations = [\n  new CoreIntegrations.InboundFilters(),\n  new CoreIntegrations.FunctionToString(),\n  new TryCatch(),\n  new Breadcrumbs(),\n  new GlobalHandlers(),\n  new LinkedErrors(),\n  new UserAgent(),\n];\n\n/**\n * The Sentry Browser SDK Client.\n *\n * To use this SDK, call the {@link init} function as early as possible when\n * loading the web page. To set context information or send manual events, use\n * the provided methods.\n *\n * @example\n *\n * ```\n *\n * import { init } from '@sentry/browser';\n *\n * init({\n *   dsn: '__DSN__',\n *   // ...\n * });\n * ```\n *\n * @example\n * ```\n *\n * import { configureScope } from '@sentry/browser';\n * configureScope((scope: Scope) => {\n *   scope.setExtra({ battery: 0.7 });\n *   scope.setTag({ user_mode: 'admin' });\n *   scope.setUser({ id: '4711' });\n * });\n * ```\n *\n * @example\n * ```\n *\n * import { addBreadcrumb } from '@sentry/browser';\n * addBreadcrumb({\n *   message: 'My Breadcrumb',\n *   // ...\n * });\n * ```\n *\n * @example\n *\n * ```\n *\n * import * as Sentry from '@sentry/browser';\n * Sentry.captureMessage('Hello, world!');\n * Sentry.captureException(new Error('Good bye'));\n * Sentry.captureEvent({\n *   message: 'Manual',\n *   stacktrace: [\n *     // ...\n *   ],\n * });\n * ```\n *\n * @see {@link BrowserOptions} for documentation on configuration options.\n */\nexport function init(options: BrowserOptions = {}): void {\n  if (options.defaultIntegrations === undefined) {\n    options.defaultIntegrations = defaultIntegrations;\n  }\n  if (options.release === undefined) {\n    const window = getGlobalObject<Window>();\n    // This supports the variable that sentry-webpack-plugin injects\n    if (window.SENTRY_RELEASE && window.SENTRY_RELEASE.id) {\n      options.release = window.SENTRY_RELEASE.id;\n    }\n  }\n  if (options.autoSessionTracking === undefined) {\n    options.autoSessionTracking = false;\n  }\n\n  initAndBind(BrowserClient, options);\n\n  if (options.autoSessionTracking) {\n    startSessionTracking();\n  }\n}\n\n/**\n * Present the user with a report dialog.\n *\n * @param options Everything is optional, we try to fetch all info need from the global scope.\n */\nexport function showReportDialog(options: ReportDialogOptions = {}): void {\n  if (!options.eventId) {\n    options.eventId = getCurrentHub().lastEventId();\n  }\n  const client = getCurrentHub().getClient<BrowserClient>();\n  if (client) {\n    client.showReportDialog(options);\n  }\n}\n\n/**\n * This is the getter for lastEventId.\n *\n * @returns The last event id of a captured event.\n */\nexport function lastEventId(): string | undefined {\n  return getCurrentHub().lastEventId();\n}\n\n/**\n * This function is here to be API compatible with the loader.\n * @hidden\n */\nexport function forceLoad(): void {\n  // Noop\n}\n\n/**\n * This function is here to be API compatible with the loader.\n * @hidden\n */\nexport function onLoad(callback: () => void): void {\n  callback();\n}\n\n/**\n * A promise that resolves when all current events have been sent.\n * If you provide a timeout and the queue takes longer to drain the promise returns false.\n *\n * @param timeout Maximum time in ms the client should wait.\n */\nexport function flush(timeout?: number): PromiseLike<boolean> {\n  const client = getCurrentHub().getClient<BrowserClient>();\n  if (client) {\n    return client.flush(timeout);\n  }\n  return SyncPromise.reject(false);\n}\n\n/**\n * A promise that resolves when all current events have been sent.\n * If you provide a timeout and the queue takes longer to drain the promise returns false.\n *\n * @param timeout Maximum time in ms the client should wait.\n */\nexport function close(timeout?: number): PromiseLike<boolean> {\n  const client = getCurrentHub().getClient<BrowserClient>();\n  if (client) {\n    return client.close(timeout);\n  }\n  return SyncPromise.reject(false);\n}\n\n/**\n * Wrap code within a try/catch block so the SDK is able to capture errors.\n *\n * @param fn A function to wrap.\n *\n * @returns The result of wrapped function call.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function wrap(fn: (...args: any) => any): any {\n  return internalWrap(fn)();\n}\n\n/**\n * Enable automatic Session Tracking for the initial page load.\n */\nfunction startSessionTracking(): void {\n  const window = getGlobalObject<Window>();\n  const hub = getCurrentHub();\n\n  /**\n   * We should be using `Promise.all([windowLoaded, firstContentfulPaint])` here,\n   * but, as always, it's not available in the IE10-11. Thanks IE.\n   */\n  let loadResolved = document.readyState === 'complete';\n  let fcpResolved = false;\n  const possiblyEndSession = (): void => {\n    if (fcpResolved && loadResolved) {\n      hub.endSession();\n    }\n  };\n  const resolveWindowLoaded = (): void => {\n    loadResolved = true;\n    possiblyEndSession();\n    window.removeEventListener('load', resolveWindowLoaded);\n  };\n\n  hub.startSession();\n\n  if (!loadResolved) {\n    // IE doesn't support `{ once: true }` for event listeners, so we have to manually\n    // attach and then detach it once completed.\n    window.addEventListener('load', resolveWindowLoaded);\n  }\n\n  try {\n    const po = new PerformanceObserver((entryList, po) => {\n      entryList.getEntries().forEach(entry => {\n        if (entry.name === 'first-contentful-paint' && entry.startTime < firstHiddenTime) {\n          po.disconnect();\n          fcpResolved = true;\n          possiblyEndSession();\n        }\n      });\n    });\n\n    // There's no need to even attach this listener if `PerformanceObserver` constructor will fail,\n    // so we do it below here.\n    let firstHiddenTime = document.visibilityState === 'hidden' ? 0 : Infinity;\n    document.addEventListener(\n      'visibilitychange',\n      event => {\n        firstHiddenTime = Math.min(firstHiddenTime, event.timeStamp);\n      },\n      { once: true },\n    );\n\n    po.observe({\n      type: 'paint',\n      buffered: true,\n    });\n  } catch (e) {\n    fcpResolved = true;\n    possiblyEndSession();\n  }\n}\n", "export * from './exports';\n\nimport { Integrations as CoreIntegrations } from '@sentry/core';\nimport { getGlobalObject } from '@sentry/utils';\n\nimport * as BrowserIntegrations from './integrations';\nimport * as Transports from './transports';\n\nlet windowIntegrations = {};\n\n// This block is needed to add compatibility with the integrations packages when used with a CDN\nconst _window = getGlobalObject<Window>();\nif (_window.Sentry && _window.Sentry.Integrations) {\n  windowIntegrations = _window.Sentry.Integrations;\n}\n\nconst INTEGRATIONS = {\n  ...windowIntegrations,\n  ...CoreIntegrations,\n  ...BrowserIntegrations,\n};\n\nexport { INTEGRATIONS as Integrations, Transports };\n", "/** The status of an Span. */\n// eslint-disable-next-line import/export\nexport enum SpanStatus {\n  /** The operation completed successfully. */\n  Ok = 'ok',\n  /** Deadline expired before operation could complete. */\n  DeadlineExceeded = 'deadline_exceeded',\n  /** 401 Unauthorized (actually does mean unauthenticated according to RFC 7235) */\n  Unauthenticated = 'unauthenticated',\n  /** 403 Forbidden */\n  PermissionDenied = 'permission_denied',\n  /** 404 Not Found. Some requested entity (file or directory) was not found. */\n  NotFound = 'not_found',\n  /** 429 Too Many Requests */\n  ResourceExhausted = 'resource_exhausted',\n  /** Client specified an invalid argument. 4xx. */\n  InvalidArgument = 'invalid_argument',\n  /** 501 Not Implemented */\n  Unimplemented = 'unimplemented',\n  /** 503 Service Unavailable */\n  Unavailable = 'unavailable',\n  /** Other/generic 5xx. */\n  InternalError = 'internal_error',\n  /** Unknown. Any non-standard HTTP status code. */\n  UnknownError = 'unknown_error',\n  /** The operation was cancelled (typically by the user). */\n  Cancelled = 'cancelled',\n  /** Already exists (409) */\n  AlreadyExists = 'already_exists',\n  /** Operation was rejected because the system is not in a state required for the operation's */\n  FailedPrecondition = 'failed_precondition',\n  /** The operation was aborted, typically due to a concurrency issue. */\n  Aborted = 'aborted',\n  /** Operation was attempted past the valid range. */\n  OutOfRange = 'out_of_range',\n  /** Unrecoverable data loss or corruption */\n  DataLoss = 'data_loss',\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace, import/export\nexport namespace SpanStatus {\n  /**\n   * Converts a HTTP status code into a {@link SpanStatus}.\n   *\n   * @param httpStatus The HTTP response status code.\n   * @returns The span status or {@link SpanStatus.UnknownError}.\n   */\n  export function fromHttpCode(httpStatus: number): SpanStatus {\n    if (httpStatus < 400) {\n      return SpanStatus.Ok;\n    }\n\n    if (httpStatus >= 400 && httpStatus < 500) {\n      switch (httpStatus) {\n        case 401:\n          return SpanStatus.Unauthenticated;\n        case 403:\n          return SpanStatus.PermissionDenied;\n        case 404:\n          return SpanStatus.NotFound;\n        case 409:\n          return SpanStatus.AlreadyExists;\n        case 413:\n          return SpanStatus.FailedPrecondition;\n        case 429:\n          return SpanStatus.ResourceExhausted;\n        default:\n          return SpanStatus.InvalidArgument;\n      }\n    }\n\n    if (httpStatus >= 500 && httpStatus < 600) {\n      switch (httpStatus) {\n        case 501:\n          return SpanStatus.Unimplemented;\n        case 503:\n          return SpanStatus.Unavailable;\n        case 504:\n          return SpanStatus.DeadlineExceeded;\n        default:\n          return SpanStatus.InternalError;\n      }\n    }\n\n    return SpanStatus.UnknownError;\n  }\n}\n", "import { getCurrentH<PERSON>, Hub } from '@sentry/hub';\nimport { Options, TraceparentData, Transaction } from '@sentry/types';\n\nexport const TRACEPARENT_REGEXP = new RegExp(\n  '^[ \\\\t]*' + // whitespace\n  '([0-9a-f]{32})?' + // trace_id\n  '-?([0-9a-f]{16})?' + // span_id\n  '-?([01])?' + // sampled\n    '[ \\\\t]*$', // whitespace\n);\n\n/**\n * Determines if tracing is currently enabled.\n *\n * Tracing is enabled when at least one of `tracesSampleRate` and `tracesSampler` is defined in the SDK config.\n */\nexport function hasTracingEnabled(options: Options): boolean {\n  return 'tracesSampleRate' in options || 'tracesSampler' in options;\n}\n\n/**\n * Extract transaction context data from a `sentry-trace` header.\n *\n * @param traceparent Traceparent string\n *\n * @returns Object containing data from the header, or undefined if traceparent string is malformed\n */\nexport function extractTraceparentData(traceparent: string): TraceparentData | undefined {\n  const matches = traceparent.match(TRACEPARENT_REGEXP);\n  if (matches) {\n    let parentSampled: boolean | undefined;\n    if (matches[3] === '1') {\n      parentSampled = true;\n    } else if (matches[3] === '0') {\n      parentSampled = false;\n    }\n    return {\n      traceId: matches[1],\n      parentSampled,\n      parentSpanId: matches[2],\n    };\n  }\n  return undefined;\n}\n\n/** Grabs active transaction off scope, if any */\nexport function getActiveTransaction<T extends Transaction>(hub: Hub = getCurrentHub()): T | undefined {\n  return hub?.getScope()?.getTransaction() as T | undefined;\n}\n\n/**\n * Converts from milliseconds to seconds\n * @param time time in ms\n */\nexport function msToSec(time: number): number {\n  return time / 1000;\n}\n\n/**\n * Converts from seconds to milliseconds\n * @param time time in seconds\n */\nexport function secToMs(time: number): number {\n  return time * 1000;\n}\n\n// so it can be used in manual instrumentation without necessitating a hard dependency on @sentry/utils\nexport { stripUrlQueryAndFragment } from '@sentry/utils';\n", "import { addInstrumentation<PERSON>andler, logger } from '@sentry/utils';\n\nimport { SpanStatus } from './spanstatus';\nimport { getActiveTransaction } from './utils';\n\n/**\n * Configures global error listeners\n */\nexport function registerErrorInstrumentation(): void {\n  addInstrumentationHandler({\n    callback: errorCallback,\n    type: 'error',\n  });\n  addInstrumentationHandler({\n    callback: errorCallback,\n    type: 'unhandledrejection',\n  });\n}\n\n/**\n * If an error or unhandled promise occurs, we mark the active transaction as failed\n */\nfunction errorCallback(): void {\n  const activeTransaction = getActiveTransaction();\n  if (activeTransaction) {\n    logger.log(`[Tracing] Transaction: ${SpanStatus.InternalError} -> Global error occured`);\n    activeTransaction.setStatus(SpanStatus.InternalError);\n  }\n}\n", "/* eslint-disable max-lines */\nimport { Primitive, Span as SpanInterface, SpanContext, Transaction } from '@sentry/types';\nimport { dropUndefinedKeys, timestampWithMs, uuid4 } from '@sentry/utils';\n\nimport { SpanStatus } from './spanstatus';\n\n/**\n * Keeps track of finished spans for a given transaction\n * @internal\n * @hideconstructor\n * @hidden\n */\nexport class SpanRecorder {\n  public spans: Span[] = [];\n\n  private readonly _maxlen: number;\n\n  public constructor(maxlen: number = 1000) {\n    this._maxlen = maxlen;\n  }\n\n  /**\n   * This is just so that we don't run out of memory while recording a lot\n   * of spans. At some point we just stop and flush out the start of the\n   * trace tree (i.e.the first n spans with the smallest\n   * start_timestamp).\n   */\n  public add(span: Span): void {\n    if (this.spans.length > this._maxlen) {\n      span.spanRecorder = undefined;\n    } else {\n      this.spans.push(span);\n    }\n  }\n}\n\n/**\n * Span contains all data about a span\n */\nexport class Span implements SpanInterface {\n  /**\n   * @inheritDoc\n   */\n  public traceId: string = uuid4();\n\n  /**\n   * @inheritDoc\n   */\n  public spanId: string = uuid4().substring(16);\n\n  /**\n   * @inheritDoc\n   */\n  public parentSpanId?: string;\n\n  /**\n   * Internal keeper of the status\n   */\n  public status?: SpanStatus | string;\n\n  /**\n   * @inheritDoc\n   */\n  public sampled?: boolean;\n\n  /**\n   * Timestamp in seconds when the span was created.\n   */\n  public startTimestamp: number = timestampWithMs();\n\n  /**\n   * Timestamp in seconds when the span ended.\n   */\n  public endTimestamp?: number;\n\n  /**\n   * @inheritDoc\n   */\n  public op?: string;\n\n  /**\n   * @inheritDoc\n   */\n  public description?: string;\n\n  /**\n   * @inheritDoc\n   */\n  public tags: { [key: string]: Primitive } = {};\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  public data: { [key: string]: any } = {};\n\n  /**\n   * List of spans that were finalized\n   */\n  public spanRecorder?: SpanRecorder;\n\n  /**\n   * @inheritDoc\n   */\n  public transaction?: Transaction;\n\n  /**\n   * You should never call the constructor manually, always use `Sentry.startTransaction()`\n   * or call `startChild()` on an existing span.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(spanContext?: SpanContext) {\n    if (!spanContext) {\n      return this;\n    }\n    if (spanContext.traceId) {\n      this.traceId = spanContext.traceId;\n    }\n    if (spanContext.spanId) {\n      this.spanId = spanContext.spanId;\n    }\n    if (spanContext.parentSpanId) {\n      this.parentSpanId = spanContext.parentSpanId;\n    }\n    // We want to include booleans as well here\n    if ('sampled' in spanContext) {\n      this.sampled = spanContext.sampled;\n    }\n    if (spanContext.op) {\n      this.op = spanContext.op;\n    }\n    if (spanContext.description) {\n      this.description = spanContext.description;\n    }\n    if (spanContext.data) {\n      this.data = spanContext.data;\n    }\n    if (spanContext.tags) {\n      this.tags = spanContext.tags;\n    }\n    if (spanContext.status) {\n      this.status = spanContext.status;\n    }\n    if (spanContext.startTimestamp) {\n      this.startTimestamp = spanContext.startTimestamp;\n    }\n    if (spanContext.endTimestamp) {\n      this.endTimestamp = spanContext.endTimestamp;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   * @deprecated\n   */\n  public child(\n    spanContext?: Pick<SpanContext, Exclude<keyof SpanContext, 'spanId' | 'sampled' | 'traceId' | 'parentSpanId'>>,\n  ): Span {\n    return this.startChild(spanContext);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public startChild(\n    spanContext?: Pick<SpanContext, Exclude<keyof SpanContext, 'spanId' | 'sampled' | 'traceId' | 'parentSpanId'>>,\n  ): Span {\n    const childSpan = new Span({\n      ...spanContext,\n      parentSpanId: this.spanId,\n      sampled: this.sampled,\n      traceId: this.traceId,\n    });\n\n    childSpan.spanRecorder = this.spanRecorder;\n    if (childSpan.spanRecorder) {\n      childSpan.spanRecorder.add(childSpan);\n    }\n\n    childSpan.transaction = this.transaction;\n\n    return childSpan;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setTag(key: string, value: Primitive): this {\n    this.tags = { ...this.tags, [key]: value };\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public setData(key: string, value: any): this {\n    this.data = { ...this.data, [key]: value };\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setStatus(value: SpanStatus): this {\n    this.status = value;\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setHttpStatus(httpStatus: number): this {\n    this.setTag('http.status_code', String(httpStatus));\n    const spanStatus = SpanStatus.fromHttpCode(httpStatus);\n    if (spanStatus !== SpanStatus.UnknownError) {\n      this.setStatus(spanStatus);\n    }\n    return this;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public isSuccess(): boolean {\n    return this.status === SpanStatus.Ok;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public finish(endTimestamp?: number): void {\n    this.endTimestamp = typeof endTimestamp === 'number' ? endTimestamp : timestampWithMs();\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toTraceparent(): string {\n    let sampledString = '';\n    if (this.sampled !== undefined) {\n      sampledString = this.sampled ? '-1' : '-0';\n    }\n    return `${this.traceId}-${this.spanId}${sampledString}`;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTraceContext(): {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    data?: { [key: string]: any };\n    description?: string;\n    op?: string;\n    parent_span_id?: string;\n    span_id: string;\n    status?: string;\n    tags?: { [key: string]: Primitive };\n    trace_id: string;\n  } {\n    return dropUndefinedKeys({\n      data: Object.keys(this.data).length > 0 ? this.data : undefined,\n      description: this.description,\n      op: this.op,\n      parent_span_id: this.parentSpanId,\n      span_id: this.spanId,\n      status: this.status,\n      tags: Object.keys(this.tags).length > 0 ? this.tags : undefined,\n      trace_id: this.traceId,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toJSON(): {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    data?: { [key: string]: any };\n    description?: string;\n    op?: string;\n    parent_span_id?: string;\n    span_id: string;\n    start_timestamp: number;\n    status?: string;\n    tags?: { [key: string]: Primitive };\n    timestamp?: number;\n    trace_id: string;\n  } {\n    return dropUndefinedKeys({\n      data: Object.keys(this.data).length > 0 ? this.data : undefined,\n      description: this.description,\n      op: this.op,\n      parent_span_id: this.parentSpanId,\n      span_id: this.spanId,\n      start_timestamp: this.startTimestamp,\n      status: this.status,\n      tags: Object.keys(this.tags).length > 0 ? this.tags : undefined,\n      timestamp: this.endTimestamp,\n      trace_id: this.traceId,\n    });\n  }\n}\n", "import { getCurrentHub, Hub } from '@sentry/hub';\nimport { Event, Measurements, Transaction as TransactionInterface, TransactionContext } from '@sentry/types';\nimport { isInstanceOf, logger } from '@sentry/utils';\n\nimport { Span as SpanClass, SpanRecorder } from './span';\n\n/** JSDoc */\nexport class Transaction extends SpanClass implements TransactionInterface {\n  public name: string;\n  private _measurements: Measurements = {};\n\n  /**\n   * The reference to the current hub.\n   */\n  private readonly _hub: Hub = (getCurrentHub() as unknown) as Hub;\n\n  private readonly _trimEnd?: boolean;\n\n  /**\n   * This constructor should never be called manually. Those instrumenting tracing should use\n   * `Sentry.startTransaction()`, and internal methods should use `hub.startTransaction()`.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   */\n  public constructor(transactionContext: TransactionContext, hub?: Hub) {\n    super(transactionContext);\n\n    if (isInstanceOf(hub, Hub)) {\n      this._hub = hub as Hub;\n    }\n\n    this.name = transactionContext.name ? transactionContext.name : '';\n\n    this._trimEnd = transactionContext.trimEnd;\n\n    // this is because transactions are also spans, and spans have a transaction pointer\n    this.transaction = this;\n  }\n\n  /**\n   * JSDoc\n   */\n  public setName(name: string): void {\n    this.name = name;\n  }\n\n  /**\n   * Attaches SpanRecorder to the span itself\n   * @param maxlen maximum number of spans that can be recorded\n   */\n  public initSpanRecorder(maxlen: number = 1000): void {\n    if (!this.spanRecorder) {\n      this.spanRecorder = new SpanRecorder(maxlen);\n    }\n    this.spanRecorder.add(this);\n  }\n\n  /**\n   * Set observed measurements for this transaction.\n   * @hidden\n   */\n  public setMeasurements(measurements: Measurements): void {\n    this._measurements = { ...measurements };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public finish(endTimestamp?: number): string | undefined {\n    // This transaction is already finished, so we should not flush it again.\n    if (this.endTimestamp !== undefined) {\n      return undefined;\n    }\n\n    if (!this.name) {\n      logger.warn('Transaction has no name, falling back to `<unlabeled transaction>`.');\n      this.name = '<unlabeled transaction>';\n    }\n\n    // just sets the end timestamp\n    super.finish(endTimestamp);\n\n    if (this.sampled !== true) {\n      // At this point if `sampled !== true` we want to discard the transaction.\n      logger.log('[Tracing] Discarding transaction because its trace was not chosen to be sampled.');\n      return undefined;\n    }\n\n    const finishedSpans = this.spanRecorder ? this.spanRecorder.spans.filter(s => s !== this && s.endTimestamp) : [];\n\n    if (this._trimEnd && finishedSpans.length > 0) {\n      this.endTimestamp = finishedSpans.reduce((prev: SpanClass, current: SpanClass) => {\n        if (prev.endTimestamp && current.endTimestamp) {\n          return prev.endTimestamp > current.endTimestamp ? prev : current;\n        }\n        return prev;\n      }).endTimestamp;\n    }\n\n    const transaction: Event = {\n      contexts: {\n        trace: this.getTraceContext(),\n      },\n      spans: finishedSpans,\n      start_timestamp: this.startTimestamp,\n      tags: this.tags,\n      timestamp: this.endTimestamp,\n      transaction: this.name,\n      type: 'transaction',\n    };\n\n    const hasMeasurements = Object.keys(this._measurements).length > 0;\n\n    if (hasMeasurements) {\n      logger.log('[Measurements] Adding measurements to transaction', JSON.stringify(this._measurements, undefined, 2));\n      transaction.measurements = this._measurements;\n    }\n\n    return this._hub.captureEvent(transaction);\n  }\n}\n", "import { Hub } from '@sentry/hub';\nimport { TransactionContext } from '@sentry/types';\nimport { logger, timestampWithMs } from '@sentry/utils';\n\nimport { Span, SpanRecorder } from './span';\nimport { SpanStatus } from './spanstatus';\nimport { Transaction } from './transaction';\n\nexport const DEFAULT_IDLE_TIMEOUT = 1000;\n\n/**\n * @inheritDoc\n */\nexport class IdleTransactionSpanRecorder extends SpanRecorder {\n  public constructor(\n    private readonly _pushActivity: (id: string) => void,\n    private readonly _popActivity: (id: string) => void,\n    public transactionSpanId: string = '',\n    maxlen?: number,\n  ) {\n    super(maxlen);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public add(span: Span): void {\n    // We should make sure we do not push and pop activities for\n    // the transaction that this span recorder belongs to.\n    if (span.spanId !== this.transactionSpanId) {\n      // We patch span.finish() to pop an activity after setting an endTimestamp.\n      span.finish = (endTimestamp?: number) => {\n        span.endTimestamp = typeof endTimestamp === 'number' ? endTimestamp : timestampWithMs();\n        this._popActivity(span.spanId);\n      };\n\n      // We should only push new activities if the span does not have an end timestamp.\n      if (span.endTimestamp === undefined) {\n        this._pushActivity(span.spanId);\n      }\n    }\n\n    super.add(span);\n  }\n}\n\nexport type BeforeFinishCallback = (transactionSpan: IdleTransaction, endTimestamp: number) => void;\n\n/**\n * An IdleTransaction is a transaction that automatically finishes. It does this by tracking child spans as activities.\n * You can have multiple IdleTransactions active, but if the `onScope` option is specified, the idle transaction will\n * put itself on the scope on creation.\n */\nexport class IdleTransaction extends Transaction {\n  // Activities store a list of active spans\n  public activities: Record<string, boolean> = {};\n\n  // Stores reference to the timeout that calls _beat().\n  private _heartbeatTimer: number = 0;\n\n  // Track state of activities in previous heartbeat\n  private _prevHeartbeatString: string | undefined;\n\n  // Amount of times heartbeat has counted. Will cause transaction to finish after 3 beats.\n  private _heartbeatCounter: number = 0;\n\n  // We should not use heartbeat if we finished a transaction\n  private _finished: boolean = false;\n\n  private readonly _beforeFinishCallbacks: BeforeFinishCallback[] = [];\n\n  // If a transaction is created and no activities are added, we want to make sure that\n  // it times out properly. This is cleared and not used when activities are added.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  private _initTimeout: any;\n\n  public constructor(\n    transactionContext: TransactionContext,\n    private readonly _idleHub?: Hub,\n    // The time to wait in ms until the idle transaction will be finished. Default: 1000\n    private readonly _idleTimeout: number = DEFAULT_IDLE_TIMEOUT,\n    // If an idle transaction should be put itself on and off the scope automatically.\n    private readonly _onScope: boolean = false,\n  ) {\n    super(transactionContext, _idleHub);\n\n    if (_idleHub && _onScope) {\n      // There should only be one active transaction on the scope\n      clearActiveTransaction(_idleHub);\n\n      // We set the transaction here on the scope so error events pick up the trace\n      // context and attach it to the error.\n      logger.log(`Setting idle transaction on scope. Span ID: ${this.spanId}`);\n      _idleHub.configureScope(scope => scope.setSpan(this));\n    }\n  }\n\n  /** {@inheritDoc} */\n  public finish(endTimestamp: number = timestampWithMs()): string | undefined {\n    this._finished = true;\n    this.activities = {};\n\n    if (this.spanRecorder) {\n      logger.log('[Tracing] finishing IdleTransaction', new Date(endTimestamp * 1000).toISOString(), this.op);\n\n      for (const callback of this._beforeFinishCallbacks) {\n        callback(this, endTimestamp);\n      }\n\n      this.spanRecorder.spans = this.spanRecorder.spans.filter((span: Span) => {\n        // If we are dealing with the transaction itself, we just return it\n        if (span.spanId === this.spanId) {\n          return true;\n        }\n\n        // We cancel all pending spans with status \"cancelled\" to indicate the idle transaction was finished early\n        if (!span.endTimestamp) {\n          span.endTimestamp = endTimestamp;\n          span.setStatus(SpanStatus.Cancelled);\n          logger.log('[Tracing] cancelling span since transaction ended early', JSON.stringify(span, undefined, 2));\n        }\n\n        const keepSpan = span.startTimestamp < endTimestamp;\n        if (!keepSpan) {\n          logger.log(\n            '[Tracing] discarding Span since it happened after Transaction was finished',\n            JSON.stringify(span, undefined, 2),\n          );\n        }\n        return keepSpan;\n      });\n\n      // this._onScope is true if the transaction was previously on the scope.\n      if (this._onScope) {\n        clearActiveTransaction(this._idleHub);\n      }\n\n      logger.log('[Tracing] flushing IdleTransaction');\n    } else {\n      logger.log('[Tracing] No active IdleTransaction');\n    }\n\n    return super.finish(endTimestamp);\n  }\n\n  /**\n   * Register a callback function that gets excecuted before the transaction finishes.\n   * Useful for cleanup or if you want to add any additional spans based on current context.\n   *\n   * This is exposed because users have no other way of running something before an idle transaction\n   * finishes.\n   */\n  public registerBeforeFinishCallback(callback: BeforeFinishCallback): void {\n    this._beforeFinishCallbacks.push(callback);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public initSpanRecorder(maxlen?: number): void {\n    if (!this.spanRecorder) {\n      this._initTimeout = setTimeout(() => {\n        if (!this._finished) {\n          this.finish();\n        }\n      }, this._idleTimeout);\n\n      const pushActivity = (id: string): void => {\n        if (this._finished) {\n          return;\n        }\n        this._pushActivity(id);\n      };\n      const popActivity = (id: string): void => {\n        if (this._finished) {\n          return;\n        }\n        this._popActivity(id);\n      };\n\n      this.spanRecorder = new IdleTransactionSpanRecorder(pushActivity, popActivity, this.spanId, maxlen);\n\n      // Start heartbeat so that transactions do not run forever.\n      logger.log('Starting heartbeat');\n      this._pingHeartbeat();\n    }\n    this.spanRecorder.add(this);\n  }\n\n  /**\n   * Start tracking a specific activity.\n   * @param spanId The span id that represents the activity\n   */\n  private _pushActivity(spanId: string): void {\n    if (this._initTimeout) {\n      clearTimeout(this._initTimeout);\n      this._initTimeout = undefined;\n    }\n    logger.log(`[Tracing] pushActivity: ${spanId}`);\n    this.activities[spanId] = true;\n    logger.log('[Tracing] new activities count', Object.keys(this.activities).length);\n  }\n\n  /**\n   * Remove an activity from usage\n   * @param spanId The span id that represents the activity\n   */\n  private _popActivity(spanId: string): void {\n    if (this.activities[spanId]) {\n      logger.log(`[Tracing] popActivity ${spanId}`);\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this.activities[spanId];\n      logger.log('[Tracing] new activities count', Object.keys(this.activities).length);\n    }\n\n    if (Object.keys(this.activities).length === 0) {\n      const timeout = this._idleTimeout;\n      // We need to add the timeout here to have the real endtimestamp of the transaction\n      // Remember timestampWithMs is in seconds, timeout is in ms\n      const end = timestampWithMs() + timeout / 1000;\n\n      setTimeout(() => {\n        if (!this._finished) {\n          this.finish(end);\n        }\n      }, timeout);\n    }\n  }\n\n  /**\n   * Checks when entries of this.activities are not changing for 3 beats.\n   * If this occurs we finish the transaction.\n   */\n  private _beat(): void {\n    clearTimeout(this._heartbeatTimer);\n    // We should not be running heartbeat if the idle transaction is finished.\n    if (this._finished) {\n      return;\n    }\n\n    const keys = Object.keys(this.activities);\n    const heartbeatString = keys.length ? keys.reduce((prev: string, current: string) => prev + current) : '';\n\n    if (heartbeatString === this._prevHeartbeatString) {\n      this._heartbeatCounter += 1;\n    } else {\n      this._heartbeatCounter = 1;\n    }\n\n    this._prevHeartbeatString = heartbeatString;\n\n    if (this._heartbeatCounter >= 3) {\n      logger.log(`[Tracing] Transaction finished because of no change for 3 heart beats`);\n      this.setStatus(SpanStatus.DeadlineExceeded);\n      this.setTag('heartbeat', 'failed');\n      this.finish();\n    } else {\n      this._pingHeartbeat();\n    }\n  }\n\n  /**\n   * Pings the heartbeat\n   */\n  private _pingHeartbeat(): void {\n    logger.log(`pinging Heartbeat -> current counter: ${this._heartbeatCounter}`);\n    this._heartbeatTimer = (setTimeout(() => {\n      this._beat();\n    }, 5000) as unknown) as number;\n  }\n}\n\n/**\n * Reset active transaction on scope\n */\nfunction clearActiveTransaction(hub?: Hub): void {\n  if (hub) {\n    const scope = hub.getScope();\n    if (scope) {\n      const transaction = scope.getTransaction();\n      if (transaction) {\n        scope.setSpan(undefined);\n      }\n    }\n  }\n}\n", "import { getActive<PERSON><PERSON>in, get<PERSON>ain<PERSON><PERSON><PERSON>, Hub } from '@sentry/hub';\nimport { CustomSamplingContext, SamplingContext, TransactionContext, TransactionSamplingMethod } from '@sentry/types';\nimport {\n  dynamicRequire,\n  extractNodeRequestData,\n  getGlobalObject,\n  isInstanceOf,\n  isNodeEnv,\n  logger,\n} from '@sentry/utils';\n\nimport { registerErrorInstrumentation } from './errors';\nimport { IdleTransaction } from './idletransaction';\nimport { Transaction } from './transaction';\nimport { hasTracingEnabled } from './utils';\n\n/** Returns all trace headers that are currently on the top scope. */\nfunction traceHeaders(this: Hub): { [key: string]: string } {\n  const scope = this.getScope();\n  if (scope) {\n    const span = scope.getSpan();\n    if (span) {\n      return {\n        'sentry-trace': span.toTraceparent(),\n      };\n    }\n  }\n  return {};\n}\n\n/**\n * Makes a sampling decision for the given transaction and stores it on the transaction.\n *\n * Called every time a transaction is created. Only transactions which emerge with a `sampled` value of `true` will be\n * sent to Sentry.\n *\n * @param hub: The hub off of which to read config options\n * @param transaction: The transaction needing a sampling decision\n * @param samplingContext: Default and user-provided data which may be used to help make the decision\n *\n * @returns The given transaction with its `sampled` value set\n */\nfunction sample<T extends Transaction>(hub: Hub, transaction: T, samplingContext: SamplingContext): T {\n  const client = hub.getClient();\n  const options = (client && client.getOptions()) || {};\n\n  // nothing to do if there's no client or if tracing is disabled\n  if (!client || !hasTracingEnabled(options)) {\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // if the user has forced a sampling decision by passing a `sampled` value in their transaction context, go with that\n  if (transaction.sampled !== undefined) {\n    transaction.tags = { ...transaction.tags, __sentry_samplingMethod: TransactionSamplingMethod.Explicit };\n    return transaction;\n  }\n\n  // we would have bailed already if neither `tracesSampler` nor `tracesSampleRate` were defined, so one of these should\n  // work; prefer the hook if so\n  let sampleRate;\n  if (typeof options.tracesSampler === 'function') {\n    sampleRate = options.tracesSampler(samplingContext);\n    // cast the rate to a number first in case it's a boolean\n    transaction.tags = {\n      ...transaction.tags,\n      __sentry_samplingMethod: TransactionSamplingMethod.Sampler,\n      // TODO kmclb - once tag types are loosened, don't need to cast to string here\n      __sentry_sampleRate: String(Number(sampleRate)),\n    };\n  } else if (samplingContext.parentSampled !== undefined) {\n    sampleRate = samplingContext.parentSampled;\n    transaction.tags = { ...transaction.tags, __sentry_samplingMethod: TransactionSamplingMethod.Inheritance };\n  } else {\n    sampleRate = options.tracesSampleRate;\n    // cast the rate to a number first in case it's a boolean\n    transaction.tags = {\n      ...transaction.tags,\n      __sentry_samplingMethod: TransactionSamplingMethod.Rate,\n      // TODO kmclb - once tag types are loosened, don't need to cast to string here\n      __sentry_sampleRate: String(Number(sampleRate)),\n    };\n  }\n\n  // Since this is coming from the user (or from a function provided by the user), who knows what we might get. (The\n  // only valid values are booleans or numbers between 0 and 1.)\n  if (!isValidSampleRate(sampleRate)) {\n    logger.warn(`[Tracing] Discarding transaction because of invalid sample rate.`);\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // if the function returned 0 (or false), or if `tracesSampleRate` is 0, it's a sign the transaction should be dropped\n  if (!sampleRate) {\n    logger.log(\n      `[Tracing] Discarding transaction because ${\n        typeof options.tracesSampler === 'function'\n          ? 'tracesSampler returned 0 or false'\n          : 'a negative sampling decision was inherited or tracesSampleRate is set to 0'\n      }`,\n    );\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // Now we roll the dice. Math.random is inclusive of 0, but not of 1, so strict < is safe here. In case sampleRate is\n  // a boolean, the < comparison will cause it to be automatically cast to 1 if it's true and 0 if it's false.\n  transaction.sampled = Math.random() < (sampleRate as number | boolean);\n\n  // if we're not going to keep it, we're done\n  if (!transaction.sampled) {\n    logger.log(\n      `[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(\n        sampleRate,\n      )})`,\n    );\n    return transaction;\n  }\n\n  // at this point we know we're keeping the transaction, whether because of an inherited decision or because it got\n  // lucky with the dice roll\n  transaction.initSpanRecorder(options._experiments?.maxSpans as number);\n\n  logger.log(`[Tracing] starting ${transaction.op} transaction - ${transaction.name}`);\n  return transaction;\n}\n/**\n * Gets the correct context to pass to the tracesSampler, based on the environment (i.e., which SDK is being used)\n *\n * @returns The default sample context\n */\nfunction getDefaultSamplingContext(transactionContext: TransactionContext): SamplingContext {\n  // promote parent sampling decision (if any) for easy access\n  const { parentSampled } = transactionContext;\n  const defaultSamplingContext: SamplingContext = { transactionContext, parentSampled };\n\n  if (isNodeEnv()) {\n    const domain = getActiveDomain();\n\n    if (domain) {\n      // for all node servers that we currently support, we store the incoming request object (which is an instance of\n      // http.IncomingMessage) on the domain\n\n      // the domain members are stored as an array, so our only way to find the request is to iterate through the array\n      // and compare types\n\n      const nodeHttpModule = dynamicRequire(module, 'http');\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      const requestType = nodeHttpModule.IncomingMessage;\n\n      const request = domain.members.find(member => isInstanceOf(member, requestType));\n      if (request) {\n        defaultSamplingContext.request = extractNodeRequestData(request);\n      }\n    }\n  }\n\n  // we must be in browser-js (or some derivative thereof)\n  else {\n    // we use `getGlobalObject()` rather than `window` since service workers also have a `location` property on `self`\n    const globalObject = getGlobalObject<WindowOrWorkerGlobalScope>();\n\n    if ('location' in globalObject) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n      defaultSamplingContext.location = { ...(globalObject as any).location };\n    }\n  }\n\n  return defaultSamplingContext;\n}\n\n/**\n * Checks the given sample rate to make sure it is valid type and value (a boolean, or a number between 0 and 1).\n */\nfunction isValidSampleRate(rate: unknown): boolean {\n  // we need to check NaN explicitly because it's of type 'number' and therefore wouldn't get caught by this typecheck\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if (isNaN(rate as any) || !(typeof rate === 'number' || typeof rate === 'boolean')) {\n    logger.warn(\n      `[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(\n        rate,\n      )} of type ${JSON.stringify(typeof rate)}.`,\n    );\n    return false;\n  }\n\n  // in case sampleRate is a boolean, it will get automatically cast to 1 if it's true and 0 if it's false\n  if (rate < 0 || rate > 1) {\n    logger.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${rate}.`);\n    return false;\n  }\n  return true;\n}\n\n/**\n * Creates a new transaction and adds a sampling decision if it doesn't yet have one.\n *\n * The Hub.startTransaction method delegates to this method to do its work, passing the Hub instance in as `this`, as if\n * it had been called on the hub directly. Exists as a separate function so that it can be injected into the class as an\n * \"extension method.\"\n *\n * @param this: The Hub starting the transaction\n * @param transactionContext: Data used to configure the transaction\n * @param CustomSamplingContext: Optional data to be provided to the `tracesSampler` function (if any)\n *\n * @returns The new transaction\n *\n * @see {@link Hub.startTransaction}\n */\nfunction _startTransaction(\n  this: Hub,\n  transactionContext: TransactionContext,\n  customSamplingContext?: CustomSamplingContext,\n): Transaction {\n  const transaction = new Transaction(transactionContext, this);\n  return sample(this, transaction, {\n    ...getDefaultSamplingContext(transactionContext),\n    ...customSamplingContext,\n  });\n}\n\n/**\n * Create new idle transaction.\n */\nexport function startIdleTransaction(\n  hub: Hub,\n  transactionContext: TransactionContext,\n  idleTimeout?: number,\n  onScope?: boolean,\n): IdleTransaction {\n  const transaction = new IdleTransaction(transactionContext, hub, idleTimeout, onScope);\n  return sample(hub, transaction, getDefaultSamplingContext(transactionContext));\n}\n\n/**\n * @private\n */\nexport function _addTracingExtensions(): void {\n  const carrier = getMainCarrier();\n  if (carrier.__SENTRY__) {\n    carrier.__SENTRY__.extensions = carrier.__SENTRY__.extensions || {};\n    if (!carrier.__SENTRY__.extensions.startTransaction) {\n      carrier.__SENTRY__.extensions.startTransaction = _startTransaction;\n    }\n    if (!carrier.__SENTRY__.extensions.traceHeaders) {\n      carrier.__SENTRY__.extensions.traceHeaders = traceHeaders;\n    }\n  }\n}\n\n/**\n * This patches the global object and injects the Tracing extensions methods\n */\nexport function addExtensionMethods(): void {\n  _addTracingExtensions();\n\n  // If an error happens globally, we should make sure transaction status is set to error.\n  registerErrorInstrumentation();\n}\n", "import { getGlobalObject, logger } from '@sentry/utils';\n\nimport { IdleTransaction } from '../idletransaction';\nimport { SpanStatus } from '../spanstatus';\nimport { getActiveTransaction } from '../utils';\n\nconst global = getGlobalObject<Window>();\n\n/**\n * Add a listener that cancels and finishes a transaction when the global\n * document is hidden.\n */\nexport function registerBackgroundTabDetection(): void {\n  if (global && global.document) {\n    global.document.addEventListener('visibilitychange', () => {\n      const activeTransaction = getActiveTransaction() as IdleTransaction;\n      if (global.document.hidden && activeTransaction) {\n        logger.log(\n          `[Tracing] Transaction: ${SpanStatus.Cancelled} -> since tab moved to the background, op: ${activeTransaction.op}`,\n        );\n        // We should not set status if it is already set, this prevent important statuses like\n        // error or data loss from being overwritten on transaction.\n        if (!activeTransaction.status) {\n          activeTransaction.setStatus(SpanStatus.Cancelled);\n        }\n        activeTransaction.setTag('visibilitychange', 'document.hidden');\n        activeTransaction.finish();\n      }\n    });\n  } else {\n    logger.warn('[Tracing] Could not set up background tab detection due to lack of global document');\n  }\n}\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Metric, ReportHandler } from '../types';\n\nexport const bindReporter = (\n  callback: ReportHandler,\n  metric: Metric,\n  po: PerformanceObserver | undefined,\n  observeAllUpdates?: boolean,\n): (() => void) => {\n  let prevValue: number;\n  return () => {\n    if (po && metric.isFinal) {\n      po.disconnect();\n    }\n    if (metric.value >= 0) {\n      if (observeAllUpdates || metric.isFinal || document.visibilityState === 'hidden') {\n        metric.delta = metric.value - (prevValue || 0);\n\n        // Report the metric if there's a non-zero delta, if the metric is\n        // final, or if no previous value exists (which can happen in the case\n        // of the document becoming hidden when the metric value is 0).\n        // See: https://github.com/GoogleChrome/web-vitals/issues/14\n        if (metric.delta || metric.isFinal || prevValue === undefined) {\n          callback(metric);\n          prevValue = metric.value;\n        }\n      }\n    }\n  };\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { onHidden } from './onHidden';\n\nlet firstHiddenTime: number;\n\ntype HiddenType = {\n  readonly timeStamp: number;\n};\n\nexport const getFirstHidden = (): HiddenType => {\n  if (firstHiddenTime === undefined) {\n    // If the document is hidden when this code runs, assume it was hidden\n    // since navigation start. This isn't a perfect heuristic, but it's the\n    // best we can do until an API is available to support querying past\n    // visibilityState.\n    firstHiddenTime = document.visibilityState === 'hidden' ? 0 : Infinity;\n\n    // Update the time if/when the document becomes hidden.\n    onHidden(({ timeStamp }) => (firstHiddenTime = timeStamp), true);\n  }\n\n  return {\n    get timeStamp() {\n      return firstHiddenTime;\n    },\n  };\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nlet inputPromise: Promise<Event>;\n\nexport const whenInput = (): Promise<Event> => {\n  if (!inputPromise) {\n    inputPromise = new Promise(r => {\n      return ['scroll', 'keydown', 'pointerdown'].map(type => {\n        addEventListener(type, r, {\n          once: true,\n          passive: true,\n          capture: true,\n        });\n      });\n    });\n  }\n  return inputPromise;\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Metric } from '../types';\nimport { generateUniqueID } from './generateUniqueID';\n\nexport const initMetric = (name: Metric['name'], value = -1): Metric => {\n  return {\n    name,\n    value,\n    delta: 0,\n    entries: [],\n    id: generateUniqueID(),\n    isFinal: false,\n  };\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Performantly generate a unique, 27-char string by combining the current\n * timestamp with a 13-digit random number.\n * @return {string}\n */\nexport const generateUniqueID = (): string => {\n  return `${Date.now()}-${Math.floor(Math.random() * (9e12 - 1)) + 1e12}`;\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface PerformanceEntryHandler {\n  (entry: PerformanceEntry): void;\n}\n\n/**\n * Takes a performance entry type and a callback function, and creates a\n * `PerformanceObserver` instance that will observe the specified entry type\n * with buffering enabled and call the callback _for each entry_.\n *\n * This function also feature-detects entry support and wraps the logic in a\n * try/catch to avoid errors in unsupporting browsers.\n */\nexport const observe = (type: string, callback: PerformanceEntryHandler): PerformanceObserver | undefined => {\n  try {\n    if (PerformanceObserver.supportedEntryTypes.includes(type)) {\n      const po: PerformanceObserver = new PerformanceObserver(l => l.getEntries().map(callback));\n\n      po.observe({ type, buffered: true });\n      return po;\n    }\n  } catch (e) {\n    // Do nothing.\n  }\n  return;\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface OnHiddenCallback {\n  // TODO(philipwalton): add `isPersisted` if needed for bfcache.\n  ({ timeStamp, isUnloading }: { timeStamp: number; isUnloading: boolean }): void;\n}\n\nlet isUnloading = false;\nlet listenersAdded = false;\n\nconst onPageHide = (event: PageTransitionEvent): void => {\n  isUnloading = !event.persisted;\n};\n\nconst addListeners = (): void => {\n  addEventListener('pagehide', onPageHide);\n\n  // `beforeunload` is needed to fix this bug:\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=987409\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  addEventListener('beforeunload', () => {});\n};\n\nexport const onHidden = (cb: OnHiddenCallback, once = false): void => {\n  if (!listenersAdded) {\n    addListeners();\n    listenersAdded = true;\n  }\n\n  addEventListener(\n    'visibilitychange',\n    ({ timeStamp }) => {\n      if (document.visibilityState === 'hidden') {\n        cb({ timeStamp, isUnloading });\n      }\n    },\n    { capture: true, once },\n  );\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { getFirstHidden } from './lib/getFirstHidden';\nimport { initMetric } from './lib/initMetric';\nimport { observe, PerformanceEntryHandler } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { whenInput } from './lib/whenInput';\nimport { ReportHandler } from './types';\n\nexport const getLCP = (onReport: ReportHandler, reportAllChanges = false): void => {\n  const metric = initMetric('LCP');\n  const firstHidden = getFirstHidden();\n\n  let report: ReturnType<typeof bindReporter>;\n\n  const entryHandler = (entry: PerformanceEntry): void => {\n    // The startTime attribute returns the value of the renderTime if it is not 0,\n    // and the value of the loadTime otherwise.\n    const value = entry.startTime;\n\n    // If the page was hidden prior to paint time of the entry,\n    // ignore it and mark the metric as final, otherwise add the entry.\n    if (value < firstHidden.timeStamp) {\n      metric.value = value;\n      metric.entries.push(entry);\n    } else {\n      metric.isFinal = true;\n    }\n\n    report();\n  };\n\n  const po = observe('largest-contentful-paint', entryHandler);\n\n  if (po) {\n    report = bindReporter(onReport, metric, po, reportAllChanges);\n\n    const onFinal = (): void => {\n      if (!metric.isFinal) {\n        po.takeRecords().map(entryHandler as PerformanceEntryHandler);\n        metric.isFinal = true;\n        report();\n      }\n    };\n\n    void whenInput().then(onFinal);\n    onHidden(onFinal, true);\n  }\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getGlobalObject } from '@sentry/utils';\n\nimport { initMetric } from './lib/initMetric';\nimport { NavigationTimingPolyfillEntry, ReportHandler } from './types';\n\nconst global = getGlobalObject<Window>();\n\nconst afterLoad = (callback: () => void): void => {\n  if (document.readyState === 'complete') {\n    // Queue a task so the callback runs after `loadEventEnd`.\n    setTimeout(callback, 0);\n  } else {\n    // Use `pageshow` so the callback runs after `loadEventEnd`.\n    addEventListener('pageshow', callback);\n  }\n};\n\nconst getNavigationEntryFromPerformanceTiming = (): NavigationTimingPolyfillEntry => {\n  // Really annoying that TypeScript errors when using `PerformanceTiming`.\n  // eslint-disable-next-line deprecation/deprecation\n  const timing = global.performance.timing;\n\n  const navigationEntry: { [key: string]: number | string } = {\n    entryType: 'navigation',\n    startTime: 0,\n  };\n\n  for (const key in timing) {\n    if (key !== 'navigationStart' && key !== 'toJSON') {\n      navigationEntry[key] = Math.max((timing[key as keyof PerformanceTiming] as number) - timing.navigationStart, 0);\n    }\n  }\n  return navigationEntry as NavigationTimingPolyfillEntry;\n};\n\nexport const getTTFB = (onReport: ReportHandler): void => {\n  const metric = initMetric('TTFB');\n\n  afterLoad(() => {\n    try {\n      // Use the NavigationTiming L2 entry if available.\n      const navigationEntry =\n        global.performance.getEntriesByType('navigation')[0] || getNavigationEntryFromPerformanceTiming();\n\n      metric.value = metric.delta = (navigationEntry as PerformanceNavigationTiming).responseStart;\n\n      metric.entries = [navigationEntry];\n\n      onReport(metric);\n    } catch (error) {\n      // Do nothing.\n    }\n  });\n};\n", "/* eslint-disable max-lines */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { Measurements, SpanContext } from '@sentry/types';\nimport { browserPerformanceTimeOrigin, getGlobalObject, logger } from '@sentry/utils';\n\nimport { Span } from '../span';\nimport { Transaction } from '../transaction';\nimport { msToSec } from '../utils';\nimport { getCLS } from './web-vitals/getCLS';\nimport { getFID } from './web-vitals/getFID';\nimport { getLCP } from './web-vitals/getLCP';\nimport { getTTFB } from './web-vitals/getTTFB';\nimport { getFirstHidden } from './web-vitals/lib/getFirstHidden';\nimport { NavigatorDeviceMemory, NavigatorNetworkInformation } from './web-vitals/types';\n\nconst global = getGlobalObject<Window>();\n\n/** Class tracking metrics  */\nexport class MetricsInstrumentation {\n  private _measurements: Measurements = {};\n\n  private _performanceCursor: number = 0;\n\n  public constructor() {\n    if (global && global.performance) {\n      if (global.performance.mark) {\n        global.performance.mark('sentry-tracing-init');\n      }\n\n      this._trackCLS();\n      this._trackLCP();\n      this._trackFID();\n      this._trackTTFB();\n    }\n  }\n\n  /** Add performance related spans to a transaction */\n  public addPerformanceEntries(transaction: Transaction): void {\n    if (!global || !global.performance || !global.performance.getEntries || !browserPerformanceTimeOrigin) {\n      // Gatekeeper if performance API not available\n      return;\n    }\n\n    logger.log('[Tracing] Adding & adjusting spans using Performance API');\n\n    const timeOrigin = msToSec(browserPerformanceTimeOrigin);\n    let entryScriptSrc: string | undefined;\n\n    if (global.document) {\n      // eslint-disable-next-line @typescript-eslint/prefer-for-of\n      for (let i = 0; i < document.scripts.length; i++) {\n        // We go through all scripts on the page and look for 'data-entry'\n        // We remember the name and measure the time between this script finished loading and\n        // our mark 'sentry-tracing-init'\n        if (document.scripts[i].dataset.entry === 'true') {\n          entryScriptSrc = document.scripts[i].src;\n          break;\n        }\n      }\n    }\n\n    let entryScriptStartTimestamp: number | undefined;\n    let tracingInitMarkStartTime: number | undefined;\n\n    global.performance\n      .getEntries()\n      .slice(this._performanceCursor)\n      .forEach((entry: Record<string, any>) => {\n        const startTime = msToSec(entry.startTime as number);\n        const duration = msToSec(entry.duration as number);\n\n        if (transaction.op === 'navigation' && timeOrigin + startTime < transaction.startTimestamp) {\n          return;\n        }\n\n        switch (entry.entryType) {\n          case 'navigation':\n            addNavigationSpans(transaction, entry, timeOrigin);\n            break;\n          case 'mark':\n          case 'paint':\n          case 'measure': {\n            const startTimestamp = addMeasureSpans(transaction, entry, startTime, duration, timeOrigin);\n            if (tracingInitMarkStartTime === undefined && entry.name === 'sentry-tracing-init') {\n              tracingInitMarkStartTime = startTimestamp;\n            }\n\n            // capture web vitals\n\n            const firstHidden = getFirstHidden();\n            // Only report if the page wasn't hidden prior to the web vital.\n            const shouldRecord = entry.startTime < firstHidden.timeStamp;\n\n            if (entry.name === 'first-paint' && shouldRecord) {\n              logger.log('[Measurements] Adding FP');\n              this._measurements['fp'] = { value: entry.startTime };\n              this._measurements['mark.fp'] = { value: startTimestamp };\n            }\n\n            if (entry.name === 'first-contentful-paint' && shouldRecord) {\n              logger.log('[Measurements] Adding FCP');\n              this._measurements['fcp'] = { value: entry.startTime };\n              this._measurements['mark.fcp'] = { value: startTimestamp };\n            }\n\n            break;\n          }\n          case 'resource': {\n            const resourceName = (entry.name as string).replace(window.location.origin, '');\n            const endTimestamp = addResourceSpans(transaction, entry, resourceName, startTime, duration, timeOrigin);\n            // We remember the entry script end time to calculate the difference to the first init mark\n            if (entryScriptStartTimestamp === undefined && (entryScriptSrc || '').indexOf(resourceName) > -1) {\n              entryScriptStartTimestamp = endTimestamp;\n            }\n            break;\n          }\n          default:\n          // Ignore other entry types.\n        }\n      });\n\n    if (entryScriptStartTimestamp !== undefined && tracingInitMarkStartTime !== undefined) {\n      _startChild(transaction, {\n        description: 'evaluation',\n        endTimestamp: tracingInitMarkStartTime,\n        op: 'script',\n        startTimestamp: entryScriptStartTimestamp,\n      });\n    }\n\n    this._performanceCursor = Math.max(performance.getEntries().length - 1, 0);\n\n    this._trackNavigator(transaction);\n\n    // Measurements are only available for pageload transactions\n    if (transaction.op === 'pageload') {\n      // normalize applicable web vital values to be relative to transaction.startTimestamp\n\n      const timeOrigin = msToSec(browserPerformanceTimeOrigin);\n\n      ['fcp', 'fp', 'lcp', 'ttfb'].forEach(name => {\n        if (!this._measurements[name] || timeOrigin >= transaction.startTimestamp) {\n          return;\n        }\n\n        // The web vitals, fcp, fp, lcp, and ttfb, all measure relative to timeOrigin.\n        // Unfortunately, timeOrigin is not captured within the transaction span data, so these web vitals will need\n        // to be adjusted to be relative to transaction.startTimestamp.\n\n        const oldValue = this._measurements[name].value;\n        const measurementTimestamp = timeOrigin + msToSec(oldValue);\n        // normalizedValue should be in milliseconds\n        const normalizedValue = Math.abs((measurementTimestamp - transaction.startTimestamp) * 1000);\n\n        const delta = normalizedValue - oldValue;\n        logger.log(`[Measurements] Normalized ${name} from ${oldValue} to ${normalizedValue} (${delta})`);\n\n        this._measurements[name].value = normalizedValue;\n      });\n\n      if (this._measurements['mark.fid'] && this._measurements['fid']) {\n        // create span for FID\n\n        _startChild(transaction, {\n          description: 'first input delay',\n          endTimestamp: this._measurements['mark.fid'].value + msToSec(this._measurements['fid'].value),\n          op: 'web.vitals',\n          startTimestamp: this._measurements['mark.fid'].value,\n        });\n      }\n\n      transaction.setMeasurements(this._measurements);\n    }\n  }\n\n  /** Starts tracking the Cumulative Layout Shift on the current page. */\n  private _trackCLS(): void {\n    getCLS(metric => {\n      const entry = metric.entries.pop();\n\n      if (!entry) {\n        return;\n      }\n\n      logger.log('[Measurements] Adding CLS');\n      this._measurements['cls'] = { value: metric.value };\n    });\n  }\n\n  /**\n   * Capture the information of the user agent.\n   */\n  private _trackNavigator(transaction: Transaction): void {\n    const navigator = global.navigator as null | (Navigator & NavigatorNetworkInformation & NavigatorDeviceMemory);\n\n    if (!navigator) {\n      return;\n    }\n\n    // track network connectivity\n\n    const connection = navigator.connection;\n    if (connection) {\n      if (connection.effectiveType) {\n        transaction.setTag('effectiveConnectionType', connection.effectiveType);\n      }\n\n      if (connection.type) {\n        transaction.setTag('connectionType', connection.type);\n      }\n\n      if (isMeasurementValue(connection.rtt)) {\n        this._measurements['connection.rtt'] = { value: connection.rtt as number };\n      }\n\n      if (isMeasurementValue(connection.downlink)) {\n        this._measurements['connection.downlink'] = { value: connection.downlink as number };\n      }\n    }\n\n    if (isMeasurementValue(navigator.deviceMemory)) {\n      transaction.setTag('deviceMemory', String(navigator.deviceMemory));\n    }\n\n    if (isMeasurementValue(navigator.hardwareConcurrency)) {\n      transaction.setTag('hardwareConcurrency', String(navigator.hardwareConcurrency));\n    }\n  }\n\n  /** Starts tracking the Largest Contentful Paint on the current page. */\n  private _trackLCP(): void {\n    getLCP(metric => {\n      const entry = metric.entries.pop();\n\n      if (!entry) {\n        return;\n      }\n\n      const timeOrigin = msToSec(performance.timeOrigin);\n      const startTime = msToSec(entry.startTime as number);\n      logger.log('[Measurements] Adding LCP');\n      this._measurements['lcp'] = { value: metric.value };\n      this._measurements['mark.lcp'] = { value: timeOrigin + startTime };\n    });\n  }\n\n  /** Starts tracking the First Input Delay on the current page. */\n  private _trackFID(): void {\n    getFID(metric => {\n      const entry = metric.entries.pop();\n\n      if (!entry) {\n        return;\n      }\n\n      const timeOrigin = msToSec(performance.timeOrigin);\n      const startTime = msToSec(entry.startTime as number);\n      logger.log('[Measurements] Adding FID');\n      this._measurements['fid'] = { value: metric.value };\n      this._measurements['mark.fid'] = { value: timeOrigin + startTime };\n    });\n  }\n\n  /** Starts tracking the Time to First Byte on the current page. */\n  private _trackTTFB(): void {\n    getTTFB(metric => {\n      const entry = metric.entries.pop();\n\n      if (!entry) {\n        return;\n      }\n\n      logger.log('[Measurements] Adding TTFB');\n      this._measurements['ttfb'] = { value: metric.value };\n\n      // Capture the time spent making the request and receiving the first byte of the response\n      const requestTime = metric.value - ((metric.entries[0] ?? entry) as PerformanceNavigationTiming).requestStart;\n      this._measurements['ttfb.requestTime'] = { value: requestTime };\n    });\n  }\n}\n\n/** Instrument navigation entries */\nfunction addNavigationSpans(transaction: Transaction, entry: Record<string, any>, timeOrigin: number): void {\n  addPerformanceNavigationTiming(transaction, entry, 'unloadEvent', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'redirect', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'domContentLoadedEvent', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'loadEvent', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'connect', timeOrigin);\n  addPerformanceNavigationTiming(transaction, entry, 'secureConnection', timeOrigin, 'connectEnd');\n  addPerformanceNavigationTiming(transaction, entry, 'fetch', timeOrigin, 'domainLookupStart');\n  addPerformanceNavigationTiming(transaction, entry, 'domainLookup', timeOrigin);\n  addRequest(transaction, entry, timeOrigin);\n}\n\n/** Create measure related spans */\nfunction addMeasureSpans(\n  transaction: Transaction,\n  entry: Record<string, any>,\n  startTime: number,\n  duration: number,\n  timeOrigin: number,\n): number {\n  const measureStartTimestamp = timeOrigin + startTime;\n  const measureEndTimestamp = measureStartTimestamp + duration;\n\n  _startChild(transaction, {\n    description: entry.name as string,\n    endTimestamp: measureEndTimestamp,\n    op: entry.entryType as string,\n    startTimestamp: measureStartTimestamp,\n  });\n\n  return measureStartTimestamp;\n}\n\nexport interface ResourceEntry extends Record<string, unknown> {\n  initiatorType?: string;\n  transferSize?: number;\n  encodedBodySize?: number;\n  decodedBodySize?: number;\n}\n\n/** Create resource-related spans */\nexport function addResourceSpans(\n  transaction: Transaction,\n  entry: ResourceEntry,\n  resourceName: string,\n  startTime: number,\n  duration: number,\n  timeOrigin: number,\n): number | undefined {\n  // we already instrument based on fetch and xhr, so we don't need to\n  // duplicate spans here.\n  if (entry.initiatorType === 'xmlhttprequest' || entry.initiatorType === 'fetch') {\n    return undefined;\n  }\n\n  const data: Record<string, any> = {};\n  if ('transferSize' in entry) {\n    data['Transfer Size'] = entry.transferSize;\n  }\n  if ('encodedBodySize' in entry) {\n    data['Encoded Body Size'] = entry.encodedBodySize;\n  }\n  if ('decodedBodySize' in entry) {\n    data['Decoded Body Size'] = entry.decodedBodySize;\n  }\n\n  const startTimestamp = timeOrigin + startTime;\n  const endTimestamp = startTimestamp + duration;\n\n  _startChild(transaction, {\n    description: resourceName,\n    endTimestamp,\n    op: entry.initiatorType ? `resource.${entry.initiatorType}` : 'resource',\n    startTimestamp,\n    data,\n  });\n\n  return endTimestamp;\n}\n\n/** Create performance navigation related spans */\nfunction addPerformanceNavigationTiming(\n  transaction: Transaction,\n  entry: Record<string, any>,\n  event: string,\n  timeOrigin: number,\n  eventEnd?: string,\n): void {\n  const end = eventEnd ? (entry[eventEnd] as number | undefined) : (entry[`${event}End`] as number | undefined);\n  const start = entry[`${event}Start`] as number | undefined;\n  if (!start || !end) {\n    return;\n  }\n  _startChild(transaction, {\n    op: 'browser',\n    description: event,\n    startTimestamp: timeOrigin + msToSec(start),\n    endTimestamp: timeOrigin + msToSec(end),\n  });\n}\n\n/** Create request and response related spans */\nfunction addRequest(transaction: Transaction, entry: Record<string, any>, timeOrigin: number): void {\n  _startChild(transaction, {\n    op: 'browser',\n    description: 'request',\n    startTimestamp: timeOrigin + msToSec(entry.requestStart as number),\n    endTimestamp: timeOrigin + msToSec(entry.responseEnd as number),\n  });\n\n  _startChild(transaction, {\n    op: 'browser',\n    description: 'response',\n    startTimestamp: timeOrigin + msToSec(entry.responseStart as number),\n    endTimestamp: timeOrigin + msToSec(entry.responseEnd as number),\n  });\n}\n\n/**\n * Helper function to start child on transactions. This function will make sure that the transaction will\n * use the start timestamp of the created child span if it is earlier than the transactions actual\n * start timestamp.\n */\nexport function _startChild(transaction: Transaction, { startTimestamp, ...ctx }: SpanContext): Span {\n  if (startTimestamp && transaction.startTimestamp > startTimestamp) {\n    transaction.startTimestamp = startTimestamp;\n  }\n\n  return transaction.startChild({\n    startTimestamp,\n    ...ctx,\n  });\n}\n\n/**\n * Checks if a given value is a valid measurement value.\n */\nfunction isMeasurementValue(value: any): boolean {\n  return typeof value === 'number' && isFinite(value);\n}\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { initMetric } from './lib/initMetric';\nimport { observe, PerformanceEntryHandler } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { ReportHandler } from './types';\n\n// https://wicg.github.io/layout-instability/#sec-layout-shift\ninterface LayoutShift extends PerformanceEntry {\n  value: number;\n  hadRecentInput: boolean;\n}\n\nexport const getCLS = (onReport: ReportHandler, reportAllChanges = false): void => {\n  const metric = initMetric('CLS', 0);\n\n  let report: ReturnType<typeof bindReporter>;\n\n  const entryHandler = (entry: LayoutShift): void => {\n    // Only count layout shifts without recent user input.\n    if (!entry.hadRecentInput) {\n      (metric.value as number) += entry.value;\n      metric.entries.push(entry);\n      report();\n    }\n  };\n\n  const po = observe('layout-shift', entryHandler as PerformanceEntryHandler);\n  if (po) {\n    report = bindReporter(onReport, metric, po, reportAllChanges);\n\n    onHidden(({ isUnloading }) => {\n      po.takeRecords().map(entryHandler as PerformanceEntryHandler);\n\n      if (isUnloading) {\n        metric.isFinal = true;\n      }\n      report();\n    });\n  }\n};\n", "/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { getFirstHidden } from './lib/getFirstHidden';\nimport { initMetric } from './lib/initMetric';\nimport { observe, PerformanceEntryHandler } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { ReportHandler } from './types';\n\ninterface FIDPolyfillCallback {\n  (value: number, event: Event): void;\n}\n\ninterface FIDPolyfill {\n  onFirstInputDelay: (onReport: FIDPolyfillCallback) => void;\n}\n\ndeclare global {\n  interface Window {\n    perfMetrics: FIDPolyfill;\n  }\n}\n\n// https://wicg.github.io/event-timing/#sec-performance-event-timing\ninterface PerformanceEventTiming extends PerformanceEntry {\n  processingStart: DOMHighResTimeStamp;\n  cancelable?: boolean;\n  target?: Element;\n}\n\nexport const getFID = (onReport: ReportHandler): void => {\n  const metric = initMetric('FID');\n  const firstHidden = getFirstHidden();\n\n  const entryHandler = (entry: PerformanceEventTiming): void => {\n    // Only report if the page wasn't hidden prior to the first input.\n    if (entry.startTime < firstHidden.timeStamp) {\n      metric.value = entry.processingStart - entry.startTime;\n      metric.entries.push(entry);\n      metric.isFinal = true;\n      report();\n    }\n  };\n\n  const po = observe('first-input', entryHandler as PerformanceEntryHandler);\n  const report = bindReporter(onReport, metric, po);\n\n  if (po) {\n    onHidden(() => {\n      po.takeRecords().map(entryHandler as PerformanceEntryHandler);\n      po.disconnect();\n    }, true);\n  } else {\n    if (window.perfMetrics && window.perfMetrics.onFirstInputDelay) {\n      window.perfMetrics.onFirstInputDelay((value: number, event: Event) => {\n        // Only report if the page wasn't hidden prior to the first input.\n        if (event.timeStamp < firstHidden.timeStamp) {\n          metric.value = value;\n          metric.isFinal = true;\n          metric.entries = [\n            {\n              entryType: 'first-input',\n              name: event.type,\n              target: event.target,\n              cancelable: event.cancelable,\n              startTime: event.timeStamp,\n              processingStart: event.timeStamp + value,\n            } as PerformanceEventTiming,\n          ];\n          report();\n        }\n      });\n    }\n  }\n};\n", "import { getCurrentHub } from '@sentry/hub';\nimport { addInstrumentationHandler, isInstanceOf, isMatchingPattern } from '@sentry/utils';\n\nimport { Span } from '../span';\nimport { getActiveTransaction, hasTracingEnabled } from '../utils';\n\nexport const DEFAULT_TRACING_ORIGINS = ['localhost', /^\\//];\n\n/** Options for Request Instrumentation */\nexport interface RequestInstrumentationOptions {\n  /**\n   * List of strings / regex where the integration should create Spans out of. Additionally this will be used\n   * to define which outgoing requests the `sentry-trace` header will be attached to.\n   *\n   * Default: ['localhost', /^\\//] {@see DEFAULT_TRACING_ORIGINS}\n   */\n  tracingOrigins: Array<string | RegExp>;\n\n  /**\n   * Flag to disable patching all together for fetch requests.\n   *\n   * Default: true\n   */\n  traceFetch: boolean;\n\n  /**\n   * Flag to disable patching all together for xhr requests.\n   *\n   * Default: true\n   */\n  traceXHR: boolean;\n\n  /**\n   * This function will be called before creating a span for a request with the given url.\n   * Return false if you don't want a span for the given url.\n   *\n   * By default it uses the `tracingOrigins` options as a url match.\n   */\n  shouldCreateSpanForRequest?(url: string): boolean;\n}\n\n/** Data returned from fetch callback */\nexport interface FetchData {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  args: any[]; // the arguments passed to the fetch call itself\n  fetchData?: {\n    method: string;\n    url: string;\n    // span_id\n    __span?: string;\n  };\n\n  // TODO Should this be unknown instead? If we vendor types, make it a Response\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  response?: any;\n\n  startTimestamp: number;\n  endTimestamp?: number;\n}\n\n/** Data returned from XHR request */\nexport interface XHRData {\n  xhr?: {\n    __sentry_xhr__?: {\n      method: string;\n      url: string;\n      status_code: number;\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      data: Record<string, any>;\n    };\n    __sentry_xhr_span_id__?: string;\n    setRequestHeader?: (key: string, val: string) => void;\n    __sentry_own_request__?: boolean;\n  };\n  startTimestamp: number;\n  endTimestamp?: number;\n}\n\nexport const defaultRequestInstrumentationOptions: RequestInstrumentationOptions = {\n  traceFetch: true,\n  traceXHR: true,\n  tracingOrigins: DEFAULT_TRACING_ORIGINS,\n};\n\n/** Registers span creators for xhr and fetch requests  */\nexport function registerRequestInstrumentation(_options?: Partial<RequestInstrumentationOptions>): void {\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  const { traceFetch, traceXHR, tracingOrigins, shouldCreateSpanForRequest } = {\n    ...defaultRequestInstrumentationOptions,\n    ..._options,\n  };\n\n  // We should cache url -> decision so that we don't have to compute\n  // regexp everytime we create a request.\n  const urlMap: Record<string, boolean> = {};\n\n  const defaultShouldCreateSpan = (url: string): boolean => {\n    if (urlMap[url]) {\n      return urlMap[url];\n    }\n    const origins = tracingOrigins;\n    urlMap[url] =\n      origins.some((origin: string | RegExp) => isMatchingPattern(url, origin)) &&\n      !isMatchingPattern(url, 'sentry_key');\n    return urlMap[url];\n  };\n\n  // We want that our users don't have to re-implement shouldCreateSpanForRequest themselves\n  // That's why we filter out already unwanted Spans from tracingOrigins\n  let shouldCreateSpan = defaultShouldCreateSpan;\n  if (typeof shouldCreateSpanForRequest === 'function') {\n    shouldCreateSpan = (url: string) => {\n      return defaultShouldCreateSpan(url) && shouldCreateSpanForRequest(url);\n    };\n  }\n\n  const spans: Record<string, Span> = {};\n\n  if (traceFetch) {\n    addInstrumentationHandler({\n      callback: (handlerData: FetchData) => {\n        fetchCallback(handlerData, shouldCreateSpan, spans);\n      },\n      type: 'fetch',\n    });\n  }\n\n  if (traceXHR) {\n    addInstrumentationHandler({\n      callback: (handlerData: XHRData) => {\n        xhrCallback(handlerData, shouldCreateSpan, spans);\n      },\n      type: 'xhr',\n    });\n  }\n}\n\n/**\n * Create and track fetch request spans\n */\nexport function fetchCallback(\n  handlerData: FetchData,\n  shouldCreateSpan: (url: string) => boolean,\n  spans: Record<string, Span>,\n): void {\n  const currentClientOptions = getCurrentHub()\n    .getClient()\n    ?.getOptions();\n  if (\n    !(currentClientOptions && hasTracingEnabled(currentClientOptions)) ||\n    !(handlerData.fetchData && shouldCreateSpan(handlerData.fetchData.url))\n  ) {\n    return;\n  }\n\n  if (handlerData.endTimestamp && handlerData.fetchData.__span) {\n    const span = spans[handlerData.fetchData.__span];\n    if (span) {\n      const response = handlerData.response;\n      if (response) {\n        // TODO (kmclb) remove this once types PR goes through\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        span.setHttpStatus(response.status);\n      }\n      span.finish();\n\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete spans[handlerData.fetchData.__span];\n    }\n    return;\n  }\n\n  const activeTransaction = getActiveTransaction();\n  if (activeTransaction) {\n    const span = activeTransaction.startChild({\n      data: {\n        ...handlerData.fetchData,\n        type: 'fetch',\n      },\n      description: `${handlerData.fetchData.method} ${handlerData.fetchData.url}`,\n      op: 'http',\n    });\n\n    handlerData.fetchData.__span = span.spanId;\n    spans[span.spanId] = span;\n\n    const request = (handlerData.args[0] = handlerData.args[0] as string | Request);\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const options = (handlerData.args[1] = (handlerData.args[1] as { [key: string]: any }) || {});\n    let headers = options.headers;\n    if (isInstanceOf(request, Request)) {\n      headers = (request as Request).headers;\n    }\n    if (headers) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      if (typeof headers.append === 'function') {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        headers.append('sentry-trace', span.toTraceparent());\n      } else if (Array.isArray(headers)) {\n        headers = [...headers, ['sentry-trace', span.toTraceparent()]];\n      } else {\n        headers = { ...headers, 'sentry-trace': span.toTraceparent() };\n      }\n    } else {\n      headers = { 'sentry-trace': span.toTraceparent() };\n    }\n    options.headers = headers;\n  }\n}\n\n/**\n * Create and track xhr request spans\n */\nexport function xhrCallback(\n  handlerData: XHRData,\n  shouldCreateSpan: (url: string) => boolean,\n  spans: Record<string, Span>,\n): void {\n  const currentClientOptions = getCurrentHub()\n    .getClient()\n    ?.getOptions();\n  if (\n    !(currentClientOptions && hasTracingEnabled(currentClientOptions)) ||\n    !(handlerData.xhr && handlerData.xhr.__sentry_xhr__ && shouldCreateSpan(handlerData.xhr.__sentry_xhr__.url)) ||\n    handlerData.xhr.__sentry_own_request__\n  ) {\n    return;\n  }\n\n  const xhr = handlerData.xhr.__sentry_xhr__;\n\n  // check first if the request has finished and is tracked by an existing span which should now end\n  if (handlerData.endTimestamp && handlerData.xhr.__sentry_xhr_span_id__) {\n    const span = spans[handlerData.xhr.__sentry_xhr_span_id__];\n    if (span) {\n      span.setHttpStatus(xhr.status_code);\n      span.finish();\n\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete spans[handlerData.xhr.__sentry_xhr_span_id__];\n    }\n    return;\n  }\n\n  // if not, create a new span to track it\n  const activeTransaction = getActiveTransaction();\n  if (activeTransaction) {\n    const span = activeTransaction.startChild({\n      data: {\n        ...xhr.data,\n        type: 'xhr',\n        method: xhr.method,\n        url: xhr.url,\n      },\n      description: `${xhr.method} ${xhr.url}`,\n      op: 'http',\n    });\n\n    handlerData.xhr.__sentry_xhr_span_id__ = span.spanId;\n    spans[handlerData.xhr.__sentry_xhr_span_id__] = span;\n\n    if (handlerData.xhr.setRequestHeader) {\n      try {\n        handlerData.xhr.setRequestHeader('sentry-trace', span.toTraceparent());\n      } catch (_) {\n        // Error: InvalidStateError: Failed to execute 'setRequestHeader' on 'XMLHttpRequest': The object's state must be OPENED.\n      }\n    }\n  }\n}\n", "import { Transaction, TransactionContext } from '@sentry/types';\nimport { addInstrumentationHandler, getGlobalObject, logger } from '@sentry/utils';\n\nconst global = getGlobalObject<Window>();\n\n/**\n * Default function implementing pageload and navigation transactions\n */\nexport function defaultRoutingInstrumentation<T extends Transaction>(\n  startTransaction: (context: TransactionContext) => T | undefined,\n  startTransactionOnPageLoad: boolean = true,\n  startTransactionOnLocationChange: boolean = true,\n): void {\n  if (!global || !global.location) {\n    logger.warn('Could not initialize routing instrumentation due to invalid location');\n    return;\n  }\n\n  let startingUrl: string | undefined = global.location.href;\n\n  let activeTransaction: T | undefined;\n  if (startTransactionOnPageLoad) {\n    activeTransaction = startTransaction({ name: global.location.pathname, op: 'pageload' });\n  }\n\n  if (startTransactionOnLocationChange) {\n    addInstrumentationHandler({\n      callback: ({ to, from }: { to: string; from?: string }) => {\n        /**\n         * This early return is there to account for some cases where a navigation transaction starts right after\n         * long-running pageload. We make sure that if `from` is undefined and a valid `startingURL` exists, we don't\n         * create an uneccessary navigation transaction.\n         *\n         * This was hard to duplicate, but this behavior stopped as soon as this fix was applied. This issue might also\n         * only be caused in certain development environments where the usage of a hot module reloader is causing\n         * errors.\n         */\n        if (from === undefined && startingUrl && startingUrl.indexOf(to) !== -1) {\n          startingUrl = undefined;\n          return;\n        }\n\n        if (from !== to) {\n          startingUrl = undefined;\n          if (activeTransaction) {\n            logger.log(`[Tracing] Finishing current transaction with op: ${activeTransaction.op}`);\n            // If there's an open transaction on the scope, we need to finish it before creating an new one.\n            activeTransaction.finish();\n          }\n          activeTransaction = startTransaction({ name: global.location.pathname, op: 'navigation' });\n        }\n      },\n      type: 'history',\n    });\n  }\n}\n", "import { Hub } from '@sentry/hub';\nimport { EventProcessor, Integration, Transaction, TransactionContext } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\nimport { startIdleTransaction } from '../hubextensions';\nimport { DEFAULT_IDLE_TIMEOUT, IdleTransaction } from '../idletransaction';\nimport { SpanStatus } from '../spanstatus';\nimport { extractTraceparentData, secToMs } from '../utils';\nimport { registerBackgroundTabDetection } from './backgroundtab';\nimport { MetricsInstrumentation } from './metrics';\nimport {\n  defaultRequestInstrumentationOptions,\n  registerRequestInstrumentation,\n  RequestInstrumentationOptions,\n} from './request';\nimport { defaultRoutingInstrumentation } from './router';\n\nexport const DEFAULT_MAX_TRANSACTION_DURATION_SECONDS = 600;\n\n/** Options for Browser Tracing integration */\nexport interface BrowserTracingOptions extends RequestInstrumentationOptions {\n  /**\n   * The time to wait in ms until the transaction will be finished. The transaction will use the end timestamp of\n   * the last finished span as the endtime for the transaction.\n   * Time is in ms.\n   *\n   * Default: 1000\n   */\n  idleTimeout: number;\n\n  /**\n   * Flag to enable/disable creation of `navigation` transaction on history changes.\n   *\n   * Default: true\n   */\n  startTransactionOnLocationChange: boolean;\n\n  /**\n   * Flag to enable/disable creation of `pageload` transaction on first pageload.\n   *\n   * Default: true\n   */\n  startTransactionOnPageLoad: boolean;\n\n  /**\n   * The maximum duration of a transaction before it will be marked as \"deadline_exceeded\".\n   * If you never want to mark a transaction set it to 0.\n   * Time is in seconds.\n   *\n   * Default: 600\n   */\n  maxTransactionDuration: number;\n\n  /**\n   * Flag Transactions where tabs moved to background with \"cancelled\". Browser background tab timing is\n   * not suited towards doing precise measurements of operations. By default, we recommend that this option\n   * be enabled as background transactions can mess up your statistics in nondeterministic ways.\n   *\n   * Default: true\n   */\n  markBackgroundTransactions: boolean;\n\n  /**\n   * beforeNavigate is called before a pageload/navigation transaction is created and allows users to modify transaction\n   * context data, or drop the transaction entirely (by setting `sampled = false` in the context).\n   *\n   * Note: For legacy reasons, transactions can also be dropped by returning `undefined`.\n   *\n   * @param context: The context data which will be passed to `startTransaction` by default\n   *\n   * @returns A (potentially) modified context object, with `sampled = false` if the transaction should be dropped.\n   */\n  beforeNavigate?(context: TransactionContext): TransactionContext | undefined;\n\n  /**\n   * Instrumentation that creates routing change transactions. By default creates\n   * pageload and navigation transactions.\n   */\n  routingInstrumentation<T extends Transaction>(\n    startTransaction: (context: TransactionContext) => T | undefined,\n    startTransactionOnPageLoad?: boolean,\n    startTransactionOnLocationChange?: boolean,\n  ): void;\n}\n\nconst DEFAULT_BROWSER_TRACING_OPTIONS = {\n  idleTimeout: DEFAULT_IDLE_TIMEOUT,\n  markBackgroundTransactions: true,\n  maxTransactionDuration: DEFAULT_MAX_TRANSACTION_DURATION_SECONDS,\n  routingInstrumentation: defaultRoutingInstrumentation,\n  startTransactionOnLocationChange: true,\n  startTransactionOnPageLoad: true,\n  ...defaultRequestInstrumentationOptions,\n};\n\n/**\n * The Browser Tracing integration automatically instruments browser pageload/navigation\n * actions as transactions, and captures requests, metrics and errors as spans.\n *\n * The integration can be configured with a variety of options, and can be extended to use\n * any routing library. This integration uses {@see IdleTransaction} to create transactions.\n */\nexport class BrowserTracing implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'BrowserTracing';\n\n  /** Browser Tracing integration options */\n  public options: BrowserTracingOptions;\n\n  /**\n   * @inheritDoc\n   */\n  public name: string = BrowserTracing.id;\n\n  private _getCurrentHub?: () => Hub;\n\n  private readonly _metrics: MetricsInstrumentation = new MetricsInstrumentation();\n\n  private readonly _emitOptionsWarning: boolean = false;\n\n  public constructor(_options?: Partial<BrowserTracingOptions>) {\n    let tracingOrigins = defaultRequestInstrumentationOptions.tracingOrigins;\n    // NOTE: Logger doesn't work in constructors, as it's initialized after integrations instances\n    if (\n      _options &&\n      _options.tracingOrigins &&\n      Array.isArray(_options.tracingOrigins) &&\n      _options.tracingOrigins.length !== 0\n    ) {\n      tracingOrigins = _options.tracingOrigins;\n    } else {\n      this._emitOptionsWarning = true;\n    }\n\n    this.options = {\n      ...DEFAULT_BROWSER_TRACING_OPTIONS,\n      ..._options,\n      tracingOrigins,\n    };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    this._getCurrentHub = getCurrentHub;\n\n    if (this._emitOptionsWarning) {\n      logger.warn(\n        '[Tracing] You need to define `tracingOrigins` in the options. Set an array of urls or patterns to trace.',\n      );\n      logger.warn(\n        `[Tracing] We added a reasonable default for you: ${defaultRequestInstrumentationOptions.tracingOrigins}`,\n      );\n    }\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const {\n      routingInstrumentation,\n      startTransactionOnLocationChange,\n      startTransactionOnPageLoad,\n      markBackgroundTransactions,\n      traceFetch,\n      traceXHR,\n      tracingOrigins,\n      shouldCreateSpanForRequest,\n    } = this.options;\n\n    routingInstrumentation(\n      (context: TransactionContext) => this._createRouteTransaction(context),\n      startTransactionOnPageLoad,\n      startTransactionOnLocationChange,\n    );\n\n    if (markBackgroundTransactions) {\n      registerBackgroundTabDetection();\n    }\n\n    registerRequestInstrumentation({ traceFetch, traceXHR, tracingOrigins, shouldCreateSpanForRequest });\n  }\n\n  /** Create routing idle transaction. */\n  private _createRouteTransaction(context: TransactionContext): Transaction | undefined {\n    if (!this._getCurrentHub) {\n      logger.warn(`[Tracing] Did not create ${context.op} transaction because _getCurrentHub is invalid.`);\n      return undefined;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    const { beforeNavigate, idleTimeout, maxTransactionDuration } = this.options;\n\n    const parentContextFromHeader = context.op === 'pageload' ? getHeaderContext() : undefined;\n\n    const expandedContext = {\n      ...context,\n      ...parentContextFromHeader,\n      trimEnd: true,\n    };\n    const modifiedContext = typeof beforeNavigate === 'function' ? beforeNavigate(expandedContext) : expandedContext;\n\n    // For backwards compatibility reasons, beforeNavigate can return undefined to \"drop\" the transaction (prevent it\n    // from being sent to Sentry).\n    const finalContext = modifiedContext === undefined ? { ...expandedContext, sampled: false } : modifiedContext;\n\n    if (finalContext.sampled === false) {\n      logger.log(`[Tracing] Will not send ${finalContext.op} transaction because of beforeNavigate.`);\n    }\n\n    const hub = this._getCurrentHub();\n    const idleTransaction = startIdleTransaction(hub, finalContext, idleTimeout, true);\n    logger.log(`[Tracing] Starting ${finalContext.op} transaction on scope`);\n    idleTransaction.registerBeforeFinishCallback((transaction, endTimestamp) => {\n      this._metrics.addPerformanceEntries(transaction);\n      adjustTransactionDuration(secToMs(maxTransactionDuration), transaction, endTimestamp);\n    });\n\n    return idleTransaction as Transaction;\n  }\n}\n\n/**\n * Gets transaction context from a sentry-trace meta.\n *\n * @returns Transaction context data from the header or undefined if there's no header or the header is malformed\n */\nexport function getHeaderContext(): Partial<TransactionContext> | undefined {\n  const header = getMetaContent('sentry-trace');\n  if (header) {\n    return extractTraceparentData(header);\n  }\n\n  return undefined;\n}\n\n/** Returns the value of a meta tag */\nexport function getMetaContent(metaName: string): string | null {\n  const el = document.querySelector(`meta[name=${metaName}]`);\n  return el ? el.getAttribute('content') : null;\n}\n\n/** Adjusts transaction value based on max transaction duration */\nfunction adjustTransactionDuration(maxDuration: number, transaction: IdleTransaction, endTimestamp: number): void {\n  const diff = endTimestamp - transaction.startTimestamp;\n  const isOutdatedTransaction = endTimestamp && (diff > maxDuration || diff < 0);\n  if (isOutdatedTransaction) {\n    transaction.setStatus(SpanStatus.DeadlineExceeded);\n    transaction.setTag('maxTransactionDurationExceeded', 'true');\n  }\n}\n", "export {\n  Breadcrumb,\n  Request,\n  SdkInfo,\n  Event,\n  Exception,\n  Response,\n  Severity,\n  StackFrame,\n  Stacktrace,\n  Status,\n  Thread,\n  User,\n} from '@sentry/types';\n\nexport {\n  addGlobalEventProcessor,\n  addBreadcrumb,\n  captureException,\n  captureEvent,\n  captureMessage,\n  configureScope,\n  getHubFromCarrier,\n  getCurrentHub,\n  Hub,\n  Scope,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  startTransaction,\n  Transports,\n  withScope,\n} from '@sentry/browser';\n\nexport { BrowserOptions } from '@sentry/browser';\nexport { BrowserClient, ReportDialogOptions } from '@sentry/browser';\nexport {\n  defaultIntegrations,\n  forceLoad,\n  init,\n  lastEventId,\n  onLoad,\n  showReportDialog,\n  flush,\n  close,\n  wrap,\n} from '@sentry/browser';\nexport { SDK_NAME, SDK_VERSION } from '@sentry/browser';\n\nimport { Integrations as BrowserIntegrations } from '@sentry/browser';\nimport { getGlobalObject } from '@sentry/utils';\n\nimport { BrowserTracing } from './browser';\nimport { addExtensionMethods } from './hubextensions';\n\nexport { Span } from './span';\n\nlet windowIntegrations = {};\n\n// This block is needed to add compatibility with the integrations packages when used with a CDN\nconst _window = getGlobalObject<Window>();\nif (_window.Sentry && _window.Sentry.Integrations) {\n  windowIntegrations = _window.Sentry.Integrations;\n}\n\nconst INTEGRATIONS = {\n  ...windowIntegrations,\n  ...BrowserIntegrations,\n  BrowserTracing,\n};\n\nexport { INTEGRATIONS as Integrations };\n\n// We are patching the global object with our hub extension methods\naddExtensionMethods();\n\nexport { addExtensionMethods };\n", "import { getCurrentHub } from '@sentry/hub';\nimport { Client, Options } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\n/** A class object that can instantiate Client objects. */\nexport type ClientClass<F extends Client, O extends Options> = new (options: O) => F;\n\n/**\n * Internal function to create a new SDK client instance. The client is\n * installed and then bound to the current scope.\n *\n * @param clientClass The client class to instantiate.\n * @param options Options to pass to the client.\n */\nexport function initAndBind<F extends Client, O extends Options>(clientClass: ClientClass<F, O>, options: O): void {\n  if (options.debug === true) {\n    logger.enable();\n  }\n  const hub = getCurrentHub();\n  const client = new clientClass(options);\n  hub.bindClient(client);\n}\n"], "names": ["LogLevel", "SessionStatus", "Severity", "Status", "TransactionSamplingMethod", "isError", "wat", "Object", "prototype", "toString", "call", "isInstanceOf", "Error", "isErrorEvent", "isDOMError", "isString", "isPrimitive", "isPlainObject", "isEvent", "Event", "isElement", "Element", "isThenable", "Boolean", "then", "base", "_e", "htmlTreeAsString", "elem", "currentElem", "out", "height", "len", "sep<PERSON><PERSON>th", "length", "nextStr", "_htmlElementAsString", "push", "parentNode", "reverse", "join", "_oO", "el", "className", "classes", "key", "attr", "i", "tagName", "toLowerCase", "id", "split", "allowedAttrs", "getAttribute", "level", "Debug", "Info", "Warning", "Fatal", "Critical", "Log", "code", "Success", "RateLimit", "Invalid", "Failed", "Unknown", "setPrototypeOf", "__proto__", "Array", "obj", "proto", "prop", "hasOwnProperty", "message", "_super", "_this", "name", "_newTarget", "constructor", "__extends", "DSN_REGEX", "from", "this", "_fromString", "_fromComponents", "_validate", "Dsn", "with<PERSON><PERSON><PERSON>", "_a", "host", "path", "pass", "port", "projectId", "str", "match", "exec", "SentryError", "protocol", "user", "_b", "_c", "slice", "pop", "projectMatch", "components", "for<PERSON>ach", "component", "ERROR_MESSAGE", "isNaN", "parseInt", "_hasWeakSet", "WeakSet", "_inner", "Memo", "has", "add", "delete", "splice", "defaultFunctionName", "getFunctionName", "fn", "e", "truncate", "max", "substr", "safeJoin", "input", "delimiter", "isArray", "output", "value", "String", "isMatchingPattern", "pattern", "test", "indexOf", "fill", "source", "replacementFactory", "original", "wrapped", "defineProperties", "__sentry_original__", "enumerable", "_Oo", "getWalkSource", "error", "err", "stack", "event_1", "type", "target", "currentTarget", "CustomEvent", "detail", "jsonSize", "encodeURI", "utf8Length", "JSON", "stringify", "normalizeToSize", "object", "depth", "maxSize", "serialized", "normalize", "normalizeValue", "_events", "global", "window", "document", "walk", "memo", "Infinity", "normalized", "serializeValue", "toJSON", "acc", "memoize", "innerKey", "unmemoize", "parse", "extractExceptionKeysForMessage", "exception", "max<PERSON><PERSON><PERSON>", "keys", "sort", "<PERSON><PERSON><PERSON><PERSON>", "dropUndefinedKeys", "val", "rv", "__values", "map", "isNodeEnv", "process", "dynamicRequire", "mod", "request", "require", "DEFAULT_REQUEST_KEYS", "fallbackGlobalObject", "getGlobalObject", "self", "uuid4", "crypto", "msCrypto", "getRandomValues", "arr", "Uint16Array", "pad", "num", "v", "replace", "c", "r", "Math", "random", "parseUrl", "url", "query", "fragment", "relative", "getEventDescription", "event", "values", "event_id", "consoleSandbox", "callback", "originalConsole", "console", "wrappedLevels", "result", "addExceptionTypeValue", "addExceptionMechanism", "mechanism", "defaultRetryAfter", "PREFIX", "_enabled", "<PERSON><PERSON>", "_i", "args", "log", "warn", "__SENTRY__", "logger", "supportsFetch", "Headers", "Request", "Response", "isNativeFetch", "func", "supportsReferrerPolicy", "referrerPolicy", "lastHref", "handlers", "instrumented", "instrument", "originalConsoleLevel", "triggerHandlers", "Function", "apply", "instrumentConsole", "addEventListener", "domEvent<PERSON><PERSON><PERSON>", "bind", "keypressEventHandler", "eventName", "options", "handleEvent", "innerOriginal", "__sentry_wrapped__", "instrumentDOM", "requestKeys", "requestValues", "xhrproto", "XMLHttpRequest", "originalOpen", "xhr", "__sentry_xhr__", "method", "toUpperCase", "__sentry_own_request__", "onreadystatechangeHandler", "readyState", "status_code", "status", "requestPos", "args_1", "undefined", "body", "endTimestamp", "Date", "now", "startTimestamp", "onreadystatechange", "readyStateArgs", "originalSend", "instrumentXHR", "fetch", "doc", "createElement", "sandbox", "hidden", "head", "append<PERSON><PERSON><PERSON>", "contentWindow", "<PERSON><PERSON><PERSON><PERSON>", "supportsNativeFetch", "originalFetch", "handlerData", "fetchData", "getFetchMethod", "getFetchUrl", "response", "instrumentFetch", "chrome", "isChromePackagedApp", "app", "runtime", "has<PERSON><PERSON>ory<PERSON><PERSON>", "history", "pushState", "replaceState", "oldOnPopState", "onpopstate", "historyReplacementFunction", "originalHistoryFunction", "to", "location", "href", "instrumentHistory", "_old<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onerror", "msg", "line", "column", "arguments", "_oldOnUnhandledRejectionHandler", "onunhandledrejection", "addInstrumentationHandler", "handler", "data", "fetch<PERSON>rgs", "keypressTimeout", "lastCapturedEvent", "debounceDuration", "deboun<PERSON><PERSON><PERSON>r", "debounce", "clearTimeout", "setTimeout", "isContentEditable", "States", "executor", "PENDING", "_setResult", "RESOLVED", "reason", "REJECTED", "state", "_state", "_resolve", "_reject", "_value", "_executeHandlers", "_handlers", "concat", "cachedHandlers", "done", "onfulfilled", "onrejected", "SyncPromise", "resolve", "_", "reject", "collection", "counter", "resolvedCollection", "item", "index", "TypeError", "_attach<PERSON><PERSON><PERSON>", "onfinally", "isRejected", "_limit", "<PERSON><PERSON><PERSON><PERSON>", "task", "isReady", "_buffer", "remove", "timeout", "capturedSetTimeout", "all", "dateTimestampSource", "nowSeconds", "platformPerformance", "module", "performance", "getNodePerformance", "<PERSON><PERSON><PERSON><PERSON>", "getBrowserPerformance", "timestampSource", "dateTimestampInSeconds", "timestampWithMs", "browserPerformanceTimeOrigin", "timing", "navigationStart", "<PERSON><PERSON>", "scope", "newScope", "_breadcrumbs", "_tags", "_extra", "_contexts", "_user", "_level", "_span", "_session", "_transactionName", "_fingerprint", "_eventProcessors", "_scopeListeners", "update", "_notifyScopeListeners", "tags", "extras", "extra", "fingerprint", "setTransactionName", "context", "span", "getSpan", "transaction", "spanRecorder", "spans", "session", "captureContext", "updatedScope", "contexts", "breadcrumb", "maxBreadcrumbs", "mergedBreadcrumb", "timestamp", "__spread", "hint", "trace", "getTraceContext", "transactionName", "_applyFingerprint", "breadcrumbs", "_notifyEventProcessors", "getGlobalEventProcessors", "processors", "processor", "final", "_notifyingListeners", "globalEventProcessors", "addGlobalEventProcessor", "Ok", "Session", "ip_address", "ip<PERSON><PERSON><PERSON>", "did", "email", "username", "sid", "started", "duration", "release", "environment", "userAgent", "errors", "Exited", "init", "toISOString", "attrs", "user_agent", "API_VERSION", "client", "_version", "getStackTop", "bindClient", "<PERSON><PERSON>", "version", "setupIntegrations", "clone", "getScope", "getStack", "getClient", "pushScope", "popScope", "_stack", "eventId", "_lastEventId", "finalHint", "syntheticException", "originalException", "_invokeClient", "beforeBreadcrumb", "_d", "finalBreadcrumb", "addBreadcrumb", "min", "setUser", "setTags", "setExtras", "setTag", "setExtra", "setContext", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "integration", "getIntegration", "_callExtensionMethod", "customSamplingContext", "endSession", "getUser", "setSession", "getSession", "close", "captureSession", "sentry", "getMainCarrier", "extensions", "carrier", "hub", "registry", "getHubFromCarrier", "setHubOnCarrier", "getCurrentHub", "hasHubOnCarrier", "isOlderThan", "activeDomain", "getActiveDomain", "registryHubTopStack", "getHubFromActiveDomain", "domain", "active", "callOnHub", "captureException", "withScope", "dsn", "_dsnObject", "API", "_getIngestEndpoint", "getStoreEndpoint", "_encodedAuth", "_getEnvelopeEndpoint", "clientName", "clientVersion", "header", "Content-Type", "X-Sen<PERSON>-<PERSON><PERSON>", "dialogOptions", "endpoint", "getBaseApiEndpoint", "encodedOptions", "encodeURIComponent", "auth", "sentry_key", "sentry_version", "installedIntegrations", "integrations", "defaultIntegrations", "userIntegrations", "userIntegrationsNames_1", "pickedIntegrationsNames_1", "defaultIntegration", "userIntegration", "integrationsNames", "getIntegrationsToSetup", "setupOnce", "setupIntegration", "originalFunctionToString", "backendClass", "_backend", "_options", "_dsn", "BaseClient", "_process", "_getBackend", "eventFromException", "_captureEvent", "promisedEvent", "eventFromMessage", "_sendSession", "_isClientProcessing", "ready", "getTransport", "transportFlushed", "flush", "getOptions", "enabled", "_isEnabled", "_integrations", "crashed", "errored", "exceptions", "exceptions_1", "handled", "headers", "Crashed", "Number", "sendSession", "ticked", "interval", "setInterval", "_processing", "clearInterval", "normalizeDepth", "prepared", "_applyClientOptions", "_applyIntegrationsMetadata", "finalScope", "applyToEvent", "evt", "_normalizeEvent", "b", "dist", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sdkInfo", "sdk", "integrationsArray", "sendEvent", "_processEvent", "finalEvent", "beforeSend", "sampleRate", "isTransaction", "_prepareEvent", "__sentry__", "beforeSendResult", "processedEvent", "_updateSessionFromEvent", "_sendEvent", "promise", "NoopTransport", "Skipped", "_transport", "_setupTransport", "BaseBackend", "_exception", "_hint", "_message", "sessionToSentryRequest", "api", "sent_at", "getEnvelopeEndpointWithUrlEncodedAuth", "eventToSentryRequest", "samplingMethod", "otherTags", "useEnvelope", "req", "getStoreEndpointWithUrlEncodedAuth", "envelope", "sample_rates", "rate", "FunctionToString", "DEFAULT_IGNORE_ERRORS", "InboundFilters", "clientOptions", "_mergeOptions", "_shouldDropEvent", "_isSentryError", "_isIgnoredError", "_isDeniedUrl", "_getEventFilterUrl", "_isAllowedUrl", "ignoreInternal", "ignoreErrors", "_getPossibleEventMessages", "some", "denyUrls", "allowUrls", "whitelistUrls", "blacklistUrls", "oO", "stacktrace", "frames_1", "frames", "filename", "frames_2", "UNKNOWN_FUNCTION", "gecko", "winjs", "gecko<PERSON>val", "chromeEval", "reactMinifiedRegexp", "computeStackTrace", "ex", "popSize", "framesToPop", "parts", "opera10Regex", "opera11Regex", "lines", "element", "extractMessage", "computeStackTraceFromStacktraceProp", "popFrames", "submatch", "isNative", "columnNumber", "computeStackTraceFromStackProp", "failed", "STACKTRACE_LIMIT", "exceptionFromStacktrace", "prepareFramesForEvent", "eventFromStacktrace", "localStack", "firstFrameFunction", "lastFrameFunction", "frame", "colno", "function", "in_app", "lineno", "eventFromUnknownInput", "domException", "name_1", "eventFromString", "DOMException.code", "rejection", "__serialized__", "eventFromPlainObject", "synthetic", "attachStacktrace", "_api", "BaseTransport", "drain", "requestType", "fromHttpCode", "_handleRateLimit", "_disabledUntil", "category", "_rateLimits", "rl<PERSON><PERSON><PERSON>", "ra<PERSON><PERSON><PERSON>", "trim", "parameters", "headerDelay", "delay", "headerDate", "parseRetryAfterHeader", "FetchTransport", "_sendRequest", "sentryRequest", "originalPayload", "_isRateLimited", "Promise", "fetchParameters", "assign", "x-sentry-rate-limits", "get", "retry-after", "_handleResponse", "catch", "XHRTransport", "getResponseHeader", "open", "setRequestHeader", "send", "BrowserBackend", "transportOptions", "transport", "ignoreOnError", "shouldIgnoreOnError", "wrap", "before", "sentryWrapped", "wrappedArguments", "arg", "addEventProcessor", "property", "defineProperty", "getOwnPropertyDescriptor", "configurable", "injectReportDialog", "script", "async", "src", "getReportDialogEndpoint", "onLoad", "onload", "GlobalHandlers", "stackTraceLimit", "_installGlobalOnErrorHandler", "_installGlobalOnUnhandledRejectionHandler", "_onErrorHandlerInstalled", "currentHub", "hasIntegration", "isFailedOwnDelivery", "_eventFromIncompleteOnError", "_enhanceEventWithInitialFrame", "captureEvent", "_onUnhandledRejectionHandlerInstalled", "_eventFromRejectionWithPrimitive", "groups", "getLocationHref", "DEFAULT_EVENT_TARGET", "TryCatch", "eventTarget", "requestAnimationFrame", "_wrapTimeFunction", "_wrapRAF", "_wrapXHR", "_wrapEventTarget", "originalCallback", "originalRemoveEventListener", "wrappedEventHandler", "originalEventHandler", "wrapOptions", "Breadcrumbs", "dom", "_consoleBreadcrumb", "_domBreadcrumb", "_xhrBreadcrumb", "_fetchBreadcrumb", "_historyBreadcrumb", "fromString", "parsedLoc", "parsedFrom", "parsedTo", "DEFAULT_KEY", "DEFAULT_LIMIT", "LinkedErrors", "_key", "limit", "_handler", "linkedErrors", "_walkErrorTree", "UserAgent", "navigator", "referrer", "<PERSON><PERSON><PERSON>", "User-Agent", "SDK_NAME", "BrowserClient", "getDsn", "platform", "packages", "addSentryBreadcrumb", "CoreIntegrations.InboundFilters", "CoreIntegrations.FunctionToString", "windowIntegrations", "_window", "Sentry", "Integrations", "SpanStatus", "INTEGRATIONS", "CoreIntegrations", "BrowserIntegrations", "httpStatus", "Unauthenticated", "PermissionDenied", "NotFound", "AlreadyExists", "FailedPrecondition", "ResourceExhausted", "InvalidArgument", "Unimplemented", "Unavailable", "DeadlineExceeded", "InternalError", "UnknownE<PERSON>r", "TRACEPARENT_REGEXP", "RegExp", "hasTracingEnabled", "getActiveTransaction", "getTransaction", "msToSec", "time", "<PERSON><PERSON><PERSON><PERSON>", "activeTransaction", "setStatus", "maxlen", "_maxlen", "SpanRecorder", "spanContext", "substring", "traceId", "spanId", "parentSpanId", "sampled", "op", "description", "Span", "startChild", "childSpan", "spanStatus", "sampledString", "parent_span_id", "span_id", "trace_id", "start_timestamp", "transactionContext", "_hub", "_trimEnd", "trimEnd", "Transaction", "measurements", "_measurements", "finish", "finishedSpans", "filter", "s", "reduce", "prev", "current", "SpanClass", "DEFAULT_IDLE_TIMEOUT", "_pushActivity", "_popActivity", "transactionSpanId", "IdleTransactionSpanRecorder", "_idleHub", "_idleTimeout", "_onScope", "clearActiveTransaction", "configureScope", "setSpan", "IdleTransaction", "_finished", "activities", "_beforeFinishCallbacks", "Cancelled", "keepSpan", "_initTimeout", "_pingHeartbeat", "end_1", "_heartbeatTimer", "heartbeatString", "_prevHeartbeatString", "_heartbeatCounter", "_beat", "traceHeaders", "sentry-trace", "toTraceparent", "sample", "samplingContext", "__sentry_samplingMethod", "Explicit", "tracesSampler", "<PERSON><PERSON>", "__sentry_sampleRate", "parentSampled", "Inheritance", "tracesSampleRate", "Rate", "isValidSampleRate", "initSpanRecorder", "_experiments", "maxSpans", "getDefaultSamplingContext", "defaultSamplingContext", "requestType_1", "IncomingMessage", "members", "find", "member", "requestData", "hostname", "secure", "socket", "encrypted", "originalUrl", "absoluteUrl", "cookies", "cookie", "query_string", "extractNodeRequestData", "globalObject", "_startTransaction", "addExtensionMethods", "startTransaction", "firstHiddenTime", "inputPromise", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metric", "po", "observeAllUpdates", "prevValue", "isFinal", "disconnect", "visibilityState", "delta", "initMetric", "entries", "floor", "observe", "PerformanceObserver", "supportedEntryTypes", "includes", "l", "getEntries", "buffered", "isUnloading", "listenersAdded", "onPageHide", "persisted", "onHidden", "cb", "once", "timeStamp", "capture", "getFirstHidden", "getLCP", "onReport", "reportAllChanges", "report", "firstHidden", "<PERSON><PERSON><PERSON><PERSON>", "entry", "startTime", "onFinal", "takeRecords", "passive", "getTTFB", "navigationEntry", "getEntriesByType", "entryType", "getNavigationEntryFromPerformanceTiming", "responseStart", "mark", "_trackCLS", "_trackLCP", "_trackFID", "_trackTTFB", "MetricsInstrumentation", "entryScriptSrc", "entryScriptStartTimestamp", "tracingInitMarkStartTime", "scripts", "dataset", "_performanceCursor", "addPerformanceNavigationTiming", "_startChild", "requestStart", "responseEnd", "addRequest", "addNavigationSpans", "measureStartTimestamp", "measureEndTimestamp", "addMeasureSpans", "<PERSON><PERSON><PERSON><PERSON>", "resourceName", "origin", "initiatorType", "transferSize", "encodedBodySize", "decodedBodySize", "addResourceSpans", "_trackNavigator", "timeOrigin_1", "oldValue", "measurementTimestamp", "normalizedValue", "abs", "setMeasurements", "hadRecentInput", "getCLS", "connection", "effectiveType", "isMeasurementValue", "rtt", "downlink", "deviceMemory", "hardwareConcurrency", "processingStart", "perfMetrics", "onFirstInputDelay", "cancelable", "requestTime", "eventEnd", "end", "start", "ctx", "isFinite", "defaultRequestInstrumentationOptions", "traceFetch", "traceXHR", "<PERSON><PERSON><PERSON><PERSON>", "registerRequestInstrumentation", "shouldCreateSpanForRequest", "urlMap", "defaultShouldCreateSpan", "origins", "shouldCreateSpan", "currentClientOptions", "__span", "setHttpStatus", "append", "fetchCallback", "__sentry_xhr_span_id__", "xhrCallback", "DEFAULT_BROWSER_TRACING_OPTIONS", "idleTimeout", "markBackgroundTransactions", "maxTransactionDuration", "routingInstrumentation", "startTransactionOnPageLoad", "startTransactionOnLocationChange", "startingUrl", "pathname", "BrowserTracing", "_emitOptionsWarning", "_getCurrentHub", "_createRouteTransaction", "beforeNavigate", "parentContextFromHeader", "metaName", "querySelector", "traceparent", "matches", "extractTraceparentData", "getHeaderContext", "expandedContext", "modifiedContext", "finalContext", "idleTransaction", "onScope", "startIdleTransaction", "registerBeforeFinishCallback", "_metrics", "addPerformanceEntries", "maxDuration", "diff", "adjustTransactionDuration", "window_1", "SENTRY_RELEASE", "autoSessionTracking", "clientClass", "debug", "enable", "initAndBind", "loadResolved", "fcpResolved", "possiblyEndSession", "resolveWindowLoaded", "removeEventListener", "startSession", "entryList", "firstHiddenTime_1", "startSessionTracking", "lastEventId", "showReportDialog", "internalWrap"], "mappings": ";gVACYA,ECmDAC,EClDAC,ECAAC,ECqGAC,4gCC5FIC,EAAQC,GACtB,OAAQC,OAAOC,UAAUC,SAASC,KAAKJ,IACrC,IAAK,iBAEL,IAAK,qBAEL,IAAK,wBACH,OAAO,EACT,QACE,OAAOK,EAAaL,EAAKM,iBAWfC,EAAaP,GAC3B,MAA+C,wBAAxCC,OAAOC,UAAUC,SAASC,KAAKJ,YAUxBQ,EAAWR,GACzB,MAA+C,sBAAxCC,OAAOC,UAAUC,SAASC,KAAKJ,YAqBxBS,EAAST,GACvB,MAA+C,oBAAxCC,OAAOC,UAAUC,SAASC,KAAKJ,YAUxBU,EAAYV,GAC1B,OAAe,OAARA,GAAgC,iBAARA,GAAmC,mBAARA,WAU5CW,EAAcX,GAC5B,MAA+C,oBAAxCC,OAAOC,UAAUC,SAASC,KAAKJ,YAUxBY,EAAQZ,GACtB,MAAwB,oBAAVa,OAAyBR,EAAaL,EAAKa,gBAU3CC,EAAUd,GACxB,MAA0B,oBAAZe,SAA2BV,EAAaL,EAAKe,kBAkB7CC,EAAWhB,GAEzB,OAAOiB,QAAQjB,GAAOA,EAAIkB,MAA4B,mBAAblB,EAAIkB,eAqB/Bb,EAAaL,EAAUmB,GACrC,IACE,OAAOnB,aAAemB,EACtB,MAAOC,GACP,OAAO,YClJKC,EAAiBC,GAS/B,IAYE,IAXA,IAAIC,EAAcD,EAGZE,EAAM,GACRC,EAAS,EACTC,EAAM,EAEJC,EADY,MACUC,OACxBC,SAGGN,GAAeE,IAVM,KAgBV,UALhBI,EAAUC,EAAqBP,KAKJE,EAAS,GAAKC,EAAMF,EAAII,OAASD,EAAYE,EAAQD,QAf3D,KAmBrBJ,EAAIO,KAAKF,GAETH,GAAOG,EAAQD,OACfL,EAAcA,EAAYS,WAG5B,OAAOR,EAAIS,UAAUC,KArBH,OAsBlB,MAAOC,GACP,MAAO,aASX,SAASL,EAAqBM,GAC5B,IAQIC,EACAC,EACAC,EACAC,EACAC,EAZEnB,EAAOc,EAOPZ,EAAM,GAOZ,IAAKF,IAASA,EAAKoB,QACjB,MAAO,GAUT,GAPAlB,EAAIO,KAAKT,EAAKoB,QAAQC,eAClBrB,EAAKsB,IACPpB,EAAIO,KAAK,IAAIT,EAAKsB,KAIpBP,EAAYf,EAAKe,YACA5B,EAAS4B,GAExB,IADAC,EAAUD,EAAUQ,MAAM,OACrBJ,EAAI,EAAGA,EAAIH,EAAQV,OAAQa,IAC9BjB,EAAIO,KAAK,IAAIO,EAAQG,IAGzB,IAAMK,EAAe,CAAC,OAAQ,OAAQ,QAAS,OAC/C,IAAKL,EAAI,EAAGA,EAAIK,EAAalB,OAAQa,IACnCF,EAAMO,EAAaL,IACnBD,EAAOlB,EAAKyB,aAAaR,KAEvBf,EAAIO,KAAK,IAAIQ,OAAQC,QAGzB,OAAOhB,EAAIU,KAAK,KN/FlB,SAAYxC,GAEVA,mBAEAA,qBAEAA,qBAEAA,yBARF,CAAYA,IAAAA,OCmDZ,SAAYC,GAEVA,UAEAA,kBAEAA,oBAEAA,sBARF,CAAYA,IAAAA,QClDAC,EAAAA,aAAAA,8BAIVA,gBAEAA,oBAEAA,YAEAA,cAEAA,gBAEAA,sBAIF,SAAiBA,GAOCA,aAAhB,SAA2BoD,GACzB,OAAQA,GACN,IAAK,QACH,OAAOpD,EAASqD,MAClB,IAAK,OACH,OAAOrD,EAASsD,KAClB,IAAK,OACL,IAAK,UACH,OAAOtD,EAASuD,QAClB,IAAK,QACH,OAAOvD,EAASU,MAClB,IAAK,QACH,OAAOV,EAASwD,MAClB,IAAK,WACH,OAAOxD,EAASyD,SAClB,IAAK,MACL,QACE,OAAOzD,EAAS0D,MAxBxB,CAAiB1D,aAAAA,iBClBLC,EAAAA,WAAAA,gCAIVA,oBAEAA,oBAEAA,yBAEAA,oBAEAA,kBAIF,SAAiBA,GAOCA,eAAhB,SAA6B0D,GAC3B,OAAIA,GAAQ,KAAOA,EAAO,IACjB1D,EAAO2D,QAGH,MAATD,EACK1D,EAAO4D,UAGZF,GAAQ,KAAOA,EAAO,IACjB1D,EAAO6D,QAGZH,GAAQ,IACH1D,EAAO8D,OAGT9D,EAAO+D,SAxBlB,CAAiB/D,WAAAA,cCqFjB,SAAYC,GACVA,4BACAA,2BACAA,qBACAA,4BAJF,CAAYA,IAAAA,OGvGL,IAAM+D,EACX5D,OAAO4D,iBAAmB,CAAEC,UAAW,cAAgBC,MAMzD,SAAoDC,EAAcC,GAGhE,OADAD,EAAIF,UAAYG,EACTD,GAOT,SAAyDA,EAAcC,GACrE,IAAK,IAAMC,KAAQD,EAEZD,EAAIG,eAAeD,KAEtBF,EAAIE,GAAQD,EAAMC,IAItB,OAAOF,ICvBT,kBAIE,WAA0BI,4BACxBC,YAAMD,gBADkBE,UAAAF,EAGxBE,EAAKC,KAAOC,EAAWtE,UAAUuE,YAAYF,KAC7CV,EAAeS,EAAME,EAAWtE,aAEpC,OAViCwE,UAAApE,OCE3BqE,EAAY,8EAuBhB,WAAmBC,GACG,iBAATA,EACTC,KAAKC,EAAYF,GAEjBC,KAAKE,EAAgBH,GAGvBC,KAAKG,IA+ET,OAnESC,qBAAP,SAAgBC,gBAAAA,MACR,IAAAC,OAAEC,SAAMC,SAAMC,SAAMC,SAAMC,cAChC,gCAC0BN,GAAgBI,EAAO,IAAIA,EAAS,IAC5D,IAAIF,GAAOG,EAAO,IAAIA,EAAS,SAAMF,EAAUA,MAAUA,GAAOG,GAK5DP,cAAR,SAAoBQ,GAClB,IAAMC,EAAQf,EAAUgB,KAAKF,GAE7B,IAAKC,EACH,MAAM,IAAIE,EApDM,eAuDZ,IAAAT,kBAACU,OAAUC,OAAMC,OAAAT,kBAAWF,OAAMY,OAAAT,kBACpCF,EAAO,GACPG,OAEE3C,EAAQ2C,EAAU3C,MAAM,KAM9B,GALIA,EAAMjB,OAAS,IACjByD,EAAOxC,EAAMoD,MAAM,GAAI,GAAG/D,KAAK,KAC/BsD,EAAY3C,EAAMqD,OAGhBV,EAAW,CACb,IAAMW,EAAeX,EAAUE,MAAM,QACjCS,IACFX,EAAYW,EAAa,IAI7BtB,KAAKE,EAAgB,CAAEK,OAAME,OAAMD,OAAMG,YAAWD,OAAMM,SAAUA,EAAyBC,UAIvFb,cAAR,SAAwBmB,GACtBvB,KAAKgB,SAAWO,EAAWP,SAC3BhB,KAAKiB,KAAOM,EAAWN,KACvBjB,KAAKS,KAAOc,EAAWd,MAAQ,GAC/BT,KAAKO,KAAOgB,EAAWhB,KACvBP,KAAKU,KAAOa,EAAWb,MAAQ,GAC/BV,KAAKQ,KAAOe,EAAWf,MAAQ,GAC/BR,KAAKW,UAAYY,EAAWZ,WAItBP,cAAR,WAAA,WAOE,GANA,CAAC,WAAY,OAAQ,OAAQ,aAAaoB,QAAQ,SAAAC,GAChD,IAAKhC,EAAKgC,GACR,MAAM,IAAIV,EAAeW,gBAAkBD,iBAI1CzB,KAAKW,UAAUE,MAAM,SACxB,MAAM,IAAIE,EAAeW,kCAAoC1B,KAAKW,WAGpE,GAAsB,SAAlBX,KAAKgB,UAAyC,UAAlBhB,KAAKgB,SACnC,MAAM,IAAID,EAAeW,iCAAmC1B,KAAKgB,UAGnE,GAAIhB,KAAKU,MAAQiB,MAAMC,SAAS5B,KAAKU,KAAM,KACzC,MAAM,IAAIK,EAAeW,6BAA+B1B,KAAKU,yBCnGjE,aACEV,KAAK6B,EAAiC,mBAAZC,QAC1B9B,KAAK+B,EAAS/B,KAAK6B,EAAc,IAAIC,QAAY,GA0CrD,OAnCSE,oBAAP,SAAe7C,GACb,GAAIa,KAAK6B,EACP,QAAI7B,KAAK+B,EAAOE,IAAI9C,KAGpBa,KAAK+B,EAAOG,IAAI/C,IACT,GAGT,IAAK,IAAIvB,EAAI,EAAGA,EAAIoC,KAAK+B,EAAOhF,OAAQa,IAAK,CAE3C,GADcoC,KAAK+B,EAAOnE,KACZuB,EACZ,OAAO,EAIX,OADAa,KAAK+B,EAAO7E,KAAKiC,IACV,GAOF6C,sBAAP,SAAiB7C,GACf,GAAIa,KAAK6B,EACP7B,KAAK+B,EAAOI,OAAOhD,QAEnB,IAAK,IAAIvB,EAAI,EAAGA,EAAIoC,KAAK+B,EAAOhF,OAAQa,IACtC,GAAIoC,KAAK+B,EAAOnE,KAAOuB,EAAK,CAC1Ba,KAAK+B,EAAOK,OAAOxE,EAAG,GACtB,aCnDJyE,EAAsB,uBAKZC,EAAgBC,GAC9B,IACE,OAAKA,GAAoB,mBAAPA,GAGXA,EAAG7C,MAFD2C,EAGT,MAAOG,GAGP,OAAOH,YCLKI,EAAS7B,EAAa8B,GACpC,oBADoCA,KACjB,iBAAR9B,GAA4B,IAAR8B,EACtB9B,EAEFA,EAAI7D,QAAU2F,EAAM9B,EAASA,EAAI+B,OAAO,EAAGD,kBAqDpCE,EAASC,EAAcC,GACrC,IAAK5D,MAAM6D,QAAQF,GACjB,MAAO,GAKT,IAFA,IAAMG,EAAS,GAENpF,EAAI,EAAGA,EAAIiF,EAAM9F,OAAQa,IAAK,CACrC,IAAMqF,EAAQJ,EAAMjF,GACpB,IACEoF,EAAO9F,KAAKgG,OAAOD,IACnB,MAAOT,GACPQ,EAAO9F,KAAK,iCAIhB,OAAO8F,EAAO3F,KAAKyF,YAQLK,EAAkBF,EAAeG,GAC/C,QAAKxH,EAASqH,KP4BS9H,EOxBViI,EPyBkC,oBAAxChI,OAAOC,UAAUC,SAASC,KAAKJ,GOxB5BiI,EAAmBC,KAAKJ,GAEX,iBAAZG,IAC0B,IAA5BH,EAAMK,QAAQF,QPoBAjI,WQrGToI,EAAKC,EAAgC9D,EAAc+D,GACjE,GAAM/D,KAAQ8D,EAAd,CAIA,IAAME,EAAWF,EAAO9D,GAClBiE,EAAUF,EAAmBC,GAInC,GAAuB,mBAAZC,EACT,IACEA,EAAQtI,UAAYsI,EAAQtI,WAAa,GACzCD,OAAOwI,iBAAiBD,EAAS,CAC/BE,oBAAqB,CACnBC,YAAY,EACZb,MAAOS,KAGX,MAAOK,IAMXP,EAAO9D,GAAQiE,GAqBjB,SAASK,EACPf,GAIA,GAAI/H,EAAQ+H,GAAQ,CAClB,IAAMgB,EAAQhB,EACRiB,EAKF,CACF3E,QAAS0E,EAAM1E,QACfG,KAAMuE,EAAMvE,KACZyE,MAAOF,EAAME,OAGf,IAAK,IAAMvG,KAAKqG,EACV7I,OAAOC,UAAUiE,eAAe/D,KAAK0I,EAAOrG,KAC9CsG,EAAItG,GAAKqG,EAAMrG,IAInB,OAAOsG,EAGT,GAAInI,EAAQkH,GAAQ,CAWlB,IAAMmB,EAAQnB,EAERO,EAEF,GAEJA,EAAOa,KAAOD,EAAMC,KAGpB,IACEb,EAAOc,OAASrI,EAAUmI,EAAME,QAC5B9H,EAAiB4H,EAAME,QACvBlJ,OAAOC,UAAUC,SAASC,KAAK6I,EAAME,QACzC,MAAOhH,GACPkG,EAAOc,OAAS,YAGlB,IACEd,EAAOe,cAAgBtI,EAAUmI,EAAMG,eACnC/H,EAAiB4H,EAAMG,eACvBnJ,OAAOC,UAAUC,SAASC,KAAK6I,EAAMG,eACzC,MAAOjH,GACPkG,EAAOe,cAAgB,YAOzB,IAAK,IAAM3G,IAJgB,oBAAhB4G,aAA+BhJ,EAAayH,EAAOuB,eAC5DhB,EAAOiB,OAASL,EAAMK,QAGRL,EACVhJ,OAAOC,UAAUiE,eAAe/D,KAAK6I,EAAOxG,KAC9C4F,EAAO5F,GAAKwG,GAIhB,OAAOZ,EAGT,OAAOP,EAYT,SAASyB,EAASzB,GAChB,OAPF,SAAoBA,GAElB,QAAS0B,UAAU1B,GAAOjF,MAAM,SAASjB,OAKlC6H,CAAWC,KAAKC,UAAU7B,aAInB8B,EACdC,EAEAC,EAEAC,gBAFAD,kBAEAC,EAAkB,QAElB,IAAMC,EAAaC,EAAUJ,EAAQC,GAErC,OAAIP,EAASS,GAAcD,EAClBH,EAAgBC,EAAQC,EAAQ,EAAGC,GAGrCC,EAuCT,SAASE,EAAkBpC,EAAUvF,GACnC,MAAY,WAARA,GAAoBuF,GAA0B,iBAAVA,GAAwBA,EAAuCqC,EAC9F,WAGG,kBAAR5H,EACK,kBAGsB,oBAAnB6H,QAAmCtC,IAAsBsC,OAC5D,WAGsB,oBAAnBC,QAAmCvC,IAAsBuC,OAC5D,WAGwB,oBAArBC,UAAqCxC,IAAsBwC,SAC9D,aRvFF3J,EADwBX,EQ4FV8H,IR3FQ,gBAAiB9H,GAAO,mBAAoBA,GAAO,oBAAqBA,EQ4F5F,mBAGY,iBAAV8H,GAAsBA,GAAUA,EAClC,aAGK,IAAVA,EACK,cAGY,mBAAVA,EACF,cAAcX,EAAgBW,OAKlB,iBAAVA,EACF,IAAIC,OAAOD,OAGC,iBAAVA,EACF,YAAYC,OAAOD,OAGrBA,MRtHwB9H,WQkIjBuK,EAAKhI,EAAauF,EAAYgC,EAA2BU,GAEvE,gBAF4CV,EAAiBW,EAAAA,gBAAUD,MAAiB3D,GAE1E,IAAViD,EACF,OA1FJ,SAAwBhC,GACtB,IAAMoB,EAAOjJ,OAAOC,UAAUC,SAASC,KAAK0H,GAG5C,GAAqB,iBAAVA,EACT,OAAOA,EAET,GAAa,oBAAToB,EACF,MAAO,WAET,GAAa,mBAATA,EACF,MAAO,UAGT,IAAMwB,EAAaR,EAAepC,GAClC,OAAOpH,EAAYgK,GAAcA,EAAaxB,EA2ErCyB,CAAe7C,GAKxB,GAAIA,MAAAA,GAAiE,mBAAjBA,EAAM8C,OACxD,OAAO9C,EAAM8C,SAKf,IAAMF,EAAaR,EAAepC,EAAOvF,GACzC,GAAI7B,EAAYgK,GACd,OAAOA,EAIT,IAAMrC,EAASQ,EAAcf,GAGvB+C,EAAM9G,MAAM6D,QAAQE,GAAS,GAAK,GAGxC,GAAI0C,EAAKM,QAAQhD,GACf,MAAO,eAIT,IAAK,IAAMiD,KAAY1C,EAEhBpI,OAAOC,UAAUiE,eAAe/D,KAAKiI,EAAQ0C,KAIjDF,EAA+BE,GAAYR,EAAKQ,EAAU1C,EAAO0C,GAAWjB,EAAQ,EAAGU,IAO1F,OAHAA,EAAKQ,UAAUlD,GAGR+C,WAgBOZ,EAAUvC,EAAYoC,GACpC,IACE,OAAOJ,KAAKuB,MAAMvB,KAAKC,UAAUjC,EAAO,SAACnF,EAAauF,GAAe,OAAAyC,EAAKhI,EAAKuF,EAAOgC,MACtF,MAAO3H,GACP,MAAO,iCAUK+I,EAA+BC,EAAgBC,gBAAAA,MAC7D,IAAMC,EAAOpL,OAAOoL,KAAKxC,EAAcsC,IAGvC,GAFAE,EAAKC,QAEAD,EAAKzJ,OACR,MAAO,uBAGT,GAAIyJ,EAAK,GAAGzJ,QAAUwJ,EACpB,OAAO9D,EAAS+D,EAAK,GAAID,GAG3B,IAAK,IAAIG,EAAeF,EAAKzJ,OAAQ2J,EAAe,EAAGA,IAAgB,CACrE,IAAMvB,EAAaqB,EAAKpF,MAAM,EAAGsF,GAAcrJ,KAAK,MACpD,KAAI8H,EAAWpI,OAASwJ,GAGxB,OAAIG,IAAiBF,EAAKzJ,OACjBoI,EAEF1C,EAAS0C,EAAYoB,GAG9B,MAAO,YAOOI,EAAqBC,WACnC,GAAI9K,EAAc8K,GAAM,CACtB,IAAMzH,EAAMyH,EACNC,EAA6B,OACnC,IAAkB,IAAA3F,EAAA4F,EAAA1L,OAAOoL,KAAKrH,kCAAM,CAA/B,IAAMzB,eACe,IAAbyB,EAAIzB,KACbmJ,EAAGnJ,GAAOiJ,EAAkBxH,EAAIzB,uGAGpC,OAAOmJ,EAGT,OAAI3H,MAAM6D,QAAQ6D,GACRA,EAAcG,IAAIJ,GAGrBC,WC1XOI,IACd,MAAwF,qBAAjF5L,OAAOC,UAAUC,SAASC,KAAwB,oBAAZ0L,QAA0BA,QAAU,YASnEC,EAAeC,EAAUC,GAEvC,OAAOD,EAAIE,QAAQD,GAIrB,IAAME,EAAuB,CAAC,UAAW,OAAQ,UAAW,SAAU,eAAgB,OCJtF,IAAMC,EAAuB,YAObC,IACd,OAAQR,IACJzB,OACkB,oBAAXC,OACPA,OACgB,oBAATiC,KACPA,KACAF,WAeUG,IACd,IAAMnC,EAASiC,IACTG,EAASpC,EAAOoC,QAAUpC,EAAOqC,SAEvC,QAAiB,IAAXD,GAAsBA,EAAOE,gBAAiB,CAElD,IAAMC,EAAM,IAAIC,YAAY,GAC5BJ,EAAOE,gBAAgBC,GAIvBA,EAAI,GAAe,KAATA,EAAI,GAAc,MAG5BA,EAAI,GAAe,MAATA,EAAI,GAAe,MAE7B,IAAME,EAAM,SAACC,GAEX,IADA,IAAIC,EAAID,EAAI3M,SAAS,IACd4M,EAAEnL,OAAS,GAChBmL,EAAI,IAAIA,EAEV,OAAOA,GAGT,OACEF,EAAIF,EAAI,IAAME,EAAIF,EAAI,IAAME,EAAIF,EAAI,IAAME,EAAIF,EAAI,IAAME,EAAIF,EAAI,IAAME,EAAIF,EAAI,IAAME,EAAIF,EAAI,IAAME,EAAIF,EAAI,IAI9G,MAAO,mCAAmCK,QAAQ,QAAS,SAAAC,GAEzD,IAAMC,EAAqB,GAAhBC,KAAKC,SAAiB,EAGjC,OADgB,MAANH,EAAYC,EAAS,EAAJA,EAAW,GAC7B/M,SAAS,eAWNkN,EACdC,GAOA,IAAKA,EACH,MAAO,GAGT,IAAM5H,EAAQ4H,EAAI5H,MAAM,kEAExB,IAAKA,EACH,MAAO,GAIT,IAAM6H,EAAQ7H,EAAM,IAAM,GACpB8H,EAAW9H,EAAM,IAAM,GAC7B,MAAO,CACLN,KAAMM,EAAM,GACZL,KAAMK,EAAM,GACZG,SAAUH,EAAM,GAChB+H,SAAU/H,EAAM,GAAK6H,EAAQC,YAQjBE,EAAoBC,GAClC,GAAIA,EAAMvJ,QACR,OAAOuJ,EAAMvJ,QAEf,GAAIuJ,EAAMxC,WAAawC,EAAMxC,UAAUyC,QAAUD,EAAMxC,UAAUyC,OAAO,GAAI,CAC1E,IAAMzC,EAAYwC,EAAMxC,UAAUyC,OAAO,GAEzC,OAAIzC,EAAUjC,MAAQiC,EAAUrD,MACpBqD,EAAUjC,UAASiC,EAAUrD,MAElCqD,EAAUjC,MAAQiC,EAAUrD,OAAS6F,EAAME,UAAY,YAEhE,OAAOF,EAAME,UAAY,qBASXC,EAAeC,GAC7B,IAAM3D,EAASiC,IAGf,KAAM,YAAajC,GACjB,OAAO2D,IAIT,IAAMC,EAAmB5D,EAAe6D,QAClCC,EAAwC,GAR/B,CAAC,QAAS,OAAQ,OAAQ,QAAS,MAAO,UAWlD7H,QAAQ,SAAArD,GAETA,KAAUoH,EAAe6D,SAAYD,EAAgBhL,GAA2B0F,sBAClFwF,EAAclL,GAASgL,EAAgBhL,GACvCgL,EAAgBhL,GAAUgL,EAAgBhL,GAA2B0F,uBAKzE,IAAMyF,EAASJ,IAOf,OAJA9N,OAAOoL,KAAK6C,GAAe7H,QAAQ,SAAArD,GACjCgL,EAAgBhL,GAASkL,EAAclL,KAGlCmL,WAUOC,EAAsBT,EAAc7F,EAAgBoB,GAClEyE,EAAMxC,UAAYwC,EAAMxC,WAAa,GACrCwC,EAAMxC,UAAUyC,OAASD,EAAMxC,UAAUyC,QAAU,GACnDD,EAAMxC,UAAUyC,OAAO,GAAKD,EAAMxC,UAAUyC,OAAO,IAAM,GACzDD,EAAMxC,UAAUyC,OAAO,GAAG9F,MAAQ6F,EAAMxC,UAAUyC,OAAO,GAAG9F,OAASA,GAAS,GAC9E6F,EAAMxC,UAAUyC,OAAO,GAAG1E,KAAOyE,EAAMxC,UAAUyC,OAAO,GAAG1E,MAAQA,GAAQ,iBAS7DmF,GACdV,EACAW,gBAAAA,MAKA,IAGEX,EAAMxC,UAAWyC,OAAQ,GAAGU,UAAYX,EAAMxC,UAAWyC,OAAQ,GAAGU,WAAa,GACjFrO,OAAOoL,KAAKiD,GAAWjI,QAAQ,SAAA9D,GAG7BoL,EAAMxC,UAAWyC,OAAQ,GAAGU,UAAU/L,GAAO+L,EAAU/L,KAEzD,MAAOJ,KAgDX,IAAMoM,GAAoB,ICxQ1B,IAAMnE,GAASiC,IAGTmC,GAAS,+BAQb,aACE3J,KAAK4J,GAAW,EA0CpB,OAtCSC,oBAAP,WACE7J,KAAK4J,GAAW,GAIXC,mBAAP,WACE7J,KAAK4J,GAAW,GAIXC,gBAAP,eAAW,aAAAC,mBAAAA,IAAAC,kBACJ/J,KAAK4J,GAGVX,EAAe,WACb1D,GAAO6D,QAAQY,IAAOL,aAAgBI,EAAK1M,KAAK,SAK7CwM,iBAAP,eAAY,aAAAC,mBAAAA,IAAAC,kBACL/J,KAAK4J,GAGVX,EAAe,WACb1D,GAAO6D,QAAQa,KAAQN,cAAiBI,EAAK1M,KAAK,SAK/CwM,kBAAP,eAAa,aAAAC,mBAAAA,IAAAC,kBACN/J,KAAK4J,GAGVX,EAAe,WACb1D,GAAO6D,QAAQnF,MAAS0F,eAAkBI,EAAK1M,KAAK,iBAMnD6M,WAAa3E,GAAO2E,YAAc,GACzC,IAAMC,GAAU5E,GAAO2E,WAAWC,SAAsB5E,GAAO2E,WAAWC,OAAS,IAAIN,aCLvEO,KACd,KAAM,UAAW5C,KACf,OAAO,EAGT,IAIE,OAHA,IAAI6C,QACJ,IAAIC,QAAQ,IACZ,IAAIC,UACG,EACP,MAAO/H,GACP,OAAO,GAOX,SAASgI,GAAcC,GACrB,OAAOA,GAAQ,mDAAmDpH,KAAKoH,EAAKnP,qBA6D9DoP,KAMd,IAAKN,KACH,OAAO,EAGT,IAIE,OAHA,IAAIE,QAAQ,IAAK,CACfK,eAAgB,YAEX,EACP,MAAOnI,GACP,OAAO,GC9IX,IA8SIoI,GA9SErF,GAASiC,IA6BTqD,GAA6E,GAC7EC,GAA6D,GAGnE,SAASC,GAAW1G,GAClB,IAAIyG,GAAazG,GAMjB,OAFAyG,GAAazG,IAAQ,EAEbA,GACN,IAAK,WA4DT,WACE,KAAM,YAAakB,IACjB,OAGF,CAAC,QAAS,OAAQ,OAAQ,QAAS,MAAO,UAAU/D,QAAQ,SAASrD,GAC7DA,KAASoH,GAAO6D,SAItB7F,EAAKgC,GAAO6D,QAASjL,EAAO,SAAS6M,GACnC,OAAO,eAAS,aAAAlB,mBAAAA,IAAAC,kBACdkB,GAAgB,UAAW,CAAElB,OAAM5L,UAG/B6M,GACFE,SAAS7P,UAAU8P,MAAM5P,KAAKyP,EAAsBzF,GAAO6D,QAASW,QA3ExEqB,GACA,MACF,IAAK,OAiTT,WACE,KAAM,aAAc7F,IAClB,OAKFA,GAAOE,SAAS4F,iBAAiB,QAASC,GAAgB,QAASL,GAAgBM,KAAK,KAAM,SAAS,GACvGhG,GAAOE,SAAS4F,iBAAiB,WAAYG,GAAqBP,GAAgBM,KAAK,KAAM,SAAS,GAGtG,CAAC,cAAe,QAAQ/J,QAAQ,SAAC8C,GAE/B,IAAMlF,EAASmG,GAAejB,IAAYiB,GAAejB,GAAQjJ,UAG5D+D,GAAUA,EAAME,gBAAmBF,EAAME,eAAe,sBAK7DiE,EAAKnE,EAAO,mBAAoB,SAC9BsE,GAMA,OAAO,SAEL+H,EACAlJ,EACAmJ,GA4BA,OA1BInJ,GAAOA,EAA2BoJ,aAClB,UAAdF,GACFlI,EAAKhB,EAAI,cAAe,SAASqJ,GAC/B,OAAO,SAAoB9C,GAEzB,OADAwC,GAAgB,QAASL,GAAgBM,KAAK,KAAM,OAApDD,CAA4DxC,GACrD8C,EAAcrQ,KAAKyE,KAAM8I,MAIpB,aAAd2C,GACFlI,EAAKhB,EAAI,cAAe,SAASqJ,GAC/B,OAAO,SAAoB9C,GAEzB,OADA0C,GAAqBP,GAAgBM,KAAK,KAAM,OAAhDC,CAAwD1C,GACjD8C,EAAcrQ,KAAKyE,KAAM8I,QAKpB,UAAd2C,GACFH,GAAgB,QAASL,GAAgBM,KAAK,KAAM,QAAQ,EAA5DD,CAAkEtL,MAElD,aAAdyL,GACFD,GAAqBP,GAAgBM,KAAK,KAAM,OAAhDC,CAAwDxL,OAIrD0D,EAASnI,KAAKyE,KAAMyL,EAAWlJ,EAAImJ,MAI9CnI,EAAKnE,EAAO,sBAAuB,SACjCsE,GAOA,OAAO,SAEL+H,EACAlJ,EACAmJ,GAEA,IACEhI,EAASnI,KAAKyE,KAAMyL,EAAalJ,EAAmCsJ,mBAAoBH,GACxF,MAAOlJ,IAGT,OAAOkB,EAASnI,KAAKyE,KAAMyL,EAAWlJ,EAAImJ,SAnY5CI,GACA,MACF,IAAK,OAkKT,WACE,KAAM,mBAAoBvG,IACxB,OAIF,IAAMwG,EAAgC,GAChCC,EAA8B,GAC9BC,EAAWC,eAAe7Q,UAEhCkI,EAAK0I,EAAU,OAAQ,SAASE,GAC9B,OAAO,eAA4C,aAAArC,mBAAAA,IAAAC,kBAEjD,IAAMqC,EAAMpM,KACNyI,EAAMsB,EAAK,GACjBqC,EAAIC,eAAiB,CAEnBC,OAAQ1Q,EAASmO,EAAK,IAAMA,EAAK,GAAGwC,cAAgBxC,EAAK,GACzDtB,IAAKsB,EAAK,IAKRnO,EAAS6M,IAAsC,SAA9B2D,EAAIC,eAAeC,QAAqB7D,EAAI5H,MAAM,gBACrEuL,EAAII,wBAAyB,GAG/B,IAAMC,EAA4B,WAChC,GAAuB,IAAnBL,EAAIM,WAAkB,CACxB,IAGMN,EAAIC,iBACND,EAAIC,eAAeM,YAAcP,EAAIQ,QAEvC,MAAOpK,IAIT,IACE,IAAMqK,EAAad,EAAYzI,QAAQ8I,GACvC,IAAoB,IAAhBS,EAAmB,CAErBd,EAAY3J,OAAOyK,GACnB,IAAMC,EAAOd,EAAc5J,OAAOyK,GAAY,GAC1CT,EAAIC,qBAA8BU,IAAZD,EAAK,KAC7BV,EAAIC,eAAeW,KAAOF,EAAK,KAGnC,MAAOtK,IAITyI,GAAgB,MAAO,CACrBlB,OACAkD,aAAcC,KAAKC,MACnBC,eAAgBF,KAAKC,MACrBf,UAgBN,MAXI,uBAAwBA,GAAyC,mBAA3BA,EAAIiB,mBAC5C9J,EAAK6I,EAAK,qBAAsB,SAAS1I,GACvC,OAAO,eAAS,aAAAoG,mBAAAA,IAAAwD,kBAEd,OADAb,IACO/I,EAASyH,MAAMiB,EAAKkB,MAI/BlB,EAAIf,iBAAiB,mBAAoBoB,GAGpCN,EAAahB,MAAMiB,EAAKrC,MAInCxG,EAAK0I,EAAU,OAAQ,SAASsB,GAC9B,OAAO,eAA4C,aAAAzD,mBAAAA,IAAAC,kBAUjD,OATAgC,EAAY7O,KAAK8C,MACjBgM,EAAc9O,KAAK6M,GAEnBkB,GAAgB,MAAO,CACrBlB,OACAqD,eAAgBF,KAAKC,MACrBf,IAAKpM,OAGAuN,EAAapC,MAAMnL,KAAM+J,MAzPhCyD,GACA,MACF,IAAK,SA2ET,WACE,eDnDA,IAAKpD,KACH,OAAO,EAGT,IAAM7E,EAASiC,IAIf,GAAIgD,GAAcjF,EAAOkI,OACvB,OAAO,EAKT,IAAInE,GAAS,EACPoE,EAAMnI,EAAOE,SAEnB,GAAIiI,GAAiD,mBAAlCA,EAAIC,cACrB,IACE,IAAMC,EAAUF,EAAIC,cAAc,UAClCC,EAAQC,QAAS,EACjBH,EAAII,KAAKC,YAAYH,GACjBA,EAAQI,eAAiBJ,EAAQI,cAAcP,QAEjDnE,EAASkB,GAAcoD,EAAQI,cAAcP,QAE/CC,EAAII,KAAKG,YAAYL,GACrB,MAAO1J,GACPiG,GAAOF,KAAK,kFAAmF/F,GAInG,OAAOoF,ECmBF4E,GACH,OAGF3K,EAAKgC,GAAQ,QAAS,SAAS4I,GAC7B,OAAO,eAAS,aAAArE,mBAAAA,IAAAC,kBACd,IAAMqE,EAAc,CAClBrE,OACAsE,UAAW,CACT/B,OAAQgC,GAAevE,GACvBtB,IAAK8F,GAAYxE,IAEnBqD,eAAgBF,KAAKC,OAQvB,OALAlC,GAAgB,aACXmD,IAIED,EAAchD,MAAM5F,GAAQwE,GAAM1N,KACvC,SAACmS,GAMC,OALAvD,GAAgB,eACXmD,IACHnB,aAAcC,KAAKC,MACnBqB,cAEKA,GAET,SAACvK,GASC,MARAgH,GAAgB,eACXmD,IACHnB,aAAcC,KAAKC,MACnBlJ,WAKIA,OAjHVwK,GACA,MACF,IAAK,WA4PT,WACE,GDtJMlJ,EAASiC,IAGTkH,EAAUnJ,EAAemJ,OACzBC,EAAsBD,GAAUA,EAAOE,KAAOF,EAAOE,IAAIC,QAEzDC,EAAgB,YAAavJ,KAAYA,EAAOwJ,QAAQC,aAAezJ,EAAOwJ,QAAQE,aAEpFN,IAAuBG,EC+I7B,WDvJIvJ,EAGAmJ,EACAC,EAEAG,ECoJN,IAAMI,EAAgB3J,GAAO4J,WAgB7B,SAASC,EAA2BC,GAClC,OAAO,eAAwB,aAAAvF,mBAAAA,IAAAC,kBAC7B,IAAMtB,EAAMsB,EAAKhN,OAAS,EAAIgN,EAAK,QAAKgD,EACxC,GAAItE,EAAK,CAEP,IAAM1I,EAAO6K,GACP0E,EAAKpM,OAAOuF,GAElBmC,GAAW0E,EACXrE,GAAgB,UAAW,CACzBlL,OACAuP,OAGJ,OAAOD,EAAwBlE,MAAMnL,KAAM+J,IA7B/CxE,GAAO4J,WAAa,eAAoC,aAAArF,mBAAAA,IAAAC,kBACtD,IAAMuF,EAAK/J,GAAOgK,SAASC,KAErBzP,EAAO6K,GAMb,GALAA,GAAW0E,EACXrE,GAAgB,UAAW,CACzBlL,OACAuP,OAEEJ,EACF,OAAOA,EAAc/D,MAAMnL,KAAM+J,IAuBrCxG,EAAKgC,GAAOwJ,QAAS,YAAaK,GAClC7L,EAAKgC,GAAOwJ,QAAS,eAAgBK,GAnSjCK,GACA,MACF,IAAK,QAsdPC,GAAqBnK,GAAOoK,QAE5BpK,GAAOoK,QAAU,SAASC,EAAUnH,EAAUoH,EAAWC,EAAa7L,GASpE,OARAgH,GAAgB,QAAS,CACvB6E,SACA7L,QACA4L,OACAD,MACAnH,UAGEiH,IAEKA,GAAmBvE,MAAMnL,KAAM+P,YAjetC,MACF,IAAK,qBA0ePC,GAAkCzK,GAAO0K,qBAEzC1K,GAAO0K,qBAAuB,SAASzN,GAGrC,OAFAyI,GAAgB,qBAAsBzI,IAElCwN,IAEKA,GAAgC7E,MAAMnL,KAAM+P,YA/enD,MACF,QACE5F,GAAOF,KAAK,gCAAiC5F,aASnC6L,GAA0BC,GACnCA,GAAmC,iBAAjBA,EAAQ9L,MAAiD,mBAArB8L,EAAQjH,WAGnE2B,GAASsF,EAAQ9L,MAAQwG,GAASsF,EAAQ9L,OAAS,GAClDwG,GAASsF,EAAQ9L,MAAsCnH,KAAKiT,EAAQjH,UACrE6B,GAAWoF,EAAQ9L,OAIrB,SAAS4G,GAAgB5G,EAA6B+L,WACpD,GAAK/L,GAASwG,GAASxG,OAIvB,IAAsB,IAAAnD,EAAA4F,EAAA+D,GAASxG,IAAS,kCAAI,CAAvC,IAAM8L,UACT,IACEA,EAAQC,GACR,MAAO5N,GACP2H,GAAOlG,MACL,0DAA0DI,aAAe/B,EACvE6N,eACW3N,uGA4FrB,SAAS8L,GAAe+B,GACtB,oBADsBA,MAClB,YAAa9K,IAAU/J,EAAa6U,EAAU,GAAI/F,UAAY+F,EAAU,GAAG/D,OACtEpJ,OAAOmN,EAAU,GAAG/D,QAAQC,cAEjC8D,EAAU,IAAMA,EAAU,GAAG/D,OACxBpJ,OAAOmN,EAAU,GAAG/D,QAAQC,cAE9B,MAIT,SAASgC,GAAY8B,GACnB,oBADmBA,MACS,iBAAjBA,EAAU,GACZA,EAAU,GAEf,YAAa9K,IAAU/J,EAAa6U,EAAU,GAAI/F,SAC7C+F,EAAU,GAAG5H,IAEfvF,OAAOmN,EAAU,IA0O1B,IAEIC,GACAC,GAHEC,GAA2B,IAC7BC,GAAwB,EAY5B,SAASnF,GAAgB5L,EAAcyQ,EAAmBO,GACxD,oBADwDA,MACjD,SAAC5H,GAINwH,QAAkBvD,EAIbjE,GAASyH,KAAsBzH,IAIpCyH,GAAoBzH,EAEhB2H,IACFE,aAAaF,IAGXC,EACFD,GAAgBG,WAAW,WACzBT,EAAQ,CAAErH,QAAOpJ,WAGnByQ,EAAQ,CAAErH,QAAOpJ,WAWvB,SAAS8L,GAAqB2E,GAI5B,OAAO,SAACrH,GACN,IAAIxE,EAEJ,IACEA,EAASwE,EAAMxE,OACf,MAAO9B,GAGP,OAGF,IAAM3E,EAAUyG,GAAWA,EAAuBzG,QAK7CA,IAAwB,UAAZA,GAAmC,aAAZA,GAA4ByG,EAAuBuM,qBAMtFP,IACHhF,GAAgB,QAAS6E,EAAzB7E,CAAkCxC,GAEpC6H,aAAaL,IAEbA,GAAmBM,WAAW,WAC5BN,QAAkBvD,GACjByD,MAIP,IAAId,GAA0C,KAuB9C,ICtiBKoB,GDsiBDd,GAA6D,MCtiBjE,SAAKc,GAEHA,oBAEAA,sBAEAA,sBANF,CAAKA,KAAAA,QAaL,kBASE,WACEC,GADF,WARQ/Q,OAAiB8Q,GAAOE,QACxBhR,OAIH,GAgJYA,OAAW,SAACiD,GAC3BxD,EAAKwR,EAAWH,GAAOI,SAAUjO,IAIlBjD,OAAU,SAACmR,GAC1B1R,EAAKwR,EAAWH,GAAOM,SAAUD,IAIlBnR,OAAa,SAACqR,EAAepO,GACxCxD,EAAK6R,IAAWR,GAAOE,UAIvB7U,EAAW8G,GACZA,EAAyB5G,KAAKoD,EAAK8R,EAAU9R,EAAK+R,IAIrD/R,EAAK6R,EAASD,EACd5R,EAAKgS,EAASxO,EAEdxD,EAAKiS,OAKU1R,OAAiB,SAACmQ,GAQjC1Q,EAAKkS,EAAYlS,EAAKkS,EAAUC,OAAOzB,GACvC1Q,EAAKiS,KAIU1R,OAAmB,WAClC,GAAIP,EAAK6R,IAAWR,GAAOE,QAA3B,CAIA,IAAMa,EAAiBpS,EAAKkS,EAAUvQ,QACtC3B,EAAKkS,EAAY,GAEjBE,EAAerQ,QAAQ,SAAA2O,GACjBA,EAAQ2B,OAIRrS,EAAK6R,IAAWR,GAAOI,UACrBf,EAAQ4B,aAEV5B,EAAQ4B,YAAatS,EAAKgS,GAI1BhS,EAAK6R,IAAWR,GAAOM,UACrBjB,EAAQ6B,YACV7B,EAAQ6B,WAAWvS,EAAKgS,GAI5BtB,EAAQ2B,MAAO,OA7MjB,IACEf,EAAS/Q,KAAKuR,EAAUvR,KAAKwR,GAC7B,MAAOhP,GACPxC,KAAKwR,EAAQhP,IA6MnB,OAxMgByP,UAAd,SAAyBhP,GACvB,OAAO,IAAIgP,EAAY,SAAAC,GACrBA,EAAQjP,MAKEgP,SAAd,SAAgCd,GAC9B,OAAO,IAAIc,EAAY,SAACE,EAAGC,GACzBA,EAAOjB,MAKGc,MAAd,SAA2BI,GACzB,OAAO,IAAIJ,EAAiB,SAACC,EAASE,GACpC,GAAKlT,MAAM6D,QAAQsP,GAKnB,GAA0B,IAAtBA,EAAWtV,OAAf,CAKA,IAAIuV,EAAUD,EAAWtV,OACnBwV,EAA0B,GAEhCF,EAAW7Q,QAAQ,SAACgR,EAAMC,GACxBR,EAAYC,QAAQM,GACjBnW,KAAK,SAAA4G,GACJsP,EAAmBE,GAASxP,EAGZ,KAFhBqP,GAAW,IAKXJ,EAAQK,KAETlW,KAAK,KAAM+V,UAlBdF,EAAQ,SALRE,EAAO,IAAIM,UAAU,+CA6BpBT,iBAAP,SACEF,EACAC,GAFF,WAIE,OAAO,IAAIC,EAAY,SAACC,EAASE,GAC/B3S,EAAKkT,EAAe,CAClBb,MAAM,EACNC,YAAa,SAAAzI,GACX,GAAKyI,EAML,IAEE,YADAG,EAAQH,EAAYzI,IAEpB,MAAO9G,GAEP,YADA4P,EAAO5P,QAPP0P,EAAQ5I,IAWZ0I,WAAY,SAAAb,GACV,GAAKa,EAIL,IAEE,YADAE,EAAQF,EAAWb,IAEnB,MAAO3O,GAEP,YADA4P,EAAO5P,QAPP4P,EAAOjB,SAgBVc,kBAAP,SACED,GAEA,OAAOhS,KAAK3D,KAAK,SAAAuK,GAAO,OAAAA,GAAKoL,IAIxBC,oBAAP,SAAwBW,GAAxB,WACE,OAAO,IAAIX,EAAqB,SAACC,EAASE,GACxC,IAAIxL,EACAiM,EAEJ,OAAOpT,EAAKpD,KACV,SAAA4G,GACE4P,GAAa,EACbjM,EAAM3D,EACF2P,GACFA,KAGJ,SAAAzB,GACE0B,GAAa,EACbjM,EAAMuK,EACFyB,GACFA,MAGJvW,KAAK,WACDwW,EACFT,EAAOxL,GAITsL,EAAStL,QAMRqL,qBAAP,WACE,MAAO,2CC9JT,WAA6Ba,GAAA9S,OAAA8S,EAFZ9S,OAAiC,GA4EpD,OArES+S,oBAAP,WACE,YAAuBhG,IAAhB/M,KAAK8S,GAAwB9S,KAAKjD,SAAWiD,KAAK8S,GASpDC,gBAAP,SAAWC,GAAX,WACE,OAAKhT,KAAKiT,YAG0B,IAAhCjT,KAAKkT,EAAQ5P,QAAQ0P,IACvBhT,KAAKkT,EAAQhW,KAAK8V,GAEpBA,EACG3W,KAAK,WAAM,OAAAoD,EAAK0T,OAAOH,KACvB3W,KAAK,KAAM,WACV,OAAAoD,EAAK0T,OAAOH,GAAM3W,KAAK,KAAM,gBAK1B2W,GAbEf,GAAYG,OAAO,IAAIrR,EAAY,qDAsBvCgS,mBAAP,SAAcC,GAEZ,OADoBhT,KAAKkT,EAAQ9Q,OAAOpC,KAAKkT,EAAQ5P,QAAQ0P,GAAO,GAAG,IAOlED,mBAAP,WACE,OAAO/S,KAAKkT,EAAQnW,QASfgW,kBAAP,SAAaK,GAAb,WACE,OAAO,IAAInB,GAAqB,SAAAC,GAC9B,IAAMmB,EAAqBzC,WAAW,WAChCwC,GAAWA,EAAU,GACvBlB,GAAQ,IAETkB,GACHnB,GAAYqB,IAAI7T,EAAKyT,GAClB7W,KAAK,WACJsU,aAAa0C,GACbnB,GAAQ,KAET7V,KAAK,KAAM,WACV6V,GAAQ,aC7DZqB,GAAuC,CAC3CC,WAAY,WAAM,OAAAtG,KAAKC,MAAQ,MA2EjC,IAAMsG,GAA+CzM,IAZrD,WACE,IAEE,OADkBE,EAAewM,OAAQ,cACxBC,YACjB,MAAOxB,GACP,QAO+DyB,GAnDnE,WACU,IAAAD,kBACR,GAAKA,GAAgBA,EAAYxG,IA2BjC,MAAO,CACLA,IAAK,WAAM,OAAAwG,EAAYxG,OACvB0G,WAJiB3G,KAAKC,MAAQwG,EAAYxG,OAwB4C2G,GAEpFC,QACoBhH,IAAxB0G,GACIF,GACA,CACEC,WAAY,WAAM,OAACC,GAAoBI,WAAaJ,GAAoBtG,OAAS,MAM5E6G,GAAyBT,GAAoBC,WAAWjI,KAAKgI,IAgB7DU,GAHqBF,GAAgBP,WAAWjI,KAAKwI,IAcrDG,GAA+B,WAClC,IAAAP,kBACR,GAAKA,EAGL,OAAIA,EAAYE,WACPF,EAAYE,WAQbF,EAAYQ,QAAUR,EAAYQ,OAAOC,iBAAoBlH,KAAKC,MAdhC,iBCzG5C,aAEYnN,QAA+B,EAG/BA,OAAiD,GAGjDA,OAAqC,GAGrCA,OAA6B,GAG7BA,OAAc,GAGdA,OAAsC,GAGtCA,OAAiB,GAGjBA,OAAsB,GAyalC,OApZgBqU,QAAd,SAAoBC,GAClB,IAAMC,EAAW,IAAIF,EAcrB,OAbIC,IACFC,EAASC,IAAmBF,EAAME,GAClCD,EAASE,OAAaH,EAAMG,GAC5BF,EAASG,OAAcJ,EAAMI,GAC7BH,EAASI,OAAiBL,EAAMK,GAChCJ,EAASK,EAAQN,EAAMM,EACvBL,EAASM,EAASP,EAAMO,EACxBN,EAASO,EAAQR,EAAMQ,EACvBP,EAASQ,EAAWT,EAAMS,EAC1BR,EAASS,EAAmBV,EAAMU,EAClCT,EAASU,EAAeX,EAAMW,EAC9BV,EAASW,IAAuBZ,EAAMY,IAEjCX,GAOFF,6BAAP,SAAwBnL,GACtBlJ,KAAKmV,EAAgBjY,KAAKgM,IAMrBmL,8BAAP,SAAyBnL,GAEvB,OADAlJ,KAAKkV,EAAiBhY,KAAKgM,GACpBlJ,MAMFqU,oBAAP,SAAepT,GAMb,OALAjB,KAAK4U,EAAQ3T,GAAQ,GACjBjB,KAAK+U,GACP/U,KAAK+U,EAASK,OAAO,CAAEnU,SAEzBjB,KAAKqV,IACErV,MAMFqU,oBAAP,WACE,OAAOrU,KAAK4U,GAMPP,oBAAP,SAAeiB,GAMb,OALAtV,KAAKyU,SACAzU,KAAKyU,GACLa,GAELtV,KAAKqV,IACErV,MAMFqU,mBAAP,SAAc3W,EAAauF,SAGzB,OAFAjD,KAAKyU,SAAazU,KAAKyU,WAAQ/W,GAAMuF,MACrCjD,KAAKqV,IACErV,MAMFqU,sBAAP,SAAiBkB,GAMf,OALAvV,KAAK0U,SACA1U,KAAK0U,GACLa,GAELvV,KAAKqV,IACErV,MAMFqU,qBAAP,SAAgB3W,EAAa8X,SAG3B,OAFAxV,KAAK0U,SAAc1U,KAAK0U,WAAShX,GAAM8X,MACvCxV,KAAKqV,IACErV,MAMFqU,2BAAP,SAAsBoB,GAGpB,OAFAzV,KAAKiV,EAAeQ,EACpBzV,KAAKqV,IACErV,MAMFqU,qBAAP,SAAgBlW,GAGd,OAFA6B,KAAK6U,EAAS1W,EACd6B,KAAKqV,IACErV,MAMFqU,+BAAP,SAA0B3U,GAGxB,OAFAM,KAAKgV,EAAmBtV,EACxBM,KAAKqV,IACErV,MAOFqU,2BAAP,SAAsB3U,GACpB,OAAOM,KAAK0V,mBAAmBhW,IAM1B2U,uBAAP,SAAkB3W,EAAaiY,SAS7B,OARgB,OAAZA,SAEK3V,KAAK2U,EAAUjX,GAEtBsC,KAAK2U,SAAiB3U,KAAK2U,WAAYjX,GAAMiY,MAG/C3V,KAAKqV,IACErV,MAMFqU,oBAAP,SAAeuB,GAGb,OAFA5V,KAAK8U,EAAQc,EACb5V,KAAKqV,IACErV,MAMFqU,oBAAP,WACE,OAAOrU,KAAK8U,GAMPT,2BAAP,uBAEQuB,EAAO5V,KAAK6V,UAGlB,iBAAID,wBAAME,uBACDF,wBAAME,iCAIXF,wBAAMG,mCAAcC,MAAM,IACrBJ,EAAKG,aAAaC,MAAM,QADjC,GAWK3B,uBAAP,SAAkB4B,GAOhB,OANKA,EAGHjW,KAAK+U,EAAWkB,SAFTjW,KAAK+U,EAId/U,KAAKqV,IACErV,MAMFqU,uBAAP,WACE,OAAOrU,KAAK+U,GAMPV,mBAAP,SAAc6B,GACZ,IAAKA,EACH,OAAOlW,KAGT,GAA8B,mBAAnBkW,EAA+B,CACxC,IAAMC,EAAgBD,EAAsClW,MAC5D,OAAOmW,aAAwB9B,EAAQ8B,EAAenW,KAiCxD,OA9BIkW,aAA0B7B,GAC5BrU,KAAKyU,SAAazU,KAAKyU,GAAUyB,EAAezB,GAChDzU,KAAK0U,SAAc1U,KAAK0U,GAAWwB,EAAexB,GAClD1U,KAAK2U,SAAiB3U,KAAK2U,GAAcuB,EAAevB,GACpDuB,EAAetB,GAASxZ,OAAOoL,KAAK0P,EAAetB,GAAO7X,SAC5DiD,KAAK4U,EAAQsB,EAAetB,GAE1BsB,EAAerB,IACjB7U,KAAK6U,EAASqB,EAAerB,GAE3BqB,EAAejB,IACjBjV,KAAKiV,EAAeiB,EAAejB,IAE5BnZ,EAAcoa,KAEvBA,EAAiBA,EACjBlW,KAAKyU,SAAazU,KAAKyU,GAAUyB,EAAeZ,MAChDtV,KAAK0U,SAAc1U,KAAK0U,GAAWwB,EAAeV,OAClDxV,KAAK2U,SAAiB3U,KAAK2U,GAAcuB,EAAeE,UACpDF,EAAejV,OACjBjB,KAAK4U,EAAQsB,EAAejV,MAE1BiV,EAAe/X,QACjB6B,KAAK6U,EAASqB,EAAe/X,OAE3B+X,EAAeT,cACjBzV,KAAKiV,EAAeiB,EAAeT,cAIhCzV,MAMFqU,kBAAP,WAYE,OAXArU,KAAKwU,EAAe,GACpBxU,KAAKyU,EAAQ,GACbzU,KAAK0U,EAAS,GACd1U,KAAK4U,EAAQ,GACb5U,KAAK2U,EAAY,GACjB3U,KAAK6U,OAAS9H,EACd/M,KAAKgV,OAAmBjI,EACxB/M,KAAKiV,OAAelI,EACpB/M,KAAK8U,OAAQ/H,EACb/M,KAAK+U,OAAWhI,EAChB/M,KAAKqV,IACErV,MAMFqU,0BAAP,SAAqBgC,EAAwBC,GAC3C,IAAMC,KACJC,UAAWxC,MACRqC,GAQL,OALArW,KAAKwU,OACgBzH,IAAnBuJ,GAAgCA,GAAkB,EAC9CG,EAAIzW,KAAKwU,GAAc+B,IAAkBnV,OAAOkV,KAC5CtW,KAAKwU,GAAc+B,IAC7BvW,KAAKqV,IACErV,MAMFqU,6BAAP,WAGE,OAFArU,KAAKwU,EAAe,GACpBxU,KAAKqV,IACErV,MAWFqU,yBAAP,SAAoBvL,EAAc4N,SAsBhC,GArBI1W,KAAK0U,GAAUtZ,OAAOoL,KAAKxG,KAAK0U,GAAQ3X,SAC1C+L,EAAM0M,aAAaxV,KAAK0U,GAAW5L,EAAM0M,QAEvCxV,KAAKyU,GAASrZ,OAAOoL,KAAKxG,KAAKyU,GAAO1X,SACxC+L,EAAMwM,YAAYtV,KAAKyU,GAAU3L,EAAMwM,OAErCtV,KAAK4U,GAASxZ,OAAOoL,KAAKxG,KAAK4U,GAAO7X,SACxC+L,EAAM7H,YAAYjB,KAAK4U,GAAU9L,EAAM7H,OAErCjB,KAAK2U,GAAavZ,OAAOoL,KAAKxG,KAAK2U,GAAW5X,SAChD+L,EAAMsN,gBAAgBpW,KAAK2U,GAAc7L,EAAMsN,WAE7CpW,KAAK6U,IACP/L,EAAM3K,MAAQ6B,KAAK6U,GAEjB7U,KAAKgV,IACPlM,EAAMgN,YAAc9V,KAAKgV,GAKvBhV,KAAK8U,EAAO,CACdhM,EAAMsN,YAAaO,MAAO3W,KAAK8U,EAAM8B,mBAAsB9N,EAAMsN,UACjE,IAAMS,YAAkB7W,KAAK8U,EAAMgB,kCAAapW,KAC5CmX,IACF/N,EAAMwM,QAASQ,YAAae,GAAoB/N,EAAMwM,OAS1D,OALAtV,KAAK8W,EAAkBhO,GAEvBA,EAAMiO,cAAmBjO,EAAMiO,aAAe,GAAQ/W,KAAKwU,GAC3D1L,EAAMiO,YAAcjO,EAAMiO,YAAYha,OAAS,EAAI+L,EAAMiO,iBAAchK,EAEhE/M,KAAKgX,IAA2BC,KAA+BjX,KAAKkV,GAAmBpM,EAAO4N,IAM7FrC,cAAV,SACE6C,EACApO,EACA4N,EACAjE,GAJF,WAME,oBAFAA,KAEO,IAAIR,GAA0B,SAACC,EAASE,GAC7C,IAAM+E,EAAYD,EAAWzE,GAC7B,GAAc,OAAV3J,GAAuC,mBAAdqO,EAC3BjF,EAAQpJ,OACH,CACL,IAAMQ,EAAS6N,OAAerO,GAAS4N,GACnCva,EAAWmN,GACZA,EACEjN,KAAK,SAAA+a,GAAS,OAAA3X,EAAKuX,EAAuBE,EAAYE,EAAOV,EAAMjE,EAAQ,GAAGpW,KAAK6V,KACnF7V,KAAK,KAAM+V,GAEd3S,EAAKuX,EAAuBE,EAAY5N,EAAQoN,EAAMjE,EAAQ,GAC3DpW,KAAK6V,GACL7V,KAAK,KAAM+V,OASZiC,cAAV,WAAA,WAIOrU,KAAKqX,IACRrX,KAAKqX,GAAsB,EAC3BrX,KAAKmV,EAAgB3T,QAAQ,SAAA0H,GAC3BA,EAASzJ,KAEXO,KAAKqX,GAAsB,IAQvBhD,cAAR,SAA0BvL,GAExBA,EAAM2M,YAAc3M,EAAM2M,YACtBvW,MAAM6D,QAAQ+F,EAAM2M,aAClB3M,EAAM2M,YACN,CAAC3M,EAAM2M,aACT,GAGAzV,KAAKiV,IACPnM,EAAM2M,YAAc3M,EAAM2M,YAAY7D,OAAO5R,KAAKiV,IAIhDnM,EAAM2M,cAAgB3M,EAAM2M,YAAY1Y,eACnC+L,EAAM2M,kBAQnB,SAASwB,KAEP,IAAM1R,EAASiC,IAGf,OAFAjC,EAAO2E,WAAa3E,EAAO2E,YAAc,GACzC3E,EAAO2E,WAAWoN,sBAAwB/R,EAAO2E,WAAWoN,uBAAyB,GAC9E/R,EAAO2E,WAAWoN,+BAQXC,GAAwBrO,GACtC+N,KAA2B/Z,KAAKgM,GCxelC,kBAaE,WAAYyM,GAXL3V,YAAiB,EAEjBA,SAAc0H,IAEd1H,eAAoBkN,KAAKC,MACzBnN,aAAkBkN,KAAKC,MACvBnN,cAAmB,EACnBA,YAAwBlF,EAAc0c,GAKvC7B,GACF3V,KAAKoV,OAAOO,GAmGlB,OA7FE8B,mBAAA,SAAO9B,gBAAAA,MACDA,EAAQ1U,OACN0U,EAAQ1U,KAAKyW,aACf1X,KAAK2X,UAAYhC,EAAQ1U,KAAKyW,YAG3B/B,EAAQiC,MACX5X,KAAK4X,IAAMjC,EAAQ1U,KAAKlD,IAAM4X,EAAQ1U,KAAK4W,OAASlC,EAAQ1U,KAAK6W,WAIrE9X,KAAKwW,UAAYb,EAAQa,WAAatJ,KAAKC,MAEvCwI,EAAQoC,MAEV/X,KAAK+X,IAA6B,KAAvBpC,EAAQoC,IAAIhb,OAAgB4Y,EAAQoC,IAAMrQ,KAEnDiO,EAAQiC,MACV5X,KAAK4X,IAAM,GAAGjC,EAAQiC,KAEO,iBAApBjC,EAAQqC,UACjBhY,KAAKgY,QAAUrC,EAAQqC,SAEO,iBAArBrC,EAAQsC,SACjBjY,KAAKiY,SAAWtC,EAAQsC,SAExBjY,KAAKiY,SAAWjY,KAAKwW,UAAYxW,KAAKgY,QAEpCrC,EAAQuC,UACVlY,KAAKkY,QAAUvC,EAAQuC,SAErBvC,EAAQwC,cACVnY,KAAKmY,YAAcxC,EAAQwC,aAEzBxC,EAAQgC,YACV3X,KAAK2X,UAAYhC,EAAQgC,WAEvBhC,EAAQyC,YACVpY,KAAKoY,UAAYzC,EAAQyC,WAEG,iBAAnBzC,EAAQ0C,SACjBrY,KAAKqY,OAAS1C,EAAQ0C,QAEpB1C,EAAQ/I,SACV5M,KAAK4M,OAAS+I,EAAQ/I,SAK1B6K,kBAAA,SAAM7K,GACAA,EACF5M,KAAKoV,OAAO,CAAExI,WACL5M,KAAK4M,SAAW9R,EAAc0c,GACvCxX,KAAKoV,OAAO,CAAExI,OAAQ9R,EAAcwd,SAEpCtY,KAAKoV,UAKTqC,mBAAA,WAgBE,OAAO9Q,EAAkB,CACvBoR,IAAK,GAAG/X,KAAK+X,IACbQ,MAAM,EACNP,QAAS,IAAI9K,KAAKlN,KAAKgY,SAASQ,cAChChC,UAAW,IAAItJ,KAAKlN,KAAKwW,WAAWgC,cACpC5L,OAAQ5M,KAAK4M,OACbyL,OAAQrY,KAAKqY,OACbT,IAAyB,iBAAb5X,KAAK4X,KAAwC,iBAAb5X,KAAK4X,IAAmB,GAAG5X,KAAK4X,SAAQ7K,EACpFkL,SAAUjY,KAAKiY,SACfQ,MAAO9R,EAAkB,CACvBuR,QAASlY,KAAKkY,QACdC,YAAanY,KAAKmY,YAClBT,WAAY1X,KAAK2X,UACjBe,WAAY1Y,KAAKoY,oBChFZO,GAAc,gBAgCzB,WAAmBC,EAAiBtE,EAA6CuE,gBAA7CvE,MAAmBD,iBAA0BwE,MAAA7Y,OAAA6Y,EAbhE7Y,OAAkB,CAAC,IAclCA,KAAK8Y,cAAcxE,MAAQA,EAC3BtU,KAAK+Y,WAAWH,GAmWpB,OA7VSI,wBAAP,SAAmBC,GACjB,OAAOjZ,KAAK6Y,EAAWI,GAMlBD,uBAAP,SAAkBJ,GACJ5Y,KAAK8Y,cACbF,OAASA,EACTA,GAAUA,EAAOM,mBACnBN,EAAOM,qBAOJF,sBAAP,WAEE,IAAM1E,EAAQD,GAAM8E,MAAMnZ,KAAKoZ,YAK/B,OAJApZ,KAAKqZ,WAAWnc,KAAK,CACnB0b,OAAQ5Y,KAAKsZ,YACbhF,UAEKA,GAMF0E,qBAAP,WACE,QAAIhZ,KAAKqZ,WAAWtc,QAAU,MACrBiD,KAAKqZ,WAAWhY,OAMpB2X,sBAAP,SAAiB9P,GACf,IAAMoL,EAAQtU,KAAKuZ,YACnB,IACErQ,EAASoL,WAETtU,KAAKwZ,aAOFR,sBAAP,WACE,OAAOhZ,KAAK8Y,cAAcF,QAIrBI,qBAAP,WACE,OAAOhZ,KAAK8Y,cAAcxE,OAIrB0E,qBAAP,WACE,OAAOhZ,KAAKyZ,GAIPT,wBAAP,WACE,OAAOhZ,KAAKyZ,EAAOzZ,KAAKyZ,EAAO1c,OAAS,IAOnCic,6BAAP,SAAwB1S,EAAgBoQ,GACtC,IAAMgD,EAAW1Z,KAAK2Z,EAAejS,IACjCkS,EAAYlD,EAMhB,IAAKA,EAAM,CACT,IAAImD,SACJ,IACE,MAAM,IAAIpe,MAAM,6BAChB,MAAO6K,GACPuT,EAAqBvT,EAEvBsT,EAAY,CACVE,kBAAmBxT,EACnBuT,sBAQJ,OAJA7Z,KAAK+Z,EAAc,mBAAoBzT,SAClCsT,IACH5Q,SAAU0Q,KAELA,GAMFV,2BAAP,SAAsBzZ,EAAiBpB,EAAkBuY,GACvD,IAAMgD,EAAW1Z,KAAK2Z,EAAejS,IACjCkS,EAAYlD,EAMhB,IAAKA,EAAM,CACT,IAAImD,SACJ,IACE,MAAM,IAAIpe,MAAM8D,GAChB,MAAO+G,GACPuT,EAAqBvT,EAEvBsT,EAAY,CACVE,kBAAmBva,EACnBsa,sBAQJ,OAJA7Z,KAAK+Z,EAAc,iBAAkBxa,EAASpB,SACzCyb,IACH5Q,SAAU0Q,KAELA,GAMFV,yBAAP,SAAoBlQ,EAAc4N,GAChC,IAAMgD,EAAW1Z,KAAK2Z,EAAejS,IAKrC,OAJA1H,KAAK+Z,EAAc,eAAgBjR,SAC9B4N,IACH1N,SAAU0Q,KAELA,GAMFV,wBAAP,WACE,OAAOhZ,KAAK2Z,GAMPX,0BAAP,SAAqB3C,EAAwBK,GACrC,IAAApW,qBAAEgU,UAAOsE,WAEf,GAAKtE,GAAUsE,EAAf,CAGM,IAAA1X,mCAAEC,qBAAA6Y,oBAAyBC,mBAAA3D,aAnMT,MAsMxB,KAAIA,GAAkB,GAAtB,CAEA,IAAME,EAAYxC,KACZuC,KAAqBC,aAAcH,GACnC6D,EAAkBF,EACnB/Q,EAAe,WAAM,OAAA+Q,EAAiBzD,EAAkBG,KACzDH,EAEoB,OAApB2D,GAEJ5F,EAAM6F,cAAcD,EAAiB5R,KAAK8R,IAAI9D,EA1M1B,SAgNf0C,oBAAP,SAAe/X,GACb,IAAMqT,EAAQtU,KAAKoZ,WACf9E,GAAOA,EAAM+F,QAAQpZ,IAMpB+X,oBAAP,SAAe1D,GACb,IAAMhB,EAAQtU,KAAKoZ,WACf9E,GAAOA,EAAMgG,QAAQhF,IAMpB0D,sBAAP,SAAiBzD,GACf,IAAMjB,EAAQtU,KAAKoZ,WACf9E,GAAOA,EAAMiG,UAAUhF,IAMtByD,mBAAP,SAActb,EAAauF,GACzB,IAAMqR,EAAQtU,KAAKoZ,WACf9E,GAAOA,EAAMkG,OAAO9c,EAAKuF,IAMxB+V,qBAAP,SAAgBtb,EAAa8X,GAC3B,IAAMlB,EAAQtU,KAAKoZ,WACf9E,GAAOA,EAAMmG,SAAS/c,EAAK8X,IAO1BwD,uBAAP,SAAkBtZ,EAAciW,GAC9B,IAAMrB,EAAQtU,KAAKoZ,WACf9E,GAAOA,EAAMoG,WAAWhb,EAAMiW,IAM7BqD,2BAAP,SAAsB9P,GACd,IAAA5I,qBAAEgU,UAAOsE,WACXtE,GAASsE,GACX1P,EAASoL,IAON0E,gBAAP,SAAW9P,GACT,IAAMyR,EAASC,GAAS5a,MACxB,IACEkJ,EAASlJ,cAET4a,GAASD,KAON3B,2BAAP,SAA6C6B,GAC3C,IAAMjC,EAAS5Y,KAAKsZ,YACpB,IAAKV,EAAQ,OAAO,KACpB,IACE,OAAOA,EAAOkC,eAAeD,GAC7B,MAAOvd,GAEP,OADA6M,GAAOF,KAAK,+BAA+B4Q,EAAY9c,4BAChD,OAOJib,sBAAP,SAAiBrD,GACf,OAAO3V,KAAK+a,EAAqB,YAAapF,IAMzCqD,6BAAP,SAAwBrD,EAA6BqF,GACnD,OAAOhb,KAAK+a,EAAqB,mBAAoBpF,EAASqF,IAMzDhC,yBAAP,WACE,OAAOhZ,KAAK+a,EAAgD,iBAMvD/B,yBAAP,SAAoBrD,GAElB3V,KAAKib,aAEC,IAAA3a,qBAAEgU,UAAOsE,WACT1X,wBAAEgX,YAASC,gBACXlC,EAAU,IAAIwB,QAClBS,UACAC,eACI7D,GAAS,CAAErT,KAAMqT,EAAM4G,YACxBvF,IAKL,OAHIrB,GACFA,EAAM6G,WAAWlF,GAEZA,GAMF+C,uBAAP,WACQ,IAAA1Y,qBAAEgU,UAAOsE,WACf,GAAKtE,EAAL,CAEA,IAAM2B,EAAU3B,EAAM8G,YAAc9G,EAAM8G,aACtCnF,IACFA,EAAQoF,QACJzC,GAAUA,EAAO0C,gBACnB1C,EAAO0C,eAAerF,GAExB3B,EAAM6G,gBAWFnC,cAAR,SAA8C1M,sBAAWxC,mBAAAA,IAAAC,oBACjD,IAAA7I,qBAAEoT,UAAOsE,WACXA,GAAUA,EAAOtM,KAEnBhM,EAACsY,GAAetM,aAAWvC,GAAMuK,MAS7B0E,cAAR,SAAgC1M,OAAgB,aAAAxC,mBAAAA,IAAAC,oBAC9C,IACMwR,EADUC,KACOtR,WACvB,GAAIqR,GAAUA,EAAOE,YAAmD,mBAA9BF,EAAOE,WAAWnP,GAC1D,OAAOiP,EAAOE,WAAWnP,GAAQnB,MAAMnL,KAAM+J,GAE/CI,GAAOF,KAAK,oBAAoBqC,uDAKpBkP,KACd,IAAME,EAAUlU,IAKhB,OAJAkU,EAAQxR,WAAawR,EAAQxR,YAAc,CACzCuR,WAAY,GACZE,SAAK5O,GAEA2O,WAQOd,GAASe,GACvB,IAAMC,EAAWJ,KACXb,EAASkB,GAAkBD,GAEjC,OADAE,GAAgBF,EAAUD,GACnBhB,WAUOoB,KAEd,IAAMH,EAAWJ,KAQjB,OALKQ,GAAgBJ,KAAaC,GAAkBD,GAAUK,YAAYtD,KACxEmD,GAAgBF,EAAU,IAAI5C,IAI5BhS,IAsBN,SAAgC4U,GAC9B,IACE,IAAMM,EAAeC,KAGrB,IAAKD,EACH,OAAOL,GAAkBD,GAI3B,IAAKI,GAAgBE,IAAiBL,GAAkBK,GAAcD,YAAYtD,IAAc,CAC9F,IAAMyD,EAAsBP,GAAkBD,GAAU9C,cACxDgD,GAAgBI,EAAc,IAAIlD,GAAIoD,EAAoBxD,OAAQvE,GAAM8E,MAAMiD,EAAoB9H,SAIpG,OAAOuH,GAAkBK,GACzB,MAAOnY,GAEP,OAAO8X,GAAkBD,IAxClBS,CAAuBT,GAGzBC,GAAkBD,YAQXO,KACd,IAAMZ,EAASC,KAAiBtR,WAEhC,OAAOqR,GAAUA,EAAOE,YAAcF,EAAOE,WAAWa,QAAUf,EAAOE,WAAWa,OAAOC,OAkC7F,SAASP,GAAgBN,GACvB,SAAUA,GAAWA,EAAQxR,YAAcwR,EAAQxR,WAAWyR,cAShDE,GAAkBH,GAChC,OAAIA,GAAWA,EAAQxR,YAAcwR,EAAQxR,WAAWyR,IAAYD,EAAQxR,WAAWyR,KACvFD,EAAQxR,WAAawR,EAAQxR,YAAc,GAC3CwR,EAAQxR,WAAWyR,IAAM,IAAI3C,GACtB0C,EAAQxR,WAAWyR,cAQZG,GAAgBJ,EAAkBC,GAChD,QAAKD,IACLA,EAAQxR,WAAawR,EAAQxR,YAAc,GAC3CwR,EAAQxR,WAAWyR,IAAMA,GAClB,GCxgBT,SAASa,GAAalQ,OAAgB,aAAAxC,mBAAAA,IAAAC,oBACpC,IAAM4R,EAAMI,KACZ,GAAIJ,GAAOA,EAAIrP,GAEb,OAAQqP,EAAIrP,SAAJqP,IAAoC5R,IAE9C,MAAM,IAAItO,MAAM,qBAAqB6Q,mEAUvBmQ,iBAAiBnW,EAAgB4P,GAC/C,IAAI2D,EACJ,IACE,MAAM,IAAIpe,MAAM,6BAChB,MAAO6K,GACPuT,EAAqBvT,EAEvB,OAAOkW,GAAU,mBAAoBlW,EAAW,CAC9C4P,iBACA4D,kBAAmBxT,EACnBuT,gCAkIY6C,GAAUxT,GACxBsT,GAAgB,YAAatT,GC/K/B,kBAOE,WAA0ByT,GAAA3c,SAAA2c,EACxB3c,KAAK4c,EAAa,IAAIxc,EAAIuc,GA6H9B,OAzHSE,mBAAP,WACE,OAAO7c,KAAK4c,GAIPC,+BAAP,WACE,IAAMF,EAAM3c,KAAK4c,EACX5b,EAAW2b,EAAI3b,SAAc2b,EAAI3b,aAAc,GAC/CN,EAAOic,EAAIjc,KAAO,IAAIic,EAAIjc,KAAS,GACzC,OAAUM,OAAa2b,EAAIpc,KAAOG,GAAOic,EAAInc,KAAO,IAAImc,EAAInc,KAAS,aAIhEqc,6BAAP,WACE,OAAO7c,KAAK8c,EAAmB,UAQ1BD,+CAAP,WACE,OAAU7c,KAAK+c,uBAAsB/c,KAAKgd,MAQrCH,kDAAP,WACE,OAAU7c,KAAKid,SAA0Bjd,KAAKgd,MAIzCH,iCAAP,WACE,IAAMF,EAAM3c,KAAK4c,EACjB,OAAUD,EAAInc,KAAO,IAAImc,EAAInc,KAAS,YAAUmc,EAAIhc,qBAO/Ckc,8BAAP,SAAyBK,EAAoBC,GAC3C,IAAMR,EAAM3c,KAAK4c,EACXQ,EAAS,CAAC,2BAMhB,OALAA,EAAOlgB,KAAK,iBAAiBggB,MAAcC,GAC3CC,EAAOlgB,KAAK,cAAcyf,EAAI1b,MAC1B0b,EAAIlc,MACN2c,EAAOlgB,KAAK,iBAAiByf,EAAIlc,MAE5B,CACL4c,eAAgB,mBAChBC,gBAAiBF,EAAO/f,KAAK,QAK1Bwf,oCAAP,SACEU,gBAAAA,MAMA,IAAMZ,EAAM3c,KAAK4c,EACXY,EAAcxd,KAAKyd,yCAEnBC,EAAiB,GAEvB,IAAK,IAAMhgB,KADXggB,EAAexgB,KAAK,OAAOyf,EAAIrhB,YACbiiB,EAChB,GAAY,QAAR7f,EAIJ,GAAY,SAARA,EAAgB,CAClB,IAAK6f,EAActc,KACjB,SAEEsc,EAActc,KAAKvB,MACrBge,EAAexgB,KAAK,QAAQygB,mBAAmBJ,EAActc,KAAKvB,OAEhE6d,EAActc,KAAK4W,OACrB6F,EAAexgB,KAAK,SAASygB,mBAAmBJ,EAActc,KAAK4W,aAGrE6F,EAAexgB,KAAQygB,mBAAmBjgB,OAAQigB,mBAAmBJ,EAAc7f,KAGvF,OAAIggB,EAAe3gB,OACPygB,MAAYE,EAAergB,KAAK,KAGrCmgB,GAIDX,eAAR,WACE,OAAO7c,KAAK8c,EAAmB,aAIzBD,cAAR,SAA2BvY,GAGzB,MAAO,GAFMtE,KAAKyd,qBACNzd,KAAK4c,EACIjc,cAAa2D,OAI5BuY,eAAR,WACE,Ib3EsB7X,Ea4EhB4Y,EAAO,CAGXC,WAJU7d,KAAK4c,EAIC3b,KAChB6c,eAjIqB,KAmIvB,OblFsB9Y,EakFL4Y,EbjFZxiB,OAAOoL,KAAKxB,GAChB+B,IAAI,SAAArJ,GAAO,OAAGigB,mBAAmBjgB,OAAQigB,mBAAmB3Y,EAAOtH,MACnEL,KAAK,WcnDG0gB,GAAkC,YAmE/B7E,GAAqCxN,GACnD,IAAMsS,EAAiC,GAKvC,gBAjEqCtS,GACrC,IAAMuS,EAAuBvS,EAAQuS,uBAA2BvS,EAAQuS,sBAAyB,GAC3FC,EAAmBxS,EAAQsS,aAC7BA,EAA8B,GAClC,GAAI9e,MAAM6D,QAAQmb,GAAmB,CACnC,IAAMC,EAAwBD,EAAiBnX,IAAI,SAAAnJ,GAAK,OAAAA,EAAE8B,OACpD0e,EAAoC,GAG1CH,EAAoBzc,QAAQ,SAAA6c,IAEoC,IAA5DF,EAAsB7a,QAAQ+a,EAAmB3e,QACa,IAA9D0e,EAAwB9a,QAAQ+a,EAAmB3e,QAEnDse,EAAa9gB,KAAKmhB,GAClBD,EAAwBlhB,KAAKmhB,EAAmB3e,SAKpDwe,EAAiB1c,QAAQ,SAAA8c,IACwC,IAA3DF,EAAwB9a,QAAQgb,EAAgB5e,QAClDse,EAAa9gB,KAAKohB,GAClBF,EAAwBlhB,KAAKohB,EAAgB5e,aAGZ,mBAArBwe,GAChBF,EAAeE,EAAiBD,GAChCD,EAAe9e,MAAM6D,QAAQib,GAAgBA,EAAe,CAACA,IAE7DA,IAAmBC,GAIrB,IAAMM,EAAoBP,EAAajX,IAAI,SAAAnJ,GAAK,OAAAA,EAAE8B,OAMlD,OAJoD,IAAhD6e,EAAkBjb,QADE,UAEtB0a,EAAa9gB,WAAb8gB,IAAqBA,EAAa5b,OAAOmc,EAAkBjb,QAFrC,SAE+D,KAGhF0a,EAqBPQ,CAAuB9S,GAASlK,QAAQ,SAAAqZ,GACtCmD,EAAanD,EAAYnb,MAAQmb,WAlBJA,IAC0B,IAArDkD,GAAsBza,QAAQuX,EAAYnb,QAG9Cmb,EAAY4D,UAAUlH,GAAyBwE,IAC/CgC,GAAsB7gB,KAAK2d,EAAYnb,MACvCyK,GAAOH,IAAI,0BAA0B6Q,EAAYnb,OAa/Cgf,CAAiB7D,KAEZmD,ECjBT,IC1DIW,iBDoFF,WAAsBC,EAAkClT,GAX9C1L,QAAkC,GAGlCA,QAAsB,EAS9BA,KAAK6e,GAAW,IAAID,EAAalT,GACjC1L,KAAK8e,GAAWpT,EAEZA,EAAQiR,MACV3c,KAAK+e,GAAO,IAAI3e,EAAIsL,EAAQiR,MA8dlC,OAtdSqC,6BAAP,SAAwB1Y,EAAgBoQ,EAAkBpC,GAA1D,WACMoF,EAA8BhD,GAAQA,EAAK1N,SAW/C,OATAhJ,KAAKif,GACHjf,KAAKkf,KACFC,mBAAmB7Y,EAAWoQ,GAC9Bra,KAAK,SAAAyM,GAAS,OAAArJ,EAAK2f,GAActW,EAAO4N,EAAMpC,KAC9CjY,KAAK,SAAAiN,GACJoQ,EAAUpQ,KAIToQ,GAMFsF,2BAAP,SAAsBzf,EAAiBpB,EAAkBuY,EAAkBpC,GAA3E,WACMoF,EAA8BhD,GAAQA,EAAK1N,SAEzCqW,EAAgBxjB,EAAY0D,GAC9BS,KAAKkf,KAAcI,iBAAiBpc,OAAO3D,GAAUpB,EAAOuY,GAC5D1W,KAAKkf,KAAcC,mBAAmB5f,EAASmX,GAUnD,OARA1W,KAAKif,GACHI,EACGhjB,KAAK,SAAAyM,GAAS,OAAArJ,EAAK2f,GAActW,EAAO4N,EAAMpC,KAC9CjY,KAAK,SAAAiN,GACJoQ,EAAUpQ,KAIToQ,GAMFsF,yBAAP,SAAoBlW,EAAc4N,EAAkBpC,GAClD,IAAIoF,EAA8BhD,GAAQA,EAAK1N,SAQ/C,OANAhJ,KAAKif,GACHjf,KAAKof,GAActW,EAAO4N,EAAMpC,GAAOjY,KAAK,SAAAiN,GAC1CoQ,EAAUpQ,KAIPoQ,GAMFsF,2BAAP,SAAsB/I,GACfA,EAAQiC,QAGXlY,KAAKuf,GAAatJ,GAFlB9L,GAAOF,KAAK,iDAST+U,mBAAP,WACE,OAAOhf,KAAK+e,IAMPC,uBAAP,WACE,OAAOhf,KAAK8e,IAMPE,kBAAP,SAAa5L,GAAb,WACE,OAAOpT,KAAKwf,GAAoBpM,GAAS/W,KAAK,SAAAojB,GAC5C,OAAOhgB,EAAKyf,KACTQ,eACArE,MAAMjI,GACN/W,KAAK,SAAAsjB,GAAoB,OAAAF,GAASE,OAOlCX,kBAAP,SAAa5L,GAAb,WACE,OAAOpT,KAAK4f,MAAMxM,GAAS/W,KAAK,SAAAiN,GAE9B,OADA7J,EAAKogB,aAAaC,SAAU,EACrBxW,KAOJ0V,8BAAP,WACMhf,KAAK+f,OACP/f,KAAKggB,GAAgB9G,GAAkBlZ,KAAK8e,MAOzCE,2BAAP,SAA6CnE,GAC3C,IACE,OAAQ7a,KAAKggB,GAAcnF,EAAY9c,KAAa,KACpD,MAAOT,GAEP,OADA6M,GAAOF,KAAK,+BAA+B4Q,EAAY9c,+BAChD,OAKDihB,eAAV,SAAkC/I,EAAkBnN,WAG9CsP,EAFA6H,GAAU,EACVC,GAAU,EAERC,EAAarX,EAAMxC,WAAawC,EAAMxC,UAAUyC,OAEtD,GAAIoX,EAAY,CACdD,GAAU,MAEV,IAAiB,IAAAE,EAAAtZ,EAAAqZ,iCAAY,CAAxB,IACG1W,UAAeA,UACrB,GAAIA,IAAmC,IAAtBA,EAAU4W,QAAmB,CAC5CJ,GAAU,EACV,0GAKN,IAAMhf,EAAO6H,EAAM7H,KACnB,IAAKgV,EAAQmC,UAAW,CACtB,IAAMkI,EAAUxX,EAAM1B,QAAU0B,EAAM1B,QAAQkZ,QAAU,GACxD,IAAK,IAAM5iB,KAAO4iB,EAChB,GAA0B,eAAtB5iB,EAAII,cAAgC,CACtCsa,EAAYkI,EAAQ5iB,GACpB,OAKNuY,EAAQb,cACF6K,GAAW,CAAErT,OAAQ9R,EAAcylB,WACvCtf,OACAmX,YACAC,OAAQpC,EAAQoC,OAASmI,OAAON,GAAWD,OAKrCjB,eAAV,SAAuB/I,GACrBjW,KAAKkf,KAAcuB,YAAYxK,IAIvB+I,eAAV,SAA8B5L,GAA9B,WACE,OAAO,IAAInB,GAAY,SAAAC,GACrB,IAAIwO,EAAiB,EAGfC,EAAWC,YAAY,WACH,GAApBnhB,EAAKohB,IACPC,cAAcH,GACdzO,GAAQ,KAERwO,GAPiB,EAQbtN,GAAWsN,GAAUtN,IACvB0N,cAAcH,GACdzO,GAAQ,MAVO,MAkBf8M,eAAV,WACE,OAAOhf,KAAK6e,IAIJG,eAAV,WACE,OAAqC,IAA9Bhf,KAAK6f,aAAaC,cAAmC/S,IAAd/M,KAAK+e,IAiB3CC,eAAV,SAAwBlW,EAAcwL,EAAeoC,GAArD,WACUpW,mCAAAygB,iBACFC,SACDlY,IACHE,SAAUF,EAAME,WAAa0N,GAAQA,EAAK1N,SAAW0N,EAAK1N,SAAWtB,KACrE8O,UAAW1N,EAAM0N,WAAaxC,OAGhChU,KAAKihB,GAAoBD,GACzBhhB,KAAKkhB,GAA2BF,GAIhC,IAAIG,EAAa7M,EACboC,GAAQA,EAAKR,iBACfiL,EAAa9M,GAAM8E,MAAMgI,GAAY/L,OAAOsB,EAAKR,iBAInD,IAAI5M,EAAS2I,GAAYC,QAAsB8O,GAS/C,OALIG,IAEF7X,EAAS6X,EAAWC,aAAaJ,EAAUtK,IAGtCpN,EAAOjN,KAAK,SAAAglB,GACjB,MAA8B,iBAAnBN,GAA+BA,EAAiB,EAClDthB,EAAK6hB,GAAgBD,EAAKN,GAE5BM,KAcDrC,eAAV,SAA0BlW,EAAqB7D,GAC7C,IAAK6D,EACH,OAAO,KAGT,IAAMjD,eACDiD,GACCA,EAAMiO,aAAe,CACvBA,YAAajO,EAAMiO,YAAYhQ,IAAI,SAAAwa,GAAK,cACnCA,GACCA,EAAEnR,MAAQ,CACZA,KAAMhL,EAAUmc,EAAEnR,KAAMnL,SAI1B6D,EAAM7H,MAAQ,CAChBA,KAAMmE,EAAU0D,EAAM7H,KAAMgE,KAE1B6D,EAAMsN,UAAY,CACpBA,SAAUhR,EAAU0D,EAAMsN,SAAUnR,KAElC6D,EAAM0M,OAAS,CACjBA,MAAOpQ,EAAU0D,EAAM0M,MAAOvQ,KAclC,OAJI6D,EAAMsN,UAAYtN,EAAMsN,SAASO,QAEnC9Q,EAAWuQ,SAASO,MAAQ7N,EAAMsN,SAASO,OAEtC9Q,GASCmZ,eAAV,SAA8BlW,GAC5B,IAAM4C,EAAU1L,KAAK6f,aACb1H,gBAAaD,YAASsJ,SAAMlhB,mBAAAmhB,mBAE9B,gBAAiB3Y,IACrBA,EAAMqP,YAAc,gBAAiBzM,EAAUyM,EAAc,mBAGzCpL,IAAlBjE,EAAMoP,cAAqCnL,IAAZmL,IACjCpP,EAAMoP,QAAUA,QAGCnL,IAAfjE,EAAM0Y,WAA+BzU,IAATyU,IAC9B1Y,EAAM0Y,KAAOA,GAGX1Y,EAAMvJ,UACRuJ,EAAMvJ,QAAUkD,EAASqG,EAAMvJ,QAASkiB,IAG1C,IAAMnb,EAAYwC,EAAMxC,WAAawC,EAAMxC,UAAUyC,QAAUD,EAAMxC,UAAUyC,OAAO,GAClFzC,GAAaA,EAAUrD,QACzBqD,EAAUrD,MAAQR,EAAS6D,EAAUrD,MAAOwe,IAG9C,IAAMra,EAAU0B,EAAM1B,QAClBA,GAAWA,EAAQqB,MACrBrB,EAAQqB,IAAMhG,EAAS2E,EAAQqB,IAAKgZ,KAQ9BzC,eAAV,SAAqClW,GACnC,IAAM4Y,EAAU5Y,EAAM6Y,IAChBC,EAAoBxmB,OAAOoL,KAAKxG,KAAKggB,IACvC0B,GAAWE,EAAkB7kB,OAAS,IACxC2kB,EAAQ1D,aAAe4D,IAQjB5C,eAAV,SAAqBlW,GACnB9I,KAAKkf,KAAc2C,UAAU/Y,IASrBkW,eAAV,SAAwBlW,EAAc4N,EAAkBpC,GACtD,OAAOtU,KAAK8hB,GAAchZ,EAAO4N,EAAMpC,GAAOjY,KAC5C,SAAA0lB,GACE,OAAOA,EAAW/Y,UAEpB,SAAAmI,GACEhH,GAAOlG,MAAMkN,MAmBT6N,eAAV,SAAwBlW,EAAc4N,EAAkBpC,GAAxD,WAEQhU,oBAAE0hB,eAAYC,eAEpB,IAAKjiB,KAAK+f,KACR,OAAO9N,GAAYG,OAAO,IAAIrR,EAAY,0CAG5C,IAAMmhB,EAA+B,gBAAfpZ,EAAMzE,KAI5B,OAAK6d,GAAuC,iBAAfD,GAA2B3Z,KAAKC,SAAW0Z,EAC/DhQ,GAAYG,OACjB,IAAIrR,EACF,oFAAoFkhB,QAKnFjiB,KAAKmiB,GAAcrZ,EAAOwL,EAAOoC,GACrCra,KAAK,SAAA2kB,GACJ,GAAiB,OAAbA,EACF,MAAM,IAAIjgB,EAAY,0DAIxB,GAD4B2V,GAAQA,EAAKtG,OAA8D,IAArDsG,EAAKtG,KAAiCgS,YAC7DF,IAAkBF,EAC3C,OAAOhB,EAGT,IAAMqB,EAAmBL,EAAWhB,EAAUtK,GAC9C,QAAgC,IAArB2L,EACT,MAAM,IAAIthB,EAAY,8DACjB,OAAI5E,EAAWkmB,GACZA,EAA+ChmB,KACrD,SAAAyM,GAAS,OAAAA,GACT,SAAAtG,GACE,MAAM,IAAIzB,EAAY,4BAA4ByB,KAIjD6f,IAERhmB,KAAK,SAAAimB,GACJ,GAAuB,OAAnBA,EACF,MAAM,IAAIvhB,EAAY,sDAGxB,IAAMkV,EAAU3B,GAASA,EAAM8G,YAAc9G,EAAM8G,aAMnD,OALK8G,GAAiBjM,GACpBxW,EAAK8iB,GAAwBtM,EAASqM,GAGxC7iB,EAAK+iB,GAAWF,GACTA,IAERjmB,KAAK,KAAM,SAAA8U,GACV,GAAIA,aAAkBpQ,EACpB,MAAMoQ,EASR,MANA1R,EAAKgd,iBAAiBtL,EAAQ,CAC5Bf,KAAM,CACJgS,YAAY,GAEdtI,kBAAmB3I,IAEf,IAAIpQ,EACR,8HAA8HoQ,MAQ5H6N,eAAV,SAAsByD,GAAtB,WACEziB,KAAK6gB,IAAe,EACpB4B,EAAQpmB,KACN,SAAA4G,GAEE,OADAxD,EAAKohB,IAAe,EACb5d,GAET,SAAAkO,GAEE,OADA1R,EAAKohB,IAAe,EACb1P,wBEjjBf,cAiBA,OAbSuR,sBAAP,SAAiBvQ,GACf,OAAOF,GAAYC,QAAQ,CACzBf,OAAQ,sEACRvE,OAAQ5R,SAAO2nB,WAOZD,kBAAP,SAAavQ,GACX,OAAOF,GAAYC,SAAQ,uBC+C7B,WAAmBxG,GACjB1L,KAAK8e,GAAWpT,EACX1L,KAAK8e,GAASnC,KACjBxS,GAAOF,KAAK,kDAEdjK,KAAK4iB,GAAa5iB,KAAK6iB,KAsD3B,OA/CSC,+BAAP,SAA0BC,EAAiBC,GACzC,MAAM,IAAIjiB,EAAY,yDAMjB+hB,6BAAP,SAAwBG,EAAkBpO,EAAmBmO,GAC3D,MAAM,IAAIjiB,EAAY,uDAMjB+hB,sBAAP,SAAiBha,GACf9I,KAAK4iB,GAAWf,UAAU/Y,GAAOzM,KAAK,KAAM,SAAA8U,GAC1ChH,GAAOlG,MAAM,8BAA8BkN,MAOxC2R,wBAAP,SAAmB7M,GACZjW,KAAK4iB,GAAWnC,YAKrBzgB,KAAK4iB,GAAWnC,YAAYxK,GAAS5Z,KAAK,KAAM,SAAA8U,GAC9ChH,GAAOlG,MAAM,gCAAgCkN,KAL7ChH,GAAOF,KAAK,4EAYT6Y,yBAAP,WACE,OAAO9iB,KAAK4iB,IAMJE,eAAV,WACE,OAAO,IAAIJ,kBCtHCQ,GAAuBjN,EAAkBkN,GAQvD,MAAO,CACLnW,KARsBnI,KAAKC,UAAU,CACrCse,SAAS,IAAIlW,MAAOsL,qBAEF3T,KAAKC,UAAU,CACjCT,KAAM,iBAIuCQ,KAAKC,UAAUmR,GAC5D5R,KAAM,UACNoE,IAAK0a,EAAIE,kDAKGC,GAAqBxa,EAAcqa,GAEjD,IAAM7iB,aAAEijB,4BAAyCtB,wBAAiCuB,yDAClF1a,EAAMwM,KAAOkO,EAEb,IAAMC,EAA6B,gBAAf3a,EAAMzE,KAEpBqf,EAAqB,CACzB1W,KAAMnI,KAAKC,UAAUgE,GACrBzE,KAAMyE,EAAMzE,MAAQ,QACpBoE,IAAKgb,EAAcN,EAAIE,wCAA0CF,EAAIQ,sCASvE,GAAIF,EAAa,CACf,IA6BMG,EA7BkB/e,KAAKC,UAAU,CACrCkE,SAAUF,EAAME,SAChBoa,SAAS,IAAIlW,MAAOsL,qBAEF3T,KAAKC,UAAU,CACjCT,KAAMyE,EAAMzE,KAIZwf,aAAc,CAAC,CAAE9lB,GAAIwlB,EAAgBO,KAAM7B,WAoBWyB,EAAI1W,KAC5D0W,EAAI1W,KAAO4W,EAGb,OAAOF,EHrET,kBAAA,aASS1jB,UAAe+jB,EAAiBhmB,GAezC,OAVSgmB,sBAAP,WAEEpF,GAA2BzT,SAAS7P,UAAUC,SAG9C4P,SAAS7P,UAAUC,SAAW,eAAgC,aAAAwO,mBAAAA,IAAAC,kBAC5D,IAAM4L,EAAU3V,KAAK6D,qBAAuB7D,KAC5C,OAAO2e,GAAyBxT,MAAMwK,EAAS5L,KAjBrCga,KAAa,wBIHvBC,GAAwB,CAAC,oBAAqB,+DA2BlD,WAAoClF,gBAAAA,MAAA9e,QAAA8e,EAF7B9e,UAAeikB,EAAelmB,GA4KvC,OArKSkmB,sBAAP,WACE1M,GAAwB,SAACzO,GACvB,IAAM6S,EAAMI,KACZ,IAAKJ,EACH,OAAO7S,EAET,IAAMrB,EAAOkU,EAAIb,eAAemJ,GAChC,GAAIxc,EAAM,CACR,IAAMmR,EAAS+C,EAAIrC,YACb4K,EAAgBtL,EAASA,EAAOiH,aAAe,GAC/CnU,EAAUjE,EAAK0c,GAAcD,GACnC,GAAIzc,EAAK2c,GAAiBtb,EAAO4C,GAC/B,OAAO,KAGX,OAAO5C,KAKHmb,eAAR,SAAyBnb,EAAc4C,GACrC,OAAI1L,KAAKqkB,GAAevb,EAAO4C,IAC7BvB,GAAOF,KAAK,6DAA6DpB,EAAoBC,KACtF,GAEL9I,KAAKskB,GAAgBxb,EAAO4C,IAC9BvB,GAAOF,KACL,wEAA0EpB,EAAoBC,KAEzF,GAEL9I,KAAKukB,GAAazb,EAAO4C,IAC3BvB,GAAOF,KACL,oEAAsEpB,EACpEC,cACU9I,KAAKwkB,GAAmB1b,KAE/B,IAEJ9I,KAAKykB,GAAc3b,EAAO4C,KAC7BvB,GAAOF,KACL,yEAA2EpB,EACzEC,cACU9I,KAAKwkB,GAAmB1b,KAE/B,IAMHmb,eAAR,SAAuBnb,EAAc4C,GACnC,IAAKA,EAAQgZ,eACX,OAAO,EAGT,IACE,OACG5b,GACCA,EAAMxC,WACNwC,EAAMxC,UAAUyC,QAChBD,EAAMxC,UAAUyC,OAAO,IACY,gBAAnCD,EAAMxC,UAAUyC,OAAO,GAAG1E,OAC5B,EAEF,MAAO/G,GACP,OAAO,IAKH2mB,eAAR,SAAwBnb,EAAc4C,GACpC,SAAKA,EAAQiZ,eAAiBjZ,EAAQiZ,aAAa5nB,SAI5CiD,KAAK4kB,GAA0B9b,GAAO+b,KAAK,SAAAtlB,GAEhD,OAACmM,EAAQiZ,aAAwCE,KAAK,SAAAzhB,GAAW,OAAAD,EAAkB5D,EAAS6D,QAKxF6gB,eAAR,SAAqBnb,EAAc4C,GAEjC,IAAKA,EAAQoZ,WAAapZ,EAAQoZ,SAAS/nB,OACzC,OAAO,EAET,IAAM0L,EAAMzI,KAAKwkB,GAAmB1b,GACpC,QAAQL,GAAciD,EAAQoZ,SAASD,KAAK,SAAAzhB,GAAW,OAAAD,EAAkBsF,EAAKrF,MAIxE6gB,eAAR,SAAsBnb,EAAc4C,GAElC,IAAKA,EAAQqZ,YAAcrZ,EAAQqZ,UAAUhoB,OAC3C,OAAO,EAET,IAAM0L,EAAMzI,KAAKwkB,GAAmB1b,GACpC,OAAQL,GAAaiD,EAAQqZ,UAAUF,KAAK,SAAAzhB,GAAW,OAAAD,EAAkBsF,EAAKrF,MAIxE6gB,eAAR,SAAsBC,GACpB,oBADoBA,MACb,CACLa,YAEM/kB,KAAK8e,GAASkG,eAAiB,GAC/BhlB,KAAK8e,GAASiG,WAAa,GAE3Bb,EAAcc,eAAiB,GAC/Bd,EAAca,WAAa,IAEjCD,WAEM9kB,KAAK8e,GAASmG,eAAiB,GAC/BjlB,KAAK8e,GAASgG,UAAY,GAE1BZ,EAAce,eAAiB,GAC/Bf,EAAcY,UAAY,IAEhCH,eACM3kB,KAAK8e,GAAS6F,cAAgB,GAC9BT,EAAcS,cAAgB,GAC/BX,IAELU,oBAAwD,IAAjC1kB,KAAK8e,GAAS4F,gBAAiC1kB,KAAK8e,GAAS4F,iBAKhFT,eAAR,SAAkCnb,GAChC,GAAIA,EAAMvJ,QACR,MAAO,CAACuJ,EAAMvJ,SAEhB,GAAIuJ,EAAMxC,UACR,IACQ,IAAAhG,gDAAEY,SAAAmD,kBAAWlD,UAAA8B,kBACnB,MAAO,CAAC,GAAGA,EAAYoB,OAASpB,GAChC,MAAOiiB,GAEP,OADA/a,GAAOlG,MAAM,oCAAoC4E,EAAoBC,IAC9D,GAGX,MAAO,IAIDmb,eAAR,SAA2Bnb,GACzB,IACE,GAAIA,EAAMqc,WAAY,CACpB,IAAMC,EAAStc,EAAMqc,WAAWE,OAChC,OAAQD,GAAUA,EAAOA,EAAOroB,OAAS,GAAGuoB,UAAa,KAE3D,GAAIxc,EAAMxC,UAAW,CACnB,IAAMif,EACJzc,EAAMxC,UAAUyC,QAAUD,EAAMxC,UAAUyC,OAAO,GAAGoc,YAAcrc,EAAMxC,UAAUyC,OAAO,GAAGoc,WAAWE,OACzG,OAAQE,GAAUA,EAAOA,EAAOxoB,OAAS,GAAGuoB,UAAa,KAE3D,OAAO,KACP,MAAOJ,GAEP,OADA/a,GAAOlG,MAAM,gCAAgC4E,EAAoBC,IAC1D,OA9KGmb,KAAa,+FCevBuB,GAAmB,IAGnB9W,GAAS,6JAIT+W,GAAQ,mMACRC,GAAQ,gHACRC,GAAY,gDACZC,GAAa,gCAEbC,GAAsB,uCAIZC,GAAkBC,GAChC,IAAI5hB,EAAQ,KACR6hB,EAAU,EAEVD,IAC4B,iBAAnBA,EAAGE,YACZD,EAAUD,EAAGE,YACJJ,GAAoBxiB,KAAK0iB,EAAGxmB,WACrCymB,EAAU,IAId,IAKE,GADA7hB,EAgHJ,SAA6C4hB,GAC3C,IAAKA,IAAOA,EAAGZ,WACb,OAAO,KAYT,IAPA,IAKIe,EALEf,EAAaY,EAAGZ,WAChBgB,EAAe,8DACfC,EAAe,sGACfC,EAAQlB,EAAWnnB,MAAM,MACzBmG,EAAQ,GAGL0L,EAAO,EAAGA,EAAOwW,EAAMtpB,OAAQ8S,GAAQ,EAAG,CACjD,IAAIyW,EAAU,MACTJ,EAAQC,EAAarlB,KAAKulB,EAAMxW,KACnCyW,EAAU,CACR7d,IAAKyd,EAAM,GACXzb,KAAMyb,EAAM,GACZnc,KAAM,GACN8F,MAAOqW,EAAM,GACbpW,OAAQ,OAEAoW,EAAQE,EAAatlB,KAAKulB,EAAMxW,OAC1CyW,EAAU,CACR7d,IAAKyd,EAAM,GACXzb,KAAMyb,EAAM,IAAMA,EAAM,GACxBnc,KAAMmc,EAAM,GAAKA,EAAM,GAAGloB,MAAM,KAAO,GACvC6R,MAAOqW,EAAM,GACbpW,QAASoW,EAAM,KAIfI,KACGA,EAAQ7b,MAAQ6b,EAAQzW,OAC3ByW,EAAQ7b,KAAO+a,IAEjBrhB,EAAMjH,KAAKopB,IAIf,IAAKniB,EAAMpH,OACT,OAAO,KAGT,MAAO,CACLwC,QAASgnB,GAAeR,GACxBrmB,KAAMqmB,EAAGrmB,KACTyE,SAjKQqiB,CAAoCT,GAE1C,OAAOU,GAAUtiB,EAAO6hB,GAE1B,MAAOxjB,IAIT,IAEE,GADA2B,EAkBJ,SAAwC4hB,GACtC,IAAKA,IAAOA,EAAG5hB,MACb,OAAO,KAUT,IAPA,IAGIuiB,EACAR,EACAI,EALEniB,EAAQ,GACRkiB,EAAQN,EAAG5hB,MAAMnG,MAAM,MAMpBJ,EAAI,EAAGA,EAAIyoB,EAAMtpB,SAAUa,EAAG,CACrC,GAAKsoB,EAAQxX,GAAO5N,KAAKulB,EAAMzoB,IAAM,CACnC,IAAM+oB,EAAWT,EAAM,IAAqC,IAA/BA,EAAM,GAAG5iB,QAAQ,UACrC4iB,EAAM,IAAmC,IAA7BA,EAAM,GAAG5iB,QAAQ,UACvBojB,EAAWd,GAAW9kB,KAAKolB,EAAM,OAE9CA,EAAM,GAAKQ,EAAS,GACpBR,EAAM,GAAKQ,EAAS,GACpBR,EAAM,GAAKQ,EAAS,IAEtBJ,EAAU,CAGR7d,IAAKyd,EAAM,IAA0C,IAApCA,EAAM,GAAG5iB,QAAQ,eAAuB4iB,EAAM,GAAGvjB,OAAO,cAAc5F,QAAUmpB,EAAM,GACvGzb,KAAMyb,EAAM,IAAMV,GAClBzb,KAAM4c,EAAW,CAACT,EAAM,IAAM,GAC9BrW,KAAMqW,EAAM,IAAMA,EAAM,GAAK,KAC7BpW,OAAQoW,EAAM,IAAMA,EAAM,GAAK,WAE5B,GAAKA,EAAQR,GAAM5kB,KAAKulB,EAAMzoB,IACnC0oB,EAAU,CACR7d,IAAKyd,EAAM,GACXzb,KAAMyb,EAAM,IAAMV,GAClBzb,KAAM,GACN8F,MAAOqW,EAAM,GACbpW,OAAQoW,EAAM,IAAMA,EAAM,GAAK,UAE5B,CAAA,KAAKA,EAAQT,GAAM3kB,KAAKulB,EAAMzoB,KAuBnC,SAtBSsoB,EAAM,IAAMA,EAAM,GAAG5iB,QAAQ,YAAc,IACrCojB,EAAWf,GAAU7kB,KAAKolB,EAAM,MAE7CA,EAAM,GAAKA,EAAM,IAAM,OACvBA,EAAM,GAAKQ,EAAS,GACpBR,EAAM,GAAKQ,EAAS,GACpBR,EAAM,GAAK,IACI,IAANtoB,GAAYsoB,EAAM,SAA0B,IAApBH,EAAGa,eAKpCziB,EAAM,GAAG2L,OAAUiW,EAAGa,aAA0B,GAElDN,EAAU,CACR7d,IAAKyd,EAAM,GACXzb,KAAMyb,EAAM,IAAMV,GAClBzb,KAAMmc,EAAM,GAAKA,EAAM,GAAGloB,MAAM,KAAO,GACvC6R,KAAMqW,EAAM,IAAMA,EAAM,GAAK,KAC7BpW,OAAQoW,EAAM,IAAMA,EAAM,GAAK,OAM9BI,EAAQ7b,MAAQ6b,EAAQzW,OAC3ByW,EAAQ7b,KAAO+a,IAGjBrhB,EAAMjH,KAAKopB,GAGb,IAAKniB,EAAMpH,OACT,OAAO,KAGT,MAAO,CACLwC,QAASgnB,GAAeR,GACxBrmB,KAAMqmB,EAAGrmB,KACTyE,SAjGQ0iB,CAA+Bd,GAErC,OAAOU,GAAUtiB,EAAO6hB,GAE1B,MAAOxjB,IAIT,MAAO,CACLjD,QAASgnB,GAAeR,GACxBrmB,KAAMqmB,GAAMA,EAAGrmB,KACfyE,MAAO,GACP2iB,QAAQ,GAiJZ,SAASL,GAAUtB,EAAwBa,GACzC,IACE,cACKb,IACHhhB,MAAOghB,EAAWhhB,MAAM/C,MAAM4kB,KAEhC,MAAOxjB,GACP,OAAO2iB,GAUX,SAASoB,GAAeR,GACtB,IAAMxmB,EAAUwmB,GAAMA,EAAGxmB,QACzB,OAAKA,EAGDA,EAAQ0E,OAA0C,iBAA1B1E,EAAQ0E,MAAM1E,QACjCA,EAAQ0E,MAAM1E,QAEhBA,EALE,mBC9PX,IAAMwnB,GAAmB,YAOTC,GAAwB7B,GACtC,IAAME,EAAS4B,GAAsB9B,EAAWhhB,OAE1CmC,EAAuB,CAC3BjC,KAAM8gB,EAAWzlB,KACjBuD,MAAOkiB,EAAW5lB,SAWpB,OARI8lB,GAAUA,EAAOtoB,SACnBuJ,EAAU6e,WAAa,CAAEE,gBAGJtY,IAAnBzG,EAAUjC,MAA0C,KAApBiC,EAAUrD,QAC5CqD,EAAUrD,MAAQ,8BAGbqD,WAyCO4gB,GAAoB/B,GAGlC,MAAO,CACL7e,UAAW,CACTyC,OAAQ,CAJMie,GAAwB7B,eAY5B8B,GAAsB9iB,GACpC,IAAKA,IAAUA,EAAMpH,OACnB,MAAO,GAGT,IAAIoqB,EAAahjB,EAEXijB,EAAqBD,EAAW,GAAG1c,MAAQ,GAC3C4c,EAAoBF,EAAWA,EAAWpqB,OAAS,GAAG0N,MAAQ,GAapE,OAVsD,IAAlD2c,EAAmB9jB,QAAQ,oBAAgF,IAApD8jB,EAAmB9jB,QAAQ,sBACpF6jB,EAAaA,EAAW/lB,MAAM,KAIoB,IAAhDimB,EAAkB/jB,QAAQ,mBAC5B6jB,EAAaA,EAAW/lB,MAAM,GAAI,IAI7B+lB,EACJ/lB,MAAM,EAAG2lB,IACThgB,IACC,SAACugB,GAA0C,OACzCC,MAAwB,OAAjBD,EAAMxX,YAAkB/C,EAAYua,EAAMxX,OACjDwV,SAAUgC,EAAM7e,KAAO0e,EAAW,GAAG1e,IACrC+e,SAAUF,EAAM7c,MAAQ,IACxBgd,QAAQ,EACRC,OAAuB,OAAfJ,EAAMzX,UAAgB9C,EAAYua,EAAMzX,QAGnDzS,mBCtDWuqB,GACdrhB,EACAuT,EACAnO,GAKA,IAAI5C,E/BfyB3N,E+BiB7B,gBAPAuQ,MAOIhQ,EAAa4K,IAA6BA,EAAyBrC,MAMrE,OADA6E,EAAQoe,GAAoBpB,GAD5Bxf,EAFmBA,EAEIrC,QAIzB,GAAItI,EAAW2K,K/BzBcnL,E+ByB2BmL,E/BxBT,0BAAxClL,OAAOC,UAAUC,SAASC,KAAKJ,I+BwB8C,CAKlF,IAAMysB,EAAethB,EACfuhB,EAAOD,EAAaloB,OAAS/D,EAAWisB,GAAgB,WAAa,gBACrEroB,EAAUqoB,EAAaroB,QAAasoB,OAASD,EAAaroB,QAAYsoB,EAQ5E,OALAte,EADAT,EAAQgf,GAAgBvoB,EAASsa,EAAoBnO,GACxBnM,GACzB,SAAUqoB,IACZ9e,EAAMwM,YAAYxM,EAAMwM,OAAMyS,oBAAqB,GAAGH,EAAalpB,QAG9DoK,EAET,OAAI5N,EAAQoL,GAEVwC,EAAQoe,GAAoBpB,GAAkBxf,IAG5CxK,EAAcwK,IAAcvK,EAAQuK,IAMtCkD,GADAV,WDtEFxC,EACAuT,EACAmO,GAEA,IAAMlf,EAAe,CACnBxC,UAAW,CACTyC,OAAQ,CACN,CACE1E,KAAMtI,EAAQuK,GAAaA,EAAU1G,YAAYF,KAAOsoB,EAAY,qBAAuB,QAC3F/kB,MAAO,cACL+kB,EAAY,oBAAsB,qCACZ3hB,EAA+BC,MAI7DkP,MAAO,CACLyS,eAAgBljB,EAAgBuB,KAIpC,GAAIuT,EAAoB,CACtB,IACMuL,EAAS6B,GADInB,GAAkBjM,GACW1V,OAChD2E,EAAMqc,WAAa,CACjBE,UAIJ,OAAOvc,EC0CGof,CADgB5hB,EACsBuT,EAAoBnO,EAAQsc,WAC7C,CAC3BG,WAAW,IAENrf,IAaTS,EADAT,EAAQgf,GAAgBxhB,EAAqBuT,EAAoBnO,GACpC,GAAGpF,OAAayG,GAC7CvD,GAAsBV,EAAO,CAC3Bqf,WAAW,IAGNrf,YAMOgf,GACdjlB,EACAgX,EACAnO,gBAAAA,MAIA,IAAM5C,EAAe,CACnBvJ,QAASsD,GAGX,GAAI6I,EAAQ0c,kBAAoBvO,EAAoB,CAClD,IACMuL,EAAS6B,GADInB,GAAkBjM,GACW1V,OAChD2E,EAAMqc,WAAa,CACjBE,UAIJ,OAAOvc,EC5IT,kBAeE,WAA0B4C,GAAA1L,aAAA0L,EALP1L,OAAyC,IAAI+S,GAAc,IAG3D/S,QAAoC,GAGrDA,KAAKqoB,GAAO,IAAIxL,GAAI7c,KAAK0L,QAAQiR,KAEjC3c,KAAKyI,IAAMzI,KAAKqoB,GAAK1E,qCAiGzB,OA3FS2E,sBAAP,SAAiBnW,GACf,MAAM,IAAIpR,EAAY,wDAMjBunB,kBAAP,SAAalV,GACX,OAAOpT,KAAKkT,EAAQqV,MAAMnV,IAMlBkV,eAAV,SAA0BhoB,OACxBkoB,gBACAha,aACA8R,YACApO,YACAE,WAQMxF,EAAS5R,SAAOytB,aAAaja,EAAS5B,QAK5B5M,KAAK0oB,GAAiBpI,IACzBnW,GAAOF,KAAK,yCAAyCjK,KAAK2oB,GAAeH,IAElF5b,IAAW5R,SAAO2D,QAKtByT,EAAO5D,GAJL0D,EAAQ,CAAEtF,YAUJ0b,eAAV,SAAyBM,GACvB,OAAO5oB,KAAK6oB,GAAYD,IAAa5oB,KAAK6oB,GAAYvV,KAM9CgV,eAAV,SAAyBM,GACvB,OAAO5oB,KAAK2oB,GAAeC,GAAY,IAAI1b,KAAKA,KAAKC,QAM7Cmb,eAAV,SAA2BhI,eACnBnT,EAAMD,KAAKC,MACX2b,EAAWxI,EAAQ,wBACnByI,EAAWzI,EAAQ,eAEzB,GAAIwI,EAAU,KAWZ,IAAoB,IAAA3nB,EAAA2F,EAAAgiB,EAASE,OAAOhrB,MAAM,oCAAM,CAA3C,IACGirB,UAAmBjrB,MAAM,IAAK,GAC9BkrB,EAActnB,SAASqnB,EAAW,GAAI,IACtCE,EAAmD,KAAzCxnB,MAAMunB,GAA6B,GAAdA,OACrC,IAAuB,IAAA3sB,YAAAuK,EAAAmiB,EAAW,GAAGjrB,MAAM,qCAAM,CAA5C,IAAM4qB,UACT5oB,KAAK6oB,GAAYD,GAAY,OAAS,IAAI1b,KAAKC,EAAMgc,wMAGzD,OAAO,EACF,QAAIJ,IACT/oB,KAAK6oB,GAAYvV,IAAM,IAAIpG,KAAKC,WtByJAA,EAAaiQ,GACjD,IAAKA,EACH,OAAO1T,GAGT,IAAMwf,EAActnB,SAAS,GAAGwb,EAAU,IAC1C,IAAKzb,MAAMunB,GACT,OAAqB,IAAdA,EAGT,IAAME,EAAalc,KAAK9G,MAAM,GAAGgX,GACjC,OAAKzb,MAAMynB,GAIJ1f,GAHE0f,EAAajc,EsBrKoBkc,CAAsBlc,EAAK4b,KAC1D,SCrHPxjB,GAASiC,mBAGf,4DAkEA,OAlEoC3H,OAI3BypB,sBAAP,SAAiBxgB,GACf,OAAO9I,KAAKupB,GAAajG,GAAqBxa,EAAO9I,KAAKqoB,IAAOvf,IAM5DwgB,wBAAP,SAAmBrT,GACjB,OAAOjW,KAAKupB,GAAarG,GAAuBjN,EAASjW,KAAKqoB,IAAOpS,IAO/DqT,eAAR,SAAqBE,EAA8BC,GAAnD,WACE,GAAIzpB,KAAK0pB,GAAeF,EAAcnlB,MACpC,OAAOslB,QAAQvX,OAAO,CACpBtJ,MAAO2gB,EACPplB,KAAMmlB,EAAcnlB,KACpB8M,OAAQ,yBAAyBnR,KAAK2oB,GAAea,EAAcnlB,mCACnEuI,OAAQ,MAIZ,IAAMlB,EAAuB,CAC3BsB,KAAMwc,EAAcxc,KACpBV,OAAQ,OAKR3B,eAAiBD,KAA2B,SAAW,IASzD,YAPqCqC,IAAjC/M,KAAK0L,QAAQke,iBACfxuB,OAAOyuB,OAAOne,EAAS1L,KAAK0L,QAAQke,sBAET7c,IAAzB/M,KAAK0L,QAAQ4U,UACf5U,EAAQ4U,QAAUtgB,KAAK0L,QAAQ4U,SAG1BtgB,KAAKkT,EAAQhR,IAClB,IAAI+P,GAAsB,SAACC,EAASE,GAClC7M,GACGkI,MAAM+b,EAAc/gB,IAAKiD,GACzBrP,KAAK,SAAAmS,GACJ,IAAM8R,EAAU,CACdwJ,uBAAwBtb,EAAS8R,QAAQyJ,IAAI,wBAC7CC,cAAexb,EAAS8R,QAAQyJ,IAAI,gBAEtCtqB,EAAKwqB,GAAgB,CACnBzB,YAAagB,EAAcnlB,KAC3BmK,WACA8R,UACApO,UACAE,aAGH8X,MAAM9X,UA9DmBkW,mBCFpC,4DAqDA,OArDkCzoB,OAIzBsqB,sBAAP,SAAiBrhB,GACf,OAAO9I,KAAKupB,GAAajG,GAAqBxa,EAAO9I,KAAKqoB,IAAOvf,IAM5DqhB,wBAAP,SAAmBlU,GACjB,OAAOjW,KAAKupB,GAAarG,GAAuBjN,EAASjW,KAAKqoB,IAAOpS,IAO/DkU,eAAR,SAAqBX,EAA8BC,GAAnD,WACE,OAAIzpB,KAAK0pB,GAAeF,EAAcnlB,MAC7BslB,QAAQvX,OAAO,CACpBtJ,MAAO2gB,EACPplB,KAAMmlB,EAAcnlB,KACpB8M,OAAQ,yBAAyBnR,KAAK2oB,GAAea,EAAcnlB,mCACnEuI,OAAQ,MAIL5M,KAAKkT,EAAQhR,IAClB,IAAI+P,GAAsB,SAACC,EAASE,GAClC,IAAMhL,EAAU,IAAI8E,eAapB,IAAK,IAAMkR,KAXXhW,EAAQiG,mBAAqB,WAC3B,GAA2B,IAAvBjG,EAAQsF,WAAkB,CAC5B,IAAM4T,EAAU,CACdwJ,uBAAwB1iB,EAAQgjB,kBAAkB,wBAClDJ,cAAe5iB,EAAQgjB,kBAAkB,gBAE3C3qB,EAAKwqB,GAAgB,CAAEzB,YAAagB,EAAcnlB,KAAMmK,SAAUpH,EAASkZ,UAASpO,UAASE,aAIjGhL,EAAQijB,KAAK,OAAQb,EAAc/gB,KACdhJ,EAAKiM,QAAQ4U,QAC5B7gB,EAAKiM,QAAQ4U,QAAQhhB,eAAe8d,IACtChW,EAAQkjB,iBAAiBlN,EAAQ3d,EAAKiM,QAAQ4U,QAAQlD,IAG1DhW,EAAQmjB,KAAKf,EAAcxc,aAjDDsb,yGCoClC,4DAoCA,OApCoCzoB,OAI3B2qB,+BAAP,SAA0BlkB,EAAoBoQ,GAC5C,gBJ5B+BhL,EAAkBpF,EAAoBoQ,GACvE,IACM5N,EAAQ6e,GAAsBrhB,EADRoQ,GAAQA,EAAKmD,yBAAuB9M,EACG,CACjEqb,iBAAkB1c,EAAQ0c,mBAU5B,OARA5e,GAAsBV,EAAO,CAC3BuX,SAAS,EACThc,KAAM,YAERyE,EAAM3K,MAAQpD,WAASU,MACnBib,GAAQA,EAAK1N,WACfF,EAAME,SAAW0N,EAAK1N,UAEjBiJ,GAAYC,QAAQpJ,GIelBqW,CAAmBnf,KAAK8e,GAAUxY,EAAWoQ,IAK/C8T,6BAAP,SAAwBjrB,EAAiBpB,EAAiCuY,GACxE,oBADuCvY,EAAkBpD,WAASsD,eJZpEqN,EACAnM,EACApB,EACAuY,gBADAvY,EAAkBpD,WAASsD,MAG3B,IACMyK,EAAQgf,GAAgBvoB,EADFmX,GAAQA,EAAKmD,yBAAuB9M,EACL,CACzDqb,iBAAkB1c,EAAQ0c,mBAM5B,OAJAtf,EAAM3K,MAAQA,EACVuY,GAAQA,EAAK1N,WACfF,EAAME,SAAW0N,EAAK1N,UAEjBiJ,GAAYC,QAAQpJ,GIAlBwW,CAAiBtf,KAAK8e,GAAUvf,EAASpB,EAAOuY,IAM/C8T,eAAV,WACE,IAAKxqB,KAAK8e,GAASnC,IAEjB,OAAOnd,YAAMqjB,cAGf,IAAM4H,SACDzqB,KAAK8e,GAAS2L,mBACjB9N,IAAK3c,KAAK8e,GAASnC,MAGrB,OAAI3c,KAAK8e,GAAS4L,UACT,IAAI1qB,KAAK8e,GAAS4L,UAAUD,GAEjCrgB,KACK,IAAIkf,GAAemB,GAErB,IAAIN,GAAaM,OAlCQ3H,ICvChC6H,GAAwB,WAKZC,KACd,OAAOD,GAAgB,WAsBTE,GACdtoB,EACAmJ,EAGAof,GAGA,gBANApf,MAMkB,mBAAPnJ,EACT,OAAOA,EAGT,IAEE,GAAIA,EAAG6f,WACL,OAAO7f,EAIT,GAAIA,EAAGsJ,mBACL,OAAOtJ,EAAGsJ,mBAEZ,MAAOrJ,GAIP,OAAOD,EAKT,IAAMwoB,cAAiC,WACrC,IAAMhhB,EAAO7K,MAAM7D,UAAU+F,MAAM7F,KAAKwU,WAExC,IACM+a,GAA4B,mBAAXA,GACnBA,EAAO3f,MAAMnL,KAAM+P,WAIrB,IAAMib,EAAmBjhB,EAAKhD,IAAI,SAACkkB,GAAa,OAAAJ,GAAKI,EAAKvf,KAE1D,OAAInJ,EAAGoJ,YAMEpJ,EAAGoJ,YAAYR,MAAMnL,KAAMgrB,GAM7BzoB,EAAG4I,MAAMnL,KAAMgrB,GACtB,MAAOjF,GAuBP,MA5FJ4E,IAAiB,EACjB/Z,WAAW,WACT+Z,IAAiB,IAsEfjO,GAAU,SAACpI,GACTA,EAAM4W,kBAAkB,SAACpiB,GACvB,IAAMwZ,OAAsBxZ,GAY5B,OAVI4C,EAAQjC,YACVF,EAAsB+Y,OAAgBvV,OAAWA,GACjDvD,GAAsB8Y,EAAgB5W,EAAQjC,YAGhD6Y,EAAe9M,aACV8M,EAAe9M,QAClBzF,UAAWhG,IAGNuY,IAGT7F,iBAAiBsJ,KAGbA,IAOV,IACE,IAAK,IAAMoF,KAAY5oB,EACjBnH,OAAOC,UAAUiE,eAAe/D,KAAKgH,EAAI4oB,KAC3CJ,cAAcI,GAAY5oB,EAAG4oB,IAGjC,MAAO7tB,IAETiF,EAAGlH,UAAYkH,EAAGlH,WAAa,GAC/B0vB,cAAc1vB,UAAYkH,EAAGlH,UAE7BD,OAAOgwB,eAAe7oB,EAAI,qBAAsB,CAC9CuB,YAAY,EACZb,MAAO8nB,gBAKT3vB,OAAOwI,iBAAiBmnB,cAAe,CACrC3I,WAAY,CACVte,YAAY,EACZb,OAAO,GAETY,oBAAqB,CACnBC,YAAY,EACZb,MAAOV,KAKX,IACqBnH,OAAOiwB,yBAAyBN,cAAe,QACnDO,cACblwB,OAAOgwB,eAAeL,cAAe,OAAQ,CAC3ChB,IAAA,WACE,OAAOxnB,EAAG7C,QAKhB,MAAOpC,IAET,OAAOytB,uBAmCOQ,GAAmB7f,GACjC,gBADiCA,MAC5BA,EAAQgO,QAIb,GAAKhO,EAAQiR,IAAb,CAKA,IAAM6O,EAAS/lB,SAASkI,cAAc,UACtC6d,EAAOC,OAAQ,EACfD,EAAOE,IAAM,IAAI7O,GAAInR,EAAQiR,KAAKgP,wBAAwBjgB,GAEtDA,EAAQkgB,SAEVJ,EAAOK,OAASngB,EAAQkgB,SAGzBnmB,SAASqI,MAAQrI,SAASuH,MAAMe,YAAYyd,QAb3CrhB,GAAOlG,MAAM,oDAJbkG,GAAOlG,MAAM,mDC7KjB,kBAqBE,WAAmByH,GAZZ1L,UAAe8rB,EAAe/tB,GAM7BiC,SAAoC,EAGpCA,SAAiD,EAIvDA,KAAK8e,MACHnP,SAAS,EACTM,sBAAsB,GACnBvE,GAiNT,OA3MSogB,sBAAP,WACErwB,MAAMswB,gBAAkB,GAEpB/rB,KAAK8e,GAASnP,UAChBxF,GAAOH,IAAI,oCACXhK,KAAKgsB,MAGHhsB,KAAK8e,GAAS7O,uBAChB9F,GAAOH,IAAI,iDACXhK,KAAKisB,OAKDH,eAAR,WAAA,WACM9rB,KAAKksB,KAIThc,GAA0B,CAExBhH,SAAU,SAACkH,GACT,IAAMnM,EAAQmM,EAAKnM,MACbkoB,EAAapQ,KACbqQ,EAAiBD,EAAWrR,eAAegR,GAC3CO,EAAsBpoB,IAA0C,IAAjCA,EAAMuI,uBAE3C,GAAK4f,IAAkBxB,OAAyByB,EAAhD,CAIA,IAAMzT,EAASuT,EAAW7S,YACpBxQ,EAAQjN,EAAYoI,GACtBxE,EAAK6sB,GAA4Blc,EAAKR,IAAKQ,EAAK3H,IAAK2H,EAAKP,KAAMO,EAAKN,QACrErQ,EAAK8sB,GACH5E,GAAsB1jB,OAAO8I,EAAW,CACtCqb,iBAAkBxP,GAAUA,EAAOiH,aAAauI,iBAChDJ,WAAW,IAEb5X,EAAK3H,IACL2H,EAAKP,KACLO,EAAKN,QAGXtG,GAAsBV,EAAO,CAC3BuX,SAAS,EACThc,KAAM,YAGR8nB,EAAWK,aAAa1jB,EAAO,CAC7BgR,kBAAmB7V,MAGvBI,KAAM,UAGRrE,KAAKksB,IAA2B,IAI1BJ,eAAR,WAAA,WACM9rB,KAAKysB,KAITvc,GAA0B,CAExBhH,SAAU,SAAC1G,GACT,IAAIyB,EAAQzB,EAGZ,IAGM,WAAYA,EACdyB,EAAQzB,EAAE2O,OAOH,WAAY3O,GAAK,WAAYA,EAAEiC,SACtCR,EAAQzB,EAAEiC,OAAO0M,QAEnB,MAAO7T,IAIT,IAAM6uB,EAAapQ,KACbqQ,EAAiBD,EAAWrR,eAAegR,GAC3CO,EAAsBpoB,IAA0C,IAAjCA,EAAMuI,uBAE3C,IAAK4f,GAAkBxB,MAAyByB,EAC9C,OAAO,EAGT,IAAMzT,EAASuT,EAAW7S,YACpBxQ,EAAQjN,EAAYoI,GACtBxE,EAAKitB,GAAiCzoB,GACtC0jB,GAAsB1jB,OAAO8I,EAAW,CACtCqb,iBAAkBxP,GAAUA,EAAOiH,aAAauI,iBAChDJ,WAAW,IAGjBlf,EAAM3K,MAAQpD,WAASU,MAEvB+N,GAAsBV,EAAO,CAC3BuX,SAAS,EACThc,KAAM,yBAGR8nB,EAAWK,aAAa1jB,EAAO,CAC7BgR,kBAAmB7V,KAKvBI,KAAM,uBAGRrE,KAAKysB,IAAwC,IAOvCX,eAAR,SAAoClc,EAAUnH,EAAUoH,EAAWC,GACjE,IAIIpQ,EADAH,EAAU7D,EAAakU,GAAOA,EAAIrQ,QAAUqQ,EAGhD,GAAIhU,EAAS2D,GAAU,CACrB,IAAMotB,EAASptB,EAAQsB,MAPF,4GAQjB8rB,IACFjtB,EAAOitB,EAAO,GACdptB,EAAUotB,EAAO,IAIrB,IAAM7jB,EAAQ,CACZxC,UAAW,CACTyC,OAAQ,CACN,CACE1E,KAAM3E,GAAQ,QACduD,MAAO1D,MAMf,OAAOS,KAAKusB,GAA8BzjB,EAAOL,EAAKoH,EAAMC,IAStDgc,eAAR,SAAyC3a,GACvC,MAAO,CACL7K,UAAW,CACTyC,OAAQ,CACN,CACE1E,KAAM,qBAENpB,MAAO,oDAAoDC,OAAOiO,QASpE2a,eAAR,SAAsChjB,EAAcL,EAAUoH,EAAWC,GACvEhH,EAAMxC,UAAYwC,EAAMxC,WAAa,GACrCwC,EAAMxC,UAAUyC,OAASD,EAAMxC,UAAUyC,QAAU,GACnDD,EAAMxC,UAAUyC,OAAO,GAAKD,EAAMxC,UAAUyC,OAAO,IAAM,GACzDD,EAAMxC,UAAUyC,OAAO,GAAGoc,WAAarc,EAAMxC,UAAUyC,OAAO,GAAGoc,YAAc,GAC/Erc,EAAMxC,UAAUyC,OAAO,GAAGoc,WAAWE,OAASvc,EAAMxC,UAAUyC,OAAO,GAAGoc,WAAWE,QAAU,GAE7F,IAAMkC,EAAQ5lB,MAAMC,SAASkO,EAAQ,UAAO/C,EAAY+C,EAClD4X,EAAS/lB,MAAMC,SAASiO,EAAM,UAAO9C,EAAY8C,EACjDyV,EAAW1pB,EAAS6M,IAAQA,EAAI1L,OAAS,EAAI0L,a3BdrD,IACE,OAAOhD,SAAS8J,SAASC,KACzB,MAAO0V,GACP,MAAO,I2BWkD0H,GAYzD,OAV2D,IAAvD9jB,EAAMxC,UAAUyC,OAAO,GAAGoc,WAAWE,OAAOtoB,QAC9C+L,EAAMxC,UAAUyC,OAAO,GAAGoc,WAAWE,OAAOnoB,KAAK,CAC/CqqB,QACAjC,WACAkC,SAAU,IACVC,QAAQ,EACRC,WAIG5e,GApOKgjB,KAAa,sBCtBvBe,GAAuB,CAC3B,cACA,SACA,OACA,mBACA,iBACA,oBACA,kBACA,cACA,aACA,qBACA,cACA,aACA,iBACA,eACA,kBACA,cACA,cACA,eACA,qBACA,SACA,YACA,eACA,gBACA,YACA,kBACA,SACA,iBACA,4BACA,sCAgCA,WAAmBnhB,GARZ1L,UAAe8sB,EAAS/uB,GAS7BiC,KAAK8e,MACH5S,gBAAgB,EAChB6gB,aAAa,EACbC,uBAAuB,EACvBpM,aAAa,EACbhQ,YAAY,GACTlF,GAkNT,OA1MSohB,sBAAP,WACE,IAAMvnB,EAASiC,KAEXxH,KAAK8e,GAASlO,YAChBrN,EAAKgC,EAAQ,aAAcvF,KAAKitB,GAAkB1hB,KAAKvL,OAGrDA,KAAK8e,GAAS8B,aAChBrd,EAAKgC,EAAQ,cAAevF,KAAKitB,GAAkB1hB,KAAKvL,OAGtDA,KAAK8e,GAASkO,uBAChBzpB,EAAKgC,EAAQ,wBAAyBvF,KAAKktB,GAAS3hB,KAAKvL,OAGvDA,KAAK8e,GAAS5S,gBAAkB,mBAAoB3G,GACtDhC,EAAK2I,eAAe7Q,UAAW,OAAQ2E,KAAKmtB,GAAS5hB,KAAKvL,OAGxDA,KAAK8e,GAASiO,eACI7tB,MAAM6D,QAAQ/C,KAAK8e,GAASiO,aAAe/sB,KAAK8e,GAASiO,YAAcF,IAC/ErrB,QAAQxB,KAAKotB,GAAiB7hB,KAAKvL,QAK3C8sB,eAAR,SAA0BppB,GAExB,OAAO,eAAoB,aAAAoG,mBAAAA,IAAAC,kBACzB,IAAMsjB,EAAmBtjB,EAAK,GAQ9B,OAPAA,EAAK,GAAK8gB,GAAKwC,EAAkB,CAC/B5jB,UAAW,CACT2G,KAAM,CAAEoX,SAAUllB,EAAgBoB,IAClC2c,SAAS,EACThc,KAAM,gBAGHX,EAASyH,MAAMnL,KAAM+J,KAMxB+iB,eAAR,SAAiBppB,GAEf,OAAO,SAAoBwF,GAEzB,OAAOxF,EAASnI,KACdyE,KACA6qB,GAAK3hB,EAAU,CACbO,UAAW,CACT2G,KAAM,CACJoX,SAAU,wBACVrX,QAAS7N,EAAgBoB,IAE3B2c,SAAS,EACThc,KAAM,mBAQRyoB,eAAR,SAAyBxoB,GAEvB,IAAMiB,EAASiC,IAETpI,EAAQmG,EAAOjB,IAAWiB,EAAOjB,GAAQjJ,UAG1C+D,GAAUA,EAAME,gBAAmBF,EAAME,eAAe,sBAI7DiE,EAAKnE,EAAO,mBAAoB,SAC9BsE,GAEA,OAAO,SAGL+H,EACAlJ,EACAmJ,GAEA,IACgC,mBAAnBnJ,EAAGoJ,cACZpJ,EAAGoJ,YAAckf,GAAKtoB,EAAGoJ,YAAYJ,KAAKhJ,GAAK,CAC7CkH,UAAW,CACT2G,KAAM,CACJoX,SAAU,cACVrX,QAAS7N,EAAgBC,GACzB+B,UAEF+b,SAAS,EACThc,KAAM,iBAIZ,MAAOH,IAIT,OAAOR,EAASnI,KACdyE,KACAyL,EAEAof,GAAMtoB,EAA+B,CACnCkH,UAAW,CACT2G,KAAM,CACJoX,SAAU,mBACVrX,QAAS7N,EAAgBC,GACzB+B,UAEF+b,SAAS,EACThc,KAAM,gBAGVqH,MAKNnI,EAAKnE,EAAO,sBAAuB,SACjCkuB,GAGA,OAAO,SAGL7hB,EACAlJ,EACAmJ,SAmBM6hB,EAAuBhrB,EAC7B,IACE,IAAMirB,YAAuBD,wBAAqB1hB,mBAC9C2hB,GACFF,EAA4B/xB,KAAKyE,KAAMyL,EAAW+hB,EAAsB9hB,GAE1E,MAAOlJ,IAGT,OAAO8qB,EAA4B/xB,KAAKyE,KAAMyL,EAAW8hB,EAAqB7hB,QAM5EohB,eAAR,SAAiBvf,GAEf,OAAO,eAA+B,aAAAzD,mBAAAA,IAAAC,kBAEpC,IAAMqC,EAAMpM,KA6BZ,MA5BkD,CAAC,SAAU,UAAW,aAAc,sBAElEwB,QAAQ,SAAAnC,GACtBA,KAAQ+M,GAA4B,mBAAdA,EAAI/M,IAE5BkE,EAAK6I,EAAK/M,EAAM,SAASqE,GACvB,IAAM+pB,EAAc,CAClBhkB,UAAW,CACT2G,KAAM,CACJoX,SAAUnoB,EACV8Q,QAAS7N,EAAgBoB,IAE3B2c,SAAS,EACThc,KAAM,eAUV,OALIX,EAASG,sBACX4pB,EAAYhkB,UAAU2G,KAAKD,QAAU7N,EAAgBoB,EAASG,sBAIzDgnB,GAAKnnB,EAAU+pB,OAKrBlgB,EAAapC,MAAMnL,KAAM+J,KAnOtB+iB,KAAa,8BCT3B,WAAmBphB,GARZ1L,UAAe0tB,EAAY3vB,GAShCiC,KAAK8e,MACH1V,SAAS,EACTukB,KAAK,EACLlgB,OAAO,EACPsB,SAAS,EACTwM,QAAQ,EACRnP,KAAK,GACFV,GA2PT,OApPSgiB,gCAAP,SAA2B5kB,GACpB9I,KAAK8e,GAASvD,QAGnBQ,KAAgB5B,cACd,CACEyO,SAAU,WAAyB,gBAAf9f,EAAMzE,KAAyB,cAAgB,SACnE2E,SAAUF,EAAME,SAChB7K,MAAO2K,EAAM3K,MACboB,QAASsJ,EAAoBC,IAE/B,CACEA,WAaC4kB,sBAAP,WAAA,WACM1tB,KAAK8e,GAAS1V,SAChB8G,GAA0B,CACxBhH,SAAU,eAAC,aAAAY,mBAAAA,IAAAC,kBACTtK,EAAKmuB,SAALnuB,IAA2BsK,KAE7B1F,KAAM,YAGNrE,KAAK8e,GAAS6O,KAChBzd,GAA0B,CACxBhH,SAAU,eAAC,aAAAY,mBAAAA,IAAAC,kBACTtK,EAAKouB,SAALpuB,IAAuBsK,KAEzB1F,KAAM,QAGNrE,KAAK8e,GAAS1S,KAChB8D,GAA0B,CACxBhH,SAAU,eAAC,aAAAY,mBAAAA,IAAAC,kBACTtK,EAAKquB,SAALruB,IAAuBsK,KAEzB1F,KAAM,QAGNrE,KAAK8e,GAASrR,OAChByC,GAA0B,CACxBhH,SAAU,eAAC,aAAAY,mBAAAA,IAAAC,kBACTtK,EAAKsuB,SAALtuB,IAAyBsK,KAE3B1F,KAAM,UAGNrE,KAAK8e,GAAS/P,SAChBmB,GAA0B,CACxBhH,SAAU,eAAC,aAAAY,mBAAAA,IAAAC,kBACTtK,EAAKuuB,SAALvuB,IAA2BsK,KAE7B1F,KAAM,aASJqpB,eAAR,SAA2Btf,GACzB,IAAMiI,EAAa,CACjBuS,SAAU,UACVxY,KAAM,CACJL,UAAW3B,EAAYrE,KACvBI,OAAQ,WAEVhM,MAAOpD,WAASkzB,WAAW7f,EAAYjQ,OACvCoB,QAASqD,EAASwL,EAAYrE,KAAM,MAGtC,GAA0B,WAAtBqE,EAAYjQ,MAAoB,CAClC,IAA4B,IAAxBiQ,EAAYrE,KAAK,GAKnB,OAJAsM,EAAW9W,QAAU,sBAAqBqD,EAASwL,EAAYrE,KAAK3I,MAAM,GAAI,MAAQ,kBACtFiV,EAAWjG,KAAKL,UAAY3B,EAAYrE,KAAK3I,MAAM,GAOvD2a,KAAgB5B,cAAc9D,EAAY,CACxCxT,MAAOuL,EAAYrE,KACnB5L,MAAOiQ,EAAYjQ,SAQfuvB,eAAR,SAAuBtf,GACrB,IAAI9J,EAGJ,IACEA,EAAS8J,EAAYtF,MAAMxE,OACvB9H,EAAiB4R,EAAYtF,MAAMxE,QACnC9H,EAAkB4R,EAAYtF,OAClC,MAAOtG,GACP8B,EAAS,YAGW,IAAlBA,EAAOvH,QAIXgf,KAAgB5B,cACd,CACEyO,SAAU,MAAMxa,EAAY1O,KAC5BH,QAAS+E,GAEX,CACEwE,MAAOsF,EAAYtF,MACnBpJ,KAAM0O,EAAY1O,QAShBguB,eAAR,SAAuBtf,GACrB,GAAIA,EAAYnB,aAAhB,CAEE,GAAImB,EAAYhC,IAAII,uBAClB,OAGI,IAAAlM,2BAAEgM,WAAQ7D,QAAKkE,gBAAaK,SAElC+O,KAAgB5B,cACd,CACEyO,SAAU,MACVxY,KAAM,CACJ9D,SACA7D,MACAkE,eAEFtI,KAAM,QAER,CACE+H,IAAKgC,EAAYhC,IACjBvJ,MAAOmK,WAYP0gB,eAAR,SAAyBtf,GAElBA,EAAYnB,eAIbmB,EAAYC,UAAU5F,IAAI5H,MAAM,eAAkD,SAAjCuN,EAAYC,UAAU/B,SAKvE8B,EAAYnK,MACd8X,KAAgB5B,cACd,CACEyO,SAAU,QACVxY,KAAMhC,EAAYC,UAClBlQ,MAAOpD,WAASU,MAChB4I,KAAM,QAER,CACE+L,KAAMhC,EAAYnK,MAClBpB,MAAOuL,EAAYrE,OAIvBgS,KAAgB5B,cACd,CACEyO,SAAU,QACVxY,YACKhC,EAAYC,YACf1B,YAAayB,EAAYI,SAAS5B,SAEpCvI,KAAM,QAER,CACExB,MAAOuL,EAAYrE,KACnByE,SAAUJ,EAAYI,cAUtBkf,eAAR,SAA2Btf,GACzB,IAAM7I,EAASiC,IACXzH,EAAOqO,EAAYrO,KACnBuP,EAAKlB,EAAYkB,GACf4e,EAAY1lB,EAASjD,EAAOgK,SAASC,MACvC2e,EAAa3lB,EAASzI,GACpBquB,EAAW5lB,EAAS8G,GAGrB6e,EAAW3tB,OACd2tB,EAAaD,GAKXA,EAAUltB,WAAaotB,EAASptB,UAAYktB,EAAU3tB,OAAS6tB,EAAS7tB,OAC1E+O,EAAK8e,EAASxlB,UAEZslB,EAAUltB,WAAamtB,EAAWntB,UAAYktB,EAAU3tB,OAAS4tB,EAAW5tB,OAC9ER,EAAOouB,EAAWvlB,UAGpBmT,KAAgB5B,cAAc,CAC5ByO,SAAU,aACVxY,KAAM,CACJrQ,OACAuP,SA5QQoe,KAAa,mBCxBvBW,GAAc,QACdC,GAAgB,gBA2BpB,WAAmB5iB,gBAAAA,MAfH1L,UAAeuuB,EAAaxwB,GAgB1CiC,KAAKwuB,GAAO9iB,EAAQhO,KAAO2wB,GAC3BruB,KAAK8S,EAASpH,EAAQ+iB,OAASH,GAuCnC,OAjCSC,sBAAP,WACEhX,GAAwB,SAACzO,EAAc4N,GACrC,IAAMjP,EAAOsU,KAAgBjB,eAAeyT,GAC5C,OAAI9mB,EACKA,EAAKinB,GAAS5lB,EAAO4N,GAEvB5N,KAOHylB,eAAR,SAAiBzlB,EAAc4N,GAC7B,KAAK5N,EAAMxC,WAAcwC,EAAMxC,UAAUyC,QAAW2N,GAASlb,EAAakb,EAAKoD,kBAAmBre,QAChG,OAAOqN,EAET,IAAM6lB,EAAe3uB,KAAK4uB,GAAelY,EAAKoD,kBAAoC9Z,KAAKwuB,IAEvF,OADA1lB,EAAMxC,UAAUyC,SAAa4lB,EAAiB7lB,EAAMxC,UAAUyC,QACvDD,GAMDylB,eAAR,SAAuBtqB,EAAsBvG,EAAayG,GACxD,gBADwDA,OACnD3I,EAAayI,EAAMvG,GAAMjC,QAAU0I,EAAMpH,OAAS,GAAKiD,KAAK8S,EAC/D,OAAO3O,EAET,IACMmC,EAAY0gB,GADClB,GAAkB7hB,EAAMvG,KAE3C,OAAOsC,KAAK4uB,GAAe3qB,EAAMvG,GAAMA,KAAM4I,GAAcnC,KA3D/CoqB,KAAa,oBCXvBhpB,GAASiC,kBAGf,aASSxH,UAAe6uB,EAAU9wB,GA8BlC,OAzBS8wB,sBAAP,WACEtX,GAAwB,SAACzO,aACvB,GAAIiT,KAAgBjB,eAAe+T,GAAY,CAE7C,IAAKtpB,GAAOupB,YAAcvpB,GAAOgK,WAAahK,GAAOE,SACnD,OAAOqD,EAIT,IAAML,aAAMK,EAAM1B,8BAASqB,iBAAOlD,GAAOgK,+BAAUC,MAC3Cuf,6BACA3W,+BAEFkI,qBACDxX,EAAM1B,8BAASkZ,SACdyO,GAAY,CAAEC,QAASD,IACvB3W,GAAa,CAAE6W,aAAc7W,IAE7BhR,SAAgBqB,GAAO,CAAEA,SAAQ6X,YAEvC,cAAYxX,IAAO1B,YAErB,OAAO0B,KAhCG+lB,KAAa,8HCXhBK,GAAW,2CCqBtB,WAAmBxjB,uBAAAA,MACjBlM,YAAMgrB,GAAgB9e,SAyD1B,OAhEmC7L,OAe1BsvB,6BAAP,SAAwBzjB,gBAAAA,MAELlE,IAA0B/B,WAKtCzF,KAAK+f,KAKVwL,UACK7f,IACHiR,IAAKjR,EAAQiR,KAAO3c,KAAKovB,YANzBjlB,GAAOlG,MAAM,iEAaPkrB,eAAV,SAAwBrmB,EAAcwL,EAAeoC,GAenD,OAdA5N,EAAMumB,SAAWvmB,EAAMumB,UAAY,aACnCvmB,EAAM6Y,WACD7Y,EAAM6Y,MACTjiB,KAAMwvB,GACNI,WACOxmB,EAAM6Y,KAAO7Y,EAAM6Y,IAAI2N,UAAa,IACzC,CACE5vB,KAAM,sBACNuZ,QD3DiB,YC8DrBA,QD9DqB,WCiEhBzZ,YAAM2iB,aAAcrZ,EAAOwL,EAAOoC,IAMjCyY,eAAV,SAAqBrmB,GACnB,IAAM+R,EAAc7a,KAAK8a,eAAe4S,IACpC7S,GACFA,EAAY0U,oBAAoBzmB,GAElCtJ,YAAMgjB,aAAW1Z,OA9DckW,ICPtBf,GAAsB,CACjC,IAAIuR,GACJ,IAAIC,GACJ,IAAI3C,GACJ,IAAIY,GACJ,IAAI5B,GACJ,IAAIyC,GACJ,IAAIM,ICPN,IAAIa,GAAqB,GAGnBC,GAAUnoB,IACZmoB,GAAQC,QAAUD,GAAQC,OAAOC,eACnCH,GAAqBC,GAAQC,OAAOC,cAGtC,ICdYC,GDcNC,YACDL,IACAM,IACAC,KCjBL,SAAYH,GAEVA,UAEAA,uCAEAA,oCAEAA,uCAEAA,uBAEAA,yCAEAA,qCAEAA,gCAEAA,4BAEAA,iCAEAA,+BAEAA,wBAEAA,iCAEAA,2CAEAA,oBAEAA,4BAEAA,uBAlCF,CAAYA,KAAAA,QAsCZ,SAAiBA,GAOCA,eAAhB,SAA6BI,GAC3B,GAAIA,EAAa,IACf,OAAOJ,EAAWtY,GAGpB,GAAI0Y,GAAc,KAAOA,EAAa,IACpC,OAAQA,GACN,KAAK,IACH,OAAOJ,EAAWK,gBACpB,KAAK,IACH,OAAOL,EAAWM,iBACpB,KAAK,IACH,OAAON,EAAWO,SACpB,KAAK,IACH,OAAOP,EAAWQ,cACpB,KAAK,IACH,OAAOR,EAAWS,mBACpB,KAAK,IACH,OAAOT,EAAWU,kBACpB,QACE,OAAOV,EAAWW,gBAIxB,GAAIP,GAAc,KAAOA,EAAa,IACpC,OAAQA,GACN,KAAK,IACH,OAAOJ,EAAWY,cACpB,KAAK,IACH,OAAOZ,EAAWa,YACpB,KAAK,IACH,OAAOb,EAAWc,iBACpB,QACE,OAAOd,EAAWe,cAIxB,OAAOf,EAAWgB,cA5CtB,CAAiBhB,KAAAA,QCrCV,IAAMiB,GAAqB,IAAIC,OACpC,sEAYcC,GAAkBvlB,GAChC,MAAO,qBAAsBA,GAAW,kBAAmBA,WA6B7CwlB,GAA4CvV,WAC1D,oBAD0DA,EAAWI,0BAC9DJ,wBAAKvC,iCAAY+X,0BAOVC,GAAQC,GACtB,OAAOA,EAAO,ICjChB,SAASC,KACP,IAAMC,EAAoBL,KACtBK,IACFpnB,GAAOH,IAAI,0BAA0B8lB,GAAWe,0CAChDU,EAAkBC,UAAU1B,GAAWe,gBCd3C,kBAKE,WAAmBY,gBAAAA,OAJZzxB,WAAgB,GAKrBA,KAAK0xB,GAAUD,EAgBnB,OAPSE,gBAAP,SAAW/b,GACL5V,KAAKgW,MAAMjZ,OAASiD,KAAK0xB,GAC3B9b,EAAKG,kBAAehJ,EAEpB/M,KAAKgW,MAAM9Y,KAAK0Y,uBAkFpB,WAAmBgc,GACjB,GAvEK5xB,aAAkB0H,IAKlB1H,YAAiB0H,IAAQmqB,UAAU,IAoBnC7xB,oBAAyBiU,KAoBzBjU,UAAqC,GAMrCA,UAA+B,IAoB/B4xB,EACH,OAAO5xB,KAEL4xB,EAAYE,UACd9xB,KAAK8xB,QAAUF,EAAYE,SAEzBF,EAAYG,SACd/xB,KAAK+xB,OAASH,EAAYG,QAExBH,EAAYI,eACdhyB,KAAKgyB,aAAeJ,EAAYI,cAG9B,YAAaJ,IACf5xB,KAAKiyB,QAAUL,EAAYK,SAEzBL,EAAYM,KACdlyB,KAAKkyB,GAAKN,EAAYM,IAEpBN,EAAYO,cACdnyB,KAAKmyB,YAAcP,EAAYO,aAE7BP,EAAYxhB,OACdpQ,KAAKoQ,KAAOwhB,EAAYxhB,MAEtBwhB,EAAYtc,OACdtV,KAAKsV,KAAOsc,EAAYtc,MAEtBsc,EAAYhlB,SACd5M,KAAK4M,OAASglB,EAAYhlB,QAExBglB,EAAYxkB,iBACdpN,KAAKoN,eAAiBwkB,EAAYxkB,gBAEhCwkB,EAAY3kB,eACdjN,KAAKiN,aAAe2kB,EAAY3kB,cA0JtC,OAlJSmlB,kBAAP,SACER,GAEA,OAAO5xB,KAAKqyB,WAAWT,IAMlBQ,uBAAP,SACER,GAEA,IAAMU,EAAY,IAAIF,SACjBR,IACHI,aAAchyB,KAAK+xB,OACnBE,QAASjyB,KAAKiyB,QACdH,QAAS9xB,KAAK8xB,WAUhB,OAPAQ,EAAUvc,aAAe/V,KAAK+V,aAC1Buc,EAAUvc,cACZuc,EAAUvc,aAAa7T,IAAIowB,GAG7BA,EAAUxc,YAAc9V,KAAK8V,YAEtBwc,GAMFF,mBAAP,SAAc10B,EAAauF,SAEzB,OADAjD,KAAKsV,YAAYtV,KAAKsV,cAAO5X,GAAMuF,MAC5BjD,MAOFoyB,oBAAP,SAAe10B,EAAauF,SAE1B,OADAjD,KAAKoQ,YAAYpQ,KAAKoQ,cAAO1S,GAAMuF,MAC5BjD,MAMFoyB,sBAAP,SAAiBnvB,GAEf,OADAjD,KAAK4M,OAAS3J,EACPjD,MAMFoyB,0BAAP,SAAqBlC,GACnBlwB,KAAKwa,OAAO,mBAAoBtX,OAAOgtB,IACvC,IAAMqC,EAAazC,GAAWrH,aAAayH,GAI3C,OAHIqC,IAAezC,GAAWgB,cAC5B9wB,KAAKwxB,UAAUe,GAEVvyB,MAMFoyB,sBAAP,WACE,OAAOpyB,KAAK4M,SAAWkjB,GAAWtY,IAM7B4a,mBAAP,SAAcnlB,GACZjN,KAAKiN,aAAuC,iBAAjBA,EAA4BA,EAAegH,MAMjEme,0BAAP,WACE,IAAII,EAAgB,GAIpB,YAHqBzlB,IAAjB/M,KAAKiyB,UACPO,EAAgBxyB,KAAKiyB,QAAU,KAAO,MAE9BjyB,KAAK8xB,YAAW9xB,KAAK+xB,OAASS,GAMnCJ,4BAAP,WAWE,OAAOzrB,EAAkB,CACvByJ,KAAMhV,OAAOoL,KAAKxG,KAAKoQ,MAAMrT,OAAS,EAAIiD,KAAKoQ,UAAOrD,EACtDolB,YAAanyB,KAAKmyB,YAClBD,GAAIlyB,KAAKkyB,GACTO,eAAgBzyB,KAAKgyB,aACrBU,QAAS1yB,KAAK+xB,OACdnlB,OAAQ5M,KAAK4M,OACb0I,KAAMla,OAAOoL,KAAKxG,KAAKsV,MAAMvY,OAAS,EAAIiD,KAAKsV,UAAOvI,EACtD4lB,SAAU3yB,KAAK8xB,WAOZM,mBAAP,WAaE,OAAOzrB,EAAkB,CACvByJ,KAAMhV,OAAOoL,KAAKxG,KAAKoQ,MAAMrT,OAAS,EAAIiD,KAAKoQ,UAAOrD,EACtDolB,YAAanyB,KAAKmyB,YAClBD,GAAIlyB,KAAKkyB,GACTO,eAAgBzyB,KAAKgyB,aACrBU,QAAS1yB,KAAK+xB,OACda,gBAAiB5yB,KAAKoN,eACtBR,OAAQ5M,KAAK4M,OACb0I,KAAMla,OAAOoL,KAAKxG,KAAKsV,MAAMvY,OAAS,EAAIiD,KAAKsV,UAAOvI,EACtDyJ,UAAWxW,KAAKiN,aAChB0lB,SAAU3yB,KAAK8xB,+BCnRnB,WAAmBe,EAAwClX,GAA3D,MACEnc,YAAMqzB,gBAjBApzB,KAA8B,GAKrBA,KAAasc,KAcxBvgB,EAAamgB,EAAK3C,MACpBvZ,EAAKqzB,GAAOnX,GAGdlc,EAAKC,KAAOmzB,EAAmBnzB,KAAOmzB,EAAmBnzB,KAAO,GAEhED,EAAKszB,GAAWF,EAAmBG,QAGnCvzB,EAAKqW,YAAcrW,IAoFvB,OAlHiCI,OAoCxBozB,oBAAP,SAAevzB,GACbM,KAAKN,KAAOA,GAOPuzB,6BAAP,SAAwBxB,gBAAAA,OACjBzxB,KAAK+V,eACR/V,KAAK+V,aAAe,IAAI4b,GAAaF,IAEvCzxB,KAAK+V,aAAa7T,IAAIlC,OAOjBizB,4BAAP,SAAuBC,GACrBlzB,KAAKmzB,QAAqBD,IAMrBD,mBAAP,SAAchmB,GAAd,WAEE,QAA0BF,IAAtB/M,KAAKiN,aAAT,CAYA,GARKjN,KAAKN,OACRyK,GAAOF,KAAK,uEACZjK,KAAKN,KAAO,2BAIdF,YAAM4zB,iBAAOnmB,IAEQ,IAAjBjN,KAAKiyB,QAAT,CAMA,IAAMoB,EAAgBrzB,KAAK+V,aAAe/V,KAAK+V,aAAaC,MAAMsd,OAAO,SAAAC,GAAK,OAAAA,IAAM9zB,GAAQ8zB,EAAEtmB,eAAgB,GAE1GjN,KAAK+yB,IAAYM,EAAct2B,OAAS,IAC1CiD,KAAKiN,aAAeomB,EAAcG,OAAO,SAACC,EAAiBC,GACzD,OAAID,EAAKxmB,cAAgBymB,EAAQzmB,aACxBwmB,EAAKxmB,aAAeymB,EAAQzmB,aAAewmB,EAAOC,EAEpDD,IACNxmB,cAGL,IAAM6I,EAAqB,CACzBM,SAAU,CACRO,MAAO3W,KAAK4W,mBAEdZ,MAAOqd,EACPT,gBAAiB5yB,KAAKoN,eACtBkI,KAAMtV,KAAKsV,KACXkB,UAAWxW,KAAKiN,aAChB6I,YAAa9V,KAAKN,KAClB2E,KAAM,eAUR,OAPwBjJ,OAAOoL,KAAKxG,KAAKmzB,IAAep2B,OAAS,IAG/DoN,GAAOH,IAAI,oDAAqDnF,KAAKC,UAAU9E,KAAKmzB,QAAepmB,EAAW,IAC9G+I,EAAYod,aAAelzB,KAAKmzB,IAG3BnzB,KAAK8yB,GAAKtG,aAAa1W,GAlC5B3L,GAAOH,IAAI,yFA9EgB2pB,ICCpBC,GAAuB,mBAMlC,WACmBC,EACAC,EACVC,EACPtC,gBADOsC,MAHT,MAMEv0B,YAAMiyB,gBALWhyB,KAAAo0B,EACAp0B,KAAAq0B,EACVr0B,oBAAAs0B,IA2BX,OA/BiDl0B,OAaxCm0B,gBAAP,SAAWpe,GAAX,WAGMA,EAAKmc,SAAW/xB,KAAK+zB,oBAEvBne,EAAKwd,OAAS,SAACnmB,GACb2I,EAAK3I,aAAuC,iBAAjBA,EAA4BA,EAAegH,KACtExU,EAAKq0B,GAAale,EAAKmc,cAIChlB,IAAtB6I,EAAK3I,cACPjN,KAAK6zB,GAAcje,EAAKmc,SAI5BvyB,YAAM0C,cAAI0T,OA7BmC+b,mBA+D/C,WACEkB,EACiBoB,EAEAC,EAEAC,gBAFAD,mBAEAC,MANnB,MAQE30B,YAAMqzB,EAAoBoB,gBANTx0B,KAAAw0B,EAEAx0B,KAAAy0B,EAEAz0B,KAAA00B,EA3BZ10B,aAAsC,GAGrCA,KAA0B,EAM1BA,KAA4B,EAG5BA,MAAqB,EAEZA,KAAiD,GAiB5Dw0B,GAAYE,IAEdC,GAAuBH,GAIvB9pB,GAAOH,IAAI,+CAA+CvK,EAAKsyB,QAC/DkC,EAASI,eAAe,SAAA/f,GAAS,OAAAA,EAAMggB,QAAQ70B,QAiLrD,OAzNqCI,OA6C5B00B,mBAAP,SAActnB,kBAIZ,gBAJYA,EAAuBgH,MACnCjU,KAAKw0B,IAAY,EACjBx0B,KAAKy0B,WAAa,GAEdz0B,KAAK+V,aAAc,CACrB5L,GAAOH,IAAI,sCAAuC,IAAIkD,KAAoB,IAAfD,GAAqBuL,cAAexY,KAAKkyB,QAEpG,IAAuB,IAAAhxB,EAAA4F,EAAA9G,KAAK00B,kCAAwB,EAClDxrB,WAASlJ,KAAMiN,qGAGjBjN,KAAK+V,aAAaC,MAAQhW,KAAK+V,aAAaC,MAAMsd,OAAO,SAAC1d,GAExD,GAAIA,EAAKmc,SAAWtyB,EAAKsyB,OACvB,OAAO,EAIJnc,EAAK3I,eACR2I,EAAK3I,aAAeA,EACpB2I,EAAK4b,UAAU1B,GAAW6E,WAC1BxqB,GAAOH,IAAI,0DAA2DnF,KAAKC,UAAU8Q,OAAM7I,EAAW,KAGxG,IAAM6nB,EAAWhf,EAAKxI,eAAiBH,EAOvC,OANK2nB,GACHzqB,GAAOH,IACL,6EACAnF,KAAKC,UAAU8Q,OAAM7I,EAAW,IAG7B6nB,IAIL50B,KAAKm0B,IACPC,GAAuBp0B,KAAKi0B,IAG9B9pB,GAAOH,IAAI,2CAEXG,GAAOH,IAAI,uCAGb,OAAOxK,YAAM4zB,iBAAOnmB,IAUfsnB,yCAAP,SAAoCrrB,GAClClJ,KAAK00B,GAAuBx3B,KAAKgM,IAM5BqrB,6BAAP,SAAwB9C,GAAxB,WACE,IAAKzxB,KAAK+V,aAAc,CACtB/V,KAAK60B,GAAejkB,WAAW,WACxBnR,EAAK+0B,IACR/0B,EAAK2zB,UAENpzB,KAAKk0B,IAeRl0B,KAAK+V,aAAe,IAAIie,GAbH,SAACj2B,GAChB0B,EAAK+0B,IAGT/0B,EAAKo0B,GAAc91B,IAED,SAACA,GACf0B,EAAK+0B,IAGT/0B,EAAKq0B,GAAa/1B,IAG2DiC,KAAK+xB,OAAQN,GAG5FtnB,GAAOH,IAAI,sBACXhK,KAAK80B,KAEP90B,KAAK+V,aAAa7T,IAAIlC,OAOhBu0B,eAAR,SAAsBxC,GAChB/xB,KAAK60B,KACPlkB,aAAa3Q,KAAK60B,IAClB70B,KAAK60B,QAAe9nB,GAEtB5C,GAAOH,IAAI,2BAA2B+nB,GACtC/xB,KAAKy0B,WAAW1C,IAAU,EAC1B5nB,GAAOH,IAAI,iCAAkC5O,OAAOoL,KAAKxG,KAAKy0B,YAAY13B,SAOpEw3B,eAAR,SAAqBxC,GAArB,WAQE,GAPI/xB,KAAKy0B,WAAW1C,KAClB5nB,GAAOH,IAAI,yBAAyB+nB,UAE7B/xB,KAAKy0B,WAAW1C,GACvB5nB,GAAOH,IAAI,iCAAkC5O,OAAOoL,KAAKxG,KAAKy0B,YAAY13B,SAGhC,IAAxC3B,OAAOoL,KAAKxG,KAAKy0B,YAAY13B,OAAc,CAC7C,IAAMqW,EAAUpT,KAAKk0B,GAGfa,EAAM9gB,KAAoBb,EAAU,IAE1CxC,WAAW,WACJnR,EAAK+0B,IACR/0B,EAAK2zB,OAAO2B,IAEb3hB,KAQCmhB,eAAR,WAGE,GAFA5jB,aAAa3Q,KAAKg1B,KAEdh1B,KAAKw0B,GAAT,CAIA,IAAMhuB,EAAOpL,OAAOoL,KAAKxG,KAAKy0B,YACxBQ,EAAkBzuB,EAAKzJ,OAASyJ,EAAKgtB,OAAO,SAACC,EAAcC,GAAoB,OAAAD,EAAOC,IAAW,GAEnGuB,IAAoBj1B,KAAKk1B,GAC3Bl1B,KAAKm1B,IAAqB,EAE1Bn1B,KAAKm1B,GAAoB,EAG3Bn1B,KAAKk1B,GAAuBD,EAExBj1B,KAAKm1B,IAAqB,GAC5BhrB,GAAOH,IAAI,yEACXhK,KAAKwxB,UAAU1B,GAAWc,kBAC1B5wB,KAAKwa,OAAO,YAAa,UACzBxa,KAAKozB,UAELpzB,KAAK80B,OAODP,eAAR,WAAA,WACEpqB,GAAOH,IAAI,yCAAyChK,KAAKm1B,IACzDn1B,KAAKg1B,GAAmBpkB,WAAW,WACjCnR,EAAK21B,MACJ,SAvN8BnC,IA8NrC,SAASmB,GAAuBzY,GAC9B,GAAIA,EAAK,CACP,IAAMrH,EAAQqH,EAAIvC,WAClB,GAAI9E,EACkBA,EAAM6c,kBAExB7c,EAAMggB,aAAQvnB,ICxQtB,SAASsoB,KACP,IAAM/gB,EAAQtU,KAAKoZ,WACnB,GAAI9E,EAAO,CACT,IAAMsB,EAAOtB,EAAMuB,UACnB,GAAID,EACF,MAAO,CACL0f,eAAgB1f,EAAK2f,iBAI3B,MAAO,GAeT,SAASC,GAA8B7Z,EAAU7F,EAAgB2f,SAkB3DxT,EAjBErJ,EAAS+C,EAAIrC,YACb5N,EAAWkN,GAAUA,EAAOiH,cAAiB,GAGnD,OAAKjH,GAAWqY,GAAkBvlB,QAMNqB,IAAxB+I,EAAYmc,SACdnc,EAAYR,YAAYQ,EAAYR,OAAMogB,wBAAyBz6B,EAA0B06B,WACtF7f,IAM4B,mBAA1BpK,EAAQkqB,eACjB3T,EAAavW,EAAQkqB,cAAcH,GAEnC3f,EAAYR,YACPQ,EAAYR,OACfogB,wBAAyBz6B,EAA0B46B,QAEnDC,oBAAqB5yB,OAAOsd,OAAOyB,YAEMlV,IAAlC0oB,EAAgBM,eACzB9T,EAAawT,EAAgBM,cAC7BjgB,EAAYR,YAAYQ,EAAYR,OAAMogB,wBAAyBz6B,EAA0B+6B,gBAE7F/T,EAAavW,EAAQuqB,iBAErBngB,EAAYR,YACPQ,EAAYR,OACfogB,wBAAyBz6B,EAA0Bi7B,KAEnDJ,oBAAqB5yB,OAAOsd,OAAOyB,OA8FzC,SAA2B6B,GAGzB,GAAIniB,MAAMmiB,IAAkC,iBAATA,GAAqC,kBAATA,EAM7D,OALA3Z,GAAOF,KACL,0GAA0GpF,KAAKC,UAC7Ggf,eACWjf,KAAKC,iBAAiBgf,SAE9B,EAIT,GAAIA,EAAO,GAAKA,EAAO,EAErB,OADA3Z,GAAOF,KAAK,oFAAoF6Z,QACzF,EAET,OAAO,EAzGFqS,CAAkBlU,GAOlBA,GAcLnM,EAAYmc,QAAU3pB,KAAKC,SAAY0Z,EAGlCnM,EAAYmc,SAWjBnc,EAAYsgB,2BAAiB1qB,EAAQ2qB,yBAAcC,UAEnDnsB,GAAOH,IAAI,sBAAsB8L,EAAYoc,qBAAoBpc,EAAYpW,MACtEoW,IAbL3L,GAAOH,IACL,oGAAoGwW,OAClGyB,QAGGnM,KAtBP3L,GAAOH,IACL,6CACmC,mBAA1B0B,EAAQkqB,cACX,oCACA,+EAGR9f,EAAYmc,SAAU,EACfnc,IAfP3L,GAAOF,KAAK,oEACZ6L,EAAYmc,SAAU,EACfnc,KAzCPA,EAAYmc,SAAU,EACfnc,GAkFX,SAASygB,GAA0B1D,GAEzB,IACF2D,EAA0C,CAAE3D,qBAAoBkD,+BAEtE,GAAI/uB,IAAa,CACf,IAAMsV,EAASH,KAEf,GAAIG,EAAQ,CAOV,IAEMma,EAFiBvvB,EAAewM,OAAQ,QAEXgjB,gBAE7BtvB,EAAUkV,EAAOqa,QAAQC,KAAK,SAAAC,GAAU,OAAAr7B,EAAaq7B,EAAQJ,KAC/DrvB,IACFovB,EAAuBpvB,iB3ClH7Bsc,EACAld,GAGA,gBAHAA,MAGKQ,IACH,MAAM,IAAIvL,MAAM,6DAGlB,IAAMq7B,EAAsC,GAKtCxW,EAAWoD,EAAIpD,SAAWoD,EAAItG,QAAU,GAMxC9Q,EAASoX,EAAIpX,OAKb/L,EAAOmjB,EAAIqT,UAAYrT,EAAInjB,MAAQ+f,EAAQ/f,MAAQ,YAInDS,EACa,UAAjB0iB,EAAI1iB,UAAwB0iB,EAAIsT,SAAYtT,EAAIuT,QAAU,IAAgCC,UACtF,QACA,OAIAC,EAAezT,EAAIyT,aAAezT,EAAIjb,KAAO,GAE7C2uB,EAAiBp2B,QAAcT,EAAO42B,EA4C5C,OA1CA3wB,EAAKhF,QAAQ,SAAA9D,GACX,OAAQA,GACN,IAAK,UACHo5B,EAAYxW,QAAUA,EACtB,MACF,IAAK,SACHwW,EAAYxqB,OAASA,EACrB,MACF,IAAK,MACHwqB,EAAYruB,IAAM2uB,EAClB,MACF,IAAK,UAKHN,EAAYO,QAAU3T,EAAI2T,SAAWnwB,EAAewM,OAAQ,UAAUtN,MAAMka,EAAQgX,QAAU,IAC9F,MACF,IAAK,eAKHR,EAAYS,aAAerwB,EAAewM,OAAQ,OAAOtN,MAAM+wB,GAAe,IAAI,GAAOzuB,MACzF,MACF,IAAK,OACH,GAAe,QAAX4D,GAA+B,SAAXA,EACtB,WAIeS,IAAb2W,EAAI1W,OACN8pB,EAAY1mB,KAAOxU,EAAS8nB,EAAI1W,MAAQ0W,EAAI1W,KAAOnI,KAAKC,UAAUM,EAAUse,EAAI1W,QAElF,MACF,SACM,IAAG1N,eAAe/D,KAAKmoB,EAAKhmB,KAC9Bo5B,EAAYp5B,GAAQgmB,EAA+BhmB,OAKpDo5B,E2CiCgCU,CAAuBpwB,SAMzD,CAEH,IAAMqwB,EAAejwB,IAEjB,aAAciwB,IAEhBjB,EAAuBjnB,cAAiBkoB,EAAqBloB,WAIjE,OAAOinB,EAyCT,SAASkB,GAEP7E,EACA7X,GAGA,OAAOwa,GAAOx1B,KADM,IAAIizB,GAAYJ,EAAoB7yB,aAEnDu2B,GAA0B1D,IAC1B7X,aAoCS2c,SAfRjc,GAAAA,EAAUF,MACJtR,aACVwR,EAAQxR,WAAWuR,WAAaC,EAAQxR,WAAWuR,YAAc,GAC5DC,EAAQxR,WAAWuR,WAAWmc,mBACjClc,EAAQxR,WAAWuR,WAAWmc,iBAAmBF,IAE9Chc,EAAQxR,WAAWuR,WAAW4Z,eACjC3Z,EAAQxR,WAAWuR,WAAW4Z,aAAeA,KJ5OjDnlB,GAA0B,CACxBhH,SAAUooB,GACVjtB,KAAM,UAER6L,GAA0B,CACxBhH,SAAUooB,GACVjtB,KAAM,uBKTV,IAAMkB,GAASiC,ICYR,ICAHqwB,GCFAC,GFESC,GAAe,SAC1B7uB,EACA8uB,EACAC,EACAC,GAEA,IAAIC,EACJ,OAAO,WACDF,GAAMD,EAAOI,SACfH,EAAGI,aAEDL,EAAO/0B,OAAS,IACdi1B,GAAqBF,EAAOI,SAAwC,WAA7B3yB,SAAS6yB,mBAClDN,EAAOO,MAAQP,EAAO/0B,OAASk1B,GAAa,IAMxCH,EAAOO,OAASP,EAAOI,cAAyBrrB,IAAdorB,KACpCjvB,EAAS8uB,GACTG,EAAYH,EAAO/0B,UGpBhBu1B,GAAa,SAAC94B,EAAsBuD,GAC/C,oBAD+CA,GAAS,GACjD,CACLvD,OACAuD,QACAs1B,MAAO,EACPE,QAAS,GACT16B,GCHQmP,KAAKC,WAAS7E,KAAKowB,MAAMpwB,KAAKC,UAAY,KAAO,IAAM,MDI/D6vB,SAAS,IEEAO,GAAU,SAACt0B,EAAc6E,GACpC,IACE,GAAI0vB,oBAAoBC,oBAAoBC,SAASz0B,GAAO,CAC1D,IAAM4zB,EAA0B,IAAIW,oBAAoB,SAAAG,GAAK,OAAAA,EAAEC,aAAajyB,IAAImC,KAGhF,OADA+uB,EAAGU,QAAQ,CAAEt0B,OAAM40B,UAAU,IACtBhB,GAET,MAAOz1B,MCfP02B,IAAc,EACdC,IAAiB,EAEfC,GAAa,SAACtwB,GAClBowB,IAAepwB,EAAMuwB,WAYVC,GAAW,SAACC,EAAsBC,gBAAAA,MACxCL,KATL9tB,iBAAiB,WAAY+tB,IAK7B/tB,iBAAiB,eAAgB,cAM/B8tB,IAAiB,GAGnB9tB,iBACE,mBACA,SAAC/K,OAAEm5B,cACgC,WAA7Bh0B,SAAS6yB,iBACXiB,EAAG,CAAEE,YAAWP,kBAGpB,CAAEQ,SAAS,EAAMF,UL1BRG,GAAiB,WAY5B,YAXwB5sB,IAApB8qB,KAKFA,GAA+C,WAA7BpyB,SAAS6yB,gBAA+B,EAAI1yB,EAAAA,EAG9D0zB,GAAS,SAACh5B,OAAEm5B,cAAgB,OAAC5B,GAAkB4B,IAAY,IAGtD,CACLA,gBACE,OAAO5B,MMdA+B,GAAS,SAACC,EAAyBC,gBAAAA,MAC9C,IAGIC,EAHE/B,EAASQ,GAAW,OACpBwB,EAAcL,KAIdM,EAAe,SAACC,GAGpB,IAAMj3B,EAAQi3B,EAAMC,UAIhBl3B,EAAQ+2B,EAAYP,WACtBzB,EAAO/0B,MAAQA,EACf+0B,EAAOS,QAAQv7B,KAAKg9B,IAEpBlC,EAAOI,SAAU,EAGnB2B,KAGI9B,EAAKU,GAAQ,2BAA4BsB,GAE/C,GAAIhC,EAAI,CACN8B,EAAShC,GAAa8B,EAAU7B,EAAQC,EAAI6B,GAE5C,IAAMM,EAAU,WACTpC,EAAOI,UACVH,EAAGoC,cAActzB,IAAIkzB,GACrBjC,EAAOI,SAAU,EACjB2B,OLrCDjC,KACHA,GAAe,IAAInO,QAAQ,SAAAthB,GACzB,MAAO,CAAC,SAAU,UAAW,eAAetB,IAAI,SAAA1C,GAC9CgH,iBAAiBhH,EAAMgE,EAAG,CACxBmxB,MAAM,EACNc,SAAS,EACTZ,SAAS,SAKV5B,IK8BYz7B,KAAK+9B,GACtBd,GAASc,GAAS,KCxChB70B,GAASiC,IA8BF+yB,GAAU,SAACV,GACtB,IA7BiB3wB,EA6BX8uB,EAASQ,GAAW,QA7BTtvB,EA+BP,WACR,IAEE,IAAMsxB,EACJj1B,GAAOoO,YAAY8mB,iBAAiB,cAAc,IAzBV,WAG9C,IAAMtmB,EAAS5O,GAAOoO,YAAYQ,OAE5BqmB,EAAsD,CAC1DE,UAAW,aACXP,UAAW,GAGb,IAAK,IAAMz8B,KAAOyW,EACJ,oBAARzW,GAAqC,WAARA,IAC/B88B,EAAgB98B,GAAO4K,KAAK5F,IAAKyR,EAAOzW,GAA6CyW,EAAOC,gBAAiB,IAGjH,OAAOomB,EAUuDG,GAE1D3C,EAAO/0B,MAAQ+0B,EAAOO,MAASiC,EAAgDI,cAE/E5C,EAAOS,QAAU,CAAC+B,GAElBX,EAAS7B,GACT,MAAO/zB,MAzCiB,aAAxBwB,SAASiH,WAEXkE,WAAW1H,EAAU,GAGrBmC,iBAAiB,WAAYnC,ICd3B3D,GAASiC,kBAQb,aAJQxH,QAA8B,GAE9BA,QAA6B,EAG/BuF,IAAUA,GAAOoO,cACfpO,GAAOoO,YAAYknB,MACrBt1B,GAAOoO,YAAYknB,KAAK,uBAG1B76B,KAAK86B,KACL96B,KAAK+6B,KACL/6B,KAAKg7B,KACLh7B,KAAKi7B,MAwPX,OAnPSC,kCAAP,SAA6BplB,GAA7B,WACE,GAAKvQ,IAAWA,GAAOoO,aAAgBpO,GAAOoO,YAAYqlB,YAAe9kB,GAAzE,CAKA/J,GAAOH,IAAI,4DAEX,IACImxB,EAeAC,EACAC,EAjBExnB,EAAaud,GAAQld,IAG3B,GAAI3O,GAAOE,SAET,IAAK,IAAI7H,EAAI,EAAGA,EAAI6H,SAAS61B,QAAQv+B,OAAQa,IAI3C,GAA0C,SAAtC6H,SAAS61B,QAAQ19B,GAAG29B,QAAQrB,MAAkB,CAChDiB,EAAiB11B,SAAS61B,QAAQ19B,GAAG8tB,IACrC,MA+EN,GAvEAnmB,GAAOoO,YACJqlB,aACA53B,MAAMpB,KAAKw7B,IACXh6B,QAAQ,SAAC04B,GACR,IAAMC,EAAY/I,GAAQ8I,EAAMC,WAC1BliB,EAAWmZ,GAAQ8I,EAAMjiB,UAE/B,KAAuB,eAAnBnC,EAAYoc,IAAuBre,EAAasmB,EAAYrkB,EAAY1I,gBAI5E,OAAQ8sB,EAAMQ,WACZ,IAAK,cA+Mf,SAA4B5kB,EAA0BokB,EAA4BrmB,GAChF4nB,GAA+B3lB,EAAaokB,EAAO,cAAermB,GAClE4nB,GAA+B3lB,EAAaokB,EAAO,WAAYrmB,GAC/D4nB,GAA+B3lB,EAAaokB,EAAO,wBAAyBrmB,GAC5E4nB,GAA+B3lB,EAAaokB,EAAO,YAAarmB,GAChE4nB,GAA+B3lB,EAAaokB,EAAO,UAAWrmB,GAC9D4nB,GAA+B3lB,EAAaokB,EAAO,mBAAoBrmB,EAAY,cACnF4nB,GAA+B3lB,EAAaokB,EAAO,QAASrmB,EAAY,qBACxE4nB,GAA+B3lB,EAAaokB,EAAO,eAAgBrmB,GA8FrE,SAAoBiC,EAA0BokB,EAA4BrmB,GACxE6nB,GAAY5lB,EAAa,CACvBoc,GAAI,UACJC,YAAa,UACb/kB,eAAgByG,EAAaud,GAAQ8I,EAAMyB,cAC3C1uB,aAAc4G,EAAaud,GAAQ8I,EAAM0B,eAG3CF,GAAY5lB,EAAa,CACvBoc,GAAI,UACJC,YAAa,WACb/kB,eAAgByG,EAAaud,GAAQ8I,EAAMU,eAC3C3tB,aAAc4G,EAAaud,GAAQ8I,EAAM0B,eAzG3CC,CAAW/lB,EAAaokB,EAAOrmB,GAvNrBioB,CAAmBhmB,EAAaokB,EAAOrmB,GACvC,MACF,IAAK,OACL,IAAK,QACL,IAAK,UACH,IAAMzG,EAsNlB,SACE0I,EACAokB,EACAC,EACAliB,EACApE,GAEA,IAAMkoB,EAAwBloB,EAAasmB,EACrC6B,EAAsBD,EAAwB9jB,EASpD,OAPAyjB,GAAY5lB,EAAa,CACvBqc,YAAa+H,EAAMx6B,KACnBuN,aAAc+uB,EACd9J,GAAIgI,EAAMQ,UACVttB,eAAgB2uB,IAGXA,EAvO0BE,CAAgBnmB,EAAaokB,EAAOC,EAAWliB,EAAUpE,QAC/C9G,IAA7BsuB,GAAyD,wBAAfnB,EAAMx6B,OAClD27B,EAA2BjuB,GAK7B,IAAM4sB,EAAcL,KAEduC,EAAehC,EAAMC,UAAYH,EAAYP,UAEhC,gBAAfS,EAAMx6B,MAA0Bw8B,IAClC/xB,GAAOH,IAAI,4BACXvK,EAAK0zB,GAAkB,GAAI,CAAElwB,MAAOi3B,EAAMC,WAC1C16B,EAAK0zB,GAAc,WAAa,CAAElwB,MAAOmK,IAGxB,2BAAf8sB,EAAMx6B,MAAqCw8B,IAC7C/xB,GAAOH,IAAI,6BACXvK,EAAK0zB,GAAmB,IAAI,CAAElwB,MAAOi3B,EAAMC,WAC3C16B,EAAK0zB,GAAc,YAAc,CAAElwB,MAAOmK,IAG5C,MAEF,IAAK,WACH,IAAM+uB,EAAgBjC,EAAMx6B,KAAgByI,QAAQ3C,OAAO+J,SAAS6sB,OAAQ,IACtEnvB,WAwNhB6I,EACAokB,EACAiC,EACAhC,EACAliB,EACApE,GAIA,GAA4B,mBAAxBqmB,EAAMmC,eAA8D,UAAxBnC,EAAMmC,cACpD,OAGF,IAAMjsB,EAA4B,GAC9B,iBAAkB8pB,IACpB9pB,EAAK,iBAAmB8pB,EAAMoC,cAE5B,oBAAqBpC,IACvB9pB,EAAK,qBAAuB8pB,EAAMqC,iBAEhC,oBAAqBrC,IACvB9pB,EAAK,qBAAuB8pB,EAAMsC,iBAGpC,IAAMpvB,EAAiByG,EAAasmB,EAC9BltB,EAAeG,EAAiB6K,EAUtC,OARAyjB,GAAY5lB,EAAa,CACvBqc,YAAagK,EACblvB,eACAilB,GAAIgI,EAAMmC,cAAgB,YAAYnC,EAAMmC,cAAkB,WAC9DjvB,iBACAgD,SAGKnD,EA3PwBwvB,CAAiB3mB,EAAaokB,EAAOiC,EAAchC,EAAWliB,EAAUpE,QAE3D9G,IAA9BquB,IAA4CD,GAAkB,IAAI73B,QAAQ64B,IAAiB,IAC7Ff,EAA4BnuB,WASJF,IAA9BquB,QAAwEruB,IAA7BsuB,GAC7CK,GAAY5lB,EAAa,CACvBqc,YAAa,aACbllB,aAAcouB,EACdnJ,GAAI,SACJ9kB,eAAgBguB,IAIpBp7B,KAAKw7B,GAAqBlzB,KAAK5F,IAAIiR,YAAYqlB,aAAaj8B,OAAS,EAAG,GAExEiD,KAAK08B,GAAgB5mB,GAGE,aAAnBA,EAAYoc,GAAmB,CAGjC,IAAMyK,EAAavL,GAAQld,IAE3B,CAAC,MAAO,KAAM,MAAO,QAAQ1S,QAAQ,SAAA9B,GACnC,GAAKD,EAAK0zB,GAAczzB,MAASi9B,GAAc7mB,EAAY1I,gBAA3D,CAQA,IAAMwvB,EAAWn9B,EAAK0zB,GAAczzB,GAAMuD,MACpC45B,EAAuBF,EAAavL,GAAQwL,GAE5CE,EAAkBx0B,KAAKy0B,IAA0D,KAArDF,EAAuB/mB,EAAY1I,iBAE/DmrB,EAAQuE,EAAkBF,EAChCzyB,GAAOH,IAAI,6BAA6BtK,WAAak9B,SAAeE,OAAoBvE,OAExF94B,EAAK0zB,GAAczzB,GAAMuD,MAAQ65B,KAG/B98B,KAAKmzB,GAAc,aAAenzB,KAAKmzB,GAAmB,KAG5DuI,GAAY5lB,EAAa,CACvBqc,YAAa,oBACbllB,aAAcjN,KAAKmzB,GAAc,YAAYlwB,MAAQmuB,GAAQpxB,KAAKmzB,GAAmB,IAAElwB,OACvFivB,GAAI,aACJ9kB,eAAgBpN,KAAKmzB,GAAc,YAAYlwB,QAInD6S,EAAYknB,gBAAgBh9B,KAAKmzB,OAK7B+H,eAAR,WAAA,YCpJoB,SAACrB,EAAyBC,gBAAAA,MAC9C,IAEIC,EAFE/B,EAASQ,GAAW,MAAO,GAI3ByB,EAAe,SAACC,GAEfA,EAAM+C,iBACRjF,EAAO/0B,OAAoBi3B,EAAMj3B,MAClC+0B,EAAOS,QAAQv7B,KAAKg9B,GACpBH,MAIE9B,EAAKU,GAAQ,eAAgBsB,GAC/BhC,IACF8B,EAAShC,GAAa8B,EAAU7B,EAAQC,EAAI6B,GAE5CR,GAAS,SAACh5B,OAAE44B,gBACVjB,EAAGoC,cAActzB,IAAIkzB,GAEjBf,IACFlB,EAAOI,SAAU,GAEnB2B,OD6HFmD,CAAO,SAAAlF,GACSA,EAAOS,QAAQp3B,QAM7B8I,GAAOH,IAAI,6BACXvK,EAAK0zB,GAAmB,IAAI,CAAElwB,MAAO+0B,EAAO/0B,WAOxCi4B,eAAR,SAAwBplB,GACtB,IAAMgZ,EAAYvpB,GAAOupB,UAEzB,GAAKA,EAAL,CAMA,IAAMqO,EAAarO,EAAUqO,WACzBA,IACEA,EAAWC,eACbtnB,EAAY0E,OAAO,0BAA2B2iB,EAAWC,eAGvDD,EAAW94B,MACbyR,EAAY0E,OAAO,iBAAkB2iB,EAAW94B,MAG9Cg5B,GAAmBF,EAAWG,OAChCt9B,KAAKmzB,GAAc,kBAAoB,CAAElwB,MAAOk6B,EAAWG,MAGzDD,GAAmBF,EAAWI,YAChCv9B,KAAKmzB,GAAc,uBAAyB,CAAElwB,MAAOk6B,EAAWI,YAIhEF,GAAmBvO,EAAU0O,eAC/B1nB,EAAY0E,OAAO,eAAgBtX,OAAO4rB,EAAU0O,eAGlDH,GAAmBvO,EAAU2O,sBAC/B3nB,EAAY0E,OAAO,sBAAuBtX,OAAO4rB,EAAU2O,wBAKvDvC,eAAR,WAAA,WACEtB,GAAO,SAAA5B,GACL,IAAMkC,EAAQlC,EAAOS,QAAQp3B,MAE7B,GAAK64B,EAAL,CAIA,IAAMrmB,EAAaud,GAAQzd,YAAYE,YACjCsmB,EAAY/I,GAAQ8I,EAAMC,WAChChwB,GAAOH,IAAI,6BACXvK,EAAK0zB,GAAmB,IAAI,CAAElwB,MAAO+0B,EAAO/0B,OAC5CxD,EAAK0zB,GAAc,YAAc,CAAElwB,MAAO4Q,EAAasmB,OAKnDe,eAAR,WAAA,IE3MqBrB,EACf7B,EACAgC,EAEAC,EAUAhC,EACA8B,SAfeF,EF4MZ,SAAA7B,GACL,IAAMkC,EAAQlC,EAAOS,QAAQp3B,MAE7B,GAAK64B,EAAL,CAIA,IAAMrmB,EAAaud,GAAQzd,YAAYE,YACjCsmB,EAAY/I,GAAQ8I,EAAMC,WAChChwB,GAAOH,IAAI,6BACXvK,EAAK0zB,GAAmB,IAAI,CAAElwB,MAAO+0B,EAAO/0B,OAC5CxD,EAAK0zB,GAAc,YAAc,CAAElwB,MAAO4Q,EAAasmB,KEtNrDnC,EAASQ,GAAW,OACpBwB,EAAcL,KAYd1B,EAAKU,GAAQ,cAVbsB,EAAe,SAACC,GAEhBA,EAAMC,UAAYH,EAAYP,YAChCzB,EAAO/0B,MAAQi3B,EAAMwD,gBAAkBxD,EAAMC,UAC7CnC,EAAOS,QAAQv7B,KAAKg9B,GACpBlC,EAAOI,SAAU,EACjB2B,OAKEA,EAAShC,GAAa8B,EAAU7B,EAAQC,GAE1CA,EACFqB,GAAS,WACPrB,EAAGoC,cAActzB,IAAIkzB,GACrBhC,EAAGI,eACF,GAEC7yB,OAAOm4B,aAAen4B,OAAOm4B,YAAYC,mBAC3Cp4B,OAAOm4B,YAAYC,kBAAkB,SAAC36B,EAAe6F,GAE/CA,EAAM2wB,UAAYO,EAAYP,YAChCzB,EAAO/0B,MAAQA,EACf+0B,EAAOI,SAAU,EACjBJ,EAAOS,QAAU,CACf,CACEiC,UAAW,cACXh7B,KAAMoJ,EAAMzE,KACZC,OAAQwE,EAAMxE,OACdu5B,WAAY/0B,EAAM+0B,WAClB1D,UAAWrxB,EAAM2wB,UACjBiE,gBAAiB50B,EAAM2wB,UAAYx2B,IAGvC82B,QFqLAmB,eAAR,WAAA,WACEX,GAAQ,SAAAvC,SACAkC,EAAQlC,EAAOS,QAAQp3B,MAE7B,GAAK64B,EAAL,CAIA/vB,GAAOH,IAAI,8BACXvK,EAAK0zB,GAAoB,KAAI,CAAElwB,MAAO+0B,EAAO/0B,OAG7C,IAAM66B,EAAc9F,EAAO/0B,SAAU+0B,EAAOS,QAAQ,aAAMyB,GAAuCyB,aACjGl8B,EAAK0zB,GAAc,oBAAsB,CAAElwB,MAAO66B,YAuFxD,SAASrC,GACP3lB,EACAokB,EACApxB,EACA+K,EACAkqB,GAEA,IAAMC,EAAMD,EAAY7D,EAAM6D,GAAoC7D,EAASpxB,SACrEm1B,EAAQ/D,EAASpxB,WAClBm1B,GAAUD,GAGftC,GAAY5lB,EAAa,CACvBoc,GAAI,UACJC,YAAarpB,EACbsE,eAAgByG,EAAaud,GAAQ6M,GACrChxB,aAAc4G,EAAaud,GAAQ4M,cA0BvBtC,GAAY5lB,EAA0BxV,GAAE,IAAA8M,mBAAgB8wB,0BAKtE,OAJI9wB,GAAkB0I,EAAY1I,eAAiBA,IACjD0I,EAAY1I,eAAiBA,GAGxB0I,EAAYuc,cACjBjlB,kBACG8wB,IAOP,SAASb,GAAmBp6B,GAC1B,MAAwB,iBAAVA,GAAsBk7B,SAASl7B,GG/ZxC,IAwEMm7B,GAAsE,CACjFC,YAAY,EACZC,UAAU,EACVC,eA3EqC,CAAC,YAAa,iBA+ErCC,GAA+B1f,GAEvC,IAAAxe,gBAAE+9B,eAAYC,aAAUC,mBAAgBE,+BAOxCC,EAAkC,GAElCC,EAA0B,SAACl2B,GAC/B,GAAIi2B,EAAOj2B,GACT,OAAOi2B,EAAOj2B,GAEhB,IAAMm2B,EAAUL,EAIhB,OAHAG,EAAOj2B,GACLm2B,EAAQ/Z,KAAK,SAACuX,GAA4B,OAAAj5B,EAAkBsF,EAAK2zB,OAChEj5B,EAAkBsF,EAAK,cACnBi2B,EAAOj2B,IAKZo2B,EAAmBF,EACmB,mBAA/BF,IACTI,EAAmB,SAACp2B,GAClB,OAAOk2B,EAAwBl2B,IAAQg2B,EAA2Bh2B,KAItE,IAAMuN,EAA8B,GAEhCqoB,GACFnuB,GAA0B,CACxBhH,SAAU,SAACkF,aAqBfA,EACAywB,EACA7oB,SAEM8oB,YAAuB/iB,KAC1BzC,kCACCuG,aACJ,KACIif,GAAwB7N,GAAkB6N,IAC1C1wB,EAAYC,WAAawwB,EAAiBzwB,EAAYC,UAAU5F,MAElE,OAGF,GAAI2F,EAAYnB,cAAgBmB,EAAYC,UAAU0wB,OAAQ,CAC5D,IAAMnpB,EAAOI,EAAM5H,EAAYC,UAAU0wB,QACzC,GAAInpB,EAAM,CACR,IAAMpH,EAAWJ,EAAYI,SACzBA,GAGFoH,EAAKopB,cAAcxwB,EAAS5B,QAE9BgJ,EAAKwd,gBAGEpd,EAAM5H,EAAYC,UAAU0wB,QAErC,OAGF,IAAMxN,EAAoBL,KAC1B,GAAIK,EAAmB,CACrB,IAAM3b,EAAO2b,EAAkBc,WAAW,CACxCjiB,YACKhC,EAAYC,YACfhK,KAAM,UAER8tB,YAAgB/jB,EAAYC,UAAU/B,WAAU8B,EAAYC,UAAU5F,IACtEypB,GAAI,SAGN9jB,EAAYC,UAAU0wB,OAASnpB,EAAKmc,OACpC/b,EAAMJ,EAAKmc,QAAUnc,EAErB,IAAMxO,EAAWgH,EAAYrE,KAAK,GAAKqE,EAAYrE,KAAK,GAElD2B,EAAW0C,EAAYrE,KAAK,GAAMqE,EAAYrE,KAAK,IAAiC,GACtFuW,EAAU5U,EAAQ4U,QAClB9kB,EAAa4L,EAASkD,WACxBgW,EAAWlZ,EAAoBkZ,SAE7BA,EAE4B,mBAAnBA,EAAQ2e,OAEjB3e,EAAQ2e,OAAO,eAAgBrpB,EAAK2f,iBAEpCjV,EADSphB,MAAM6D,QAAQud,KACTA,GAAS,CAAC,eAAgB1K,EAAK2f,0BAE9BjV,IAASgV,eAAgB1f,EAAK2f,kBAG/CjV,EAAU,CAAEgV,eAAgB1f,EAAK2f,iBAEnC7pB,EAAQ4U,QAAUA,GArFd4e,CAAc9wB,EAAaywB,EAAkB7oB,IAE/C3R,KAAM,UAINi6B,GACFpuB,GAA0B,CACxBhH,SAAU,SAACkF,aAqFfA,EACAywB,EACA7oB,SAEM8oB,YAAuB/iB,KAC1BzC,kCACCuG,aACJ,IACIif,IAAwB7N,GAAkB6N,MAC1C1wB,EAAYhC,KAAOgC,EAAYhC,IAAIC,gBAAkBwyB,EAAiBzwB,EAAYhC,IAAIC,eAAe5D,OACvG2F,EAAYhC,IAAII,uBAEhB,OAGF,IAAMJ,EAAMgC,EAAYhC,IAAIC,eAG5B,GAAI+B,EAAYnB,cAAgBmB,EAAYhC,IAAI+yB,uBAAwB,CACtE,IAAMvpB,EAAOI,EAAM5H,EAAYhC,IAAI+yB,wBAQnC,YAPIvpB,IACFA,EAAKopB,cAAc5yB,EAAIO,aACvBiJ,EAAKwd,gBAGEpd,EAAM5H,EAAYhC,IAAI+yB,0BAMjC,IAAM5N,EAAoBL,KAC1B,GAAIK,EAAmB,CACrB,IAAM3b,EAAO2b,EAAkBc,WAAW,CACxCjiB,YACKhE,EAAIgE,OACP/L,KAAM,MACNiI,OAAQF,EAAIE,OACZ7D,IAAK2D,EAAI3D,MAEX0pB,YAAgB/lB,EAAIE,WAAUF,EAAI3D,IAClCypB,GAAI,SAMN,GAHA9jB,EAAYhC,IAAI+yB,uBAAyBvpB,EAAKmc,OAC9C/b,EAAM5H,EAAYhC,IAAI+yB,wBAA0BvpB,EAE5CxH,EAAYhC,IAAIke,iBAClB,IACElc,EAAYhC,IAAIke,iBAAiB,eAAgB1U,EAAK2f,iBACtD,MAAOpjB,MAtIPitB,CAAYhxB,EAAaywB,EAAkB7oB,IAE7C3R,KAAM,QCjIZ,IAAMkB,GAASiC,ICcR,IAoED63B,MACJC,YAAa1L,GACb2L,4BAA4B,EAC5BC,uBAvEsD,IAwEtDC,gCDhFA7H,EACA8H,EACAC,GAEA,gBAHAD,mBACAC,MAEKp6B,IAAWA,GAAOgK,SAAvB,CAKA,IAEIgiB,EAFAqO,EAAkCr6B,GAAOgK,SAASC,KAGlDkwB,IACFnO,EAAoBqG,EAAiB,CAAEl4B,KAAM6F,GAAOgK,SAASswB,SAAU3N,GAAI,cAGzEyN,GACFzvB,GAA0B,CACxBhH,SAAU,SAAC5I,OAAEgP,OAAIvP,cAUFgN,IAAThN,GAAsB6/B,IAA4C,IAA7BA,EAAYt8B,QAAQgM,GAC3DswB,OAAc7yB,EAIZhN,IAASuP,IACXswB,OAAc7yB,EACVwkB,IACFpnB,GAAOH,IAAI,oDAAoDunB,EAAkBW,IAEjFX,EAAkB6B,UAEpB7B,EAAoBqG,EAAiB,CAAEl4B,KAAM6F,GAAOgK,SAASswB,SAAU3N,GAAI,iBAG/E7tB,KAAM,iBAtCR8F,GAAOF,KAAK,yEC4Ed01B,kCAAkC,EAClCD,4BAA4B,GACzBtB,kBA8BH,WAAmBtf,GARZ9e,UAAe8/B,EAAe/hC,GAIpBiC,QAAmC,IAAIk7B,GAEvCl7B,SAA+B,EAG9C,IAAIu+B,EAAiBH,GAAqCG,eAGxDzf,GACAA,EAASyf,gBACTr/B,MAAM6D,QAAQ+b,EAASyf,iBACY,IAAnCzf,EAASyf,eAAexhC,OAExBwhC,EAAiBzf,EAASyf,eAE1Bv+B,KAAK+/B,IAAsB,EAG7B//B,KAAK0L,iBACA2zB,IACAvgB,IACHyf,mBAiFN,OA1ESuB,sBAAP,SAAiB3tB,EAAuC4J,GAAxD,WACE/b,KAAKggC,GAAiBjkB,EAElB/b,KAAK+/B,KACP51B,GAAOF,KACL,4GAEFE,GAAOF,KACL,oDAAoDm0B,GAAqCG,iBAKvF,IAAAj+B,eACJm/B,2BACAE,qCACAD,+BACAH,+BACAlB,eACAC,aACAC,mBACAE,+BAGFgB,EACE,SAAC9pB,GAAgC,OAAAlW,EAAKwgC,GAAwBtqB,IAC9D+pB,EACAC,GAGEJ,IfnKFh6B,IAAUA,GAAOE,SACnBF,GAAOE,SAAS4F,iBAAiB,mBAAoB,WACnD,IAAMkmB,EAAoBL,KACtB3rB,GAAOE,SAASoI,QAAU0jB,IAC5BpnB,GAAOH,IACL,0BAA0B8lB,GAAW6E,wDAAuDpD,EAAkBW,IAI3GX,EAAkB3kB,QACrB2kB,EAAkBC,UAAU1B,GAAW6E,WAEzCpD,EAAkB/W,OAAO,mBAAoB,mBAC7C+W,EAAkB6B,YAItBjpB,GAAOF,KAAK,uFesJZu0B,GAA+B,CAAEH,aAAYC,WAAUC,iBAAgBE,gCAIjEqB,eAAR,SAAgCnqB,GAAhC,WACE,GAAK3V,KAAKggC,GAAV,CAMM,IAAA1/B,eAAE4/B,mBAAgBZ,gBAAaE,2BAE/BW,EAAyC,aAAfxqB,EAAQuc,cAmC1C,IAAM9U,GASuBgjB,EATC,eAUxB7iC,EAAKkI,SAAS46B,cAAc,aAAaD,OACxC7iC,EAAKA,EAAGW,aAAa,WAAa,UAFZkiC,EACvB7iC,EATN,GAAI6f,EACF,gBrB3MmCkjB,GACrC,IAAMC,EAAUD,EAAYz/B,MAAMkwB,IAClC,GAAIwP,EAAS,CACX,IAAIxK,SAMJ,MALmB,MAAfwK,EAAQ,GACVxK,GAAgB,EACQ,MAAfwK,EAAQ,KACjBxK,GAAgB,GAEX,CACLjE,QAASyO,EAAQ,GACjBxK,gBACA/D,aAAcuO,EAAQ,KqB+LjBC,CAAuBpjB,GAGhC,OAxC8DqjB,QAAqB1zB,EAE3E2zB,WACD/qB,GACAwqB,IACHnN,SAAS,IAEL2N,EAA4C,mBAAnBT,EAAgCA,EAAeQ,GAAmBA,EAI3FE,OAAmC7zB,IAApB4zB,SAAqCD,IAAiBzO,SAAS,IAAU0O,GAEjE,IAAzBC,EAAa3O,SACf9nB,GAAOH,IAAI,2BAA2B42B,EAAa1O,8CAGrD,IACM2O,WhBcRllB,EACAkX,EACAyM,EACAwB,GAGA,OAAOtL,GAAO7Z,EADM,IAAI4Y,GAAgB1B,EAAoBlX,EAAK2jB,EAAawB,GAC9CvK,GAA0B1D,IgBpBhCkO,CADZ/gC,KAAKggC,KACiCY,EAActB,GAAa,GAO7E,OANAn1B,GAAOH,IAAI,sBAAsB42B,EAAa1O,4BAC9C2O,EAAgBG,6BAA6B,SAAClrB,EAAa7I,GACzDxN,EAAKwhC,GAASC,sBAAsBprB,GA6B1C,SAAmCqrB,EAAqBrrB,EAA8B7I,GACpF,IAAMm0B,EAAOn0B,EAAe6I,EAAY1I,eACVH,IAAiBm0B,EAAOD,GAAeC,EAAO,KAE1EtrB,EAAY0b,UAAU1B,GAAWc,kBACjC9a,EAAY0E,OAAO,iCAAkC,SAjCnD6mB,CrBxJU,IqBwJwB7B,EAAyB1pB,EAAa7I,KAGnE4zB,EAhCL12B,GAAOF,KAAK,4BAA4B0L,EAAQuc,uDAhFtC4N,KAAa,sBC9C7B,IAAIpQ,GAAqB,GAGnBC,GAAUnoB,IACZmoB,GAAQC,QAAUD,GAAQC,OAAOC,eACnCH,GAAqBC,GAAQC,OAAOC,kBAGhCE,YACDL,IACAO,KACH6P,2BAMFnI,+E3B5E2B,uEtBuGGthB,GAC5BmG,GAAgB,gBAAiBnG,kFArBNvN,GAC3B,OAAO0T,GAAU,eAAgB1T,kEA3BJvJ,EAAiB2W,GAC9C,IAAI2D,EACJ,IACE,MAAM,IAAIpe,MAAM8D,GAChB,MAAO+G,GACPuT,EAAqBvT,EAQvB,OAAOkW,GAAU,iBAAkBjd,EAHK,iBAAnB2W,EAA8BA,OAAiBnJ,KAIlE+M,kBAAmBva,EACnBsa,sBAJwC,iBAAnB3D,EAA8B,CAAEA,uBAAmBnJ,sBwBwFtDqG,GACpB,IAAMwF,EAASmD,KAAgBzC,YAC/B,OAAIV,EACKA,EAAOyC,MAAMjI,GAEfnB,GAAYG,QAAO,8BxBtEGlJ,GAC7BsT,GAAgB,iBAAkBtT,8CwBkDdkK,GACpB,IAAMwF,EAASmD,KAAgBzC,YAC/B,OAAIV,EACKA,EAAOgH,MAAMxM,GAEfnB,GAAYG,QAAO,uFAzEP1G,GAInB,gBAJmBA,WACiBqB,IAAhCrB,EAAQuS,sBACVvS,EAAQuS,oBAAsBA,SAERlR,IAApBrB,EAAQwM,QAAuB,CACjC,IAAMopB,EAAS95B,IAEX85B,EAAOC,gBAAkBD,EAAOC,eAAexjC,KACjD2N,EAAQwM,QAAUopB,EAAOC,eAAexjC,SAGRgP,IAAhCrB,EAAQ81B,sBACV91B,EAAQ81B,qBAAsB,Y0BzE+BC,EAAgC/1B,IACzE,IAAlBA,EAAQg2B,OACVv3B,GAAOw3B,SAET,IAAMhmB,EAAMI,KACNnD,EAAS,IAAI6oB,EAAY/1B,GAC/BiQ,EAAI5C,WAAWH,G1BsEfgpB,CAAYzS,GAAezjB,GAEvBA,EAAQ81B,qBAwFd,WACE,IAAMh8B,EAASgC,IACTmU,EAAMI,KAMR8lB,EAAuC,aAAxBp8B,SAASiH,WACxBo1B,GAAc,EACZC,EAAqB,WACrBD,GAAeD,GACjBlmB,EAAIV,cAGF+mB,EAAsB,WAC1BH,GAAe,EACfE,IACAv8B,EAAOy8B,oBAAoB,OAAQD,IAGrCrmB,EAAIumB,eAECL,GAGHr8B,EAAO6F,iBAAiB,OAAQ22B,GAGlC,IACE,IAAM/J,EAAK,IAAIW,oBAAoB,SAACuJ,EAAWlK,GAC7CkK,EAAUnJ,aAAax3B,QAAQ,SAAA04B,GACV,2BAAfA,EAAMx6B,MAAqCw6B,EAAMC,UAAYiI,IAC/DnK,EAAGI,aACHyJ,GAAc,EACdC,SAOFK,EAA+C,WAA7B38B,SAAS6yB,gBAA+B,EAAI1yB,EAAAA,EAClEH,SAAS4F,iBACP,mBACA,SAAAvC,GACEs5B,EAAkB95B,KAAK8R,IAAIgoB,EAAiBt5B,EAAM2wB,YAEpD,CAAED,MAAM,IAGVvB,EAAGU,QAAQ,CACTt0B,KAAM,QACN40B,UAAU,IAEZ,MAAOz2B,GACPs/B,GAAc,EACdC,KAhJAM,6BAyBF,OAAOtmB,KAAgBumB,iCAeFp5B,GACrBA,2BxBpByBxJ,EAAciW,GACvC6G,GAAgB,aAAc9c,EAAMiW,wBAwBbjY,EAAa8X,GACpCgH,GAAgB,WAAY9e,EAAK8X,yBAlBTD,GACxBiH,GAAgB,YAAajH,sBA4BR7X,EAAauF,GAClCuZ,GAAgB,SAAU9e,EAAKuF,uBAtBTqS,GACtBkH,GAAgB,UAAWlH,uBA6BLrU,GACtBub,GAAgB,UAAWvb,gCwB3DIyK,gBAAAA,MAC1BA,EAAQgO,UACXhO,EAAQgO,QAAUqC,KAAgBumB,eAEpC,IAAM1pB,EAASmD,KAAgBzC,YAC3BV,GACFA,EAAO2pB,iBAAiB72B,gCxB0G1BiK,EACAqF,GAEA,OAAOwB,GAAU,wBAAyB7G,GAAWqF,mCwB5ClCzY,GACnB,OAAOigC,GAAajgC,EAAbigC"}