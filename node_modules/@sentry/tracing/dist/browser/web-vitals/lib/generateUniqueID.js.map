{"version": 3, "file": "generateUniqueID.js", "sourceRoot": "", "sources": ["../../../../src/browser/web-vitals/lib/generateUniqueID.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH;;;;GAIG;AACU,QAAA,gBAAgB,GAAG;IAC9B,OAAU,IAAI,CAAC,GAAG,EAAE,UAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAE,CAAC;AAC1E,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Performantly generate a unique, 27-char string by combining the current\n * timestamp with a 13-digit random number.\n * @return {string}\n */\nexport const generateUniqueID = (): string => {\n  return `${Date.now()}-${Math.floor(Math.random() * (9e12 - 1)) + 1e12}`;\n};\n"]}