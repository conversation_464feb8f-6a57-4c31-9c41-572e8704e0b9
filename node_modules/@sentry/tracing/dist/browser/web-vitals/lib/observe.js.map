{"version": 3, "file": "observe.js", "sourceRoot": "", "sources": ["../../../../src/browser/web-vitals/lib/observe.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAMH;;;;;;;GAOG;AACU,QAAA,OAAO,GAAG,UAAC,IAAY,EAAE,QAAiC;IACrE,IAAI;QACF,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC1D,IAAM,EAAE,GAAwB,IAAI,mBAAmB,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,EAA5B,CAA4B,CAAC,CAAC;YAE3F,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,MAAA,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YACrC,OAAO,EAAE,CAAC;SACX;KACF;IAAC,OAAO,CAAC,EAAE;QACV,cAAc;KACf;IACD,OAAO;AACT,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface PerformanceEntryHandler {\n  (entry: PerformanceEntry): void;\n}\n\n/**\n * Takes a performance entry type and a callback function, and creates a\n * `PerformanceObserver` instance that will observe the specified entry type\n * with buffering enabled and call the callback _for each entry_.\n *\n * This function also feature-detects entry support and wraps the logic in a\n * try/catch to avoid errors in unsupporting browsers.\n */\nexport const observe = (type: string, callback: PerformanceEntryHandler): PerformanceObserver | undefined => {\n  try {\n    if (PerformanceObserver.supportedEntryTypes.includes(type)) {\n      const po: PerformanceObserver = new PerformanceObserver(l => l.getEntries().map(callback));\n\n      po.observe({ type, buffered: true });\n      return po;\n    }\n  } catch (e) {\n    // Do nothing.\n  }\n  return;\n};\n"]}