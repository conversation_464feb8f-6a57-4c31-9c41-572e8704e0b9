{"version": 3, "file": "hubextensions.js", "sourceRoot": "", "sources": ["../src/hubextensions.ts"], "names": [], "mappings": ";;AAAA,mCAAmE;AACnE,uCAAsH;AACtH,uCAOuB;AAEvB,mCAAwD;AACxD,qDAAoD;AACpD,6CAA4C;AAC5C,iCAA4C;AAE5C,qEAAqE;AACrE,SAAS,YAAY;IACnB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,IAAI,KAAK,EAAE;QACT,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,IAAI,EAAE;YACR,OAAO;gBACL,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE;aACrC,CAAC;SACH;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;;;;;;;GAWG;AACH,SAAS,MAAM,CAAwB,GAAQ,EAAE,WAAc,EAAE,eAAgC;;IAC/F,IAAM,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;IAC/B,IAAM,OAAO,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC;IAEtD,+DAA+D;IAC/D,IAAI,CAAC,MAAM,IAAI,CAAC,yBAAiB,CAAC,OAAO,CAAC,EAAE;QAC1C,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC;QAC5B,OAAO,WAAW,CAAC;KACpB;IAED,qHAAqH;IACrH,IAAI,WAAW,CAAC,OAAO,KAAK,SAAS,EAAE;QACrC,WAAW,CAAC,IAAI,yCAAQ,WAAW,CAAC,IAAI,KAAE,uBAAuB,EAAE,iCAAyB,CAAC,QAAQ,GAAE,CAAC;QACxG,OAAO,WAAW,CAAC;KACpB;IAED,sHAAsH;IACtH,8BAA8B;IAC9B,IAAI,UAAU,CAAC;IACf,IAAI,OAAO,OAAO,CAAC,aAAa,KAAK,UAAU,EAAE;QAC/C,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QACpD,yDAAyD;QACzD,WAAW,CAAC,IAAI,yCACX,WAAW,CAAC,IAAI,KACnB,uBAAuB,EAAE,iCAAyB,CAAC,OAAO;YAC1D,8EAA8E;YAC9E,mBAAmB,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAChD,CAAC;KACH;SAAM,IAAI,eAAe,CAAC,aAAa,KAAK,SAAS,EAAE;QACtD,UAAU,GAAG,eAAe,CAAC,aAAa,CAAC;QAC3C,WAAW,CAAC,IAAI,yCAAQ,WAAW,CAAC,IAAI,KAAE,uBAAuB,EAAE,iCAAyB,CAAC,WAAW,GAAE,CAAC;KAC5G;SAAM;QACL,UAAU,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACtC,yDAAyD;QACzD,WAAW,CAAC,IAAI,yCACX,WAAW,CAAC,IAAI,KACnB,uBAAuB,EAAE,iCAAyB,CAAC,IAAI;YACvD,8EAA8E;YAC9E,mBAAmB,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,GAChD,CAAC;KACH;IAED,kHAAkH;IAClH,8DAA8D;IAC9D,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;QAClC,cAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QAChF,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC;QAC5B,OAAO,WAAW,CAAC;KACpB;IAED,sHAAsH;IACtH,IAAI,CAAC,UAAU,EAAE;QACf,cAAM,CAAC,GAAG,CACR,+CACE,OAAO,OAAO,CAAC,aAAa,KAAK,UAAU;YACzC,CAAC,CAAC,mCAAmC;YACrC,CAAC,CAAC,4EAA4E,CAChF,CACH,CAAC;QACF,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC;QAC5B,OAAO,WAAW,CAAC;KACpB;IAED,qHAAqH;IACrH,4GAA4G;IAC5G,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,GAAI,UAA+B,CAAC;IAEvE,4CAA4C;IAC5C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;QACxB,cAAM,CAAC,GAAG,CACR,sGAAoG,MAAM,CACxG,UAAU,CACX,MAAG,CACL,CAAC;QACF,OAAO,WAAW,CAAC;KACpB;IAED,kHAAkH;IAClH,2BAA2B;IAC3B,WAAW,CAAC,gBAAgB,CAAC,MAAA,OAAO,CAAC,YAAY,0CAAE,QAAkB,CAAC,CAAC;IAEvE,cAAM,CAAC,GAAG,CAAC,wBAAsB,WAAW,CAAC,EAAE,uBAAkB,WAAW,CAAC,IAAM,CAAC,CAAC;IACrF,OAAO,WAAW,CAAC;AACrB,CAAC;AACD;;;;GAIG;AACH,SAAS,yBAAyB,CAAC,kBAAsC;IACvE,4DAA4D;IACpD,IAAA,gDAAa,CAAwB;IAC7C,IAAM,sBAAsB,GAAoB,EAAE,kBAAkB,oBAAA,EAAE,aAAa,eAAA,EAAE,CAAC;IAEtF,IAAI,iBAAS,EAAE,EAAE;QACf,IAAM,MAAM,GAAG,qBAAe,EAAE,CAAC;QAEjC,IAAI,MAAM,EAAE;YACV,gHAAgH;YAChH,sCAAsC;YAEtC,iHAAiH;YACjH,oBAAoB;YAEpB,IAAM,cAAc,GAAG,sBAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACtD,sEAAsE;YACtE,IAAM,aAAW,GAAG,cAAc,CAAC,eAAe,CAAC;YAEnD,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAA,MAAM,IAAI,OAAA,oBAAY,CAAC,MAAM,EAAE,aAAW,CAAC,EAAjC,CAAiC,CAAC,CAAC;YACjF,IAAI,OAAO,EAAE;gBACX,sBAAsB,CAAC,OAAO,GAAG,8BAAsB,CAAC,OAAO,CAAC,CAAC;aAClE;SACF;KACF;IAED,wDAAwD;SACnD;QACH,kHAAkH;QAClH,IAAM,YAAY,GAAG,uBAAe,EAA6B,CAAC;QAElE,IAAI,UAAU,IAAI,YAAY,EAAE;YAC9B,0GAA0G;YAC1G,sBAAsB,CAAC,QAAQ,wBAAS,YAAoB,CAAC,QAAQ,CAAE,CAAC;SACzE;KACF;IAED,OAAO,sBAAsB,CAAC;AAChC,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,IAAa;IACtC,oHAAoH;IACpH,8DAA8D;IAC9D,IAAI,KAAK,CAAC,IAAW,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,SAAS,CAAC,EAAE;QAClF,cAAM,CAAC,IAAI,CACT,4GAA0G,IAAI,CAAC,SAAS,CACtH,IAAI,CACL,iBAAY,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,MAAG,CAC5C,CAAC;QACF,OAAO,KAAK,CAAC;KACd;IAED,wGAAwG;IACxG,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE;QACxB,cAAM,CAAC,IAAI,CAAC,sFAAoF,IAAI,MAAG,CAAC,CAAC;QACzG,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,SAAS,iBAAiB,CAExB,kBAAsC,EACtC,qBAA6C;IAE7C,IAAM,WAAW,GAAG,IAAI,yBAAW,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;IAC9D,OAAO,MAAM,CAAC,IAAI,EAAE,WAAW,wCAC1B,yBAAyB,CAAC,kBAAkB,CAAC,GAC7C,qBAAqB,EACxB,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,GAAQ,EACR,kBAAsC,EACtC,WAAoB,EACpB,OAAiB;IAEjB,IAAM,WAAW,GAAG,IAAI,iCAAe,CAAC,kBAAkB,EAAE,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IACvF,OAAO,MAAM,CAAC,GAAG,EAAE,WAAW,EAAE,yBAAyB,CAAC,kBAAkB,CAAC,CAAC,CAAC;AACjF,CAAC;AARD,oDAQC;AAED;;GAEG;AACH,SAAgB,qBAAqB;IACnC,IAAM,OAAO,GAAG,oBAAc,EAAE,CAAC;IACjC,IAAI,OAAO,CAAC,UAAU,EAAE;QACtB,OAAO,CAAC,UAAU,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,UAAU,IAAI,EAAE,CAAC;QACpE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE;YACnD,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,GAAG,iBAAiB,CAAC;SACpE;QACD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,EAAE;YAC/C,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,GAAG,YAAY,CAAC;SAC3D;KACF;AACH,CAAC;AAXD,sDAWC;AAED;;GAEG;AACH,SAAgB,mBAAmB;IACjC,qBAAqB,EAAE,CAAC;IAExB,wFAAwF;IACxF,qCAA4B,EAAE,CAAC;AACjC,CAAC;AALD,kDAKC", "sourcesContent": ["import { getActive<PERSON><PERSON>in, get<PERSON>ain<PERSON><PERSON><PERSON>, Hub } from '@sentry/hub';\nimport { CustomSamplingContext, SamplingContext, TransactionContext, TransactionSamplingMethod } from '@sentry/types';\nimport {\n  dynamicRequire,\n  extractNodeRequestData,\n  getGlobalObject,\n  isInstanceOf,\n  isNodeEnv,\n  logger,\n} from '@sentry/utils';\n\nimport { registerErrorInstrumentation } from './errors';\nimport { IdleTransaction } from './idletransaction';\nimport { Transaction } from './transaction';\nimport { hasTracingEnabled } from './utils';\n\n/** Returns all trace headers that are currently on the top scope. */\nfunction traceHeaders(this: Hub): { [key: string]: string } {\n  const scope = this.getScope();\n  if (scope) {\n    const span = scope.getSpan();\n    if (span) {\n      return {\n        'sentry-trace': span.toTraceparent(),\n      };\n    }\n  }\n  return {};\n}\n\n/**\n * Makes a sampling decision for the given transaction and stores it on the transaction.\n *\n * Called every time a transaction is created. Only transactions which emerge with a `sampled` value of `true` will be\n * sent to Sentry.\n *\n * @param hub: The hub off of which to read config options\n * @param transaction: The transaction needing a sampling decision\n * @param samplingContext: Default and user-provided data which may be used to help make the decision\n *\n * @returns The given transaction with its `sampled` value set\n */\nfunction sample<T extends Transaction>(hub: Hub, transaction: T, samplingContext: SamplingContext): T {\n  const client = hub.getClient();\n  const options = (client && client.getOptions()) || {};\n\n  // nothing to do if there's no client or if tracing is disabled\n  if (!client || !hasTracingEnabled(options)) {\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // if the user has forced a sampling decision by passing a `sampled` value in their transaction context, go with that\n  if (transaction.sampled !== undefined) {\n    transaction.tags = { ...transaction.tags, __sentry_samplingMethod: TransactionSamplingMethod.Explicit };\n    return transaction;\n  }\n\n  // we would have bailed already if neither `tracesSampler` nor `tracesSampleRate` were defined, so one of these should\n  // work; prefer the hook if so\n  let sampleRate;\n  if (typeof options.tracesSampler === 'function') {\n    sampleRate = options.tracesSampler(samplingContext);\n    // cast the rate to a number first in case it's a boolean\n    transaction.tags = {\n      ...transaction.tags,\n      __sentry_samplingMethod: TransactionSamplingMethod.Sampler,\n      // TODO kmclb - once tag types are loosened, don't need to cast to string here\n      __sentry_sampleRate: String(Number(sampleRate)),\n    };\n  } else if (samplingContext.parentSampled !== undefined) {\n    sampleRate = samplingContext.parentSampled;\n    transaction.tags = { ...transaction.tags, __sentry_samplingMethod: TransactionSamplingMethod.Inheritance };\n  } else {\n    sampleRate = options.tracesSampleRate;\n    // cast the rate to a number first in case it's a boolean\n    transaction.tags = {\n      ...transaction.tags,\n      __sentry_samplingMethod: TransactionSamplingMethod.Rate,\n      // TODO kmclb - once tag types are loosened, don't need to cast to string here\n      __sentry_sampleRate: String(Number(sampleRate)),\n    };\n  }\n\n  // Since this is coming from the user (or from a function provided by the user), who knows what we might get. (The\n  // only valid values are booleans or numbers between 0 and 1.)\n  if (!isValidSampleRate(sampleRate)) {\n    logger.warn(`[Tracing] Discarding transaction because of invalid sample rate.`);\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // if the function returned 0 (or false), or if `tracesSampleRate` is 0, it's a sign the transaction should be dropped\n  if (!sampleRate) {\n    logger.log(\n      `[Tracing] Discarding transaction because ${\n        typeof options.tracesSampler === 'function'\n          ? 'tracesSampler returned 0 or false'\n          : 'a negative sampling decision was inherited or tracesSampleRate is set to 0'\n      }`,\n    );\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // Now we roll the dice. Math.random is inclusive of 0, but not of 1, so strict < is safe here. In case sampleRate is\n  // a boolean, the < comparison will cause it to be automatically cast to 1 if it's true and 0 if it's false.\n  transaction.sampled = Math.random() < (sampleRate as number | boolean);\n\n  // if we're not going to keep it, we're done\n  if (!transaction.sampled) {\n    logger.log(\n      `[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(\n        sampleRate,\n      )})`,\n    );\n    return transaction;\n  }\n\n  // at this point we know we're keeping the transaction, whether because of an inherited decision or because it got\n  // lucky with the dice roll\n  transaction.initSpanRecorder(options._experiments?.maxSpans as number);\n\n  logger.log(`[Tracing] starting ${transaction.op} transaction - ${transaction.name}`);\n  return transaction;\n}\n/**\n * Gets the correct context to pass to the tracesSampler, based on the environment (i.e., which SDK is being used)\n *\n * @returns The default sample context\n */\nfunction getDefaultSamplingContext(transactionContext: TransactionContext): SamplingContext {\n  // promote parent sampling decision (if any) for easy access\n  const { parentSampled } = transactionContext;\n  const defaultSamplingContext: SamplingContext = { transactionContext, parentSampled };\n\n  if (isNodeEnv()) {\n    const domain = getActiveDomain();\n\n    if (domain) {\n      // for all node servers that we currently support, we store the incoming request object (which is an instance of\n      // http.IncomingMessage) on the domain\n\n      // the domain members are stored as an array, so our only way to find the request is to iterate through the array\n      // and compare types\n\n      const nodeHttpModule = dynamicRequire(module, 'http');\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      const requestType = nodeHttpModule.IncomingMessage;\n\n      const request = domain.members.find(member => isInstanceOf(member, requestType));\n      if (request) {\n        defaultSamplingContext.request = extractNodeRequestData(request);\n      }\n    }\n  }\n\n  // we must be in browser-js (or some derivative thereof)\n  else {\n    // we use `getGlobalObject()` rather than `window` since service workers also have a `location` property on `self`\n    const globalObject = getGlobalObject<WindowOrWorkerGlobalScope>();\n\n    if ('location' in globalObject) {\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n      defaultSamplingContext.location = { ...(globalObject as any).location };\n    }\n  }\n\n  return defaultSamplingContext;\n}\n\n/**\n * Checks the given sample rate to make sure it is valid type and value (a boolean, or a number between 0 and 1).\n */\nfunction isValidSampleRate(rate: unknown): boolean {\n  // we need to check NaN explicitly because it's of type 'number' and therefore wouldn't get caught by this typecheck\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if (isNaN(rate as any) || !(typeof rate === 'number' || typeof rate === 'boolean')) {\n    logger.warn(\n      `[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(\n        rate,\n      )} of type ${JSON.stringify(typeof rate)}.`,\n    );\n    return false;\n  }\n\n  // in case sampleRate is a boolean, it will get automatically cast to 1 if it's true and 0 if it's false\n  if (rate < 0 || rate > 1) {\n    logger.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${rate}.`);\n    return false;\n  }\n  return true;\n}\n\n/**\n * Creates a new transaction and adds a sampling decision if it doesn't yet have one.\n *\n * The Hub.startTransaction method delegates to this method to do its work, passing the Hub instance in as `this`, as if\n * it had been called on the hub directly. Exists as a separate function so that it can be injected into the class as an\n * \"extension method.\"\n *\n * @param this: The Hub starting the transaction\n * @param transactionContext: Data used to configure the transaction\n * @param CustomSamplingContext: Optional data to be provided to the `tracesSampler` function (if any)\n *\n * @returns The new transaction\n *\n * @see {@link Hub.startTransaction}\n */\nfunction _startTransaction(\n  this: Hub,\n  transactionContext: TransactionContext,\n  customSamplingContext?: CustomSamplingContext,\n): Transaction {\n  const transaction = new Transaction(transactionContext, this);\n  return sample(this, transaction, {\n    ...getDefaultSamplingContext(transactionContext),\n    ...customSamplingContext,\n  });\n}\n\n/**\n * Create new idle transaction.\n */\nexport function startIdleTransaction(\n  hub: Hub,\n  transactionContext: TransactionContext,\n  idleTimeout?: number,\n  onScope?: boolean,\n): IdleTransaction {\n  const transaction = new IdleTransaction(transactionContext, hub, idleTimeout, onScope);\n  return sample(hub, transaction, getDefaultSamplingContext(transactionContext));\n}\n\n/**\n * @private\n */\nexport function _addTracingExtensions(): void {\n  const carrier = getMainCarrier();\n  if (carrier.__SENTRY__) {\n    carrier.__SENTRY__.extensions = carrier.__SENTRY__.extensions || {};\n    if (!carrier.__SENTRY__.extensions.startTransaction) {\n      carrier.__SENTRY__.extensions.startTransaction = _startTransaction;\n    }\n    if (!carrier.__SENTRY__.extensions.traceHeaders) {\n      carrier.__SENTRY__.extensions.traceHeaders = traceHeaders;\n    }\n  }\n}\n\n/**\n * This patches the global object and injects the Tracing extensions methods\n */\nexport function addExtensionMethods(): void {\n  _addTracingExtensions();\n\n  // If an error happens globally, we should make sure transaction status is set to error.\n  registerErrorInstrumentation();\n}\n"]}