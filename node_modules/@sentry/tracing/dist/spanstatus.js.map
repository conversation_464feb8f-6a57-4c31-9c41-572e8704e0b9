{"version": 3, "file": "spanstatus.js", "sourceRoot": "", "sources": ["../src/spanstatus.ts"], "names": [], "mappings": ";AAAA,6BAA6B;AAC7B,yCAAyC;AACzC,IAAY,UAmCX;AAnCD,WAAY,UAAU;IACpB,4CAA4C;IAC5C,uBAAS,CAAA;IACT,wDAAwD;IACxD,oDAAsC,CAAA;IACtC,kFAAkF;IAClF,iDAAmC,CAAA;IACnC,oBAAoB;IACpB,oDAAsC,CAAA;IACtC,8EAA8E;IAC9E,oCAAsB,CAAA;IACtB,4BAA4B;IAC5B,sDAAwC,CAAA;IACxC,iDAAiD;IACjD,kDAAoC,CAAA;IACpC,0BAA0B;IAC1B,6CAA+B,CAAA;IAC/B,8BAA8B;IAC9B,yCAA2B,CAAA;IAC3B,yBAAyB;IACzB,8CAAgC,CAAA;IAChC,kDAAkD;IAClD,4CAA8B,CAAA;IAC9B,2DAA2D;IAC3D,qCAAuB,CAAA;IACvB,2BAA2B;IAC3B,8CAAgC,CAAA;IAChC,+FAA+F;IAC/F,wDAA0C,CAAA;IAC1C,uEAAuE;IACvE,iCAAmB,CAAA;IACnB,oDAAoD;IACpD,yCAA2B,CAAA;IAC3B,4CAA4C;IAC5C,oCAAsB,CAAA;AACxB,CAAC,EAnCW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAmCrB;AAED,0EAA0E;AAC1E,WAAiB,UAAU;IACzB;;;;;OAKG;IACH,SAAgB,YAAY,CAAC,UAAkB;QAC7C,IAAI,UAAU,GAAG,GAAG,EAAE;YACpB,OAAO,UAAU,CAAC,EAAE,CAAC;SACtB;QAED,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE;YACzC,QAAQ,UAAU,EAAE;gBAClB,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,eAAe,CAAC;gBACpC,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,gBAAgB,CAAC;gBACrC,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,QAAQ,CAAC;gBAC7B,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,aAAa,CAAC;gBAClC,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,kBAAkB,CAAC;gBACvC,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,iBAAiB,CAAC;gBACtC;oBACE,OAAO,UAAU,CAAC,eAAe,CAAC;aACrC;SACF;QAED,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE;YACzC,QAAQ,UAAU,EAAE;gBAClB,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,aAAa,CAAC;gBAClC,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,WAAW,CAAC;gBAChC,KAAK,GAAG;oBACN,OAAO,UAAU,CAAC,gBAAgB,CAAC;gBACrC;oBACE,OAAO,UAAU,CAAC,aAAa,CAAC;aACnC;SACF;QAED,OAAO,UAAU,CAAC,YAAY,CAAC;IACjC,CAAC;IAtCe,uBAAY,eAsC3B,CAAA;AACH,CAAC,EA9CgB,UAAU,GAAV,kBAAU,KAAV,kBAAU,QA8C1B", "sourcesContent": ["/** The status of an Span. */\n// eslint-disable-next-line import/export\nexport enum SpanStatus {\n  /** The operation completed successfully. */\n  Ok = 'ok',\n  /** Deadline expired before operation could complete. */\n  DeadlineExceeded = 'deadline_exceeded',\n  /** 401 Unauthorized (actually does mean unauthenticated according to RFC 7235) */\n  Unauthenticated = 'unauthenticated',\n  /** 403 Forbidden */\n  PermissionDenied = 'permission_denied',\n  /** 404 Not Found. Some requested entity (file or directory) was not found. */\n  NotFound = 'not_found',\n  /** 429 Too Many Requests */\n  ResourceExhausted = 'resource_exhausted',\n  /** Client specified an invalid argument. 4xx. */\n  InvalidArgument = 'invalid_argument',\n  /** 501 Not Implemented */\n  Unimplemented = 'unimplemented',\n  /** 503 Service Unavailable */\n  Unavailable = 'unavailable',\n  /** Other/generic 5xx. */\n  InternalError = 'internal_error',\n  /** Unknown. Any non-standard HTTP status code. */\n  UnknownError = 'unknown_error',\n  /** The operation was cancelled (typically by the user). */\n  Cancelled = 'cancelled',\n  /** Already exists (409) */\n  AlreadyExists = 'already_exists',\n  /** Operation was rejected because the system is not in a state required for the operation's */\n  FailedPrecondition = 'failed_precondition',\n  /** The operation was aborted, typically due to a concurrency issue. */\n  Aborted = 'aborted',\n  /** Operation was attempted past the valid range. */\n  OutOfRange = 'out_of_range',\n  /** Unrecoverable data loss or corruption */\n  DataLoss = 'data_loss',\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace, import/export\nexport namespace SpanStatus {\n  /**\n   * Converts a HTTP status code into a {@link SpanStatus}.\n   *\n   * @param httpStatus The HTTP response status code.\n   * @returns The span status or {@link SpanStatus.UnknownError}.\n   */\n  export function fromHttpCode(httpStatus: number): SpanStatus {\n    if (httpStatus < 400) {\n      return SpanStatus.Ok;\n    }\n\n    if (httpStatus >= 400 && httpStatus < 500) {\n      switch (httpStatus) {\n        case 401:\n          return SpanStatus.Unauthenticated;\n        case 403:\n          return SpanStatus.PermissionDenied;\n        case 404:\n          return SpanStatus.NotFound;\n        case 409:\n          return SpanStatus.AlreadyExists;\n        case 413:\n          return SpanStatus.FailedPrecondition;\n        case 429:\n          return SpanStatus.ResourceExhausted;\n        default:\n          return SpanStatus.InvalidArgument;\n      }\n    }\n\n    if (httpStatus >= 500 && httpStatus < 600) {\n      switch (httpStatus) {\n        case 501:\n          return SpanStatus.Unimplemented;\n        case 503:\n          return SpanStatus.Unavailable;\n        case 504:\n          return SpanStatus.DeadlineExceeded;\n        default:\n          return SpanStatus.InternalError;\n      }\n    }\n\n    return SpanStatus.UnknownError;\n  }\n}\n"]}