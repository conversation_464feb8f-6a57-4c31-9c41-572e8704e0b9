/*
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export var bindReporter = function (callback, metric, po, observeAllUpdates) {
    var prevValue;
    return function () {
        if (po && metric.isFinal) {
            po.disconnect();
        }
        if (metric.value >= 0) {
            if (observeAllUpdates || metric.isFinal || document.visibilityState === 'hidden') {
                metric.delta = metric.value - (prevValue || 0);
                // Report the metric if there's a non-zero delta, if the metric is
                // final, or if no previous value exists (which can happen in the case
                // of the document becoming hidden when the metric value is 0).
                // See: https://github.com/GoogleChrome/web-vitals/issues/14
                if (metric.delta || metric.isFinal || prevValue === undefined) {
                    callback(metric);
                    prevValue = metric.value;
                }
            }
        }
    };
};
//# sourceMappingURL=bindReporter.js.map