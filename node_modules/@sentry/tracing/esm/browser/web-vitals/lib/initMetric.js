/*
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { generateUniqueID } from './generateUniqueID';
export var initMetric = function (name, value) {
    if (value === void 0) { value = -1; }
    return {
        name: name,
        value: value,
        delta: 0,
        entries: [],
        id: generateUniqueID(),
        isFinal: false,
    };
};
//# sourceMappingURL=initMetric.js.map