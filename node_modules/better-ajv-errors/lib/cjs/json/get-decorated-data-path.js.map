{"version": 3, "sources": ["../../../src/json/get-decorated-data-path.js"], "sourcesContent": ["import { getPointers } from './utils';\n\nexport default function getDecoratedDataPath(jsonAst, dataPath) {\n  let decoratedPath = '';\n  getPointers(dataPath).reduce((obj, pointer) => {\n    switch (obj.type) {\n      case 'Object': {\n        decoratedPath += `/${pointer}`;\n        const filtered = obj.members.filter(\n          child => child.name.value === pointer\n        );\n        if (filtered.length !== 1) {\n          throw new Error(`Couldn't find property ${pointer} of ${dataPath}`);\n        }\n        return filtered[0].value;\n      }\n      case 'Array': {\n        decoratedPath += `/${pointer}${getTypeName(obj.elements[pointer])}`;\n        return obj.elements[pointer];\n      }\n      default:\n        console.log(obj);\n    }\n  }, jsonAst.body);\n  return decoratedPath;\n}\n\nfunction getTypeName(obj) {\n  if (!obj || !obj.elements) {\n    return '';\n  }\n  const type = obj.elements.filter(\n    child => child && child.name && child.name.value === 'type'\n  );\n\n  if (!type.length) {\n    return '';\n  }\n\n  return (type[0].value && `:${type[0].value.value}`) || '';\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAA4B;AAEb,SAAR,qBAAsC,SAAS,UAAU;AAC9D,MAAI,gBAAgB;AACpB,gCAAY,QAAQ,EAAE,OAAO,CAAC,KAAK,YAAY;AAC7C,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK,UAAU;AACb,yBAAiB,IAAI,OAAO;AAC5B,cAAM,WAAW,IAAI,QAAQ;AAAA,UAC3B,WAAS,MAAM,KAAK,UAAU;AAAA,QAChC;AACA,YAAI,SAAS,WAAW,GAAG;AACzB,gBAAM,IAAI,MAAM,0BAA0B,OAAO,OAAO,QAAQ,EAAE;AAAA,QACpE;AACA,eAAO,SAAS,CAAC,EAAE;AAAA,MACrB;AAAA,MACA,KAAK,SAAS;AACZ,yBAAiB,IAAI,OAAO,GAAG,YAAY,IAAI,SAAS,OAAO,CAAC,CAAC;AACjE,eAAO,IAAI,SAAS,OAAO;AAAA,MAC7B;AAAA,MACA;AACE,gBAAQ,IAAI,GAAG;AAAA,IACnB;AAAA,EACF,GAAG,QAAQ,IAAI;AACf,SAAO;AACT;AAEA,SAAS,YAAY,KAAK;AACxB,MAAI,CAAC,OAAO,CAAC,IAAI,UAAU;AACzB,WAAO;AAAA,EACT;AACA,QAAM,OAAO,IAAI,SAAS;AAAA,IACxB,WAAS,SAAS,MAAM,QAAQ,MAAM,KAAK,UAAU;AAAA,EACvD;AAEA,MAAI,CAAC,KAAK,QAAQ;AAChB,WAAO;AAAA,EACT;AAEA,SAAQ,KAAK,CAAC,EAAE,SAAS,IAAI,KAAK,CAAC,EAAE,MAAM,KAAK,MAAO;AACzD;", "names": []}