var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var utils_exports = {};
__export(utils_exports, {
  concatAll: () => concatAll,
  getChildren: () => getChildren,
  getErrors: () => getErrors,
  getSiblings: () => getSiblings,
  isAnyOfError: () => isAnyOfError,
  isEnumError: () => isEnumError,
  isRequiredError: () => isRequiredError,
  notUndefined: () => notUndefined
});
module.exports = __toCommonJS(utils_exports);
const eq = (x) => (y) => x === y;
const not = (fn) => (x) => !fn(x);
const getValues = (
  /*::<Obj: Object>*/
  (o) => Object.values(o)
);
const notUndefined = (x) => x !== void 0;
const isXError = (x) => (error) => error.keyword === x;
const isRequiredError = isXError("required");
const isAnyOfError = isXError("anyOf");
const isEnumError = isXError("enum");
const getErrors = (node) => node && node.errors ? node.errors.map(
  (e) => e.keyword === "errorMessage" ? { ...e.params.errors[0], message: e.message } : e
) : [];
const getChildren = (node) => node && getValues(node.children) || [];
const getSiblings = (parent) => (node) => getChildren(parent).filter(not(eq(node)));
const concatAll = (
  /*::<T>*/
  (xs) => (ys) => ys.reduce((zs, z) => zs.concat(z), xs)
);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  concatAll,
  getChildren,
  getErrors,
  getSiblings,
  isAnyOfError,
  isEnumError,
  isRequiredError,
  notUndefined
});
//# sourceMappingURL=utils.js.map
