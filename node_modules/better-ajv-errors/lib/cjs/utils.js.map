{"version": 3, "sources": ["../../src/utils.js"], "sourcesContent": ["// @flow\n\n/*::\nimport type { Error, Node } from './types';\n*/\n\n// Basic\nconst eq = x => y => x === y;\nconst not = fn => x => !fn(x);\n\n// https://github.com/facebook/flow/issues/2221\nconst getValues = /*::<Obj: Object>*/ (\n  o /*: Obj*/\n) /*: $ReadOnlyArray<$Values<Obj>>*/ => Object.values(o);\n\nexport const notUndefined = (x /*: mixed*/) => x !== undefined;\n\n// Error\nconst isXError = x => (error /*: Error */) => error.keyword === x;\nexport const isRequiredError = isXError('required');\nexport const isAnyOfError = isXError('anyOf');\nexport const isEnumError = isXError('enum');\nexport const getErrors = (node /*: Node*/) =>\n  node && node.errors\n    ? node.errors.map(e =>\n        e.keyword === 'errorMessage'\n          ? { ...e.params.errors[0], message: e.message }\n          : e\n      )\n    : [];\n\n// Node\nexport const getChildren = (node /*: Node*/) /*: $ReadOnlyArray<Node>*/ =>\n  (node && getValues(node.children)) || [];\n\nexport const getSiblings =\n  (parent /*: Node*/) => (node /*: Node*/) /*: $ReadOnlyArray<Node>*/ =>\n    getChildren(parent).filter(not(eq(node)));\n\nexport const concatAll =\n  /*::<T>*/\n\n\n    (xs /*: $ReadOnlyArray<T>*/) =>\n    (ys /*: $ReadOnlyArray<T>*/) /*: $ReadOnlyArray<T>*/ =>\n      ys.reduce((zs, z) => zs.concat(z), xs);\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,MAAM,KAAK,OAAK,OAAK,MAAM;AAC3B,MAAM,MAAM,QAAM,OAAK,CAAC,GAAG,CAAC;AAG5B,MAAM;AAAA;AAAA,EAAgC,CACpC,MACsC,OAAO,OAAO,CAAC;AAAA;AAEhD,MAAM,eAAe,CAAC,MAAkB,MAAM;AAGrD,MAAM,WAAW,OAAK,CAAC,UAAuB,MAAM,YAAY;AACzD,MAAM,kBAAkB,SAAS,UAAU;AAC3C,MAAM,eAAe,SAAS,OAAO;AACrC,MAAM,cAAc,SAAS,MAAM;AACnC,MAAM,YAAY,CAAC,SACxB,QAAQ,KAAK,SACT,KAAK,OAAO;AAAA,EAAI,OACd,EAAE,YAAY,iBACV,EAAE,GAAG,EAAE,OAAO,OAAO,CAAC,GAAG,SAAS,EAAE,QAAQ,IAC5C;AACN,IACA,CAAC;AAGA,MAAM,cAAc,CAAC,SACzB,QAAQ,UAAU,KAAK,QAAQ,KAAM,CAAC;AAElC,MAAM,cACX,CAAC,WAAsB,CAAC,SACtB,YAAY,MAAM,EAAE,OAAO,IAAI,GAAG,IAAI,CAAC,CAAC;AAErC,MAAM;AAAA;AAAA,EAIT,CAAC,OACD,CAAC,OACC,GAAG,OAAO,CAAC,IAAI,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE;AAAA;", "names": []}