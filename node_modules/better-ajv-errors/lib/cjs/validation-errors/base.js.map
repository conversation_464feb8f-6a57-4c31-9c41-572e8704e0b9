{"version": 3, "sources": ["../../../src/validation-errors/base.js"], "sourcesContent": ["import { codeFrameColumns } from '@babel/code-frame';\nimport { getMetaFromPath, getDecoratedDataPath } from '../json/index';\n\nexport default class BaseValidationError {\n  constructor(\n    options = { isIdentifierLocation: false },\n    { data, schema, jsonAst, jsonRaw }\n  ) {\n    this.options = options;\n    this.data = data;\n    this.schema = schema;\n    this.jsonAst = jsonAst;\n    this.jsonRaw = jsonRaw;\n  }\n\n  getLocation(dataPath = this.instancePath) {\n    const { isIdentifierLocation, isSkipEndLocation } = this.options;\n    const { loc } = getMetaFromPath(\n      this.jsonAst,\n      dataPath,\n      isIdentifierLocation\n    );\n    return {\n      start: loc.start,\n      end: isSkipEndLocation ? undefined : loc.end,\n    };\n  }\n\n  getDecoratedPath(dataPath = this.instancePath) {\n    const decoratedPath = getDecoratedDataPath(this.jsonAst, dataPath);\n    return decoratedPath;\n  }\n\n  getCodeFrame(message, dataPath = this.instancePath) {\n    return codeFrameColumns(this.jsonRaw, this.getLocation(dataPath), {\n      highlightCode: true,\n      message,\n    });\n  }\n\n  /**\n   * @return {string}\n   */\n  get instancePath() {\n    return typeof this.options.instancePath !== 'undefined'\n      ? this.options.instancePath\n      : this.options.dataPath;\n  }\n\n  print() {\n    throw new Error(\n      `Implement the 'print' method inside ${this.constructor.name}!`\n    );\n  }\n\n  getError() {\n    throw new Error(\n      `Implement the 'getError' method inside ${this.constructor.name}!`\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAAiC;AACjC,kBAAsD;AAEtD,MAAO,oBAAkC;AAAA,EACvC,YACE,UAAU,EAAE,sBAAsB,MAAM,GACxC,EAAE,MAAM,QAAQ,SAAS,QAAQ,GACjC;AACA,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,YAAY,WAAW,KAAK,cAAc;AACxC,UAAM,EAAE,sBAAsB,kBAAkB,IAAI,KAAK;AACzD,UAAM,EAAE,IAAI,QAAI;AAAA,MACd,KAAK;AAAA,MACL;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO,IAAI;AAAA,MACX,KAAK,oBAAoB,SAAY,IAAI;AAAA,IAC3C;AAAA,EACF;AAAA,EAEA,iBAAiB,WAAW,KAAK,cAAc;AAC7C,UAAM,oBAAgB,kCAAqB,KAAK,SAAS,QAAQ;AACjE,WAAO;AAAA,EACT;AAAA,EAEA,aAAa,SAAS,WAAW,KAAK,cAAc;AAClD,eAAO,oCAAiB,KAAK,SAAS,KAAK,YAAY,QAAQ,GAAG;AAAA,MAChE,eAAe;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,eAAe;AACjB,WAAO,OAAO,KAAK,QAAQ,iBAAiB,cACxC,KAAK,QAAQ,eACb,KAAK,QAAQ;AAAA,EACnB;AAAA,EAEA,QAAQ;AACN,UAAM,IAAI;AAAA,MACR,uCAAuC,KAAK,YAAY,IAAI;AAAA,IAC9D;AAAA,EACF;AAAA,EAEA,WAAW;AACT,UAAM,IAAI;AAAA,MACR,0CAA0C,KAAK,YAAY,IAAI;AAAA,IACjE;AAAA,EACF;AACF;", "names": []}