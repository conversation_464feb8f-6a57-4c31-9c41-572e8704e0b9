{"version": 3, "sources": ["../../src/helpers.js"], "sourcesContent": ["import {\n  getC<PERSON>dren,\n  getErrors,\n  getSiblings,\n  isAnyOfError,\n  isEnumError,\n  isRequiredError,\n  concatAll,\n  notUndefined,\n} from './utils';\nimport {\n  AdditionalPropValidationError,\n  RequiredValidationError,\n  EnumValidationError,\n  DefaultValidationError,\n} from './validation-errors/index';\n\nconst JSON_POINTERS_REGEX = /\\/[\\w_-]+(\\/\\d+)?/g;\n\n// Make a tree of errors from ajv errors array\nexport function makeTree(ajvErrors = []) {\n  const root = { children: {} };\n  ajvErrors.forEach(ajvError => {\n    const instancePath =\n      typeof ajvError.instancePath !== 'undefined'\n        ? ajvError.instancePath\n        : ajvError.dataPath;\n\n    // `dataPath === ''` is root\n    const paths =\n      instancePath === '' ? [''] : instancePath.match(JSON_POINTERS_REGEX);\n    paths &&\n      paths.reduce((obj, path, i) => {\n        obj.children[path] = obj.children[path] || { children: {}, errors: [] };\n        if (i === paths.length - 1) {\n          obj.children[path].errors.push(ajvError);\n        }\n        return obj.children[path];\n      }, root);\n  });\n  return root;\n}\n\nexport function filterRedundantErrors(root, parent, key) {\n  /**\n   * If there is a `required` error then we can just skip everythig else.\n   * And, also `required` should have more priority than `anyOf`. @see #8\n   */\n  getErrors(root).forEach(error => {\n    if (isRequiredError(error)) {\n      root.errors = [error];\n      root.children = {};\n    }\n  });\n\n  /**\n   * If there is an `anyOf` error that means we have more meaningful errors\n   * inside children. So we will just remove all errors from this level.\n   *\n   * If there are no children, then we don't delete the errors since we should\n   * have at least one error to report.\n   */\n  if (getErrors(root).some(isAnyOfError)) {\n    if (Object.keys(root.children).length > 0) {\n      delete root.errors;\n    }\n  }\n\n  /**\n   * If all errors are `enum` and siblings have any error then we can safely\n   * ignore the node.\n   *\n   * **CAUTION**\n   * Need explicit `root.errors` check because `[].every(fn) === true`\n   * https://en.wikipedia.org/wiki/Vacuous_truth#Vacuous_truths_in_mathematics\n   */\n  if (root.errors && root.errors.length && getErrors(root).every(isEnumError)) {\n    if (\n      getSiblings(parent)(root)\n        // Remove any reference which becomes `undefined` later\n        .filter(notUndefined)\n        .some(getErrors)\n    ) {\n      delete parent.children[key];\n    }\n  }\n\n  Object.entries(root.children).forEach(([key, child]) =>\n    filterRedundantErrors(child, root, key)\n  );\n}\n\nexport function createErrorInstances(root, options) {\n  const errors = getErrors(root);\n  if (errors.length && errors.every(isEnumError)) {\n    const uniqueValues = new Set(\n      concatAll([])(errors.map(e => e.params.allowedValues))\n    );\n    const allowedValues = [...uniqueValues];\n    const error = errors[0];\n    return [\n      new EnumValidationError(\n        {\n          ...error,\n          params: { allowedValues },\n        },\n        options\n      ),\n    ];\n  } else {\n    return concatAll(\n      errors.reduce((ret, error) => {\n        switch (error.keyword) {\n          case 'additionalProperties':\n            return ret.concat(\n              new AdditionalPropValidationError(error, options)\n            );\n          case 'required':\n            return ret.concat(new RequiredValidationError(error, options));\n          default:\n            return ret.concat(new DefaultValidationError(error, options));\n        }\n      }, [])\n    )(getChildren(root).map(child => createErrorInstances(child, options)));\n  }\n}\n\nexport default (ajvErrors, options) => {\n  const tree = makeTree(ajvErrors || []);\n  filterRedundantErrors(tree);\n  return createErrorInstances(tree, options);\n};\n"], "mappings": ";AAAA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP,IAAM,sBAAsB;AAGrB,SAAS,SAAS,YAAY,CAAC,GAAG;AACvC,QAAM,OAAO,EAAE,UAAU,CAAC,EAAE;AAC5B,YAAU,QAAQ,cAAY;AAC5B,UAAM,eACJ,OAAO,SAAS,iBAAiB,cAC7B,SAAS,eACT,SAAS;AAGf,UAAM,QACJ,iBAAiB,KAAK,CAAC,EAAE,IAAI,aAAa,MAAM,mBAAmB;AACrE,aACE,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM;AAC7B,UAAI,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,KAAK,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,EAAE;AACtE,UAAI,MAAM,MAAM,SAAS,GAAG;AAC1B,YAAI,SAAS,IAAI,EAAE,OAAO,KAAK,QAAQ;AAAA,MACzC;AACA,aAAO,IAAI,SAAS,IAAI;AAAA,IAC1B,GAAG,IAAI;AAAA,EACX,CAAC;AACD,SAAO;AACT;AAEO,SAAS,sBAAsB,MAAM,QAAQ,KAAK;AAKvD,YAAU,IAAI,EAAE,QAAQ,WAAS;AAC/B,QAAI,gBAAgB,KAAK,GAAG;AAC1B,WAAK,SAAS,CAAC,KAAK;AACpB,WAAK,WAAW,CAAC;AAAA,IACnB;AAAA,EACF,CAAC;AASD,MAAI,UAAU,IAAI,EAAE,KAAK,YAAY,GAAG;AACtC,QAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,SAAS,GAAG;AACzC,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAUA,MAAI,KAAK,UAAU,KAAK,OAAO,UAAU,UAAU,IAAI,EAAE,MAAM,WAAW,GAAG;AAC3E,QACE,YAAY,MAAM,EAAE,IAAI,EAErB,OAAO,YAAY,EACnB,KAAK,SAAS,GACjB;AACA,aAAO,OAAO,SAAS,GAAG;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO,QAAQ,KAAK,QAAQ,EAAE;AAAA,IAAQ,CAAC,CAACA,MAAK,KAAK,MAChD,sBAAsB,OAAO,MAAMA,IAAG;AAAA,EACxC;AACF;AAEO,SAAS,qBAAqB,MAAM,SAAS;AAClD,QAAM,SAAS,UAAU,IAAI;AAC7B,MAAI,OAAO,UAAU,OAAO,MAAM,WAAW,GAAG;AAC9C,UAAM,eAAe,IAAI;AAAA,MACvB,UAAU,CAAC,CAAC,EAAE,OAAO,IAAI,OAAK,EAAE,OAAO,aAAa,CAAC;AAAA,IACvD;AACA,UAAM,gBAAgB,CAAC,GAAG,YAAY;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,WAAO;AAAA,MACL,IAAI;AAAA,QACF;AAAA,UACE,GAAG;AAAA,UACH,QAAQ,EAAE,cAAc;AAAA,QAC1B;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO;AAAA,MACL,OAAO,OAAO,CAAC,KAAK,UAAU;AAC5B,gBAAQ,MAAM,SAAS;AAAA,UACrB,KAAK;AACH,mBAAO,IAAI;AAAA,cACT,IAAI,8BAA8B,OAAO,OAAO;AAAA,YAClD;AAAA,UACF,KAAK;AACH,mBAAO,IAAI,OAAO,IAAI,wBAAwB,OAAO,OAAO,CAAC;AAAA,UAC/D;AACE,mBAAO,IAAI,OAAO,IAAI,uBAAuB,OAAO,OAAO,CAAC;AAAA,QAChE;AAAA,MACF,GAAG,CAAC,CAAC;AAAA,IACP,EAAE,YAAY,IAAI,EAAE,IAAI,WAAS,qBAAqB,OAAO,OAAO,CAAC,CAAC;AAAA,EACxE;AACF;AAEA,IAAO,kBAAQ,CAAC,WAAW,YAAY;AACrC,QAAM,OAAO,SAAS,aAAa,CAAC,CAAC;AACrC,wBAAsB,IAAI;AAC1B,SAAO,qBAAqB,MAAM,OAAO;AAC3C;", "names": ["key"]}