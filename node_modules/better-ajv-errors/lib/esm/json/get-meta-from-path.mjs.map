{"version": 3, "sources": ["../../../src/json/get-meta-from-path.js"], "sourcesContent": ["import { getPointers } from './utils';\n\nexport default function getMetaFromPath(\n  jsonAst,\n  dataPath,\n  includeIdentifierLocation\n) {\n  const pointers = getPointers(dataPath);\n  const lastPointerIndex = pointers.length - 1;\n  return pointers.reduce((obj, pointer, idx) => {\n    switch (obj.type) {\n      case 'Object': {\n        const filtered = obj.members.filter(\n          child => child.name.value === pointer\n        );\n        if (filtered.length !== 1) {\n          throw new Error(`Couldn't find property ${pointer} of ${dataPath}`);\n        }\n\n        const { name, value } = filtered[0];\n        return includeIdentifierLocation && idx === lastPointerIndex\n          ? name\n          : value;\n      }\n      case 'Array':\n        return obj.elements[pointer];\n      default:\n        console.log(obj);\n    }\n  }, jsonAst.body);\n}\n"], "mappings": ";AAAA,SAAS,mBAAmB;AAEb,SAAR,gBACL,SACA,UACA,2BACA;AACA,QAAM,WAAW,YAAY,QAAQ;AACrC,QAAM,mBAAmB,SAAS,SAAS;AAC3C,SAAO,SAAS,OAAO,CAAC,KAAK,SAAS,QAAQ;AAC5C,YAAQ,IAAI,MAAM;AAAA,MAChB,KAAK,UAAU;AACb,cAAM,WAAW,IAAI,QAAQ;AAAA,UAC3B,WAAS,MAAM,KAAK,UAAU;AAAA,QAChC;AACA,YAAI,SAAS,WAAW,GAAG;AACzB,gBAAM,IAAI,MAAM,0BAA0B,OAAO,OAAO,QAAQ,EAAE;AAAA,QACpE;AAEA,cAAM,EAAE,MAAM,MAAM,IAAI,SAAS,CAAC;AAClC,eAAO,6BAA6B,QAAQ,mBACxC,OACA;AAAA,MACN;AAAA,MACA,KAAK;AACH,eAAO,IAAI,SAAS,OAAO;AAAA,MAC7B;AACE,gBAAQ,IAAI,GAAG;AAAA,IACnB;AAAA,EACF,GAAG,QAAQ,IAAI;AACjB;", "names": []}