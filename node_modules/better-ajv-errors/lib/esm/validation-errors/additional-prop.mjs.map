{"version": 3, "sources": ["../../../src/validation-errors/additional-prop.js"], "sourcesContent": ["import chalk from 'chalk';\nimport BaseValidationError from './base';\n\nexport default class AdditionalPropValidationError extends BaseValidationError {\n  constructor(...args) {\n    super(...args);\n    this.options.isIdentifierLocation = true;\n  }\n\n  print() {\n    const { message, params } = this.options;\n    const output = [chalk`{red {bold ADDTIONAL PROPERTY} ${message}}\\n`];\n\n    return output.concat(\n      this.getCodeFrame(\n        chalk`😲  {magentaBright ${params.additionalProperty}} is not expected to be here!`,\n        `${this.instancePath}/${params.additionalProperty}`\n      )\n    );\n  }\n\n  getError() {\n    const { params } = this.options;\n\n    return {\n      ...this.getLocation(`${this.instancePath}/${params.additionalProperty}`),\n      error: `${this.getDecoratedPath()} Property ${\n        params.additionalProperty\n      } is not expected to be here`,\n      path: this.instancePath,\n    };\n  }\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAO,yBAAyB;AAEhC,IAAqB,gCAArB,cAA2D,oBAAoB;AAAA,EAC7E,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,SAAK,QAAQ,uBAAuB;AAAA,EACtC;AAAA,EAEA,QAAQ;AACN,UAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AACjC,UAAM,SAAS,CAAC,uCAAuC,OAAO,KAAK;AAEnE,WAAO,OAAO;AAAA,MACZ,KAAK;AAAA,QACH,2BAA2B,OAAO,kBAAkB;AAAA,QACpD,GAAG,KAAK,YAAY,IAAI,OAAO,kBAAkB;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW;AACT,UAAM,EAAE,OAAO,IAAI,KAAK;AAExB,WAAO;AAAA,MACL,GAAG,KAAK,YAAY,GAAG,KAAK,YAAY,IAAI,OAAO,kBAAkB,EAAE;AAAA,MACvE,OAAO,GAAG,KAAK,iBAAiB,CAAC,aAC/B,OAAO,kBACT;AAAA,MACA,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AACF;", "names": []}