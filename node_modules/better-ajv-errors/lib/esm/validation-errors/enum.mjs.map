{"version": 3, "sources": ["../../../src/validation-errors/enum.js"], "sourcesContent": ["import chalk from 'chalk';\nimport leven from 'leven';\nimport pointer from 'jsonpointer';\nimport BaseValidationError from './base';\n\nexport default class EnumValidationError extends BaseValidationError {\n  print() {\n    const {\n      message,\n      params: { allowedValues },\n    } = this.options;\n    const bestMatch = this.findBestMatch();\n\n    const output = [\n      chalk`{red {bold ENUM} ${message}}`,\n      chalk`{red (${allowedValues.join(', ')})}\\n`,\n    ];\n\n    return output.concat(\n      this.getCodeFrame(\n        bestMatch !== null\n          ? chalk`👈🏽  Did you mean {magentaBright ${bestMatch}} here?`\n          : chalk`👈🏽  Unexpected value, should be equal to one of the allowed values`\n      )\n    );\n  }\n\n  getError() {\n    const { message, params } = this.options;\n    const bestMatch = this.findBestMatch();\n    const allowedValues = params.allowedValues.join(', ');\n\n    const output = {\n      ...this.getLocation(),\n      error: `${this.getDecoratedPath()} ${message}: ${allowedValues}`,\n      path: this.instancePath,\n    };\n\n    if (bestMatch !== null) {\n      output.suggestion = `Did you mean ${bestMatch}?`;\n    }\n\n    return output;\n  }\n\n  findBestMatch() {\n    const {\n      params: { allowedValues },\n    } = this.options;\n\n    const currentValue =\n      this.instancePath === ''\n        ? this.data\n        : pointer.get(this.data, this.instancePath);\n\n    if (!currentValue) {\n      return null;\n    }\n\n    const bestMatch = allowedValues\n      .map(value => ({\n        value,\n        weight: leven(value, currentValue.toString()),\n      }))\n      .sort((x, y) =>\n        x.weight > y.weight ? 1 : x.weight < y.weight ? -1 : 0\n      )[0];\n\n    return allowedValues.length === 1 ||\n      bestMatch.weight < bestMatch.value.length\n      ? bestMatch.value\n      : null;\n  }\n}\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAO,WAAW;AAClB,OAAO,aAAa;AACpB,OAAO,yBAAyB;AAEhC,IAAqB,sBAArB,cAAiD,oBAAoB;AAAA,EACnE,QAAQ;AACN,UAAM;AAAA,MACJ;AAAA,MACA,QAAQ,EAAE,cAAc;AAAA,IAC1B,IAAI,KAAK;AACT,UAAM,YAAY,KAAK,cAAc;AAErC,UAAM,SAAS;AAAA,MACb,yBAAyB,OAAO;AAAA,MAChC,cAAc,cAAc,KAAK,IAAI,CAAC;AAAA,IACxC;AAEA,WAAO,OAAO;AAAA,MACZ,KAAK;AAAA,QACH,cAAc,OACV,0CAA0C,SAAS,YACnD;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW;AACT,UAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AACjC,UAAM,YAAY,KAAK,cAAc;AACrC,UAAM,gBAAgB,OAAO,cAAc,KAAK,IAAI;AAEpD,UAAM,SAAS;AAAA,MACb,GAAG,KAAK,YAAY;AAAA,MACpB,OAAO,GAAG,KAAK,iBAAiB,CAAC,IAAI,OAAO,KAAK,aAAa;AAAA,MAC9D,MAAM,KAAK;AAAA,IACb;AAEA,QAAI,cAAc,MAAM;AACtB,aAAO,aAAa,gBAAgB,SAAS;AAAA,IAC/C;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,gBAAgB;AACd,UAAM;AAAA,MACJ,QAAQ,EAAE,cAAc;AAAA,IAC1B,IAAI,KAAK;AAET,UAAM,eACJ,KAAK,iBAAiB,KAClB,KAAK,OACL,QAAQ,IAAI,KAAK,MAAM,KAAK,YAAY;AAE9C,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,cACf,IAAI,YAAU;AAAA,MACb;AAAA,MACA,QAAQ,MAAM,OAAO,aAAa,SAAS,CAAC;AAAA,IAC9C,EAAE,EACD;AAAA,MAAK,CAAC,GAAG,MACR,EAAE,SAAS,EAAE,SAAS,IAAI,EAAE,SAAS,EAAE,SAAS,KAAK;AAAA,IACvD,EAAE,CAAC;AAEL,WAAO,cAAc,WAAW,KAC9B,UAAU,SAAS,UAAU,MAAM,SACjC,UAAU,QACV;AAAA,EACN;AACF;", "names": []}