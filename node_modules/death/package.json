{"name": "death", "version": "1.1.0", "description": "Gracefully cleanup when termination signals are sent to your process.", "repository": {"type": "git", "url": "**************:jprichardson/node-death.git"}, "keywords": ["sigint", "sigterm", "sigkill", "sig<PERSON><PERSON>", "exception", "kill", "terminate", "process", "clean"], "author": "<PERSON> <jp<PERSON><PERSON><EMAIL>>", "licenses": [{"type": "MIT", "url": ""}], "dependencies": {}, "devDependencies": {"win-spawn": "~1.1.1", "autoresolve": "0.0.3", "testutil": "~0.4.0", "colors": "~0.6.0-1"}, "main": "./lib/death.js", "scripts": {"test": "mocha test"}}