'use strict';

const utils = require('../utils');

const define = (key, fn) => {
  utils.defineExport(exports, key, fn);
  utils.defineExport(exports, key.toLowerCase(), fn);
};

define('AutoComplete', () => require('./autocomplete'));
define('BasicAuth', () => require('./basicauth'));
define('Confirm', () => require('./confirm'));
define('Editable', () => require('./editable'));
define('Form', () => require('./form'));
define('Input', () => require('./input'));
define('Invisible', () => require('./invisible'));
define('List', () => require('./list'));
define('MultiSelect', () => require('./multiselect'));
define('Numeral', () => require('./numeral'));
define('Password', () => require('./password'));
define('Scale', () => require('./scale'));
define('Select', () => require('./select'));
define('Snippet', () => require('./snippet'));
define('Sort', () => require('./sort'));
define('Survey', () => require('./survey'));
define('Text', () => require('./text'));
define('Toggle', () => require('./toggle'));
define('Quiz', () => require('./quiz'));
