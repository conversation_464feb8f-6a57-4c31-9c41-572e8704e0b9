{"name": "escodegen", "description": "ECMAScript code generator", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "bin": {"esgenerate": "./bin/esgenerate.js", "escodegen": "./bin/escodegen.js"}, "files": ["LICENSE.BSD", "LICENSE.source-map", "README.md", "bin", "escodegen.js", "package.json"], "version": "1.8.1", "engines": {"node": ">=0.12.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://github.com/Constellation"}], "repository": {"type": "git", "url": "http://github.com/estools/escodegen.git"}, "dependencies": {"estraverse": "^1.9.1", "esutils": "^2.0.2", "esprima": "^2.7.1", "optionator": "^0.8.1"}, "optionalDependencies": {"source-map": "~0.2.0"}, "devDependencies": {"acorn": "^2.7.0", "bluebird": "^2.3.11", "bower-registry-client": "^0.2.1", "chai": "^1.10.0", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^0.2.0", "gulp-mocha": "^2.0.0", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint", "release": "node tools/release.js", "build-min": "./node_modules/.bin/cjsify -ma path: tools/entry-point.js > escodegen.browser.min.js", "build": "./node_modules/.bin/cjsify -a path: tools/entry-point.js > escodegen.browser.js"}}