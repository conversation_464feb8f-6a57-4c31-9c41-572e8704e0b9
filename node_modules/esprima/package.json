{"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.7.3", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.10.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://ariya.ofilabs.com"}], "repository": {"type": "git", "url": "https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "eslint": "~1.7.2", "everything.js": "~1.0.3", "glob": "^5.0.15", "istanbul": "~0.4.0", "jscs": "~2.3.5", "json-diff": "~0.3.1", "karma": "^0.13.11", "karma-chrome-launcher": "^0.2.1", "karma-detect-browsers": "^2.0.2", "karma-firefox-launcher": "^0.1.6", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.0", "karma-safari-launcher": "^0.1.1", "karma-sauce-launcher": "^0.2.14", "lodash": "^3.10.0", "mocha": "^2.3.3", "node-tick-processor": "~0.0.2", "regenerate": "~1.2.1", "temp": "~0.8.3", "unicode-7.0.0": "~0.1.5"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "jscs": "jscs -p crockford esprima.js && jscs -p crockford test/*.js", "eslint": "node node_modules/eslint/bin/eslint.js -c .lintrc esprima.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run jscs && npm run eslint && npm run complexity", "unit-tests": "node test/unit-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run grammar-tests && npm run regression-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "test": "npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run all-tests && npm run browser-tests && npm run dynamic-analysis", "droneio": "npm test && npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "generate-regex": "node tools/generate-identifier-regex.js"}}