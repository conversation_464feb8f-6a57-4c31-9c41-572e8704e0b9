import type { Artifacts, EIP1193<PERSON>rovider, EthereumProvider, HardhatConfig, HDAccountsUserConfig, HttpNetworkAccountsUserConfig, NetworkConfig, ProviderExtender } from "../../../types";
export declare function isHDAccountsConfig(accounts?: HttpNetworkAccountsUserConfig): accounts is HDAccountsUserConfig;
export declare function createProvider(config: HardhatConfig, networkName: string, artifacts?: Artifacts, extenders?: ProviderExtender[]): Promise<EthereumProvider>;
export declare function applyProviderWrappers(provider: EIP1193Provider, netConfig: Partial<NetworkConfig>, extenders: ProviderExtender[]): EIP1193Provider;
//# sourceMappingURL=construction.d.ts.map