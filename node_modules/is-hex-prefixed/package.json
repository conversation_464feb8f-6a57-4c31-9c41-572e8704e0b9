{"name": "is-hex-prefixed", "version": "1.0.0", "description": "A simple method to check if a string is hex prefixed.", "main": "src/index.js", "scripts": {"test": "mocha src/tests/**/**.js", "test-travis": "node ./node_modules/istanbul/lib/cli.js cover ./node_modules/mocha/bin/_mocha -- src/tests/**/*.js", "coveralls": "npm run test-travis && cat ./coverage/lcov.info | coveralls"}, "repository": {"type": "git", "url": "git+ssh://**************/SilentCicero/is-hex-prefixed.git"}, "engines": {"npm": ">=3", "node": ">=6.5.0"}, "keywords": ["is", "hex", "prefixed", "prefix", "checker", "method"], "author": "<PERSON> <then<PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "bugs": {"url": "https://github.com/SilentCicero/is-hex-prefixed/issues"}, "homepage": "https://github.com/SilentCicero/is-hex-prefixed#readme", "devDependencies": {"chai": "3.5.0", "mocha": "3.2.0", "istanbul": "0.4.5", "coveralls": "2.11.9"}}