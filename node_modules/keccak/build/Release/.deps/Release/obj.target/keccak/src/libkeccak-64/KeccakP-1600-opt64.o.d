cmd_Release/obj.target/keccak/src/libkeccak-64/KeccakP-1600-opt64.o := cc -o Release/obj.target/keccak/src/libkeccak-64/KeccakP-1600-opt64.o ../src/libkeccak-64/KeccakP-1600-opt64.c '-DNODE_GYP_MODULE_NAME=keccak' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPEN<PERSON>L_THREADS' -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/src -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/v8/include  -O3 -gdwarf-2 -mmacosx-version-min=10.13 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/keccak/src/libkeccak-64/KeccakP-1600-opt64.o.d.raw   -c
Release/obj.target/keccak/src/libkeccak-64/KeccakP-1600-opt64.o: \
  ../src/libkeccak-64/KeccakP-1600-opt64.c \
  ../src/libkeccak-64/brg_endian.h \
  ../src/libkeccak-64/KeccakP-1600-opt64-config.h \
  ../src/libkeccak-64/KeccakP-1600-64.macros \
  ../src/libkeccak-64/KeccakP-1600-unrolling.macros \
  ../src/libkeccak-64/SnP-Relaned.h
../src/libkeccak-64/KeccakP-1600-opt64.c:
../src/libkeccak-64/brg_endian.h:
../src/libkeccak-64/KeccakP-1600-opt64-config.h:
../src/libkeccak-64/KeccakP-1600-64.macros:
../src/libkeccak-64/KeccakP-1600-unrolling.macros:
../src/libkeccak-64/SnP-Relaned.h:
