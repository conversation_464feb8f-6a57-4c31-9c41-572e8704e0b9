# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v3.1.3](https://github.com/browserify/pbkdf2/compare/v3.1.2...v3.1.3) - 2025-06-20

### Commits

- Only apps should have lockfiles [`8b06730`](https://github.com/browserify/pbkdf2/commit/8b067308eedcc3b7da3e06202d437ff532de75bd)
- [lint] fix whitespace [`9a76e2f`](https://github.com/browserify/pbkdf2/commit/9a76e2f37ef4541aeff92d97b7335b7fa6e989a8)
- [lint] fix parens/curlies/semis/etc [`6fd84bf`](https://github.com/browserify/pbkdf2/commit/6fd84bf64a423d207d9e665f3e9fd88b148c4480)
- [meta] add `auto-changelog` [`796c38d`](https://github.com/browserify/pbkdf2/commit/796c38d428ed9546e7ddfbb38322faa99921d716)
- [Tests] fix tests in node 17 [`3661fb0`](https://github.com/browserify/pbkdf2/commit/3661fb0156177bfe380c7bcc133917e7ee501bc7)
- Revert "[Tests] fix tests in node &lt; 3" [`7431b57`](https://github.com/browserify/pbkdf2/commit/7431b5766805cbf880c1b3d0f38d484357844ff7)
- [Tests] fix tests in node &lt; 3 [`eb9f97a`](https://github.com/browserify/pbkdf2/commit/eb9f97a66ed83836bebc4ff563a1588248708501)
- [Fix] ensure unknown algorithms throw + known ones match node [`26d4fd3`](https://github.com/browserify/pbkdf2/commit/26d4fd391e00ed806840ea1ef7341937985f4ba8)
- [Tests] add GHA, always run nyc [`513906a`](https://github.com/browserify/pbkdf2/commit/513906a735e80a0e125e1a3363c5dbe90bb34bb7)
- [lint] fix a few more rules [`ab04da8`](https://github.com/browserify/pbkdf2/commit/ab04da834a0342cb3779486314fa67ebc1e3f035)
- [lint] switch to eslint [`89694cf`](https://github.com/browserify/pbkdf2/commit/89694cf7e4062a8c924fbe74ad2b2d3cf78f1715)
- [Tests] add coverage [`d0d534b`](https://github.com/browserify/pbkdf2/commit/d0d534bfdc4db6d6ea9f719606ad18a78db6c124)
- [Refactor] use `to-buffer` [`e3102a8`](https://github.com/browserify/pbkdf2/commit/e3102a8cd4830a3ac85cd0dd011cc002fdde33bb)
- [readme] improve badges [`fca0c9d`](https://github.com/browserify/pbkdf2/commit/fca0c9d4c5b053d911315a3607367d1db9f9f605)
- [Tests] remove unused travis file [`a2c7d93`](https://github.com/browserify/pbkdf2/commit/a2c7d93bbcad73c49dad0c18fef6b8a4af8c3310)
- [meta] switch from `files` to `npmignore` [`7f31fbc`](https://github.com/browserify/pbkdf2/commit/7f31fbca528fa5af3b7d3910daf09a7e5ed8adb4)
- [Tests] use .nycrc [`8d628e8`](https://github.com/browserify/pbkdf2/commit/8d628e8d5559b4acd4d4a714afb04a2990f901b7)
- [Refactor] minor tweaks [`fc61005`](https://github.com/browserify/pbkdf2/commit/fc61005c8cbd480e24ee103ac39a45f0d90ac636)
- [Deps] update `create-hmac`, `safe-buffer`, `sha.js` [`ae2a7d0`](https://github.com/browserify/pbkdf2/commit/ae2a7d051c9133c25d5212ba4f46747c0ed4c190)
- [Fix] pin `create-hash`, `ripemd160` due to breaking changes [`e079968`](https://github.com/browserify/pbkdf2/commit/e07996890af79296f1913431fb3b252e25df2f0e)
- [Tests] fix tests in node 3 [`45fbcf3`](https://github.com/browserify/pbkdf2/commit/45fbcf30431f9585ae3b61f2207c0a16c1953915)
- [meta] skip publishing benchmarks [`19ea57b`](https://github.com/browserify/pbkdf2/commit/19ea57bf11d055ddcd97d2556cd3097663b050b9)
- [Dev Deps] add missing peer dep [`645e252`](https://github.com/browserify/pbkdf2/commit/645e252375087e97beb95e518038b99eb5724c5b)

## [v3.1.2](https://github.com/browserify/pbkdf2/compare/v3.1.1...v3.1.2) - 2021-04-09

### Commits

- handle nextTick [`36457b9`](https://github.com/browserify/pbkdf2/commit/36457b9fecfd1e128c8e9884a57e277649679832)
- Check for process before accessing [`449e510`](https://github.com/browserify/pbkdf2/commit/449e510f861f56ed673613d8102f6b1a3e680ced)
- handle different returns [`fb82137`](https://github.com/browserify/pbkdf2/commit/fb82137ecefb56b5e93b70686e0dc0b4bb591b7c)

## [v3.1.1](https://github.com/browserify/pbkdf2/compare/v3.1.0...v3.1.1) - 2020-06-04

### Commits

- style nit [`6d994f2`](https://github.com/browserify/pbkdf2/commit/6d994f2e71934c26f96791bde440e86d9d93ff2b)
- remove arrow [`70ea270`](https://github.com/browserify/pbkdf2/commit/70ea2709b761b8fbb0551f4411feb52efb9c8690)

## [v3.1.0](https://github.com/browserify/pbkdf2/compare/v3.0.17...v3.1.0) - 2020-06-03

### Merged

- check if process.version is available [`#85`](https://github.com/browserify/pbkdf2/pull/85)

### Commits

- fix style [`1674cbd`](https://github.com/browserify/pbkdf2/commit/1674cbdb359ae166e6c99733e0af40e92ebb3cd4)
- add support for all ArrayBuffers [`b888820`](https://github.com/browserify/pbkdf2/commit/b88882005853ad5d8ecbc00a56e101c336b5ea04)
- Build type check into toBuffer [`0b77ea9`](https://github.com/browserify/pbkdf2/commit/0b77ea96191a374fa6774ebaa4fe89be86c3d1af)
- fix fixtures [`6af65e7`](https://github.com/browserify/pbkdf2/commit/6af65e797f5c0738fd9f4e9dfa9b00ae0885f5d7)
- Add tests for Uint8Array [`bca4182`](https://github.com/browserify/pbkdf2/commit/bca4182a30ce5d73ed527fa254c6d3a62e07e8f8)
- fix encodings [`b1c669b`](https://github.com/browserify/pbkdf2/commit/b1c669b6dfa9a0f82cecdec7bd4a14a042d9a070)
- minor fixes for Uint8Array support [`f61abc7`](https://github.com/browserify/pbkdf2/commit/f61abc7596d40540d5d759bc583044082c52badc)
- fix tests [`ab62504`](https://github.com/browserify/pbkdf2/commit/ab6250458dac29f4f9d7144e903e18726bed8535)
- Add Uint8Array support [`3fbb793`](https://github.com/browserify/pbkdf2/commit/3fbb7935e2aa810a5ed861ef32c44092fb3fafbb)
- adjust node versions [`a572cbc`](https://github.com/browserify/pbkdf2/commit/a572cbc655a1cc626ae72ccf8bb29f25418d394e)
- update travis [`4a4deed`](https://github.com/browserify/pbkdf2/commit/4a4deed4d115d55a698cf1292abef4f38cdbe922)
- fix standard typo (that was not caught locally for some reason) [`47c59ad`](https://github.com/browserify/pbkdf2/commit/47c59ad879f5fcce529a69ed902ca9b664813020)

## [v3.0.17](https://github.com/browserify/pbkdf2/compare/v3.0.16...v3.0.17) - 2018-09-25

### Commits

- Add ripemd160 test [`9da9d9c`](https://github.com/browserify/pbkdf2/commit/9da9d9c4f426237e72b8f189ae14e3e473c84c69)
- Do not inline rmd160Func [`b498c9b`](https://github.com/browserify/pbkdf2/commit/b498c9b779c40823fc504764c9a333fd44c79f31)
- Fix RIPEMD160 support [`68729a6`](https://github.com/browserify/pbkdf2/commit/68729a6b54e1438e1960931fd6aa3bb343fcc173)

## [v3.0.16](https://github.com/browserify/pbkdf2/compare/v3.0.15...v3.0.16) - 2018-04-20

### Merged

- default to SHA1 for native [`#76`](https://github.com/browserify/pbkdf2/pull/76)

### Commits

- fix standard indentation [`740d93e`](https://github.com/browserify/pbkdf2/commit/740d93e2f5798bc66d32d3a648ec2d96f7254c12)
- tests: fix JS/sync tests not being run [`1416637`](https://github.com/browserify/pbkdf2/commit/1416637d82848494bd427a38643a58795213191f)
- increase sync.js internal coverage [`4e07660`](https://github.com/browserify/pbkdf2/commit/4e076601b82e518cac6179ea62cc4f3c4553e43b)
- travis: add 8 and 9 [`d82c51b`](https://github.com/browserify/pbkdf2/commit/d82c51bb04897f099512b3add9f892a36a71cedc)

## [v3.0.15](https://github.com/browserify/pbkdf2/compare/v3.0.14...v3.0.15) - 2018-04-01

### Merged

- Throw if password / salt is not a buffer or string [`#77`](https://github.com/browserify/pbkdf2/pull/77)
- Update README.md for async recommendation [`#75`](https://github.com/browserify/pbkdf2/pull/75)

### Commits

- throw if password / salt is not a buffer or string [`2410eb0`](https://github.com/browserify/pbkdf2/commit/2410eb0ee1f41da47d1ba2e53411b489d88838ce)
- typos [`9dae437`](https://github.com/browserify/pbkdf2/commit/9dae437df90f5940eb332a5c6f86f78309f79f06)
- Update README.md [`ad85843`](https://github.com/browserify/pbkdf2/commit/ad8584337742d4cf83d9244a73abce2a7af90ff2)

## [v3.0.14](https://github.com/browserify/pbkdf2/compare/v3.0.13...v3.0.14) - 2017-09-08

### Merged

- use the local implementations if crypto.bpkdf2Sync [`#72`](https://github.com/browserify/pbkdf2/pull/72)

### Commits

- use the local implementations if *either* crypto.bpkdf2Sync doesn't exist OR it doesn't contain the marker string [`9132b7a`](https://github.com/browserify/pbkdf2/commit/9132b7a81540a5743ece086bea87ada10028e05f)

## [v3.0.13](https://github.com/browserify/pbkdf2/compare/v3.0.12...v3.0.13) - 2017-08-02

### Merged

- fail better when node version loaded in browser [`#71`](https://github.com/browserify/pbkdf2/pull/71)
- remove unnecessary r, T values [`#68`](https://github.com/browserify/pbkdf2/pull/68)

### Commits

- tests: add dkLen &lt; and &gt; [`c3d2998`](https://github.com/browserify/pbkdf2/commit/c3d29981a600cd30fa57849ce12bf95d45bb4b8f)
- align definitions with assignment, formatting [`5c50528`](https://github.com/browserify/pbkdf2/commit/5c5052859b7ea4dba9484ce4ad0c1edfb507790b)
- lib/sync*: avoid inner-loop T copy [`74b80d3`](https://github.com/browserify/pbkdf2/commit/74b80d32640d1c90a4a6a90d99784dadc9d2d880)
- sync: align definitions with assignment [`c818cf1`](https://github.com/browserify/pbkdf2/commit/c818cf1bf6b7c23278ebd8196ebddc6b54c92c8c)
- remove unused r variable [`9e0d168`](https://github.com/browserify/pbkdf2/commit/9e0d1684abca98e08a1334b0ead141ebf6c91806)
- travis: drop 0.12 [`0985c3e`](https://github.com/browserify/pbkdf2/commit/0985c3e03cb4392a5f931c4a62869257f3feaeba)
- sync-browser: consistent exports [`90ac748`](https://github.com/browserify/pbkdf2/commit/90ac7488016ad97023d3b42795dc1da00b310490)
- just remove microtime [`0fa9638`](https://github.com/browserify/pbkdf2/commit/0fa9638dad5384d7afb8943c1e22d2e4a16957d4)
- standard --fix [`8281fc6`](https://github.com/browserify/pbkdf2/commit/8281fc64b281bacab50f4b735aa768474eaaadcd)

## [v3.0.12](https://github.com/browserify/pbkdf2/compare/v3.0.11...v3.0.12) - 2017-05-11

### Merged

- fix #60 - use browser.js in the browser, instead of index.js; include lib folder in the published package [`#62`](https://github.com/browserify/pbkdf2/pull/62)

### Fixed

- fix #60 - use browser.js in the browser, instead of index.js; include lib folder in the published package (#62) [`#60`](https://github.com/browserify/pbkdf2/issues/60)

### Commits

- switch to optional dep #63, 65 [`7d246e9`](https://github.com/browserify/pbkdf2/commit/7d246e91bbdaedbb55fa0c323306cbcc58d9ec73)

## [v3.0.11](https://github.com/browserify/pbkdf2/compare/v3.0.10...v3.0.11) - 2017-05-11

### Commits

- update files [`3313867`](https://github.com/browserify/pbkdf2/commit/****************************************)

## [v3.0.10](https://github.com/browserify/pbkdf2/compare/v3.0.9...v3.0.10) - 2017-05-11

### Merged

- node for the node, js for the browser [`#59`](https://github.com/browserify/pbkdf2/pull/59)
- real optimizations [`#57`](https://github.com/browserify/pbkdf2/pull/57)
- benchmark [`#56`](https://github.com/browserify/pbkdf2/pull/56)
- do our own hmacing [`#54`](https://github.com/browserify/pbkdf2/pull/54)

### Commits

- use native crypto when available [`cdaf5c0`](https://github.com/browserify/pbkdf2/commit/cdaf5c0da8502c47a40d2b2caaafb497836e3b42)
- actually add bench [`fa2e0a1`](https://github.com/browserify/pbkdf2/commit/fa2e0a106adf8f2b2529bb9912f16d07f061cb17)
- passing tests [`216a6a7`](https://github.com/browserify/pbkdf2/commit/216a6a7d137ba5a363d1be9c3a7a42d47398429e)
- hmac ourselves [`9699045`](https://github.com/browserify/pbkdf2/commit/9699045c37a07f8319cfb8d44e2ff4252d7a7078)
- use hashes directly [`89e0e80`](https://github.com/browserify/pbkdf2/commit/89e0e80dbce67e16b8946deb38dc755aeea78222)
- correctly test both [`fb56873`](https://github.com/browserify/pbkdf2/commit/fb56873fa885059a8c17410eba0b544e5932cd28)
- avoid Buffer.concat [`96518aa`](https://github.com/browserify/pbkdf2/commit/96518aac66af803a0040e3bd1ca48b2828f38881)
- formatting [`dd3467c`](https://github.com/browserify/pbkdf2/commit/dd3467c75abd0136fc93f2acdbf1df94af10924f)
- some fixes [`15b2cbf`](https://github.com/browserify/pbkdf2/commit/15b2cbfd9c3575593fc147dbd170e23c293a6f12)
- cleanup [`fec6652`](https://github.com/browserify/pbkdf2/commit/fec66520e38de3b2fccaf924dd61807bf500ad36)
- formating [`dc2e15a`](https://github.com/browserify/pbkdf2/commit/dc2e15aa0f25b091a94fd575eb0edbd41c2300b0)
- fix coverage [`ae3f2a5`](https://github.com/browserify/pbkdf2/commit/ae3f2a5d0b475d0f4fb5a38b7ce6c9206a4a0c9b)
- move stuff around [`52c3960`](https://github.com/browserify/pbkdf2/commit/52c3960b217a9933e8ef8a833b9a60141ddbd70a)
- 100% [`ef38b2b`](https://github.com/browserify/pbkdf2/commit/ef38b2b9bb02bb4167689ab8955a0b323c03d644)
- trailing space [`03832fb`](https://github.com/browserify/pbkdf2/commit/03832fbdd47fb354329bdbb51c463ed60033e2ea)
- reduce coverage [`2f720b4`](https://github.com/browserify/pbkdf2/commit/2f720b4b4d3441f743d1637a479cda596c39b4a9)
- switch to optional dep [`8423738`](https://github.com/browserify/pbkdf2/commit/8423738759f67929760c828cadd88c5867d4e5f2)
- fix style [`5b33204`](https://github.com/browserify/pbkdf2/commit/5b33204d0172b06d7d93238ef72a1240b3e3f3c6)

## [v3.0.9](https://github.com/browserify/pbkdf2/compare/v3.0.8...v3.0.9) - 2016-10-05

### Merged

- protect 0.10 users by throwing [`#47`](https://github.com/browserify/pbkdf2/pull/47)

### Commits

- warn 0.10 users by throwing [`5e63f63`](https://github.com/browserify/pbkdf2/commit/5e63f63c149182f617b209d0419c408691b918d7)

## [v3.0.8](https://github.com/browserify/pbkdf2/compare/v3.0.7...v3.0.8) - 2016-09-22

### Fixed

- package: add engines [`#46`](https://github.com/browserify/pbkdf2/issues/46)

### Commits

- package: 1 too many indentations [`bffd057`](https://github.com/browserify/pbkdf2/commit/bffd0578fd2eb892a36cfd830fadc535deae9a49)
- package: 90% branch coverage is fine for now [`c3d57f9`](https://github.com/browserify/pbkdf2/commit/c3d57f96666f556034b8b59839b28b7fa7c342e9)

## [v3.0.7](https://github.com/browserify/pbkdf2/compare/v3.0.6...v3.0.7) - 2016-09-16

### Merged

- browser: avoid process.version split before process.browser [`#44`](https://github.com/browserify/pbkdf2/pull/44)

## [v3.0.6](https://github.com/browserify/pbkdf2/compare/v3.0.5...v3.0.6) - 2016-09-14

### Merged

- tests: fix UTF-8 encoding [`#42`](https://github.com/browserify/pbkdf2/pull/42)
- Add browserify dependency, rename index.html to bundle.html [`#41`](https://github.com/browserify/pbkdf2/pull/41)
- package: add browser.js [`#39`](https://github.com/browserify/pbkdf2/pull/39)

### Commits

- add browserify and remove mocha from test/index.html [`f28965d`](https://github.com/browserify/pbkdf2/commit/f28965d0a494e9f6f95f616bb3f608d3a15bff9c)
- rename index.html to bundle.html [`7d850d2`](https://github.com/browserify/pbkdf2/commit/7d850d247a93122a5e1b60fabddf64f7958cfeeb)

## [v3.0.5](https://github.com/browserify/pbkdf2/compare/3.0.4...v3.0.5) - 2016-09-12

### Merged

- use tape instead of mocha [`#35`](https://github.com/browserify/pbkdf2/pull/35)
- tests: remove binary assumption, fixes #34 [`#36`](https://github.com/browserify/pbkdf2/pull/36)
- README: add documentation, close #31 [`#33`](https://github.com/browserify/pbkdf2/pull/33)
- fix coverage [`#32`](https://github.com/browserify/pbkdf2/pull/32)
- [trivial] update README.md [`#28`](https://github.com/browserify/pbkdf2/pull/28)
- [trivial] rename async-shim.js to node-shim-async.js [`#29`](https://github.com/browserify/pbkdf2/pull/29)
- update travis & standard [`#24`](https://github.com/browserify/pbkdf2/pull/24)
- browser: delay async computation, fixes #13 [`#14`](https://github.com/browserify/pbkdf2/pull/14)

### Fixed

- Merge pull request #36 from crypto-browserify/testfix [`#34`](https://github.com/browserify/pbkdf2/issues/34)
- tests: remove binary assumption, fixes #34 [`#34`](https://github.com/browserify/pbkdf2/issues/34)
- Merge pull request #33 from crypto-browserify/dcousens-patch-1 [`#31`](https://github.com/browserify/pbkdf2/issues/31)
- README: add documentation, close #31 [`#31`](https://github.com/browserify/pbkdf2/issues/31)
- README: fix pbkdf2 spelling, resolves #20 [`#20`](https://github.com/browserify/pbkdf2/issues/20)
- Merge pull request #14 from crypto-browserify/dcousens-patch-1 [`#13`](https://github.com/browserify/pbkdf2/issues/13)
- browser: delay async computation, fixes #13 [`#13`](https://github.com/browserify/pbkdf2/issues/13)

### Commits

- add node-shim.js [`5eaa6ba`](https://github.com/browserify/pbkdf2/commit/5eaa6baa87564ab46775d1b8d547dd0f4ca8515b)
- browser: default the encoding [`94e7979`](https://github.com/browserify/pbkdf2/commit/94e79791fcab6c91cc5cb773857e2d51783edada)
- update tests [`931b7d5`](https://github.com/browserify/pbkdf2/commit/931b7d527a7a46e7518506680a1d431e6eb24dd3)
- package: update standard [`dd02315`](https://github.com/browserify/pbkdf2/commit/dd02315c41b8339d14c656c7e70ba433c893b36f)
- move checkParameters to separate file [`93ffd51`](https://github.com/browserify/pbkdf2/commit/93ffd519878f22d881a36eadcde4ad3e710caecc)
- tests: merge to avoid double up of code [`59f6577`](https://github.com/browserify/pbkdf2/commit/59f6577f8783d6e71a99988397c3a1e0811ed6d5)
- travis: update config [`7ab61b4`](https://github.com/browserify/pbkdf2/commit/7ab61b401c7c3d40bcb16d98bab97143c89d97e1)
- update README.md [`8bf4958`](https://github.com/browserify/pbkdf2/commit/8bf4958da1da9a8a5d13cbe91aff0fe9e632371c)
- pull out type checking [`e6b815f`](https://github.com/browserify/pbkdf2/commit/e6b815fa4c0b2edf222c663c7b5d761dddf23d57)
- browser: s/testParams/checkParameters/ [`7730a55`](https://github.com/browserify/pbkdf2/commit/7730a552e89e09da0d8a42b56f8a376c851b2c11)
- package: add files field to package.json [`3ee45ed`](https://github.com/browserify/pbkdf2/commit/3ee45ed1bd6b751d7918075b2a76d166eb61ae8a)
- rename async-shim.js to node-shim-async.js [`2268e11`](https://github.com/browserify/pbkdf2/commit/2268e11ad301ebf377a1f859e05214bdf3622919)
- browser: return null for error [`c470de1`](https://github.com/browserify/pbkdf2/commit/c470de18779fcb0f998d8c542bad5787dfb5f5f1)
- .travis: test node 6 [`9f949d4`](https://github.com/browserify/pbkdf2/commit/9f949d46d9392be9e937e2916df7863ac17f2815)

## [3.0.4](https://github.com/browserify/pbkdf2/compare/v3.0.3...3.0.4) - 2015-03-27

### Merged

- Fix shim encoding and more reliably close child process [`#12`](https://github.com/browserify/pbkdf2/pull/12)

### Commits

- async errors and exiting [`2bcfba8`](https://github.com/browserify/pbkdf2/commit/2bcfba85216694c10663540b34a4e3b400c205ed)
- tests: add failing utf8 test [`ea1ddaf`](https://github.com/browserify/pbkdf2/commit/ea1ddaf28d06a288a955ae41b717011c1596fb0b)
- fix 2 types of errors [`0cbba29`](https://github.com/browserify/pbkdf2/commit/0cbba29b0a81a7acf2619fd49ab5b9f40c47bfa8)
- fix standard formatting issues [`93b06d3`](https://github.com/browserify/pbkdf2/commit/93b06d3762fdb850d5532e0f63b8051515db666f)
- fix shim encoding [`a57d9c6`](https://github.com/browserify/pbkdf2/commit/a57d9c6b37fba515eb6a83cf8239bcd67356a203)
- index/browser: rename MAX_INT to MAX_ALLOC [`9f6fb05`](https://github.com/browserify/pbkdf2/commit/9f6fb05b7284bc0fc72cde6e37524deffdc2d3a8)
- package: use standard 3.0.0 [`941b46b`](https://github.com/browserify/pbkdf2/commit/941b46ba580918ec44bc0072b6ae0ecfa9fde277)

## [v3.0.3](https://github.com/browserify/pbkdf2/compare/3.0.2...v3.0.3) - 2015-02-15

### Commits

- repo name [`a6516d9`](https://github.com/browserify/pbkdf2/commit/a6516d9fa34bb250c2ef6e0d75ef7e47b7c1b1a4)
- change name [`471fb83`](https://github.com/browserify/pbkdf2/commit/471fb83c50fb1019b099fe9926fc28d3d25900e1)

## [3.0.2](https://github.com/browserify/pbkdf2/compare/3.0.1...3.0.2) - 2015-02-15

### Merged

- fix bug in encoding [`#10`](https://github.com/browserify/pbkdf2/pull/10)

### Commits

- tests: add non-printable fixture handling and example fixture [`040707d`](https://github.com/browserify/pbkdf2/commit/040707da888f4eb7737dd671a3e0c71875b94b2b)
- add 0.12 and iojs to travis [`45165c4`](https://github.com/browserify/pbkdf2/commit/45165c45bf2c1938431eed592eef5c087a43aa20)

## [3.0.1](https://github.com/browserify/pbkdf2/compare/2.0.1...3.0.1) - 2015-01-29

### Commits

- modularize [`68c6c36`](https://github.com/browserify/pbkdf2/commit/68c6c36e4a0af2862274d0a8effc82d0650a546d)
- all: standard cmpliance [`2f5ff3b`](https://github.com/browserify/pbkdf2/commit/2f5ff3ba739f84b2e51335c5f3cfacbf856bc587)
- add shim [`386d4af`](https://github.com/browserify/pbkdf2/commit/386d4afd32b1ea3384cc175d3993ada757836e01)
- add browser tests [`b88ad8d`](https://github.com/browserify/pbkdf2/commit/b88ad8d14ee02fefddb99f3b89c89ca51e90122f)
- package: add coveralls and re-order [`5834697`](https://github.com/browserify/pbkdf2/commit/58346973b7d2e45b9bb313e159b43e05ab5024ae)
- package: add standard test suite [`e504eed`](https://github.com/browserify/pbkdf2/commit/e504eed0a6e38846a752f9f49aa1b38ceb8dbd67)
- package: update repository URL [`b217d1a`](https://github.com/browserify/pbkdf2/commit/b217d1ab6c153e9899cbd96b29653f7da308d444)
- tests: remove unnecessary semicolon/vars [`24f8ab3`](https://github.com/browserify/pbkdf2/commit/24f8ab3b5d1928f5b87f50aa357475cd5855b03d)
- package: add coveralls [`c5b6d51`](https://github.com/browserify/pbkdf2/commit/c5b6d510bdbd5792b98e4d27daf94849c70b9d57)
- index: remove unnecessary __pbkdf2Export [`e51121f`](https://github.com/browserify/pbkdf2/commit/e51121f46887e545fedd817ac8624e0d68c9ace5)
- index: fix digest algorithm typo [`558b3dd`](https://github.com/browserify/pbkdf2/commit/558b3dd06bb17b782ee0c1994c5aed19624d4c3b)
- README: update repository URL [`f1fc7b4`](https://github.com/browserify/pbkdf2/commit/f1fc7b448f93e21a23bfe40f39670129f2ff31d6)
- package: latest devDependencies [`9624836`](https://github.com/browserify/pbkdf2/commit/9624836014c4976f8907273a4a77020b51a4fe8c)
- package: use latest create-hmac [`ffd5b94`](https://github.com/browserify/pbkdf2/commit/ffd5b943434dc78a36858f0bd61399aba6176617)
- package: update description [`7fbec99`](https://github.com/browserify/pbkdf2/commit/7fbec99bfc993d01161aae4c4b00c3718a8737f4)

## [2.0.1](https://github.com/browserify/pbkdf2/compare/2.0.0...2.0.1) - 2014-09-25

### Merged

- move istanbul to dev dependencies [`#1`](https://github.com/browserify/pbkdf2/pull/1)

## [2.0.0](https://github.com/browserify/pbkdf2/compare/1.0.1...2.0.0) - 2014-09-24

### Commits

- pbkdf2: add async impl. and invalid tests [`16d209e`](https://github.com/browserify/pbkdf2/commit/16d209ecd2e997ca775f32b47b8c2318a55ecdd8)
- README: update to latest API [`c2f01cf`](https://github.com/browserify/pbkdf2/commit/c2f01cf719063975dd943d8f6c280896f1c0958d)
- index: re-order var declarations [`8ad010d`](https://github.com/browserify/pbkdf2/commit/8ad010d6ad5869b06ec9a8c0d80650c9b517da7f)

## 1.0.1 - 2014-09-17

### Commits

- index: adds implementation [`1792996`](https://github.com/browserify/pbkdf2/commit/1792996f8f967ff5297990c498338b6790fc8a8b)
- fixtures: adds test fixtures [`33e999e`](https://github.com/browserify/pbkdf2/commit/33e999e8630ce967adc28f64462bdc98b5ab5a47)
- Initial commit [`f17b827`](https://github.com/browserify/pbkdf2/commit/f17b827efadd65634ab2f98de538b05ec526d959)
- package: adds package.json [`2782f71`](https://github.com/browserify/pbkdf2/commit/2782f7152d283c709e4700bf473f15069adcd5c5)
- README: add badges + usage instructions [`51ac17d`](https://github.com/browserify/pbkdf2/commit/51ac17db44d4b7e0b1bfc5995123dc5b2d031bc6)
- index: adhere to node.js spec [`98ec635`](https://github.com/browserify/pbkdf2/commit/********************483de4080a19b86b45f4)
- travis: adds yml [`965f37f`](https://github.com/browserify/pbkdf2/commit/965f37fb409f1daffd01fad353bd923ed0aa2b2d)
- README: add credits [`aa9ac2f`](https://github.com/browserify/pbkdf2/commit/aa9ac2f38e74a834500ec1fdaa7fe14ca976b26d)
- README: no need for there [`0f6c37f`](https://github.com/browserify/pbkdf2/commit/0f6c37f10924fc3ceb8b2bf4074508e559454feb)
