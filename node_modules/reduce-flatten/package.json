{"name": "reduce-flatten", "author": "<PERSON> <<EMAIL>>", "version": "2.0.0", "description": "Flatten an array into the supplied array.", "repository": "https://github.com/75lb/reduce-flatten.git", "license": "MIT", "keywords": ["array", "reduce", "flatten"], "engines": {"node": ">=6"}, "scripts": {"test": "test-runner test.js", "docs": "jsdoc2md -t README.hbs index.js > README.md; echo"}, "devDependencies": {"jsdoc-to-markdown": "^4.0.1", "test-runner": "^0.5.0"}}