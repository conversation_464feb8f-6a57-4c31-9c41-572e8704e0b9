{
    "extends": "@ljharb",
    "root": true,
    "rules": {
        "array-bracket-newline": 0,
        "array-element-newline": 0,
        "indent": [2, 4],
        "strict": 0,
        "complexity": 0,
        "consistent-return": 0,
        "curly": 0,
        "dot-notation": [2, { "allowKeywords": true }],
        "func-name-matching": 0,
        "func-style": 0,
        "global-require": 0,
        "id-length": [2, { "min": 1, "max": 30 }],
        "max-lines-per-function": 0,
        "max-nested-callbacks": 0,
        "max-params": 0,
        "max-statements-per-line": [2, { "max": 2 }],
        "max-statements": 0,
        "no-magic-numbers": 0,
        "no-console": 0,
        "no-shadow": 0,
        "no-unused-vars": [2, { "vars": "all", "args": "none" }],
        "no-use-before-define": 0,
        "object-curly-newline": 0,
        "operator-linebreak": [2, "before"],
        "sort-keys": 0,
    },
    "overrides": [
        {
            "files": "test/resolver/nested_symlinks/mylib/*.js",
            "rules": {
                "no-throw-literal": 0,
            },
        },
    ],
}
