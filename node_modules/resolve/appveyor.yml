version: 1.0.{build}
skip_branch_with_pr: true
build: off

environment:
  matrix:
    - nodejs_version: "12"
    - nodejs_version: "11"
    - nodejs_version: "10"
    - nodejs_version: "9"
    - nodejs_version: "8"
    - nodejs_version: "7"
    - nodejs_version: "6"
    - nodejs_version: "5"
    - nodejs_version: "4"
    - nodejs_version: "3"
    - nodejs_version: "2"
    - nodejs_version: "1"
    - nodejs_version: "0.12"
    - nodejs_version: "0.10"
    - nodejs_version: "0.8"
    - nodejs_version: "0.6"
matrix:
  # fast_finish: true
  allow_failures:
    - nodejs_version: "5" # due to windows npm bug, registry-side
    - nodejs_version: "0.8"
    - nodejs_version: "0.6"

platform:
  - x86
  - x64

# Install scripts. (runs after repo cloning)
install:
 # Fix symlinks in working copy (see https://github.com/appveyor/ci/issues/650#issuecomment-186592582) / https://github.com/charleskorn/batect/commit/d08986802ec43086902958c4ee7e57ff3e71dbef
 - git config core.symlinks true
 - git reset --hard
 # Get the latest stable version of Node.js or io.js
 - ps: Install-Product node $env:nodejs_version $env:platform
 - IF %nodejs_version% EQU 0.6 npm config set strict-ssl false && npm -g install npm@1.3
 - IF %nodejs_version% EQU 0.8 npm config set strict-ssl false && npm -g install npm@1.4.28 && npm install -g npm@4.5
 - set PATH=%APPDATA%\npm;%PATH%
 #- IF %nodejs_version% NEQ 0.6 AND %nodejs_version% NEQ 0.8 npm -g install npm
 # install modules
 - npm install

# Post-install test scripts.
test_script:
 # Output useful info for debugging.
 - node --version
 - npm --version
 # run tests
 - npm run tests-only
