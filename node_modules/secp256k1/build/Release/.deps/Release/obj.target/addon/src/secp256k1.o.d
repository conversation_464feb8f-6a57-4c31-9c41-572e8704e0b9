cmd_Release/obj.target/addon/src/secp256k1.o := c++ -o Release/obj.target/addon/src/secp256k1.o ../src/secp256k1.cc '-DNODE_GYP_MODULE_NAME=addon' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-DV8_DEPRECATION_WARNINGS' '-DV8_IMMINENT_DEPRECATION_WARNINGS' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DNAPI_VERSION=3' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/src -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/16.20.2/deps/v8/include -I/Users/<USER>/Desktop/reacthreejs/swap-contracts/node_modules/node-addon-api -I../src  -O3 -gdwarf-2 -mmacosx-version-min=10.7 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++14 -stdlib=libc++ -fno-rtti -fno-strict-aliasing -MMD -MF ./Release/.deps/Release/obj.target/addon/src/secp256k1.o.d.raw   -c
Release/obj.target/addon/src/secp256k1.o: ../src/secp256k1.cc \
  ../src/secp256k1.h \
  /Users/<USER>/Desktop/reacthreejs/swap-contracts/node_modules/node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node/node_api_types.h \
  /Users/<USER>/Desktop/reacthreejs/swap-contracts/node_modules/node-addon-api/napi-inl.h \
  /Users/<USER>/Desktop/reacthreejs/swap-contracts/node_modules/node-addon-api/napi-inl.deprecated.h \
  ../src/secp256k1/include/secp256k1.h \
  ../src/secp256k1/include/secp256k1_ecdh.h \
  ../src/secp256k1/include/secp256k1_preallocated.h \
  ../src/secp256k1/include/secp256k1_recovery.h
../src/secp256k1.cc:
../src/secp256k1.h:
/Users/<USER>/Desktop/reacthreejs/swap-contracts/node_modules/node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/16.20.2/include/node/node_api_types.h:
/Users/<USER>/Desktop/reacthreejs/swap-contracts/node_modules/node-addon-api/napi-inl.h:
/Users/<USER>/Desktop/reacthreejs/swap-contracts/node_modules/node-addon-api/napi-inl.deprecated.h:
../src/secp256k1/include/secp256k1.h:
../src/secp256k1/include/secp256k1_ecdh.h:
../src/secp256k1/include/secp256k1_preallocated.h:
../src/secp256k1/include/secp256k1_recovery.h:
