import { ParseOptions } from '../src/types';
export declare function parseContract(source: string, options?: ParseOptions): any;
export declare function parseNode(source: string, options?: {}): any;
export declare function parseStatement(source: string, options?: {}): any;
export declare function parseExpression(source: string, options?: {}): any;
export declare function parseAssembly(source: string, options?: {}): any;
