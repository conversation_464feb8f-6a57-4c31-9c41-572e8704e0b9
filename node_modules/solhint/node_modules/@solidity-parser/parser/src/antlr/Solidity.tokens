T__0=1
T__1=2
T__2=3
T__3=4
T__4=5
T__5=6
T__6=7
T__7=8
T__8=9
T__9=10
T__10=11
T__11=12
T__12=13
T__13=14
T__14=15
T__15=16
T__16=17
T__17=18
T__18=19
T__19=20
T__20=21
T__21=22
T__22=23
T__23=24
T__24=25
T__25=26
T__26=27
T__27=28
T__28=29
T__29=30
T__30=31
T__31=32
T__32=33
T__33=34
T__34=35
T__35=36
T__36=37
T__37=38
T__38=39
T__39=40
T__40=41
T__41=42
T__42=43
T__43=44
T__44=45
T__45=46
T__46=47
T__47=48
T__48=49
T__49=50
T__50=51
T__51=52
T__52=53
T__53=54
T__54=55
T__55=56
T__56=57
T__57=58
T__58=59
T__59=60
T__60=61
T__61=62
T__62=63
T__63=64
T__64=65
T__65=66
T__66=67
T__67=68
T__68=69
T__69=70
T__70=71
T__71=72
T__72=73
T__73=74
T__74=75
T__75=76
T__76=77
T__77=78
T__78=79
T__79=80
T__80=81
T__81=82
T__82=83
T__83=84
T__84=85
T__85=86
T__86=87
T__87=88
T__88=89
T__89=90
T__90=91
T__91=92
T__92=93
T__93=94
T__94=95
T__95=96
Int=97
Uint=98
Byte=99
Fixed=100
Ufixed=101
BooleanLiteral=102
DecimalNumber=103
HexNumber=104
NumberUnit=105
HexLiteralFragment=106
ReservedKeyword=107
AnonymousKeyword=108
BreakKeyword=109
ConstantKeyword=110
ImmutableKeyword=111
ContinueKeyword=112
LeaveKeyword=113
ExternalKeyword=114
IndexedKeyword=115
InternalKeyword=116
PayableKeyword=117
PrivateKeyword=118
PublicKeyword=119
VirtualKeyword=120
PureKeyword=121
TypeKeyword=122
ViewKeyword=123
GlobalKeyword=124
ConstructorKeyword=125
FallbackKeyword=126
ReceiveKeyword=127
Identifier=128
StringLiteralFragment=129
VersionLiteral=130
WS=131
COMMENT=132
LINE_COMMENT=133
'pragma'=1
';'=2
'*'=3
'||'=4
'^'=5
'~'=6
'>='=7
'>'=8
'<'=9
'<='=10
'='=11
'as'=12
'import'=13
'from'=14
'{'=15
','=16
'}'=17
'abstract'=18
'contract'=19
'interface'=20
'library'=21
'is'=22
'('=23
')'=24
'error'=25
'using'=26
'for'=27
'|'=28
'&'=29
'+'=30
'-'=31
'/'=32
'%'=33
'=='=34
'!='=35
'struct'=36
'modifier'=37
'function'=38
'returns'=39
'event'=40
'enum'=41
'['=42
']'=43
'address'=44
'.'=45
'mapping'=46
'=>'=47
'memory'=48
'storage'=49
'calldata'=50
'if'=51
'else'=52
'try'=53
'catch'=54
'while'=55
'unchecked'=56
'assembly'=57
'do'=58
'return'=59
'throw'=60
'emit'=61
'revert'=62
'var'=63
'bool'=64
'string'=65
'byte'=66
'++'=67
'--'=68
'new'=69
':'=70
'delete'=71
'!'=72
'**'=73
'<<'=74
'>>'=75
'&&'=76
'?'=77
'|='=78
'^='=79
'&='=80
'<<='=81
'>>='=82
'+='=83
'-='=84
'*='=85
'/='=86
'%='=87
'let'=88
':='=89
'=:'=90
'switch'=91
'case'=92
'default'=93
'->'=94
'callback'=95
'override'=96
'anonymous'=108
'break'=109
'constant'=110
'immutable'=111
'continue'=112
'leave'=113
'external'=114
'indexed'=115
'internal'=116
'payable'=117
'private'=118
'public'=119
'virtual'=120
'pure'=121
'type'=122
'view'=123
'global'=124
'constructor'=125
'fallback'=126
'receive'=127
