const t = require('typical')
const Padding = require('./padding')

/**
 * @module column
 */

const _padding = new WeakMap()

// setting any column property which is a factor of the width should trigger autoSize()

/**
 * Represents a table column
 */
class Column {
  constructor (column) {
    /**
     * @type {string}
     */
    if (t.isDefined(column.name)) this.name = column.name
    /**
     * @type {number}
     */
    if (t.isDefined(column.width)) this.width = column.width
    if (t.isDefined(column.maxWidth)) this.maxWidth = column.maxWidth
    if (t.isDefined(column.minWidth)) this.minWidth = column.minWidth
    if (t.isDefined(column.noWrap)) this.noWrap = column.noWrap
    if (t.isDefined(column.break)) this.break = column.break
    if (t.isDefined(column.contentWrappable)) this.contentWrappable = column.contentWrappable
    if (t.isDefined(column.contentWidth)) this.contentWidth = column.contentWidth
    if (t.isDefined(column.minContentWidth)) this.minContentWidth = column.minContentWidth
    this.padding = column.padding || { left: ' ', right: ' ' }
    this.generatedWidth = null
  }

  set padding (padding) {
    _padding.set(this, new Padding(padding))
  }
  get padding () {
    return _padding.get(this)
  }

  /**
   * the width of the content (excluding padding) after being wrapped
   */
  get wrappedContentWidth () {
    return Math.max(this.generatedWidth - this.padding.length(), 0)
  }

  isResizable () {
    return !this.isFixed()
  }

  isFixed () {
    return t.isDefined(this.width) || this.noWrap || !this.contentWrappable
  }

  generateWidth () {
    this.generatedWidth = this.width || (this.contentWidth + this.padding.length())
  }

  generateMinWidth () {
    this.minWidth = this.minContentWidth + this.padding.length()
  }
}

module.exports = Column
