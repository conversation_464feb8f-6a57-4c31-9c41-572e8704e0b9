{"name": "then-request", "version": "6.0.2", "description": "A request library that returns promises, inspired by request", "keywords": [], "main": "lib/index.js", "browser": "lib/browser.js", "types": "lib/index.d.ts", "files": ["lib"], "dependencies": {"@types/concat-stream": "^1.6.0", "@types/form-data": "0.0.33", "@types/node": "^8.0.0", "@types/qs": "^6.2.31", "caseless": "~0.12.0", "concat-stream": "^1.6.0", "form-data": "^2.2.0", "http-basic": "^8.1.1", "http-response-object": "^3.0.1", "promise": "^8.0.0", "qs": "^6.4.0"}, "devDependencies": {"browserify": "^14.4.0", "busboy": "^0.2.14", "exorcist": "^0.4.0", "flowgen2": "^2.2.2", "istanbul": "^0.4.5", "jsdom": "^11.1.0", "minifyify": "^7.3.5", "mkdirp": "^0.5.1", "multiparty": "^4.1.3", "rimraf": "^2.6.1", "testit": "^2.1.3", "typescript": "^2.4.0"}, "scripts": {"pretest": "npm run build:types", "test": "node test/index.js && istanbul cover test/index.js", "prepublishOnly": "npm run build", "prebuild": "rimraf dist && mkdirp dist", "build": "npm run build:types && npm run build:full && npm run build:min", "build:types": "tsc && flowgen lib/**/*", "build:full": "browserify -d --standalone request lib/browser.js | exorcist -u request.js.map dist/request.js.map > dist/request.js", "build:min": "browserify -d --standalone request lib/browser.js -p [minifyify --compressPath . --map request.min.js.map --output dist/request.min.js.map] > dist/request.min.js"}, "engines": {"node": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/then/then-request.git"}, "author": "ForbesLindesay", "license": "MIT"}