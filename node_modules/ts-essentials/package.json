{"name": "ts-essentials", "description": "All essential TypeScript types in one place", "keywords": ["typescript", "types", "essentials", "utils", "toolbox", "toolbelt", "lodash", "underscore"], "version": "7.0.3", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": "**************:krzkaczor/ts-essentials.git", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"build": "rimraf ./dist && tsc -p tsconfig.prod.json --outDir ./dist", "formatDeclarations": "prettier --ignore-path *.js --write dist/*.d.ts", "prepublishOnly": "yarn test && yarn build && yarn formatDeclarations", "test": "prettier -c **/*.ts && tsc --noEmit", "test:fix": "prettier --write **/*.ts && tsc --noEmit", "prerelease": "yarn test", "release": "yarn build && yarn formatDeclarations && yarn changeset publish"}, "files": ["dist"], "peerDependencies": {"typescript": ">=3.7.0"}, "devDependencies": {"@changesets/cli": "^2.11.2", "@codechecks/build-size-watcher": "^0.1.0", "@codechecks/client": "^0.1.11", "conditional-type-checks": "^1.0.4", "prettier": "^1.19.1", "rimraf": "^3.0.2", "typescript": "^3.7.2"}}