{"name": "typical", "author": "<PERSON> <<EMAIL>>", "version": "4.0.0", "description": "Robust Javascript type-checking", "repository": "https://github.com/75lb/typical", "license": "MIT", "main": "dist/index.js", "keywords": ["type", "checking", "check", "value", "valid", "is", "number", "object", "plainobject", "array", "like", "defined", "string", "boolean", "function", "promise", "iterable", "class", "primitive"], "engines": {"node": ">=8"}, "scripts": {"test": "npm run test:js && npm run test:esm && npm run test:web", "test:js": "rollup test.mjs -f cjs -o dist/test.js && rollup test-v10.mjs -f cjs -o dist/test-v10.js && test-runner dist/test*.js", "test:esm": "esm-runner test*.mjs", "test:web": "web-runner test.mjs", "dist": "rollup index.mjs -f umd -n typical -o dist/index.js", "docs": "jsdoc2md -c jsdoc.conf -t README.hbs index.mjs > README.md; echo", "cover": "nyc --reporter=text-lcov test-runner test.js | coveralls"}, "devDependencies": {"@test-runner/web": "^0.1.4", "coveralls": "^3.0.3", "esm-runner": "^0.1.2", "jsdoc-to-markdown": "^4.0.1", "rollup": "^1.7.0", "test-object-model": "^0.3.8", "test-runner": "^0.6.0-14"}, "files": ["index.mjs", "dist/index.js"]}