{"name": "ethereum-swap-dex", "version": "1.0.0", "description": "Professional Ethereum DEX with comprehensive token swapping", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy": "hardhat run scripts/deploy.js", "deploy:mainnet": "hardhat run scripts/deploy.js --network mainnet", "deploy:goerli": "hardhat run scripts/deploy.js --network goerli", "verify": "hardhat verify", "coverage": "hardhat coverage", "lint": "solhint 'contracts/**/*.sol'", "format": "prettier --write 'contracts/**/*.sol'"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^3.0.2", "@openzeppelin/contracts": "^4.9.3", "hardhat": "^2.17.1", "dotenv": "^16.3.1", "chai": "^4.3.7", "ethers": "^5.7.2", "solhint": "^3.6.2", "prettier": "^3.0.0", "prettier-plugin-solidity": "^1.1.3"}, "keywords": ["ethereum", "defi", "dex", "swap", "solidity"], "author": "Quantlink", "license": "MIT"}