const { ethers } = require("hardhat");
const fs = require("fs");

async function main() {
    const [deployer] = await ethers.getSigners();
    
    console.log(" Starting deployment...");
    console.log("Deploying contracts with account:", deployer.address);
    console.log("Account balance:", (await deployer.getBalance()).toString());
    
    const deployments = {};
    
    try {
        // 1. Deploy WETH
        console.log("\n📦 Deploying WETH9...");
        const WETH = await ethers.getContractFactory("WETH9");
        const weth = await WETH.deploy();
        await weth.deployed();
        deployments.WETH = weth.address;
        console.log("✅ WETH deployed to:", weth.address);
        
        // 2. Deploy Factory
        console.log("\n📦 Deploying SwapFactory...");
        const SwapFactory = await ethers.getContractFactory("SwapFactory");
        const factory = await SwapFactory.deploy(deployer.address);
        await factory.deployed();
        deployments.Factory = factory.address;
        console.log("✅ SwapFactory deployed to:", factory.address);
        
        // 3. Deploy TokenRegistry
        console.log("\n📦 Deploying TokenRegistry...");
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        const tokenRegistry = await TokenRegistry.deploy();
        await tokenRegistry.deployed();
        deployments.TokenRegistry = tokenRegistry.address;
        console.log("✅ TokenRegistry deployed to:", tokenRegistry.address);

        // 4. Deploy SecurityManager
        console.log("\n📦 Deploying SecurityManager...");
        const SecurityManager = await ethers.getContractFactory("SecurityManager");
        const securityManager = await SecurityManager.deploy();
        await securityManager.deployed();
        deployments.SecurityManager = securityManager.address;
        console.log("✅ SecurityManager deployed to:", securityManager.address);

        // 5. Deploy PriceOracle
        console.log("\n📦 Deploying PriceOracle...");
        const PriceOracle = await ethers.getContractFactory("PriceOracle");
        const priceOracle = await PriceOracle.deploy(factory.address, weth.address);
        await priceOracle.deployed();
        deployments.PriceOracle = priceOracle.address;
        console.log("✅ PriceOracle deployed to:", priceOracle.address);

        // 6. Deploy SmartRouter
        console.log("\n📦 Deploying SmartRouter...");
        const SmartRouter = await ethers.getContractFactory("SmartRouter");
        const smartRouter = await SmartRouter.deploy(
            factory.address,
            weth.address,
            tokenRegistry.address,
            priceOracle.address
        );
        await smartRouter.deployed();
        deployments.SmartRouter = smartRouter.address;
        console.log("✅ SmartRouter deployed to:", smartRouter.address);

        // 7. Deploy GovernanceToken
        console.log("\n📦 Deploying GovernanceToken...");
        const GovernanceToken = await ethers.getContractFactory("GovernanceToken");
        const governanceToken = await GovernanceToken.deploy();
        await governanceToken.deployed();
        deployments.GovernanceToken = governanceToken.address;
        console.log("✅ GovernanceToken deployed to:", governanceToken.address);

        // 8. Deploy LiquidityMining
        console.log("\n📦 Deploying LiquidityMining...");
        const LiquidityMining = await ethers.getContractFactory("LiquidityMining");
        const rewardPerBlock = ethers.utils.parseEther("10"); // 10 tokens per block
        const startBlock = await ethers.provider.getBlockNumber() + 100; // Start in 100 blocks
        const endBlock = startBlock + (365 * 24 * 60 * 60 / 12); // 1 year assuming 12s blocks

        const liquidityMining = await LiquidityMining.deploy(
            governanceToken.address,
            rewardPerBlock,
            startBlock,
            endBlock
        );
        await liquidityMining.deployed();
        deployments.LiquidityMining = liquidityMining.address;
        console.log("✅ LiquidityMining deployed to:", liquidityMining.address);

        // 9. Deploy SwapRouter
        console.log("\n📦 Deploying SwapRouter...");
        const SwapRouter = await ethers.getContractFactory("SwapRouter");
        const router = await SwapRouter.deploy(
            factory.address,
            weth.address,
            tokenRegistry.address,
            securityManager.address
        );
        await router.deployed();
        deployments.Router = router.address;
        console.log("✅ SwapRouter deployed to:", router.address);
        
        // 4. Deploy test tokens (only on testnet/localhost)
        const network = await ethers.provider.getNetwork();
        if (network.chainId === 31337 || network.chainId === 5) { // localhost or goerli
            console.log("\n📦 Deploying test tokens...");
            
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            
            const tokenA = await ERC20Mock.deploy("Test Token A", "TTA", ethers.utils.parseEther("1000000"));
            await tokenA.deployed();
            deployments.TokenA = tokenA.address;
            console.log("✅ Test Token A deployed to:", tokenA.address);
            
            const tokenB = await ERC20Mock.deploy("Test Token B", "TTB", ethers.utils.parseEther("1000000"));
            await tokenB.deployed();
            deployments.TokenB = tokenB.address;
            console.log("✅ Test Token B deployed to:", tokenB.address);
            
            const usdc = await ERC20Mock.deploy("USD Coin", "USDC", ethers.utils.parseUnits("1000000", 6));
            await usdc.deployed();
            deployments.USDC = usdc.address;
            console.log("✅ USDC Mock deployed to:", usdc.address);
            
            // Create initial pairs
            console.log("\n🔗 Creating initial pairs...");
            
            await factory.createPair(tokenA.address, tokenB.address);
            const pairAB = await factory.getPair(tokenA.address, tokenB.address);
            deployments.PairAB = pairAB;
            console.log("✅ TokenA-TokenB pair created at:", pairAB);
            
            await factory.createPair(tokenA.address, weth.address);
            const pairAETH = await factory.getPair(tokenA.address, weth.address);
            deployments.PairAETH = pairAETH;
            console.log("✅ TokenA-WETH pair created at:", pairAETH);
            
            await factory.createPair(usdc.address, weth.address);
            const pairUSDCETH = await factory.getPair(usdc.address, weth.address);
            deployments.PairUSDCETH = pairUSDCETH;
            console.log("✅ USDC-WETH pair created at:", pairUSDCETH);
        }
        
        // 10. Deploy Test Tokens (Sepolia only)
        if (network.chainId === 11155111) {
            console.log("\n🪙 Deploying Test Tokens for Sepolia...");

            const TestTokenDeployer = await ethers.getContractFactory("TestTokenDeployer");
            const testTokenDeployer = await TestTokenDeployer.deploy();
            await testTokenDeployer.deployed();
            deployments.TestTokenDeployer = testTokenDeployer.address;
            console.log("✅ TestTokenDeployer deployed to:", testTokenDeployer.address);

            // Deploy all test tokens
            const deployTx = await testTokenDeployer.deployAllTokens();
            await deployTx.wait();
            console.log("✅ All test tokens deployed");

            // Get deployed token addresses
            const tokenCount = await testTokenDeployer.getTokenCount();
            console.log(`📋 Deployed ${tokenCount} test tokens:`);

            for (let i = 0; i < tokenCount; i++) {
                const tokenInfo = await testTokenDeployer.deployedTokens(i);
                console.log(`   ${tokenInfo.symbol}: ${tokenInfo.tokenAddress}`);

                // Add to token registry
                await tokenRegistry.addToken(
                    tokenInfo.tokenAddress,
                    tokenInfo.symbol,
                    tokenInfo.name,
                    tokenInfo.decimals,
                    "", // logoURI
                    "", // website
                    1000000 // marketCap
                );
                console.log(`   ✅ Added ${tokenInfo.symbol} to registry`);
            }
        }

        // 11. Configure system integrations
        console.log("\n⚙️ Configuring system integrations...");

        // Add LiquidityMining as minter for GovernanceToken
        await governanceToken.addMinter(liquidityMining.address);
        console.log("✅ Added LiquidityMining as GovernanceToken minter");

        // Chainlink feeds are automatically initialized in PriceOracle constructor
        console.log("✅ Chainlink price feeds initialized automatically");

        // Add intermediate tokens to SmartRouter
        const supportedTokens = await tokenRegistry.getVerifiedTokens();
        const topTokens = supportedTokens.slice(0, 5); // Add top 5 tokens as intermediates

        for (const token of topTokens) {
            if (token !== weth.address) {
                try {
                    await smartRouter.addIntermediateToken(token);
                    console.log(`✅ Added ${token} as intermediate token`);
                } catch (error) {
                    console.log(`⚠️ Could not add ${token} as intermediate token:`, error.message);
                }
            }
        }

        // Configure TWAP for main pairs (if pairs exist)
        console.log("\n📊 Configuring TWAP for main pairs...");
        const mainTokens = [weth.address, ...topTokens.slice(0, 3)];

        for (let i = 0; i < mainTokens.length; i++) {
            for (let j = i + 1; j < mainTokens.length; j++) {
                const pairAddress = await factory.getPair(mainTokens[i], mainTokens[j]);
                if (pairAddress !== ethers.constants.AddressZero) {
                    try {
                        await priceOracle.configureTWAP(pairAddress, 1800, 8, true); // 30 min, 8 observations
                        console.log(`✅ Configured TWAP for pair ${mainTokens[i]}-${mainTokens[j]}`);
                    } catch (error) {
                        console.log(`⚠️ Could not configure TWAP for pair:`, error.message);
                    }
                }
            }
        }

        // 11. Verify contracts (if not localhost)
        if (network.chainId !== 31337) {
            console.log("\n🔍 Waiting for block confirmations...");
            await weth.deployTransaction.wait(5);
            await factory.deployTransaction.wait(5);
            await tokenRegistry.deployTransaction.wait(5);
            await securityManager.deployTransaction.wait(5);
            await priceOracle.deployTransaction.wait(5);
            await smartRouter.deployTransaction.wait(5);
            await governanceToken.deployTransaction.wait(5);
            await liquidityMining.deployTransaction.wait(5);
            await router.deployTransaction.wait(5);

            console.log("\n📋 Run these commands to verify contracts:");
            console.log(`npx hardhat verify --network ${network.name} ${weth.address}`);
            console.log(`npx hardhat verify --network ${network.name} ${factory.address} "${deployer.address}"`);
            console.log(`npx hardhat verify --network ${network.name} ${tokenRegistry.address}`);
            console.log(`npx hardhat verify --network ${network.name} ${securityManager.address}`);
            console.log(`npx hardhat verify --network ${network.name} ${priceOracle.address} "${factory.address}" "${weth.address}"`);
            console.log(`npx hardhat verify --network ${network.name} ${smartRouter.address} "${factory.address}" "${weth.address}" "${tokenRegistry.address}" "${priceOracle.address}"`);
            console.log(`npx hardhat verify --network ${network.name} ${governanceToken.address}`);
            console.log(`npx hardhat verify --network ${network.name} ${liquidityMining.address} "${governanceToken.address}" "${rewardPerBlock}" "${startBlock}" "${endBlock}"`);
            console.log(`npx hardhat verify --network ${network.name} ${router.address} "${factory.address}" "${weth.address}" "${tokenRegistry.address}" "${securityManager.address}"`);

            console.log("\n📝 Note: Sepolia testnet is now used instead of Goerli");
            console.log("🔗 Sepolia Explorer: https://sepolia.etherscan.io/");
        }
        
        // 6. Save deployment info
        const deploymentInfo = {
            network: network.name,
            chainId: network.chainId,
            deployer: deployer.address,
            timestamp: new Date().toISOString(),
            contracts: deployments,
            gasUsed: {
                // These would be calculated from transaction receipts
                total: "Calculated after deployment"
            }
        };
        
        fs.writeFileSync(
            `deployments/${network.name}-${Date.now()}.json`,
            JSON.stringify(deploymentInfo, null, 2)
        );
        
        console.log("\n🎉 Deployment completed successfully!");
        console.log("\n=== DEPLOYMENT SUMMARY ===");
        Object.entries(deployments).forEach(([name, address]) => {
            console.log(`${name}: ${address}`);
        });
        
        console.log("\n📝 Deployment info saved to deployments/ directory");
        
        // 10. Basic functionality test
        if (network.chainId === 31337) {
            console.log("\n🧪 Running comprehensive functionality test...");

            // Test Factory
            const factoryContract = await ethers.getContractAt("SwapFactory", factory.address);
            const allPairsLength = await factoryContract.allPairsLength();
            console.log(`✅ Factory has ${allPairsLength} pairs created`);

            // Test Router
            const routerContract = await ethers.getContractAt("SwapRouter", router.address);
            const factoryAddress = await routerContract.factory();
            const wethAddress = await routerContract.WETH();
            console.log(`✅ Router connected to factory: ${factoryAddress}`);
            console.log(`✅ Router connected to WETH: ${wethAddress}`);

            // Test TokenRegistry
            const tokenRegistryContract = await ethers.getContractAt("TokenRegistry", tokenRegistry.address);
            const totalTokens = await tokenRegistryContract.getTotalTokens();
            const activeTokens = await tokenRegistryContract.getActiveTokens();
            console.log(`✅ TokenRegistry has ${totalTokens} total tokens, ${activeTokens.length} active`);

            // Test SecurityManager
            const securityManagerContract = await ethers.getContractAt("SecurityManager", securityManager.address);
            const circuitBreakerConfig = await securityManagerContract.circuitBreaker();
            console.log(`✅ SecurityManager circuit breaker enabled: ${circuitBreakerConfig.enabled}`);

            // Test SmartRouter
            const smartRouterContract = await ethers.getContractAt("SmartRouter", smartRouter.address);
            const intermediateTokenCount = await smartRouterContract.getIntermediateTokenCount();
            console.log(`✅ SmartRouter has ${intermediateTokenCount} intermediate tokens`);

            // Test PriceOracle
            console.log(`✅ PriceOracle deployed and configured`);

            // Test a simple swap if test tokens exist
            if (deployments.TokenA && deployments.TokenB) {
                try {
                    const path = await smartRouterContract.findOptimalPath(
                        deployments.TokenA,
                        deployments.TokenB,
                        ethers.utils.parseEther("1")
                    );
                    console.log(`✅ SmartRouter found path: ${path.isValid ? 'Valid' : 'Invalid'}`);
                } catch (error) {
                    console.log(`⚠️ SmartRouter path finding test failed:`, error.message);
                }
            }
        }
        
    } catch (error) {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    }
}

// Create deployments directory if it doesn't exist
if (!fs.existsSync("deployments")) {
    fs.mkdirSync("deployments");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });