const { ethers } = require("hardhat");
const fs = require("fs");

async function main() {
    const [deployer] = await ethers.getSigners();
    
    console.log(" Starting deployment...");
    console.log("Deploying contracts with account:", deployer.address);
    console.log("Account balance:", (await deployer.getBalance()).toString());
    
    const deployments = {};
    
    try {
        // 1. Deploy WETH
        console.log("\n📦 Deploying WETH9...");
        const WETH = await ethers.getContractFactory("WETH9");
        const weth = await WETH.deploy();
        await weth.deployed();
        deployments.WETH = weth.address;
        console.log("✅ WETH deployed to:", weth.address);
        
        // 2. Deploy Factory
        console.log("\n📦 Deploying SwapFactory...");
        const SwapFactory = await ethers.getContractFactory("SwapFactory");
        const factory = await SwapFactory.deploy(deployer.address);
        await factory.deployed();
        deployments.Factory = factory.address;
        console.log("✅ SwapFactory deployed to:", factory.address);
        
        // 3. Deploy Router
        console.log("\n📦 Deploying SwapRouter...");
        const SwapRouter = await ethers.getContractFactory("SwapRouter");
        const router = await SwapRouter.deploy(factory.address, weth.address);
        await router.deployed();
        deployments.Router = router.address;
        console.log("✅ SwapRouter deployed to:", router.address);
        
        // 4. Deploy test tokens (only on testnet/localhost)
        const network = await ethers.provider.getNetwork();
        if (network.chainId === 31337 || network.chainId === 5) { // localhost or goerli
            console.log("\n📦 Deploying test tokens...");
            
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            
            const tokenA = await ERC20Mock.deploy("Test Token A", "TTA", ethers.utils.parseEther("1000000"));
            await tokenA.deployed();
            deployments.TokenA = tokenA.address;
            console.log("✅ Test Token A deployed to:", tokenA.address);
            
            const tokenB = await ERC20Mock.deploy("Test Token B", "TTB", ethers.utils.parseEther("1000000"));
            await tokenB.deployed();
            deployments.TokenB = tokenB.address;
            console.log("✅ Test Token B deployed to:", tokenB.address);
            
            const usdc = await ERC20Mock.deploy("USD Coin", "USDC", ethers.utils.parseUnits("1000000", 6));
            await usdc.deployed();
            deployments.USDC = usdc.address;
            console.log("✅ USDC Mock deployed to:", usdc.address);
            
            // Create initial pairs
            console.log("\n🔗 Creating initial pairs...");
            
            await factory.createPair(tokenA.address, tokenB.address);
            const pairAB = await factory.getPair(tokenA.address, tokenB.address);
            deployments.PairAB = pairAB;
            console.log("✅ TokenA-TokenB pair created at:", pairAB);
            
            await factory.createPair(tokenA.address, weth.address);
            const pairAETH = await factory.getPair(tokenA.address, weth.address);
            deployments.PairAETH = pairAETH;
            console.log("✅ TokenA-WETH pair created at:", pairAETH);
            
            await factory.createPair(usdc.address, weth.address);
            const pairUSDCETH = await factory.getPair(usdc.address, weth.address);
            deployments.PairUSDCETH = pairUSDCETH;
            console.log("✅ USDC-WETH pair created at:", pairUSDCETH);
        }
        
        // 5. Verify contracts (if not localhost)
        if (network.chainId !== 31337) {
            console.log("\n🔍 Waiting for block confirmations...");
            await weth.deployTransaction.wait(5);
            await factory.deployTransaction.wait(5);
            await router.deployTransaction.wait(5);
            
            console.log("📋 Run these commands to verify contracts:");
            console.log(`npx hardhat verify --network ${network.name} ${weth.address}`);
            console.log(`npx hardhat verify --network ${network.name} ${factory.address} "${deployer.address}"`);
            console.log(`npx hardhat verify --network ${network.name} ${router.address} "${factory.address}" "${weth.address}"`);
        }
        
        // 6. Save deployment info
        const deploymentInfo = {
            network: network.name,
            chainId: network.chainId,
            deployer: deployer.address,
            timestamp: new Date().toISOString(),
            contracts: deployments,
            gasUsed: {
                // These would be calculated from transaction receipts
                total: "Calculated after deployment"
            }
        };
        
        fs.writeFileSync(
            `deployments/${network.name}-${Date.now()}.json`,
            JSON.stringify(deploymentInfo, null, 2)
        );
        
        console.log("\n🎉 Deployment completed successfully!");
        console.log("\n=== DEPLOYMENT SUMMARY ===");
        Object.entries(deployments).forEach(([name, address]) => {
            console.log(`${name}: ${address}`);
        });
        
        console.log("\n📝 Deployment info saved to deployments/ directory");
        
        // 7. Basic functionality test
        if (network.chainId === 31337) {
            console.log("\n🧪 Running basic functionality test...");
            
            const factoryContract = await ethers.getContractAt("SwapFactory", factory.address);
            const allPairsLength = await factoryContract.allPairsLength();
            console.log(`✅ Factory has ${allPairsLength} pairs created`);
            
            const routerContract = await ethers.getContractAt("SwapRouter", router.address);
            const factoryAddress = await routerContract.factory();
            const wethAddress = await routerContract.WETH();
            
            console.log(`✅ Router connected to factory: ${factoryAddress}`);
            console.log(`✅ Router connected to WETH: ${wethAddress}`);
        }
        
    } catch (error) {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    }
}

// Create deployments directory if it doesn't exist
if (!fs.existsSync("deployments")) {
    fs.mkdirSync("deployments");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });