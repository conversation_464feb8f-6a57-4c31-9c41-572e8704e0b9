const fs = require('fs');
const path = require('path');

/**
 * @title Export Contract Addresses
 * @dev Script to export deployed contract addresses to frontend
 */

async function exportAddresses() {
    console.log("📤 Exporting contract addresses to frontend...");

    try {
        // Read deployments.json
        const deploymentsPath = path.join(__dirname, '..', 'deployments.json');
        if (!fs.existsSync(deploymentsPath)) {
            console.log("❌ deployments.json not found. Please run deployment first.");
            return;
        }

        const deployments = JSON.parse(fs.readFileSync(deploymentsPath, 'utf8'));
        const network = await ethers.provider.getNetwork();

        // Create contract addresses configuration
        const contractConfig = {
            chainId: network.chainId,
            name: network.chainId === 1 ? 'Ethereum Mainnet' : 'Sepolia Testnet',
            rpcUrl: network.chainId === 1 
                ? 'https://mainnet.infura.io/v3/********************************'
                : 'https://sepolia.infura.io/v3/********************************',
            blockExplorer: network.chainId === 1 
                ? 'https://etherscan.io'
                : 'https://sepolia.etherscan.io',
            contracts: {
                router: deployments.Router || '******************************************',
                factory: deployments.Factory || '******************************************',
                tokenRegistry: deployments.TokenRegistry || '******************************************',
                securityManager: deployments.SecurityManager || '******************************************',
                priceOracle: deployments.PriceOracle || '******************************************',
                smartRouter: deployments.SmartRouter || '******************************************',
                liquidityMining: deployments.LiquidityMining || '******************************************',
                governanceToken: deployments.GovernanceToken || '******************************************',
                ...(deployments.TestTokenDeployer && { testTokenDeployer: deployments.TestTokenDeployer })
            }
        };

        // Export to TypeScript file
        const frontendPath = path.join(__dirname, '..', 'swap frontend', 'src', 'config');
        if (!fs.existsSync(frontendPath)) {
            fs.mkdirSync(frontendPath, { recursive: true });
        }

        const configContent = `// Auto-generated contract addresses
// Generated on: ${new Date().toISOString()}
// Network: ${contractConfig.name} (${contractConfig.chainId})

export const NETWORK_CONFIG = ${JSON.stringify(contractConfig, null, 2)};

export const CONTRACT_ADDRESSES = NETWORK_CONFIG.contracts;

export const PLATFORM_FEE_RECIPIENT = '******************************************';

// Network configurations for both mainnet and testnet
export const NETWORKS = {
  1: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    rpcUrl: 'https://mainnet.infura.io/v3/********************************',
    blockExplorer: 'https://etherscan.io',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18,
    },
    contracts: {
      // Will be updated with actual mainnet addresses
      router: '******************************************',
      factory: '******************************************',
      tokenRegistry: '******************************************',
      securityManager: '******************************************',
      priceOracle: '******************************************',
      smartRouter: '******************************************',
      liquidityMining: '******************************************',
      governanceToken: '******************************************',
    },
  },
  11155111: {
    chainId: 11155111,
    name: 'Sepolia Testnet',
    rpcUrl: 'https://sepolia.infura.io/v3/********************************',
    blockExplorer: 'https://sepolia.etherscan.io',
    nativeCurrency: {
      name: 'Sepolia ETH',
      symbol: 'SEP',
      decimals: 18,
    },
    contracts: ${JSON.stringify(contractConfig.contracts, null, 6)},
  },
};

// Update the current network configuration
NETWORKS[${contractConfig.chainId}].contracts = ${JSON.stringify(contractConfig.contracts, null, 2)};
`;

        const configFilePath = path.join(frontendPath, 'contracts.ts');
        fs.writeFileSync(configFilePath, configContent);

        console.log("✅ Contract addresses exported to:", configFilePath);
        console.log("\n📋 Exported Configuration:");
        console.log(`Network: ${contractConfig.name}`);
        console.log(`Chain ID: ${contractConfig.chainId}`);
        console.log("\n🏗️ Contract Addresses:");
        Object.entries(contractConfig.contracts).forEach(([name, address]) => {
            console.log(`  ${name}: ${address}`);
        });

        // Also update useWeb3.ts with the new addresses
        const useWeb3Path = path.join(__dirname, '..', 'swap frontend', 'src', 'hooks', 'useWeb3.ts');
        if (fs.existsSync(useWeb3Path)) {
            let useWeb3Content = fs.readFileSync(useWeb3Path, 'utf8');
            
            // Update the NETWORKS configuration
            const networksRegex = /export const NETWORKS: Record<number, NetworkConfig> = \{[\s\S]*?\};/;
            const newNetworksConfig = `export const NETWORKS: Record<number, NetworkConfig> = {
  // Ethereum Mainnet
  1: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    rpcUrl: 'https://mainnet.infura.io/v3/********************************',
    blockExplorer: 'https://etherscan.io',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18,
    },
    contracts: {
      router: '******************************************', // Will be updated after deployment
      factory: '******************************************',
      tokenRegistry: '******************************************',
      securityManager: '******************************************',
      priceOracle: '******************************************',
      smartRouter: '******************************************',
      liquidityMining: '******************************************',
      governanceToken: '******************************************',
    },
  },
  // Sepolia Testnet
  11155111: {
    chainId: 11155111,
    name: 'Sepolia Testnet',
    rpcUrl: 'https://sepolia.infura.io/v3/********************************',
    blockExplorer: 'https://sepolia.etherscan.io',
    nativeCurrency: {
      name: 'Sepolia ETH',
      symbol: 'SEP',
      decimals: 18,
    },
    contracts: ${JSON.stringify(contractConfig.contracts, null, 6)},
  },
};`;

            if (networksRegex.test(useWeb3Content)) {
                useWeb3Content = useWeb3Content.replace(networksRegex, newNetworksConfig);
                fs.writeFileSync(useWeb3Path, useWeb3Content);
                console.log("✅ Updated useWeb3.ts with new contract addresses");
            }
        }

        // Create a summary file
        const summaryPath = path.join(__dirname, '..', 'DEPLOYMENT_SUMMARY.md');
        const summaryContent = `# Deployment Summary

**Generated:** ${new Date().toISOString()}
**Network:** ${contractConfig.name} (Chain ID: ${contractConfig.chainId})

## Contract Addresses

${Object.entries(contractConfig.contracts).map(([name, address]) => 
    `- **${name}**: \`${address}\``
).join('\n')}

## Platform Configuration

- **Platform Fee Recipient**: \`******************************************\`
- **Default Platform Fee**: 0.3% (30 basis points)
- **Network**: ${contractConfig.name}
- **RPC URL**: ${contractConfig.rpcUrl}
- **Block Explorer**: ${contractConfig.blockExplorer}

## Frontend Integration

Contract addresses have been automatically exported to:
- \`swap frontend/src/config/contracts.ts\`
- \`swap frontend/src/hooks/useWeb3.ts\` (updated)

## Next Steps

1. **Install Dependencies**:
   \`\`\`bash
   cd "swap frontend"
   npm install
   \`\`\`

2. **Start Frontend**:
   \`\`\`bash
   npm run dev
   \`\`\`

3. **Connect Wallet** and switch to ${contractConfig.name}

4. **Test Swaps** with the deployed contracts

${network.chainId === 11155111 ? `
## Testnet Setup

For Sepolia testnet, run the setup script to deploy test tokens and add initial liquidity:

\`\`\`bash
npm run setup:sepolia
\`\`\`

This will:
- Deploy test tokens (TUSDC, TUSDT, TDAI, etc.)
- Add initial liquidity to pairs
- Enable faucet functionality for test tokens
` : ''}

## Verification

Verify contracts on Etherscan:
${Object.entries(contractConfig.contracts).map(([name, address]) => 
    `- [${name}](${contractConfig.blockExplorer}/address/${address})`
).join('\n')}
`;

        fs.writeFileSync(summaryPath, summaryContent);
        console.log("✅ Deployment summary created:", summaryPath);

    } catch (error) {
        console.error("❌ Error exporting addresses:", error);
    }
}

// Run if called directly
if (require.main === module) {
    exportAddresses()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
}

module.exports = { exportAddresses };
