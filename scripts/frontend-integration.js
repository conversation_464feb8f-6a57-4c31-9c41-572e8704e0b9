const { ethers } = require("hardhat");

/**
 * @title SwapSDK
 * @dev Comprehensive SDK for frontend integration with the DEX
 * @notice Provides easy-to-use functions for interacting with all DEX contracts
 */
class SwapSDK {
    constructor(config, provider) {
        this.provider = provider;
        this.config = config;

        // Initialize contract instances
        this.router = null;
        this.factory = null;
        this.tokenRegistry = null;
        this.securityManager = null;
        this.priceOracle = null;
        this.smartRouter = null;
        this.liquidityMining = null;
        this.governanceToken = null;

        // Initialize contracts asynchronously
        this.initializeContracts(config);

        // Cache for frequently accessed data
        this.cache = {
            supportedTokens: null,
            verifiedTokens: null,
            pools: null,
            lastUpdate: 0
        };

        this.CACHE_DURATION = 300000; // 5 minutes
    }

    /**
     * @dev Initialize contract instances with ABIs
     */
    async initializeContracts(config) {
        try {
            // Get contract artifacts
            const SwapRouter = await ethers.getContractFactory("SwapRouter");
            const SwapFactory = await ethers.getContractFactory("SwapFactory");
            const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
            const SecurityManager = await ethers.getContractFactory("SecurityManager");
            const PriceOracle = await ethers.getContractFactory("PriceOracle");
            const SmartRouter = await ethers.getContractFactory("SmartRouter");
            const LiquidityMining = await ethers.getContractFactory("LiquidityMining");
            const GovernanceToken = await ethers.getContractFactory("GovernanceToken");

            // Initialize contracts
            this.router = SwapRouter.attach(config.router);
            this.factory = SwapFactory.attach(config.factory);
            this.tokenRegistry = TokenRegistry.attach(config.tokenRegistry);
            this.securityManager = SecurityManager.attach(config.securityManager);
            this.priceOracle = PriceOracle.attach(config.priceOracle);
            this.smartRouter = SmartRouter.attach(config.smartRouter);
            this.liquidityMining = LiquidityMining.attach(config.liquidityMining);
            this.governanceToken = GovernanceToken.attach(config.governanceToken);

            console.log("✅ All contracts initialized successfully");
        } catch (error) {
            console.error("❌ Error initializing contracts:", error);
        }
    }

    /**
     * @dev Get token decimals with caching
     */
    async getTokenDecimals(tokenAddress) {
        try {
            const ERC20 = await ethers.getContractFactory("ERC20Mock");
            const token = ERC20.attach(tokenAddress);
            return await token.decimals();
        } catch (error) {
            console.error("Error getting token decimals:", error);
            return 18; // Default to 18 decimals
        }
    }

    // **** TOKEN FUNCTIONS ****

    /**
     * @dev Get supported tokens from registry
     */
    async getSupportedTokens(useCache = true) {
        if (useCache && this.cache.supportedTokens && Date.now() - this.cache.lastUpdate < this.CACHE_DURATION) {
            return this.cache.supportedTokens;
        }

        try {
            const tokens = await this.tokenRegistry.getActiveTokens();
            const tokenDetails = await Promise.all(
                tokens.map(async (address) => {
                    const info = await this.tokenRegistry.getTokenInfo(address);
                    return {
                        address,
                        symbol: info.symbol,
                        name: info.name,
                        decimals: info.decimals,
                        logoURI: info.logoURI,
                        website: info.website,
                        marketCap: info.marketCap.toString(),
                        isVerified: info.isVerified
                    };
                })
            );

            this.cache.supportedTokens = tokenDetails;
            this.cache.lastUpdate = Date.now();
            return tokenDetails;
        } catch (error) {
            console.error("Error fetching supported tokens:", error);
            return [];
        }
    }

    /**
     * @dev Get verified tokens only
     */
    async getVerifiedTokens() {
        try {
            const tokens = await this.tokenRegistry.getVerifiedTokens();
            const tokenDetails = await Promise.all(
                tokens.map(async (address) => {
                    const info = await this.tokenRegistry.getTokenInfo(address);
                    return {
                        address,
                        symbol: info.symbol,
                        name: info.name,
                        decimals: info.decimals,
                        logoURI: info.logoURI,
                        website: info.website,
                        marketCap: info.marketCap.toString(),
                        isVerified: true
                    };
                })
            );

            return tokenDetails;
        } catch (error) {
            console.error("Error fetching verified tokens:", error);
            return [];
        }
    }

    /**
     * @dev Get token price with multiple sources
     */
    async getTokenPrice(tokenA, tokenB, amountIn) {
        try {
            // Try to get price from oracle first
            const [oraclePrice, reliable] = await this.priceOracle.getCombinedPrice(tokenA, tokenB);

            if (reliable && oraclePrice.gt(0)) {
                const decimalsA = await this.getTokenDecimals(tokenA);
                const decimalsB = await this.getTokenDecimals(tokenB);
                const adjustedAmountIn = ethers.utils.parseUnits(amountIn.toString(), decimalsA);
                const amountOut = adjustedAmountIn.mul(oraclePrice).div(ethers.utils.parseEther("1"));
                return ethers.utils.formatUnits(amountOut, decimalsB);
            }

            // Fallback to router calculation
            const decimalsA = await this.getTokenDecimals(tokenA);
            const adjustedAmountIn = ethers.utils.parseUnits(amountIn.toString(), decimalsA);
            const amounts = await this.router.getAmountsOut(adjustedAmountIn, [tokenA, tokenB]);

            const decimalsB = await this.getTokenDecimals(tokenB);
            return ethers.utils.formatUnits(amounts[1], decimalsB);
        } catch (error) {
            console.error("Price calculation error:", error);
            return "0";
        }
    }

    /**
     * @dev Get optimal swap path
     */
    async getOptimalPath(tokenIn, tokenOut, amountIn) {
        try {
            const decimals = await this.getTokenDecimals(tokenIn);
            const adjustedAmountIn = ethers.utils.parseUnits(amountIn.toString(), decimals);

            const pathInfo = await this.smartRouter.findOptimalPath(tokenIn, tokenOut, adjustedAmountIn);

            if (!pathInfo.isValid) {
                return null;
            }

            return {
                tokens: pathInfo.tokens,
                expectedOutput: ethers.utils.formatUnits(pathInfo.expectedOutput, await this.getTokenDecimals(tokenOut)),
                priceImpact: pathInfo.priceImpact.toNumber() / 100, // Convert to percentage
                gasEstimate: pathInfo.gasEstimate.toNumber(),
                isValid: pathInfo.isValid
            };
        } catch (error) {
            console.error("Error finding optimal path:", error);
            return null;
        }
    }
    
    // Swap işlemi yap
    async swapTokens(tokenA, tokenB, amountIn, minAmountOut, signer) {
        const deadline = Math.floor(Date.now() / 1000) + 1800; // 30 dakika
        
        try {
            const tx = await this.router.connect(signer).swapExactTokensForTokens(
                ethers.utils.parseEther(amountIn.toString()),
                ethers.utils.parseEther(minAmountOut.toString()),
                [tokenA, tokenB],
                await signer.getAddress(),
                deadline
            );
            
            return await tx.wait();
        } catch (error) {
            console.error("Swap error:", error);
            throw error;
        }
    }
    
    // Likidite ekle
    async addLiquidity(tokenA, tokenB, amountA, amountB, signer) {
        const deadline = Math.floor(Date.now() / 1000) + 1800;
        
        try {
            const tx = await this.router.connect(signer).addLiquidity(
                tokenA,
                tokenB,
                ethers.utils.parseEther(amountA.toString()),
                ethers.utils.parseEther(amountB.toString()),
                0, // min amounts - should be calculated properly in production
                0,
                await signer.getAddress(),
                deadline
            );
            
            return await tx.wait();
        } catch (error) {
            console.error("Add liquidity error:", error);
            throw error;
        }
    }
    
    // Pair bilgilerini al
    async getPairInfo(tokenA, tokenB) {
        try {
            const pairAddress = await this.factory.getPair(tokenA, tokenB);
            if (pairAddress === ethers.constants.AddressZero) {
                return null;
            }
            
            const pair = new ethers.Contract(pairAddress, pairABI, this.provider);
            const reserves = await pair.getReserves();
            const totalSupply = await pair.totalSupply();
            
            return {
                address: pairAddress,
                reserve0: ethers.utils.formatEther(reserves[0]),
                reserve1: ethers.utils.formatEther(reserves[1]),
                totalSupply: ethers.utils.formatEther(totalSupply)
            };
        } catch (error) {
            console.error("Get pair info error:", error);
            return null;
        }
    }

    // **** SWAP FUNCTIONS ****

    /**
     * @dev Execute token swap
     */
    async executeSwap(tokenIn, tokenOut, amountIn, minAmountOut, userAddress, signer) {
        try {
            // Get optimal path
            const pathInfo = await this.getOptimalPath(tokenIn, tokenOut, amountIn);
            if (!pathInfo) {
                throw new Error("No valid swap path found");
            }

            // Check security limits
            const decimals = await this.getTokenDecimals(tokenIn);
            const adjustedAmountIn = ethers.utils.parseUnits(amountIn.toString(), decimals);

            const isAllowed = await this.securityManager.checkTradeAllowed(
                userAddress,
                adjustedAmountIn,
                Math.floor(pathInfo.priceImpact * 100) // Convert to basis points
            );

            if (!isAllowed) {
                throw new Error("Trade blocked by security manager");
            }

            // Execute swap
            const deadline = Math.floor(Date.now() / 1000) + 1800; // 30 minutes
            const routerWithSigner = this.router.connect(signer);

            const tx = await routerWithSigner.swapExactTokensForTokens(
                adjustedAmountIn,
                ethers.utils.parseUnits(minAmountOut.toString(), await this.getTokenDecimals(tokenOut)),
                pathInfo.tokens,
                userAddress,
                deadline
            );

            return {
                hash: tx.hash,
                wait: () => tx.wait()
            };
        } catch (error) {
            console.error("Swap execution error:", error);
            throw error;
        }
    }

    // **** LIQUIDITY FUNCTIONS ****

    /**
     * @dev Add liquidity to a pair
     */
    async addLiquidity(tokenA, tokenB, amountA, amountB, minAmountA, minAmountB, userAddress, signer) {
        try {
            const deadline = Math.floor(Date.now() / 1000) + 1800; // 30 minutes
            const routerWithSigner = this.router.connect(signer);

            const decimalsA = await this.getTokenDecimals(tokenA);
            const decimalsB = await this.getTokenDecimals(tokenB);

            const tx = await routerWithSigner.addLiquidity(
                tokenA,
                tokenB,
                ethers.utils.parseUnits(amountA.toString(), decimalsA),
                ethers.utils.parseUnits(amountB.toString(), decimalsB),
                ethers.utils.parseUnits(minAmountA.toString(), decimalsA),
                ethers.utils.parseUnits(minAmountB.toString(), decimalsB),
                userAddress,
                deadline
            );

            return {
                hash: tx.hash,
                wait: () => tx.wait()
            };
        } catch (error) {
            console.error("Add liquidity error:", error);
            throw error;
        }
    }

    /**
     * @dev Get pair information
     */
    async getPairInfo(tokenA, tokenB) {
        try {
            const pairAddress = await this.factory.getPair(tokenA, tokenB);
            if (pairAddress === ethers.constants.AddressZero) {
                return null;
            }

            const SwapPair = await ethers.getContractFactory("SwapPair");
            const pair = SwapPair.attach(pairAddress);

            const [reserves, totalSupply] = await Promise.all([
                pair.getReserves(),
                pair.totalSupply()
            ]);

            const token0 = await pair.token0();
            const token1 = await pair.token1();

            return {
                address: pairAddress,
                token0,
                token1,
                reserve0: ethers.utils.formatUnits(reserves[0], await this.getTokenDecimals(token0)),
                reserve1: ethers.utils.formatUnits(reserves[1], await this.getTokenDecimals(token1)),
                totalSupply: ethers.utils.formatEther(totalSupply),
                lastUpdate: reserves[2]
            };
        } catch (error) {
            console.error("Error getting pair info:", error);
            return null;
        }
    }

    // **** LIQUIDITY MINING FUNCTIONS ****

    /**
     * @dev Get all liquidity mining pools
     */
    async getLiquidityPools() {
        try {
            const poolLength = await this.liquidityMining.poolLength();
            const pools = [];

            for (let i = 0; i < poolLength; i++) {
                const poolInfo = await this.liquidityMining.getPoolInfo(i);
                const apr = await this.liquidityMining.getPoolAPR(i);

                pools.push({
                    pid: i,
                    lpToken: poolInfo.lpToken,
                    allocPoint: poolInfo.allocPoint.toString(),
                    totalStaked: poolInfo.totalStaked.toString(),
                    isActive: poolInfo.isActive,
                    name: poolInfo.name,
                    apr: apr.toNumber() / 100 // Convert from basis points to percentage
                });
            }

            return pools;
        } catch (error) {
            console.error("Error getting liquidity pools:", error);
            return [];
        }
    }

    /**
     * @dev Get user's staking information
     */
    async getUserStakingInfo(userAddress) {
        try {
            const poolLength = await this.liquidityMining.poolLength();
            const stakingInfo = [];

            for (let i = 0; i < poolLength; i++) {
                const userInfo = await this.liquidityMining.getUserInfo(i, userAddress);
                const [primaryReward, bonusRewards] = await this.liquidityMining.pendingRewards(i, userAddress);

                if (userInfo.amount.gt(0)) {
                    stakingInfo.push({
                        pid: i,
                        stakedAmount: ethers.utils.formatEther(userInfo.amount),
                        pendingReward: ethers.utils.formatEther(primaryReward),
                        bonusRewards: bonusRewards.map(reward => ethers.utils.formatEther(reward)),
                        stakingStartBlock: userInfo.stakingStartBlock.toNumber(),
                        lastClaimBlock: userInfo.lastClaimBlock.toNumber()
                    });
                }
            }

            return stakingInfo;
        } catch (error) {
            console.error("Error getting user staking info:", error);
            return [];
        }
    }

    // **** GOVERNANCE FUNCTIONS ****

    /**
     * @dev Get governance token information
     */
    async getGovernanceTokenInfo(userAddress) {
        try {
            const [balance, votes, totalSupply, remainingMintingCapacity] = await Promise.all([
                this.governanceToken.balanceOf(userAddress),
                this.governanceToken.getVotes(userAddress),
                this.governanceToken.totalSupply(),
                this.governanceToken.getRemainingMintingCapacity()
            ]);

            return {
                balance: ethers.utils.formatEther(balance),
                votes: ethers.utils.formatEther(votes),
                totalSupply: ethers.utils.formatEther(totalSupply),
                remainingMintingCapacity: ethers.utils.formatEther(remainingMintingCapacity)
            };
        } catch (error) {
            console.error("Error getting governance token info:", error);
            return null;
        }
    }

    // **** UTILITY FUNCTIONS ****

    /**
     * @dev Check if user needs to approve token spending
     */
    async checkAllowance(tokenAddress, userAddress, spenderAddress, amount) {
        try {
            const ERC20 = await ethers.getContractFactory("ERC20Mock");
            const token = ERC20.attach(tokenAddress);

            const allowance = await token.allowance(userAddress, spenderAddress);
            const decimals = await token.decimals();
            const requiredAmount = ethers.utils.parseUnits(amount.toString(), decimals);

            return allowance.gte(requiredAmount);
        } catch (error) {
            console.error("Error checking allowance:", error);
            return false;
        }
    }

    /**
     * @dev Approve token spending
     */
    async approveToken(tokenAddress, spenderAddress, amount, signer) {
        try {
            const ERC20 = await ethers.getContractFactory("ERC20Mock");
            const token = ERC20.attach(tokenAddress).connect(signer);

            const decimals = await token.decimals();
            const approveAmount = amount === "max"
                ? ethers.constants.MaxUint256
                : ethers.utils.parseUnits(amount.toString(), decimals);

            const tx = await token.approve(spenderAddress, approveAmount);
            return {
                hash: tx.hash,
                wait: () => tx.wait()
            };
        } catch (error) {
            console.error("Error approving token:", error);
            throw error;
        }
    }

    /**
     * @dev Get user's token balance
     */
    async getTokenBalance(tokenAddress, userAddress) {
        try {
            const ERC20 = await ethers.getContractFactory("ERC20Mock");
            const token = ERC20.attach(tokenAddress);

            const [balance, decimals] = await Promise.all([
                token.balanceOf(userAddress),
                token.decimals()
            ]);

            return ethers.utils.formatUnits(balance, decimals);
        } catch (error) {
            console.error("Error getting token balance:", error);
            return "0";
        }
    }

    /**
     * @dev Get network gas price
     */
    async getGasPrice() {
        try {
            const gasPrice = await this.provider.getGasPrice();
            return ethers.utils.formatUnits(gasPrice, "gwei");
        } catch (error) {
            console.error("Error getting gas price:", error);
            return "20"; // Default 20 gwei
        }
    }

    /**
     * @dev Subscribe to contract events
     */
    subscribeToEvents(eventName, callback) {
        try {
            switch (eventName) {
                case 'Swap':
                    this.router.on('Swap', callback);
                    break;
                case 'AddLiquidity':
                    this.router.on('AddLiquidity', callback);
                    break;
                case 'PairCreated':
                    this.factory.on('PairCreated', callback);
                    break;
                case 'Deposit':
                    this.liquidityMining.on('Deposit', callback);
                    break;
                case 'Withdraw':
                    this.liquidityMining.on('Withdraw', callback);
                    break;
                default:
                    console.warn(`Unknown event: ${eventName}`);
            }
        } catch (error) {
            console.error("Error subscribing to events:", error);
        }
    }

    /**
     * @dev Unsubscribe from all events
     */
    unsubscribeFromEvents() {
        try {
            this.router.removeAllListeners();
            this.factory.removeAllListeners();
            this.liquidityMining.removeAllListeners();
        } catch (error) {
            console.error("Error unsubscribing from events:", error);
        }
    }
}

// Export for use in frontend applications
module.exports = { SwapSDK };

const pairABI = [
    "function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)",
    "function totalSupply() external view returns (uint256)"
];

module.exports = { SwapSDK };