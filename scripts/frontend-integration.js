const { ethers } = require("hardhat");

// Frontend entegrasyonu için yardımcı fonksiyonlar
class SwapSDK {
    constructor(routerAddress, factoryAddress, provider) {
        this.router = new ethers.Contract(routerAddress, routerABI, provider);
        this.factory = new ethers.Contract(factoryAddress, factoryABI, provider);
        this.provider = provider;
    }
    
    // Token fiyatı al
    async getTokenPrice(tokenA, tokenB, amountIn) {
        try {
            const amounts = await this.router.getAmountsOut(
                ethers.utils.parseEther(amountIn.toString()),
                [tokenA, tokenB]
            );
            return ethers.utils.formatEther(amounts[1]);
        } catch (error) {
            console.error("Price calculation error:", error);
            return "0";
        }
    }
    
    // Swap işlemi yap
    async swapTokens(tokenA, tokenB, amountIn, minAmountOut, signer) {
        const deadline = Math.floor(Date.now() / 1000) + 1800; // 30 dakika
        
        try {
            const tx = await this.router.connect(signer).swapExactTokensForTokens(
                ethers.utils.parseEther(amountIn.toString()),
                ethers.utils.parseEther(minAmountOut.toString()),
                [tokenA, tokenB],
                await signer.getAddress(),
                deadline
            );
            
            return await tx.wait();
        } catch (error) {
            console.error("Swap error:", error);
            throw error;
        }
    }
    
    // Likidite ekle
    async addLiquidity(tokenA, tokenB, amountA, amountB, signer) {
        const deadline = Math.floor(Date.now() / 1000) + 1800;
        
        try {
            const tx = await this.router.connect(signer).addLiquidity(
                tokenA,
                tokenB,
                ethers.utils.parseEther(amountA.toString()),
                ethers.utils.parseEther(amountB.toString()),
                0, // min amounts - should be calculated properly in production
                0,
                await signer.getAddress(),
                deadline
            );
            
            return await tx.wait();
        } catch (error) {
            console.error("Add liquidity error:", error);
            throw error;
        }
    }
    
    // Pair bilgilerini al
    async getPairInfo(tokenA, tokenB) {
        try {
            const pairAddress = await this.factory.getPair(tokenA, tokenB);
            if (pairAddress === ethers.constants.AddressZero) {
                return null;
            }
            
            const pair = new ethers.Contract(pairAddress, pairABI, this.provider);
            const reserves = await pair.getReserves();
            const totalSupply = await pair.totalSupply();
            
            return {
                address: pairAddress,
                reserve0: ethers.utils.formatEther(reserves[0]),
                reserve1: ethers.utils.formatEther(reserves[1]),
                totalSupply: ethers.utils.formatEther(totalSupply)
            };
        } catch (error) {
            console.error("Get pair info error:", error);
            return null;
        }
    }
}

// ABI'ler (kısaltılmış)
const routerABI = [
    "function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)",
    "function addLiquidity(address tokenA, address tokenB, uint amountADesired, uint amountBDesired, uint amountAMin, uint amountBMin, address to, uint deadline) external returns (uint amountA, uint amountB, uint liquidity)",
    "function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)"
];

const factoryABI = [
    "function getPair(address tokenA, address tokenB) external view returns (address pair)",
    "function createPair(address tokenA, address tokenB) external returns (address pair)"
];

const pairABI = [
    "function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)",
    "function totalSupply() external view returns (uint256)"
];

module.exports = { SwapSDK };