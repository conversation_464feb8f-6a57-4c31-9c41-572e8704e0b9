const { ethers } = require("hardhat");

/**
 * @title Setup Test Tokens
 * @dev Script to setup test tokens and initial liquidity on Sepolia
 */

async function main() {
    console.log("🪙 Setting up Test Tokens for Sepolia");
    console.log("====================================\n");

    const [deployer] = await ethers.getSigners();
    console.log("Deployer address:", deployer.address);
    console.log("Deployer balance:", ethers.utils.formatEther(await deployer.getBalance()), "ETH\n");

    // Check if we're on Sepolia
    const network = await ethers.provider.getNetwork();
    if (network.chainId !== 11155111) {
        console.log("❌ This script is only for Sepolia testnet (Chain ID: 11155111)");
        console.log(`Current network: ${network.name} (Chain ID: ${network.chainId})`);
        return;
    }

    // Load deployment addresses
    let deployments;
    try {
        deployments = JSON.parse(require('fs').readFileSync('deployments.json', 'utf8'));
    } catch (error) {
        console.log("❌ deployments.json not found. Please run deployment first.");
        return;
    }

    // Get contracts
    const testTokenDeployer = await ethers.getContractAt("TestTokenDeployer", deployments.TestTokenDeployer);
    const factory = await ethers.getContractAt("SwapFactory", deployments.Factory);
    const router = await ethers.getContractAt("SwapRouter", deployments.Router);
    const tokenRegistry = await ethers.getContractAt("TokenRegistry", deployments.TokenRegistry);

    console.log("📋 Contract Addresses:");
    console.log(`TestTokenDeployer: ${testTokenDeployer.address}`);
    console.log(`Factory: ${factory.address}`);
    console.log(`Router: ${router.address}`);
    console.log(`TokenRegistry: ${tokenRegistry.address}\n`);

    // Get deployed test tokens
    const tokenCount = await testTokenDeployer.getTokenCount();
    console.log(`🪙 Found ${tokenCount} test tokens:`);

    const tokens = {};
    for (let i = 0; i < tokenCount; i++) {
        const tokenInfo = await testTokenDeployer.deployedTokens(i);
        tokens[tokenInfo.symbol] = {
            address: tokenInfo.tokenAddress,
            name: tokenInfo.name,
            symbol: tokenInfo.symbol,
            decimals: tokenInfo.decimals,
            contract: await ethers.getContractAt("TestToken", tokenInfo.tokenAddress)
        };
        console.log(`   ${tokenInfo.symbol}: ${tokenInfo.tokenAddress}`);
    }

    // WETH address
    const WETH_ADDRESS = "******************************************";
    const weth = await ethers.getContractAt("IWETH", WETH_ADDRESS);
    console.log(`   WETH: ${WETH_ADDRESS}\n`);

    // 1. Get test tokens from faucet
    console.log("💧 Getting tokens from faucet...");
    for (const [symbol, token] of Object.entries(tokens)) {
        try {
            const faucetAmount = symbol.includes("USDC") || symbol.includes("USDT") 
                ? 1000 * 10**6  // 1000 for 6 decimal tokens
                : ethers.utils.parseEther("1000"); // 1000 for 18 decimal tokens

            const tx = await token.contract.faucet(faucetAmount);
            await tx.wait();
            
            const balance = await token.contract.balanceOf(deployer.address);
            const formattedBalance = ethers.utils.formatUnits(balance, token.decimals);
            console.log(`   ✅ ${symbol}: ${formattedBalance} tokens`);
        } catch (error) {
            console.log(`   ❌ ${symbol}: ${error.message}`);
        }
    }

    // 2. Wrap some ETH to WETH
    console.log("\n🔄 Wrapping ETH to WETH...");
    try {
        const wrapAmount = ethers.utils.parseEther("1"); // Wrap 1 ETH
        const tx = await weth.deposit({ value: wrapAmount });
        await tx.wait();
        
        const wethBalance = await weth.balanceOf(deployer.address);
        console.log(`   ✅ WETH: ${ethers.utils.formatEther(wethBalance)} WETH`);
    } catch (error) {
        console.log(`   ❌ WETH wrapping failed: ${error.message}`);
    }

    // 3. Create pairs and add initial liquidity
    console.log("\n🏊 Creating pairs and adding liquidity...");

    const liquidityPairs = [
        { tokenA: "TUSDC", tokenB: "TUSDT", amountA: "500", amountB: "500" },
        { tokenA: "TUSDC", tokenB: "TDAI", amountA: "500", amountB: "500" },
        { tokenA: "TUNI", tokenB: "TAAVE", amountA: "100", amountB: "100" },
    ];

    // Add WETH pairs
    const wethPairs = [
        { token: "TUSDC", wethAmount: "0.5", tokenAmount: "1000" },
        { token: "TUSDT", wethAmount: "0.5", tokenAmount: "1000" },
        { token: "TDAI", wethAmount: "0.5", tokenAmount: "1000" },
        { token: "TUNI", wethAmount: "0.1", tokenAmount: "100" },
        { token: "TAAVE", wethAmount: "0.1", tokenAmount: "10" },
        { token: "TLINK", wethAmount: "0.1", tokenAmount: "100" },
        { token: "TWBTC", wethAmount: "0.1", tokenAmount: "0.01" },
    ];

    // Create token-token pairs
    for (const pair of liquidityPairs) {
        if (!tokens[pair.tokenA] || !tokens[pair.tokenB]) continue;

        try {
            const tokenA = tokens[pair.tokenA];
            const tokenB = tokens[pair.tokenB];

            // Check if pair exists
            const pairAddress = await factory.getPair(tokenA.address, tokenB.address);
            if (pairAddress === ethers.constants.AddressZero) {
                console.log(`   Creating pair ${pair.tokenA}-${pair.tokenB}...`);
                const createTx = await factory.createPair(tokenA.address, tokenB.address);
                await createTx.wait();
            }

            // Approve tokens
            const amountA = ethers.utils.parseUnits(pair.amountA, tokenA.decimals);
            const amountB = ethers.utils.parseUnits(pair.amountB, tokenB.decimals);

            await tokenA.contract.approve(router.address, amountA);
            await tokenB.contract.approve(router.address, amountB);

            // Add liquidity
            const deadline = Math.floor(Date.now() / 1000) + 1800; // 30 minutes
            const addLiquidityTx = await router.addLiquidity(
                tokenA.address,
                tokenB.address,
                amountA,
                amountB,
                0, // min amounts
                0,
                deployer.address,
                deadline
            );
            await addLiquidityTx.wait();

            console.log(`   ✅ Added liquidity: ${pair.amountA} ${pair.tokenA} + ${pair.amountB} ${pair.tokenB}`);
        } catch (error) {
            console.log(`   ❌ ${pair.tokenA}-${pair.tokenB}: ${error.message}`);
        }
    }

    // Create WETH pairs
    for (const pair of wethPairs) {
        if (!tokens[pair.token]) continue;

        try {
            const token = tokens[pair.token];

            // Check if pair exists
            const pairAddress = await factory.getPair(WETH_ADDRESS, token.address);
            if (pairAddress === ethers.constants.AddressZero) {
                console.log(`   Creating pair WETH-${pair.token}...`);
                const createTx = await factory.createPair(WETH_ADDRESS, token.address);
                await createTx.wait();
            }

            // Approve tokens
            const wethAmount = ethers.utils.parseEther(pair.wethAmount);
            const tokenAmount = ethers.utils.parseUnits(pair.tokenAmount, token.decimals);

            await weth.approve(router.address, wethAmount);
            await token.contract.approve(router.address, tokenAmount);

            // Add liquidity
            const deadline = Math.floor(Date.now() / 1000) + 1800;
            const addLiquidityTx = await router.addLiquidity(
                WETH_ADDRESS,
                token.address,
                wethAmount,
                tokenAmount,
                0,
                0,
                deployer.address,
                deadline
            );
            await addLiquidityTx.wait();

            console.log(`   ✅ Added liquidity: ${pair.wethAmount} WETH + ${pair.tokenAmount} ${pair.token}`);
        } catch (error) {
            console.log(`   ❌ WETH-${pair.token}: ${error.message}`);
        }
    }

    // 4. Test swaps
    console.log("\n🔄 Testing swaps...");

    const testSwaps = [
        { from: "TUSDC", to: "TUSDT", amount: "10" },
        { from: "TUNI", to: "TAAVE", amount: "5" },
        { from: "TUSDC", to: "TDAI", amount: "10" },
    ];

    for (const swap of testSwaps) {
        if (!tokens[swap.from] || !tokens[swap.to]) continue;

        try {
            const fromToken = tokens[swap.from];
            const toToken = tokens[swap.to];
            const swapAmount = ethers.utils.parseUnits(swap.amount, fromToken.decimals);

            // Get optimal path
            const path = await router.getOptimalSwapPath(fromToken.address, toToken.address);
            if (path.length === 0) {
                console.log(`   ❌ No path found for ${swap.from} -> ${swap.to}`);
                continue;
            }

            // Get expected output
            const amounts = await router.getAmountsOut(swapAmount, path);
            const expectedOutput = ethers.utils.formatUnits(amounts[amounts.length - 1], toToken.decimals);

            // Approve and swap
            await fromToken.contract.approve(router.address, swapAmount);
            
            const deadline = Math.floor(Date.now() / 1000) + 1800;
            const swapTx = await router.swapExactTokensForTokens(
                swapAmount,
                0, // min output
                path,
                deployer.address,
                deadline
            );
            await swapTx.wait();

            console.log(`   ✅ Swapped ${swap.amount} ${swap.from} -> ${expectedOutput} ${swap.to}`);
            console.log(`      Path: ${path.map(addr => {
                if (addr === WETH_ADDRESS) return "WETH";
                const token = Object.values(tokens).find(t => t.address === addr);
                return token ? token.symbol : addr.slice(0, 6) + "...";
            }).join(" -> ")}`);
        } catch (error) {
            console.log(`   ❌ ${swap.from} -> ${swap.to}: ${error.message}`);
        }
    }

    // 5. Test ETH-WETH swaps
    console.log("\n⚡ Testing ETH-WETH swaps...");

    try {
        // ETH -> WETH
        const ethAmount = ethers.utils.parseEther("0.01");
        const ethToWethTx = await router.swapETHForWETH({ value: ethAmount });
        await ethToWethTx.wait();
        console.log(`   ✅ Swapped 0.01 ETH -> WETH (with platform fee)`);

        // WETH -> ETH
        const wethAmount = ethers.utils.parseEther("0.005");
        await weth.approve(router.address, wethAmount);
        const wethToEthTx = await router.swapWETHForETH(wethAmount);
        await wethToEthTx.wait();
        console.log(`   ✅ Swapped 0.005 WETH -> ETH (with platform fee)`);
    } catch (error) {
        console.log(`   ❌ ETH-WETH swap failed: ${error.message}`);
    }

    // 6. Display platform fee info
    console.log("\n💰 Platform Fee Information:");
    console.log(`   Fee Recipient: ${await router.getPlatformFeeRecipient()}`);
    console.log(`   Fee Rate: ${await router.getPlatformFeeRate()} basis points (${(await router.getPlatformFeeRate()) / 100}%)`);

    console.log("\n🎉 Test token setup completed!");
    console.log("\n📋 Available tokens for swapping:");
    for (const [symbol, token] of Object.entries(tokens)) {
        const balance = await token.contract.balanceOf(deployer.address);
        const formattedBalance = ethers.utils.formatUnits(balance, token.decimals);
        console.log(`   ${symbol}: ${formattedBalance} (${token.address})`);
    }
    
    const wethBalance = await weth.balanceOf(deployer.address);
    console.log(`   WETH: ${ethers.utils.formatEther(wethBalance)} (${WETH_ADDRESS})`);

    console.log("\n💡 Next steps:");
    console.log("1. Use the frontend to connect to Sepolia");
    console.log("2. Import token addresses to MetaMask");
    console.log("3. Start swapping with platform fees!");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Error:", error);
        process.exit(1);
    });
