const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Starting simple deployment...");
  
  try {
    const [deployer] = await ethers.getSigners();
    console.log("📝 Deploying with account:", deployer.address);
    
    const balance = await deployer.getBalance();
    console.log("💰 Account balance:", ethers.utils.formatEther(balance), "ETH");

    // Deploy a simple test token first
    console.log("\n📦 Deploying Test Token...");
    const TestToken = await ethers.getContractFactory("TestToken");
    const testToken = await TestToken.deploy(
      "Test USDC",
      "TUSDC", 
      6,
      ethers.utils.parseUnits("1000000", 6)
    );
    
    await testToken.deployed();
    console.log("✅ Test Token deployed to:", testToken.address);

    // Test the token
    const name = await testToken.name();
    const symbol = await testToken.symbol();
    const decimals = await testToken.decimals();
    const totalSupply = await testToken.totalSupply();
    
    console.log("📋 Token Details:");
    console.log("Name:", name);
    console.log("Symbol:", symbol);
    console.log("Decimals:", decimals);
    console.log("Total Supply:", ethers.utils.formatUnits(totalSupply, decimals));

    // Save deployment info
    const deploymentInfo = {
      testToken: testToken.address,
      deployer: deployer.address,
      network: "sepolia",
      chainId: 11155111,
      timestamp: new Date().toISOString()
    };

    console.log("\n🎉 Deployment completed!");
    console.log("Contract Address:", testToken.address);
    
    return deploymentInfo;
  } catch (error) {
    console.error("❌ Deployment failed:", error);
    throw error;
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = main;
