const { ethers } = require("hardhat");

/**
 * @title Swap Examples
 * @dev Gerçek tokenlarla swap örnekleri
 * @notice Bu script gerçek Ethereum mainnet tokenları ile swap işlemlerini gösterir
 */

// Gerçek token adresleri - Ethereum Mainnet
const TOKENS = {
    // Stablecoins
    USDT: "******************************************",
    USDC: "******************************************", 
    DAI: "******************************************",
    BUSD: "******************************************",
    
    // Major tokens
    WETH: "******************************************",
    WBTC: "******************************************",
    
    // DeFi tokens
    UNI: "******************************************",
    AAVE: "******************************************",
    LINK: "******************************************",
    MKR: "******************************************",
    SUSHI: "******************************************",
    
    // Layer 2
    MATIC: "******************************************",
    
    // Meme
    SHIB: "******************************************"
};

// Token decimals
const DECIMALS = {
    [TOKENS.USDT]: 6,
    [TOKENS.USDC]: 6,
    [TOKENS.DAI]: 18,
    [TOKENS.BUSD]: 18,
    [TOKENS.WETH]: 18,
    [TOKENS.WBTC]: 8,
    [TOKENS.UNI]: 18,
    [TOKENS.AAVE]: 18,
    [TOKENS.LINK]: 18,
    [TOKENS.MKR]: 18,
    [TOKENS.SUSHI]: 18,
    [TOKENS.MATIC]: 18,
    [TOKENS.SHIB]: 18
};

async function main() {
    console.log("🚀 DEX Swap Examples - Gerçek Tokenlar");
    console.log("=====================================\n");

    // Get deployed contracts
    const deployments = JSON.parse(require('fs').readFileSync('deployments.json', 'utf8'));
    
    const router = await ethers.getContractAt("SwapRouter", deployments.Router);
    const smartRouter = await ethers.getContractAt("SmartRouter", deployments.SmartRouter);
    const priceOracle = await ethers.getContractAt("PriceOracle", deployments.PriceOracle);
    const tokenRegistry = await ethers.getContractAt("TokenRegistry", deployments.TokenRegistry);

    console.log("📋 Contract Addresses:");
    console.log(`Router: ${router.address}`);
    console.log(`SmartRouter: ${smartRouter.address}`);
    console.log(`PriceOracle: ${priceOracle.address}`);
    console.log(`TokenRegistry: ${tokenRegistry.address}\n`);

    // 1. Stablecoin Arbitrage Example
    console.log("💰 1. Stablecoin Arbitrage (USDC → USDT)");
    console.log("=========================================");
    
    try {
        const usdcAmount = ethers.utils.parseUnits("1000", DECIMALS[TOKENS.USDC]); // 1000 USDC
        const path = await smartRouter.findOptimalPath(TOKENS.USDC, TOKENS.USDT, usdcAmount);
        
        if (path.isValid) {
            console.log(`✅ Path found: ${path.tokens.join(" → ")}`);
            console.log(`💵 Input: 1000 USDC`);
            console.log(`💵 Expected Output: ${ethers.utils.formatUnits(path.expectedOutput, DECIMALS[TOKENS.USDT])} USDT`);
            console.log(`📊 Price Impact: ${(path.priceImpact / 100).toFixed(2)}%`);
            console.log(`⛽ Gas Estimate: ${path.gasEstimate.toLocaleString()}`);
        } else {
            console.log("❌ No valid path found");
        }
    } catch (error) {
        console.log(`⚠️ Error: ${error.message}`);
    }
    
    console.log();

    // 2. ETH/BTC Trading Example
    console.log("🔷 2. ETH/BTC Trading (WETH → WBTC)");
    console.log("===================================");
    
    try {
        const wethAmount = ethers.utils.parseEther("10"); // 10 WETH
        const path = await smartRouter.findOptimalPath(TOKENS.WETH, TOKENS.WBTC, wethAmount);
        
        if (path.isValid) {
            console.log(`✅ Path found: ${path.tokens.join(" → ")}`);
            console.log(`💎 Input: 10 WETH`);
            console.log(`₿ Expected Output: ${ethers.utils.formatUnits(path.expectedOutput, DECIMALS[TOKENS.WBTC])} WBTC`);
            console.log(`📊 Price Impact: ${(path.priceImpact / 100).toFixed(2)}%`);
            console.log(`⛽ Gas Estimate: ${path.gasEstimate.toLocaleString()}`);
        } else {
            console.log("❌ No valid path found");
        }
    } catch (error) {
        console.log(`⚠️ Error: ${error.message}`);
    }
    
    console.log();

    // 3. DeFi Token Trading Example
    console.log("🦄 3. DeFi Token Trading (UNI → AAVE)");
    console.log("=====================================");
    
    try {
        const uniAmount = ethers.utils.parseEther("100"); // 100 UNI
        const path = await smartRouter.findOptimalPath(TOKENS.UNI, TOKENS.AAVE, uniAmount);
        
        if (path.isValid) {
            console.log(`✅ Path found: ${path.tokens.join(" → ")}`);
            console.log(`🦄 Input: 100 UNI`);
            console.log(`👻 Expected Output: ${ethers.utils.formatEther(path.expectedOutput)} AAVE`);
            console.log(`📊 Price Impact: ${(path.priceImpact / 100).toFixed(2)}%`);
            console.log(`⛽ Gas Estimate: ${path.gasEstimate.toLocaleString()}`);
        } else {
            console.log("❌ No valid path found");
        }
    } catch (error) {
        console.log(`⚠️ Error: ${error.message}`);
    }
    
    console.log();

    // 4. Multi-hop Example (Meme → Stablecoin)
    console.log("🐕 4. Multi-hop Trading (SHIB → WETH → USDC)");
    console.log("============================================");
    
    try {
        const shibAmount = ethers.utils.parseEther("1000000"); // 1M SHIB
        const path = await smartRouter.findOptimalPath(TOKENS.SHIB, TOKENS.USDC, shibAmount);
        
        if (path.isValid) {
            console.log(`✅ Path found: ${path.tokens.join(" → ")}`);
            console.log(`🐕 Input: 1,000,000 SHIB`);
            console.log(`💵 Expected Output: ${ethers.utils.formatUnits(path.expectedOutput, DECIMALS[TOKENS.USDC])} USDC`);
            console.log(`📊 Price Impact: ${(path.priceImpact / 100).toFixed(2)}%`);
            console.log(`⛽ Gas Estimate: ${path.gasEstimate.toLocaleString()}`);
        } else {
            console.log("❌ No valid path found");
        }
    } catch (error) {
        console.log(`⚠️ Error: ${error.message}`);
    }
    
    console.log();

    // 5. Price Comparison (Oracle vs DEX)
    console.log("📊 5. Price Comparison (Oracle vs DEX)");
    console.log("======================================");
    
    try {
        // WETH/USDC price comparison
        const [oraclePrice, reliable] = await priceOracle.getCombinedPrice(TOKENS.WETH, TOKENS.USDC);
        const dexAmounts = await router.getAmountsOut(
            ethers.utils.parseEther("1"), 
            [TOKENS.WETH, TOKENS.USDC]
        );
        
        const oraclePriceFormatted = ethers.utils.formatUnits(oraclePrice, 18);
        const dexPriceFormatted = ethers.utils.formatUnits(dexAmounts[1], DECIMALS[TOKENS.USDC]);
        
        console.log(`🔮 Oracle Price (WETH/USDC): $${parseFloat(oraclePriceFormatted).toFixed(2)}`);
        console.log(`🏪 DEX Price (WETH/USDC): $${parseFloat(dexPriceFormatted).toFixed(2)}`);
        console.log(`✅ Oracle Reliable: ${reliable}`);
        
        const deviation = Math.abs(parseFloat(oraclePriceFormatted) - parseFloat(dexPriceFormatted)) / parseFloat(oraclePriceFormatted) * 100;
        console.log(`📈 Price Deviation: ${deviation.toFixed(2)}%`);
        
    } catch (error) {
        console.log(`⚠️ Error: ${error.message}`);
    }
    
    console.log();

    // 6. Supported Tokens List
    console.log("📋 6. Supported Tokens");
    console.log("======================");
    
    try {
        const supportedTokens = await tokenRegistry.getActiveTokens();
        const verifiedTokens = await tokenRegistry.getVerifiedTokens();
        
        console.log(`✅ Total Active Tokens: ${supportedTokens.length}`);
        console.log(`🔒 Verified Tokens: ${verifiedTokens.length}`);
        
        console.log("\n🏆 Top 10 Tokens by Market Cap:");
        const topTokens = await tokenRegistry.getTokensByMarketCap(10);
        
        for (let i = 0; i < Math.min(10, topTokens.length); i++) {
            const tokenInfo = await tokenRegistry.getTokenInfo(topTokens[i]);
            const hasChainlink = await priceOracle.isChainlinkAvailable(topTokens[i]);
            
            console.log(`${i + 1}. ${tokenInfo.symbol} (${tokenInfo.name})`);
            console.log(`   📍 Address: ${topTokens[i]}`);
            console.log(`   💰 Market Cap: $${(tokenInfo.marketCap / 1e9).toFixed(1)}B`);
            console.log(`   🔗 Chainlink: ${hasChainlink ? '✅' : '❌'}`);
            console.log(`   ✅ Verified: ${tokenInfo.isVerified ? '✅' : '❌'}`);
            console.log();
        }
        
    } catch (error) {
        console.log(`⚠️ Error: ${error.message}`);
    }

    // 7. Gas Cost Estimates
    console.log("⛽ 7. Gas Cost Estimates");
    console.log("========================");
    
    const gasEstimates = [
        { operation: "Simple Swap (A→B)", gas: 120000 },
        { operation: "Multi-hop Swap (A→B→C)", gas: 200000 },
        { operation: "Add Liquidity", gas: 180000 },
        { operation: "Remove Liquidity", gas: 150000 },
        { operation: "Stake LP Tokens", gas: 100000 },
        { operation: "Claim Rewards", gas: 80000 }
    ];
    
    const gasPrice = 20; // 20 gwei
    
    gasEstimates.forEach(estimate => {
        const costInEth = (estimate.gas * gasPrice * 1e-9).toFixed(4);
        const costInUsd = (parseFloat(costInEth) * 2000).toFixed(2); // Assuming $2000 ETH
        
        console.log(`${estimate.operation}:`);
        console.log(`  ⛽ Gas: ${estimate.gas.toLocaleString()}`);
        console.log(`  💎 Cost: ${costInEth} ETH (~$${costInUsd})`);
        console.log();
    });

    console.log("🎉 Swap Examples Completed!");
    console.log("\n💡 Next Steps:");
    console.log("1. Deploy contracts to mainnet");
    console.log("2. Add initial liquidity for major pairs");
    console.log("3. Set up monitoring and alerts");
    console.log("4. Launch frontend interface");
    console.log("5. Start liquidity mining program");
}

// Error handling
main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("❌ Error:", error);
        process.exit(1);
    });
