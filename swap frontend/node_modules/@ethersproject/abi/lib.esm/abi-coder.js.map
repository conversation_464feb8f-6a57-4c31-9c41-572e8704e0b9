{"version": 3, "file": "abi-coder.js", "sourceRoot": "", "sources": ["../src.ts/abi-coder.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,mEAAmE;AAEnE,OAAO,EAAE,QAAQ,EAAa,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,OAAO,EAAS,MAAM,EAAU,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACxE,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC1C,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAC9C,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAE5C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAGxC,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACrD,MAAM,eAAe,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAKxD,MAAM,OAAO,QAAQ;IAGjB,YAAY,UAAuB;QAC/B,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,UAAU,IAAI,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,SAAS,CAAC,KAAgB;QAEtB,QAAQ,KAAK,CAAC,QAAQ,EAAE;YACpB,KAAK,SAAS;gBACV,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,MAAM;gBACP,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACxC,KAAK,QAAQ;gBACT,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvC,KAAK,OAAO;gBACR,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACtC,KAAK,OAAO;gBACR,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAC9F,KAAK,OAAO;gBACR,OAAO,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBAC7D,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBACrC,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACpB,KAAK,EAAE;gBACH,OAAO,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACxC;QAED,cAAc;QACd,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAC9C,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;YACvC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;gBAC9C,MAAM,CAAC,kBAAkB,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aACpF;YACD,OAAO,IAAI,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SACtE;QAED,cAAc;QACd,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACzC,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,EAAE,EAAE;gBACzB,MAAM,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aACrE;YACD,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;SAChD;QAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAED,YAAY,KAAa,OAAO,EAAE,CAAC,CAAC,CAAC;IAErC,UAAU,CAAC,IAAgB,EAAE,UAAoB;QAC7C,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC9E,CAAC;IAED,UAAU;QACN,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,eAAe,CAAC,KAAwC;QACpD,MAAM,MAAM,GAAiB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvF,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,YAAY,EAAE,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,KAAwC,EAAE,MAA0B;QACvE,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;YAChC,MAAM,CAAC,UAAU,CAAC,8BAA8B,EAAE,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;gBAC9E,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE;gBACrD,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;aAC1C,CAAC,CAAC;SACN;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzE,MAAM,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;QAE5C,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7B,OAAO,MAAM,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,KAAwC,EAAE,IAAe,EAAE,KAAe;QAC7E,MAAM,MAAM,GAAiB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvF,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAChE,CAAC;CACJ;AAED,MAAM,CAAC,MAAM,eAAe,GAAa,IAAI,QAAQ,EAAE,CAAC"}