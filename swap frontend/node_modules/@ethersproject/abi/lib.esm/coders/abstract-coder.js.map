{"version": 3, "file": "abstract-coder.js", "sourceRoot": "", "sources": ["../../src.ts/coders/abstract-coder.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,QAAQ,EAAa,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvF,OAAO,EAAE,SAAS,EAAgB,MAAM,0BAA0B,CAAC;AACnE,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AACtC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAMnC,MAAM,UAAU,iBAAiB,CAAC,MAAc;IAC5C,gCAAgC;IAChC,MAAM,MAAM,GAA0D,EAAG,CAAC;IAE1E,MAAM,WAAW,GAAG,UAAS,IAA4B,EAAE,MAAW;QAClE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAAE,OAAO;SAAE;QACvC,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;YACpB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEpB,IAAI;gBACC,WAAW,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;aACxC;YAAC,OAAO,KAAK,EAAE;gBACZ,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;aAClD;SACJ;IACL,CAAC,CAAA;IACD,WAAW,CAAC,EAAG,EAAE,MAAM,CAAC,CAAC;IAEzB,OAAO,MAAM,CAAC;AAElB,CAAC;AAID,MAAM,OAAgB,KAAK;IAmBvB,YAAY,IAAY,EAAE,IAAY,EAAE,SAAiB,EAAE,OAAgB;QACvE,8BAA8B;QAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,KAAU;QACnC,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC9D,CAAC;CAMJ;AAED,MAAM,OAAO,MAAM;IAOf,YAAY,QAAiB;QACzB,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,GAAG,EAAG,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,IAAI;QACJ,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IACD,IAAI,MAAM,KAAa,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAEjD,UAAU,CAAC,IAAgB;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC;QAChC,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,YAAY,CAAC,MAAc;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,kDAAkD;IAClD,UAAU,CAAC,KAAgB;QACvB,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC5B,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;QACnD,IAAI,aAAa,EAAE;YACf,KAAK,GAAG,MAAM,CAAC,CAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAE,CAAC,CAAA;SAChE;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,SAAS,CAAC,KAAmB;QACzB,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5C,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;gBACnE,MAAM,EAAE,IAAI,CAAC,QAAQ;gBACrB,MAAM,EAAE,KAAK,CAAC,MAAM;aACvB,CAAC,CAAC;SACN;QACD,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;YAC9B,KAAK,GAAG,MAAM,CAAC,CAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAE,CAAC,CAAC;SAChF;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,qDAAqD;IACrD,UAAU,CAAC,KAAmB;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,mBAAmB;QACf,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/B,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC;QAClC,OAAO,CAAC,KAAmB,EAAE,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC;IACN,CAAC;CACJ;AAED,MAAM,OAAO,MAAM;IASf,YAAY,IAAe,EAAE,QAAiB,EAAE,UAAuB,EAAE,UAAoB;QACzF,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;QACjD,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAChD,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;QAE/C,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IACrB,CAAC;IAED,IAAI,IAAI,KAAa,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClD,IAAI,QAAQ,KAAa,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAE/C,8BAA8B;IAC9B,MAAM,CAAC,MAAM,CAAC,IAAY,EAAE,KAAU;QAClC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC1C,IAAI,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE;YAAE,KAAK,GAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;SAAE;QACrE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,IAAY,EAAE,KAAU;QAC3B,IAAI,IAAI,CAAC,WAAW,EAAE;YAAE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAAE;QAC/D,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,UAAU,CAAC,MAAc,EAAE,MAAc,EAAE,KAAe;QACtD,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;QACtE,IAAI,IAAI,CAAC,OAAO,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YAClD,IAAI,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACxE,aAAa,GAAG,MAAM,CAAC;aAC1B;iBAAM;gBACH,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE;oBAClE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;oBACzB,MAAM,EAAE,IAAI,CAAC,OAAO,GAAG,aAAa;iBACvC,CAAC,CAAC;aACN;SACJ;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,CAAA;IACvE,CAAC;IAED,SAAS,CAAC,MAAc;QACpB,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACjH,CAAC;IAED,SAAS,CAAC,MAAc,EAAE,KAAe;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC;QAC7B,oDAAoD;QACpD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC;IAED,SAAS;QACL,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IACzD,CAAC;CACJ"}