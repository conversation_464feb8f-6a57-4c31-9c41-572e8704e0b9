{"version": 3, "file": "tuple.js", "sourceRoot": "", "sources": ["../../src.ts/coders/tuple.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;AAEb,OAAO,EAAE,KAAK,EAAkB,MAAM,kBAAkB,CAAC;AACzD,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AAEvC,MAAM,OAAO,UAAW,SAAQ,KAAK;IAGjC,YAAY,MAAoB,EAAE,SAAiB;QAC/C,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,KAAK,GAAkB,EAAE,CAAC;QAChC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACrB,IAAI,KAAK,CAAC,OAAO,EAAE;gBAAE,OAAO,GAAG,IAAI,CAAC;aAAE;YACtC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QAEhD,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED,YAAY;QACR,MAAM,MAAM,GAAQ,EAAG,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,4DAA4D;QAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC7B,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAAE;gBACtC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;aACjB;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,EAAgC,EAAG,CAAC,CAAC;QAEtC,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAY,EAAE,KAAa,EAAE,EAAE;YAChD,IAAI,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC;YAC3B,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAAE,OAAO;aAAE;YAEjD,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAAE,IAAI,GAAG,SAAS,CAAC;aAAE;YAE5C,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;gBAAE,OAAO;aAAE;YAErC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,MAAM,CAAC,MAAc,EAAE,KAA6C;QAChE,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,CAAC,MAAc;QACjB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACjE,CAAC;CACJ"}