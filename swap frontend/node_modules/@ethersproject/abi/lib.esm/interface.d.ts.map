{"version": 3, "file": "interface.d.ts", "sourceRoot": "", "sources": ["../src.ts/interface.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACnE,OAAO,EAAY,SAAS,EAA0D,MAAM,sBAAsB,CAAC;AAGnH,OAAO,EAAkB,WAAW,EAAa,MAAM,2BAA2B,CAAC;AAEnF,OAAO,EAAE,QAAQ,EAAmB,MAAM,aAAa,CAAC;AACxD,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACpE,OAAO,EAAE,mBAAmB,EAAE,aAAa,EAAE,aAAa,EAAe,QAAQ,EAAE,gBAAgB,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAMlJ,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC;AAErC,qBAAa,cAAe,SAAQ,WAAW,CAAC,cAAc,CAAC;IAC3D,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC;IACtC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;IAC3B,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;IACvB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;CACxB;AAED,qBAAa,sBAAuB,SAAQ,WAAW,CAAC,sBAAsB,CAAC;IAC3E,QAAQ,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IAC5C,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;IAC3B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;IACzB,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC;CAC7B;AAED,qBAAa,gBAAiB,SAAQ,WAAW,CAAC,gBAAgB,CAAC;IAC/D,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC;IACtC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC;IAC3B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;CAC5B;AAED,qBAAa,OAAQ,SAAQ,WAAW,CAAC,OAAO,CAAC;IAC7C,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC;IAE7B,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,OAAO;CAGjD;AA0BD,qBAAa,SAAS;IAClB,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;IAE5C,QAAQ,CAAC,MAAM,EAAE;QAAE,CAAE,IAAI,EAAE,MAAM,GAAI,aAAa,CAAA;KAAE,CAAC;IACrD,QAAQ,CAAC,MAAM,EAAE;QAAE,CAAE,IAAI,EAAE,MAAM,GAAI,aAAa,CAAA;KAAE,CAAC;IACrD,QAAQ,CAAC,SAAS,EAAE;QAAE,CAAE,IAAI,EAAE,MAAM,GAAI,gBAAgB,CAAA;KAAE,CAAC;IAC3D,QAAQ,CAAC,OAAO,EAAE;QAAE,CAAE,IAAI,EAAE,MAAM,GAAI,GAAG,CAAA;KAAE,CAAC;IAE5C,QAAQ,CAAC,MAAM,EAAE,mBAAmB,CAAC;IAErC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC;IAE7B,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;gBAEnB,SAAS,EAAE,MAAM,GAAG,aAAa,CAAC,QAAQ,GAAG,YAAY,GAAG,MAAM,CAAC;IAmE/E,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAiB/C,MAAM,CAAC,WAAW,IAAI,QAAQ;IAI9B,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM;IAI1C,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,aAAa,GAAG,gBAAgB,GAAG,MAAM;IAIrE,MAAM,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,GAAG,MAAM;IAK1D,WAAW,CAAC,wBAAwB,EAAE,MAAM,GAAG,gBAAgB;IAgC/D,QAAQ,CAAC,sBAAsB,EAAE,MAAM,GAAG,aAAa;IAiCvD,QAAQ,CAAC,wBAAwB,EAAE,MAAM,GAAG,aAAa;IAkCzD,UAAU,CAAC,QAAQ,EAAE,aAAa,GAAG,gBAAgB,GAAG,MAAM,GAAG,MAAM;IAiBvE,aAAa,CAAC,aAAa,EAAE,aAAa,GAAG,MAAM,GAAG,MAAM;IAS5D,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM;IAIxE,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAInF,YAAY,CAAC,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAIjD,iBAAiB,CAAC,QAAQ,EAAE,aAAa,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM;IAc5E,iBAAiB,CAAC,QAAQ,EAAE,aAAa,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAYxF,kBAAkB,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM;IAexF,kBAAkB,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAYpG,oBAAoB,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM;IAmD1F,oBAAoB,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM;IAStG,kBAAkB,CAAC,aAAa,EAAE,aAAa,GAAG,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAiEpH,cAAc,CAAC,aAAa,EAAE,aAAa,GAAG,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,GAAG;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;KAAE;IA4C1H,cAAc,CAAC,aAAa,EAAE,aAAa,GAAG,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,GAAG,MAAM;IA4F9G,gBAAgB,CAAC,EAAE,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,KAAK,CAAC,EAAE,YAAY,CAAA;KAAE,GAAG,sBAAsB;IAoBpF,QAAQ,CAAC,GAAG,EAAE;QAAE,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAC,GAAG,cAAc;IAmBrE,UAAU,CAAC,IAAI,EAAE,SAAS,GAAG,gBAAgB;IA4B7C,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,SAAS;CAGrD"}