{"version": 3, "file": "keystore.js", "sourceRoot": "", "sources": ["../src.ts/keystore.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,kDAAyB;AACzB,wDAA+B;AAG/B,kDAAoD;AACpD,8CAAmF;AACnF,gDAA4G;AAC5G,sDAAqD;AACrD,gDAA0D;AAC1D,gDAAoD;AACpD,wDAAwD;AACxD,4DAA6D;AAE7D,iCAA+E;AAE/E,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,iBAAiB;AAEjB,SAAS,WAAW,CAAC,KAAU;IAC3B,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtE,CAAC;AAUD;IAAqC,mCAA6B;IAAlE;;IAUA,CAAC;IAHG,2CAAiB,GAAjB,UAAkB,KAAU;QACxB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACjD,CAAC;IACL,sBAAC;AAAD,CAAC,AAVD,CAAqC,wBAAW,GAU/C;AAVY,0CAAe;AA2B5B,SAAS,QAAQ,CAAC,IAAS,EAAE,GAAe,EAAE,UAAsB;IAChE,IAAM,MAAM,GAAG,IAAA,kBAAU,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IACjD,IAAI,MAAM,KAAK,aAAa,EAAE;QAC1B,IAAM,EAAE,GAAG,IAAA,qBAAa,EAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,wBAAwB,CAAC,CAAC,CAAA;QACpE,IAAM,OAAO,GAAG,IAAI,gBAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEpC,IAAM,MAAM,GAAG,IAAI,gBAAG,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAEzD,OAAO,IAAA,gBAAQ,EAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;KAC/C;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,WAAW,CAAC,IAAS,EAAE,GAAe;IAC3C,IAAM,UAAU,GAAG,IAAA,qBAAa,EAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAExE,IAAM,WAAW,GAAG,IAAA,eAAO,EAAC,IAAA,qBAAS,EAAC,IAAA,cAAM,EAAC,CAAE,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC/F,IAAI,WAAW,KAAK,IAAA,kBAAU,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC,WAAW,EAAE,EAAE;QAC9D,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KACvC;IAED,IAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;IAEhE,IAAI,CAAC,UAAU,EAAE;QACb,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACzE,SAAS,EAAE,SAAS;SACvB,CAAC,CAAC;KACN;IAED,IAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAEtC,IAAM,OAAO,GAAG,IAAA,6BAAc,EAAC,UAAU,CAAC,CAAC;IAC3C,IAAI,IAAI,CAAC,OAAO,EAAE;QACd,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;YAAE,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;SAAE;QAE7D,IAAI,IAAA,oBAAU,EAAC,KAAK,CAAC,KAAK,OAAO,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;KACJ;IAED,IAAM,OAAO,GAAqB;QAC9B,kBAAkB,EAAE,IAAI;QACxB,OAAO,EAAE,OAAO;QAChB,UAAU,EAAE,IAAA,eAAO,EAAC,UAAU,CAAC;KAClC,CAAC;IAEF,0EAA0E;IAC1E,IAAI,IAAA,kBAAU,EAAC,IAAI,EAAE,kBAAkB,CAAC,KAAK,KAAK,EAAE;QAChD,IAAM,kBAAkB,GAAG,IAAA,qBAAa,EAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,6BAA6B,CAAC,CAAC,CAAC;QAC1F,IAAM,UAAU,GAAG,IAAA,qBAAa,EAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,0BAA0B,CAAC,CAAC,CAAC;QAE/E,IAAM,eAAe,GAAG,IAAI,gBAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACpD,IAAM,cAAc,GAAG,IAAI,gBAAG,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAEjF,IAAM,IAAI,GAAG,IAAA,kBAAU,EAAC,IAAI,EAAE,eAAe,CAAC,IAAI,oBAAW,CAAC;QAC9D,IAAM,MAAM,GAAG,IAAA,kBAAU,EAAC,IAAI,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC;QAE3D,IAAM,OAAO,GAAG,IAAA,gBAAQ,EAAC,cAAc,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAErE,IAAI;YACA,IAAM,QAAQ,GAAG,IAAA,0BAAiB,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACpD,IAAM,IAAI,GAAG,eAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAE1E,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,EAAE;gBACvC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;aACxC;YAED,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;SAEpC;QAAC,OAAO,KAAK,EAAE;YACZ,oDAAoD;YACpD,kDAAkD;YAClD,WAAW;YACX,IAAI,KAAK,CAAC,IAAI,KAAK,eAAM,CAAC,MAAM,CAAC,gBAAgB,IAAI,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE;gBAChF,MAAM,KAAK,CAAC;aACf;SACJ;KACJ;IAED,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;AACxC,CAAC;AAKD,SAAS,UAAU,CAAC,aAAyB,EAAE,IAAgB,EAAE,KAAa,EAAE,KAAa,EAAE,OAAe;IAC1G,OAAO,IAAA,gBAAQ,EAAC,IAAA,eAAO,EAAC,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;AACzE,CAAC;AAED,SAAS,MAAM,CAAC,aAAyB,EAAE,IAAgB,EAAE,KAAa,EAAE,KAAa,EAAE,OAAe;IACtG,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;AACnF,CAAC;AAED,SAAS,cAAc,CAAI,IAAS,EAAE,QAAwB,EAAE,UAAyB,EAAE,UAAyB,EAAE,gBAAmC;IACrJ,IAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,QAAQ,CAAC,CAAC;IAE5C,IAAM,GAAG,GAAG,IAAA,kBAAU,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC;IAE3C,IAAI,GAAG,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;QACjC,IAAM,UAAU,GAAG,UAAS,IAAY,EAAE,KAAU;YAChD,OAAO,MAAM,CAAC,kBAAkB,CAAC,4CAA4C,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAChG,CAAC,CAAA;QAED,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;YAChC,IAAM,IAAI,GAAG,IAAA,qBAAa,EAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC,CAAC;YACtE,IAAM,CAAC,GAAG,QAAQ,CAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC;YAC3D,IAAM,CAAC,GAAG,QAAQ,CAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC;YAC3D,IAAM,CAAC,GAAG,QAAQ,CAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC;YAE3D,oCAAoC;YACpC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;gBAAE,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;aAAE;YAE/C,8BAA8B;YAC9B,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBAAE,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;aAAE;YAEhD,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,wBAAwB,CAAC,CAAC,CAAC;YACnE,IAAI,KAAK,KAAK,EAAE,EAAE;gBAAE,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aAAE;YAEjD,OAAO,UAAU,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,gBAAgB,CAAC,CAAC;SAEzE;aAAM,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;YAEvC,IAAM,IAAI,GAAG,IAAA,qBAAa,EAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC,CAAC;YAEtE,IAAI,OAAO,GAAW,IAAI,CAAC;YAC3B,IAAM,GAAG,GAAG,IAAA,kBAAU,EAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;YACrD,IAAI,GAAG,KAAK,aAAa,EAAE;gBACvB,OAAO,GAAG,QAAQ,CAAC;aACtB;iBAAM,IAAI,GAAG,KAAK,aAAa,EAAE;gBAC9B,OAAO,GAAG,QAAQ,CAAC;aACtB;iBAAM;gBACH,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;aAC1B;YAED,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC;YAE/D,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAA,kBAAU,EAAC,IAAI,EAAE,wBAAwB,CAAC,CAAC,CAAC;YACnE,IAAI,KAAK,KAAK,EAAE,EAAE;gBAAE,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aAAE;YAEjD,OAAO,UAAU,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACjE;KACJ;IAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,qCAAqC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACxF,CAAC;AAGD,SAAgB,WAAW,CAAC,IAAY,EAAE,QAAwB;IAC9D,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE9B,IAAM,GAAG,GAAG,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,mBAAM,CAAC,UAAU,CAAC,CAAC;IAC1E,OAAO,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AALD,kCAKC;AAED,SAAsB,OAAO,CAAC,IAAY,EAAE,QAAwB,EAAE,gBAAmC;;;;;;oBAC/F,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAElB,qBAAM,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,mBAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,EAAA;;oBAAnF,GAAG,GAAG,SAA6E;oBACzF,sBAAO,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,EAAC;;;;CACjC;AALD,0BAKC;AAGD,SAAgB,OAAO,CAAC,OAA+B,EAAE,QAAwB,EAAE,OAAwB,EAAE,gBAAmC;IAE5I,IAAI;QACA,4CAA4C;QAC5C,IAAI,IAAA,oBAAU,EAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAA,6BAAc,EAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACpE,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAClD;QAED,sDAAsD;QACtD,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;YACtB,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAClC,IAAM,IAAI,GAAG,eAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,oBAAW,CAAC,CAAC;YAElH,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,EAAE;gBACvC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;aACxC;SACJ;KAEJ;IAAC,OAAO,CAAC,EAAE;QACR,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAC5B;IAED,yDAAyD;IACzD,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,UAAU,IAAI,CAAC,gBAAgB,EAAE;QACrD,gBAAgB,GAAG,OAAO,CAAC;QAC3B,OAAO,GAAG,EAAE,CAAC;KAChB;IACD,IAAI,CAAC,OAAO,EAAE;QAAE,OAAO,GAAG,EAAE,CAAC;KAAE;IAE/B,IAAM,UAAU,GAAe,IAAA,gBAAQ,EAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC5D,IAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,QAAQ,CAAC,CAAC;IAE5C,IAAI,OAAO,GAAe,IAAI,CAAA;IAC9B,IAAI,IAAI,GAAW,IAAI,CAAC;IACxB,IAAI,MAAM,GAAW,IAAI,CAAC;IAC1B,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE;QACtB,IAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC;QACrC,OAAO,GAAG,IAAA,gBAAQ,EAAC,IAAA,0BAAiB,EAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC;QACtF,IAAI,GAAG,WAAW,CAAC,IAAI,IAAI,oBAAW,CAAC;QACvC,MAAM,GAAG,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC;KACvC;IAED,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAC5B,IAAI,CAAC,MAAM,EAAE;QAAE,MAAM,GAAG,WAAW,CAAC;KAAE;IAEtC,0BAA0B;IAC1B,IAAI,IAAI,GAAe,IAAI,CAAC;IAC5B,IAAI,OAAO,CAAC,IAAI,EAAE;QACd,IAAI,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACjC;SAAM;QACH,IAAI,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC;QAAA,CAAC;KAC3B;IAED,iCAAiC;IACjC,IAAI,EAAE,GAAe,IAAI,CAAC;IAC1B,IAAI,OAAO,CAAC,EAAE,EAAE;QACZ,EAAE,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1B,IAAI,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;SAAE;KAC3D;SAAM;QACJ,EAAE,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC;KACvB;IAED,oBAAoB;IACpB,IAAI,UAAU,GAAe,IAAI,CAAC;IAClC,IAAI,OAAO,CAAC,IAAI,EAAE;QACd,UAAU,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;SAAE;KACrE;SAAM;QACH,UAAU,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC;KAChC;IAED,wEAAwE;IACxE,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAChC,IAAI,OAAO,CAAC,MAAM,EAAE;QAChB,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;YAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;SAAE;QAC/C,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;YAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;SAAE;QAC/C,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;YAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;SAAE;KAClD;IAED,oBAAoB;IACpB,+EAA+E;IAC/E,sFAAsF;IACtF,OAAO,mBAAM,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG;QAC9E,GAAG,GAAG,IAAA,gBAAQ,EAAC,GAAG,CAAC,CAAC;QAEpB,uEAAuE;QACvE,IAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACpC,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAEpC,4DAA4D;QAC5D,IAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAEtC,0BAA0B;QAC1B,IAAM,OAAO,GAAG,IAAI,gBAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACpC,IAAM,MAAM,GAAG,IAAI,gBAAG,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAChE,IAAM,UAAU,GAAG,IAAA,gBAAQ,EAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAExD,sEAAsE;QACtE,IAAM,GAAG,GAAG,IAAA,qBAAS,EAAC,IAAA,cAAM,EAAC,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAA;QAEtD,4EAA4E;QAC5E,IAAM,IAAI,GAA2B;YACjC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;YACnD,EAAE,EAAE,IAAA,cAAM,EAAC,UAAU,CAAC;YACtB,OAAO,EAAE,CAAC;YACV,MAAM,EAAE;gBACJ,MAAM,EAAE,aAAa;gBACrB,YAAY,EAAE;oBACV,EAAE,EAAE,IAAA,eAAO,EAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;iBAC/B;gBACD,UAAU,EAAE,IAAA,eAAO,EAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC5C,GAAG,EAAE,QAAQ;gBACb,SAAS,EAAE;oBACP,IAAI,EAAE,IAAA,eAAO,EAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;oBAChC,CAAC,EAAE,CAAC;oBACJ,KAAK,EAAE,EAAE;oBACT,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;iBACP;gBACD,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;aACxB;SACJ,CAAC;QAEF,yDAAyD;QACzD,IAAI,OAAO,EAAE;YACT,IAAM,UAAU,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC;YACnC,IAAM,eAAe,GAAG,IAAI,gBAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACpD,IAAM,cAAc,GAAG,IAAI,gBAAG,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YACjF,IAAM,kBAAkB,GAAG,IAAA,gBAAQ,EAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YACrE,IAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAM,SAAS,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,GAAG;gBAC1B,IAAA,YAAI,EAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG;gBACpC,IAAA,YAAI,EAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG;gBAC/B,IAAA,YAAI,EAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG;gBAChC,IAAA,YAAI,EAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG;gBAClC,IAAA,YAAI,EAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,CACpC,CAAC;YACpB,IAAI,CAAC,UAAU,CAAC,GAAG;gBACf,MAAM,EAAE,MAAM;gBACd,YAAY,EAAE,CAAC,OAAO,GAAG,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;gBACzD,eAAe,EAAE,IAAA,eAAO,EAAC,UAAU,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBACjD,kBAAkB,EAAE,IAAA,eAAO,EAAC,kBAAkB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC5D,IAAI,EAAE,IAAI;gBACV,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,KAAK;aACjB,CAAC;SACL;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;AACP,CAAC;AAtJD,0BAsJC"}