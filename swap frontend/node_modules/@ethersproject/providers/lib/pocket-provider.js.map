{"version": 3, "file": "pocket-provider.js", "sourceRoot": "", "sources": ["../src.ts/pocket-provider.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;AAKb,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,iEAA6D;AAE7D,IAAM,oBAAoB,GAAG,0BAA0B,CAAC;AAGxD;IAAoC,kCAAkB;IAAtD;;IA8EA,CAAC;IAzEU,wBAAS,GAAhB,UAAiB,MAAW;QACxB,IAAM,SAAS,GAAmF;YAC9F,aAAa,EAAE,IAAI;YACnB,YAAY,EAAE,IAAI;YAClB,oBAAoB,EAAE,IAAI;SAC7B,CAAC;QAEF,+CAA+C;QAC/C,IAAI,MAAM,IAAI,IAAI,EAAE;YAChB,SAAS,CAAC,aAAa,GAAG,oBAAoB,CAAC;SAElD;aAAM,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;YACrC,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC;SAEpC;aAAM,IAAI,MAAM,CAAC,oBAAoB,IAAI,IAAI,EAAE;YAC5C,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;YAC/C,SAAS,CAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC;SAEhE;aAAM,IAAI,MAAM,CAAC,aAAa,EAAE;YAC7B,SAAS,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;SAElD;aAAM;YACH,MAAM,CAAC,kBAAkB,CAAC,mCAAmC,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;SACpF;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEM,qBAAM,GAAb,UAAc,OAAgB,EAAE,MAAW;QACvC,IAAI,IAAI,GAAW,IAAI,CAAC;QACxB,QAAQ,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE;YACxC,KAAK,QAAQ;gBACT,IAAI,GAAG,iCAAiC,CAAC;gBACzC,MAAM;YACV,KAAK,WAAW;gBACZ,IAAI,GAAG,kCAAkC,CAAC;gBAC1C,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,GAAG,gCAAgC,CAAC;gBACxC,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,GAAG,mCAAmC,CAAC;gBAC3C,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,GAAG,yCAAyC,CAAC;gBACjD,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,GAAG,kCAAkC,CAAC;gBAC1C,MAAM;YACV,KAAK,SAAS;gBACV,IAAI,GAAG,kCAAkC,CAAC;gBAC1C,MAAM;YACV;gBACI,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,eAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;oBACrE,QAAQ,EAAE,SAAS;oBACnB,KAAK,EAAE,OAAO;iBACjB,CAAC,CAAC;SACV;QAED,IAAM,GAAG,GAAG,aAAa,IAAI,eAAY,MAAM,CAAC,aAAgB,CAAA;QAEhE,IAAM,UAAU,GAAmB,EAAE,OAAO,EAAE,EAAG,EAAE,GAAG,KAAA,EAAE,CAAC;QACzD,IAAI,MAAM,CAAC,oBAAoB,IAAI,IAAI,EAAE;YACrC,UAAU,CAAC,IAAI,GAAG,EAAE,CAAC;YACrB,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAA;SACpD;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,4CAAmB,GAAnB;QACI,OAAO,CAAC,IAAI,CAAC,aAAa,KAAK,oBAAoB,CAAC,CAAC;IACzD,CAAC;IACL,qBAAC;AAAD,CAAC,AA9ED,CAAoC,0CAAkB,GA8ErD;AA9EY,wCAAc"}