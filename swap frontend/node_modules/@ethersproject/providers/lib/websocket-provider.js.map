{"version": 3, "file": "websocket-provider.js", "sourceRoot": "", "sources": ["../src.ts/websocket-provider.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,sDAAqD;AAErD,wDAA2D;AAG3D,yDAAsD;AACtD,2BAAiC;AAEjC,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC;;;;;;;;;;;;;GAaG;AAEH,IAAI,MAAM,GAAG,CAAC,CAAC;AAuBf,mDAAmD;AACnD,8CAA8C;AAE9C;IAAuC,qCAAe;IAalD,2BAAY,GAA2B,EAAE,OAAoB;QAA7D,iBAiGC;QA/FG,qEAAqE;QACrE,IAAI,OAAO,KAAK,KAAK,EAAE;YACnB,MAAM,CAAC,UAAU,CAAC,sDAAsD,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC3G,SAAS,EAAE,aAAa;aAC3B,CAAC,CAAC;SACN;QAED,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;YAC1B,QAAA,kBAAM,GAAG,EAAE,OAAO,CAAC,SAAC;SACvB;aAAM;YACH,QAAA,kBAAM,YAAY,EAAE,OAAO,CAAC,SAAC;SAChC;QAED,KAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAE3B,KAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,OAAM,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;YAC1B,IAAA,2BAAc,EAAC,KAAI,EAAE,YAAY,EAAE,IAAI,cAAS,CAAC,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;SAC1E;aAAM;YACH,IAAA,2BAAc,EAAC,KAAI,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;SAC3C;QAED,IAAA,2BAAc,EAAC,KAAI,EAAE,WAAW,EAAE,EAAG,CAAC,CAAC;QACvC,IAAA,2BAAc,EAAC,KAAI,EAAE,OAAO,EAAE,EAAG,CAAC,CAAC;QACnC,IAAA,2BAAc,EAAC,KAAI,EAAE,SAAS,EAAE,EAAG,CAAC,CAAC;QACrC,IAAA,2BAAc,EAAC,KAAI,EAAE,gBAAgB,EAAE,iBAAM,aAAa,YAAE,CAAC,CAAC;QAE9D,qDAAqD;QACrD,KAAI,CAAC,SAAS,CAAC,MAAM,GAAG;YACpB,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAC,EAAE;gBACnC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,KAAI,CAAC,SAAS,CAAC,SAAS,GAAG,UAAC,YAA8B;YACtD,IAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;YAC/B,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,MAAM,CAAC,EAAE,IAAI,IAAI,EAAE;gBACnB,IAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC7B,IAAM,OAAO,GAAG,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBACnC,OAAO,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAE1B,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;oBAC7B,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBAEtC,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACf,MAAM,EAAE,UAAU;wBAClB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;wBACpC,QAAQ,EAAE,MAAM,CAAC,MAAM;wBACvB,QAAQ,EAAE,KAAI;qBACjB,CAAC,CAAC;iBAEN;qBAAM;oBACH,IAAI,KAAK,GAAU,IAAI,CAAC;oBACxB,IAAI,MAAM,CAAC,KAAK,EAAE;wBACd,KAAK,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,eAAe,CAAC,CAAC;wBAC3D,IAAA,2BAAc,EAAM,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;wBAC9D,IAAA,2BAAc,EAAM,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;qBAChD;yBAAM;wBACH,KAAK,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;qBACtC;oBAED,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAEnC,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE;wBACf,MAAM,EAAE,UAAU;wBAClB,KAAK,EAAE,KAAK;wBACZ,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;wBACpC,QAAQ,EAAE,KAAI;qBACjB,CAAC,CAAC;iBAEN;aAEJ;iBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,kBAAkB,EAAE;gBAC7C,kBAAkB;gBAClB,IAAM,GAAG,GAAG,KAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBACnD,IAAI,GAAG,EAAE;oBACL,2CAA2C;oBAC3C,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;iBACxC;aAEJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;aAC1C;QACL,CAAC,CAAC;QAEF,+DAA+D;QAC/D,gEAAgE;QAChE,iCAAiC;QACjC,IAAM,QAAQ,GAAG,WAAW,CAAC;YACzB,KAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC,EAAE,IAAI,CAAC,CAAC;QACT,IAAI,QAAQ,CAAC,KAAK,EAAE;YAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;SAAE;;IAC7C,CAAC;IAID,sBAAI,wCAAS;QAFb,4EAA4E;QAC5E,4DAA4D;aAC5D,cAAiC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;;;OAAA;IAE1D,yCAAa,GAAb;QACI,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,sBAAI,8CAAe;aAAnB;YACI,OAAO,CAAC,CAAC;QACb,CAAC;aAQD,UAAoB,KAAa;YAC7B,MAAM,CAAC,UAAU,CAAC,kDAAkD,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBACvG,SAAS,EAAE,oBAAoB;aAClC,CAAC,CAAC;QACP,CAAC;;;OAZA;IAED,4CAAgB,GAAhB,UAAiB,WAAmB;QAChC,MAAM,CAAC,UAAU,CAAC,gDAAgD,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACrG,SAAS,EAAE,iBAAiB;SAC/B,CAAC,CAAC;IACP,CAAC;IAQK,gCAAI,GAAV;;;gBACI,sBAAO,IAAI,EAAC;;;KACf;IAED,sBAAI,sCAAO;aAAX,UAAY,KAAc;YACtB,IAAI,CAAC,KAAK,EAAE;gBAAE,OAAO;aAAE;YAEvB,MAAM,CAAC,UAAU,CAAC,yCAAyC,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBAC9F,SAAS,EAAE,YAAY;aAC1B,CAAC,CAAC;QACP,CAAC;;;OAAA;IAED,gCAAI,GAAJ,UAAK,MAAc,EAAE,MAAmB;QAAxC,iBA0BC;QAzBG,IAAM,GAAG,GAAG,MAAM,EAAE,CAAC;QAErB,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,SAAS,QAAQ,CAAC,KAAY,EAAE,MAAW;gBACvC,IAAI,KAAK,EAAE;oBAAE,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;iBAAE;gBACpC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3B,CAAC;YAED,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC3B,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,MAAM;gBACd,EAAE,EAAE,GAAG;gBACP,OAAO,EAAE,KAAK;aACjB,CAAC,CAAC;YAEH,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACf,MAAM,EAAE,SAAS;gBACjB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBAC5B,QAAQ,EAAE,KAAI;aACjB,CAAC,CAAC;YAEH,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,QAAQ,UAAA,EAAE,OAAO,SAAA,EAAE,CAAC;YAEpD,IAAI,KAAI,CAAC,QAAQ,EAAE;gBAAE,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAAE;QACxD,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,4BAAU,GAAjB;QACI,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAEK,sCAAU,GAAhB,UAAiB,GAAW,EAAE,KAAiB,EAAE,WAAkC;;;;;;;wBAC3E,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;wBACrC,IAAI,YAAY,IAAI,IAAI,EAAE;4BACtB,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAC,KAAK;gCACzC,OAAO,KAAI,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;4BAC7C,CAAC,CAAC,CAAC;4BACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;yBACpC;wBACa,qBAAM,YAAY,EAAA;;wBAA1B,KAAK,GAAG,SAAkB;wBAChC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,KAAA,EAAE,WAAW,aAAA,EAAE,CAAC;;;;;KAC5C;IAED,uCAAW,GAAX,UAAY,KAAY;QAAxB,iBAyDC;QAxDG,QAAQ,KAAK,CAAC,IAAI,EAAE;YAChB,KAAK,OAAO;gBACR,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAE,UAAU,CAAE,EAAE,UAAC,MAAW;oBACjD,IAAM,WAAW,GAAG,qBAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC7D,KAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC;oBAClC,KAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,SAAS;gBACV,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAE,wBAAwB,CAAE,EAAE,UAAC,MAAW;oBACjE,KAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,QAAQ;gBACT,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,CAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAE,EAAE,UAAC,MAAW;oBAC9E,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;wBAAE,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;qBAAE;oBACvD,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,IAAI,CAAC,CAAC;gBACP,IAAM,aAAW,GAAG,UAAC,KAAY;oBAC7B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;oBACxB,KAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAC,OAAO;wBAC1C,IAAI,CAAC,OAAO,EAAE;4BAAE,OAAO;yBAAE;wBACzB,KAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC;gBAEF,8BAA8B;gBAC9B,aAAW,CAAC,KAAK,CAAC,CAAC;gBAEnB,oEAAoE;gBACpE,2DAA2D;gBAC3D,mEAAmE;gBACnE,gCAAgC;gBAChC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAE,UAAU,CAAE,EAAE,UAAC,MAAW;oBAC9C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAjB,CAAiB,CAAC,CAAC,OAAO,CAAC,aAAW,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;gBACH,MAAM;aACT;YAED,oBAAoB;YACpB,KAAK,OAAO,CAAC;YACb,KAAK,MAAM,CAAC;YACZ,KAAK,UAAU,CAAC;YAChB,KAAK,SAAS,CAAC;YACf,KAAK,OAAO;gBACR,MAAM;YAEV;gBACI,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBACjC,MAAM;SACb;IACL,CAAC;IAED,sCAAU,GAAV,UAAW,KAAY;QAAvB,iBAuBC;QAtBG,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;QAEpB,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;YACrB,kDAAkD;YAClD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAjB,CAAiB,CAAC,CAAC,MAAM,EAAE;gBACtD,OAAO;aACV;YACD,GAAG,GAAG,IAAI,CAAC;SACd;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACxC,sCAAsC;YACtC,OAAO;SACV;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,KAAK,EAAE;YAAE,OAAO;SAAE;QAExB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACzB,KAAK,CAAC,IAAI,CAAC,UAAC,KAAK;YACZ,IAAI,CAAC,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAAE,OAAO;aAAE;YACnC,OAAO,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzB,KAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAE,KAAK,CAAE,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;IAEK,mCAAO,GAAb;;;;;;6BAEQ,CAAA,IAAI,CAAC,SAAS,CAAC,UAAU,KAAK,cAAS,CAAC,UAAU,CAAA,EAAlD,wBAAkD;wBAClD,qBAAM,CAAC,IAAI,OAAO,CAAC,UAAC,OAAO;gCACvB,KAAI,CAAC,SAAS,CAAC,MAAM,GAAG;oCACpB,OAAO,CAAC,IAAI,CAAC,CAAC;gCAClB,CAAC,CAAC;gCAEF,KAAI,CAAC,SAAS,CAAC,OAAO,GAAG;oCACrB,OAAO,CAAC,KAAK,CAAC,CAAC;gCACnB,CAAC,CAAC;4BACN,CAAC,CAAC,CAAC,EAAA;;wBARH,SAQG,CAAC;;;wBAGR,SAAS;wBACT,gFAAgF;wBAChF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;;;;KAC9B;IACL,wBAAC;AAAD,CAAC,AAtSD,CAAuC,mCAAe,GAsSrD;AAtSY,8CAAiB"}