{"_ethers.alias": {"elliptic.js": "browser-elliptic.js"}, "author": "<PERSON> <<EMAIL>>", "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "@ethersproject/properties": "^5.8.0", "bn.js": "^5.2.1", "elliptic": "6.6.1", "hash.js": "1.1.7"}, "description": "Elliptic curve library functions for the secp256k1 curve.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "5ff3dc99101d7291e34012f1a45f0f28b43fade9", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/signing-key", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/signing-key", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x7a09f564e1d852a8af8518d18359e1e9488c8978f394607cf154d5ec779f2f1a", "types": "./lib/index.d.ts", "version": "5.8.0"}