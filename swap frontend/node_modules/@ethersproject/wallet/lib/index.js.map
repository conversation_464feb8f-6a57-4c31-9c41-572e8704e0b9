{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src.ts/index.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEb,kDAAoD;AACpD,sEAAgF;AAChF,kEAAkI;AAClI,8CAAmI;AACnI,4CAAqE;AACrE,gDAAyF;AACzF,sDAAqD;AACrD,wDAA8E;AAC9E,gDAAoD;AACpD,0DAAwD;AACxD,4DAA0H;AAC1H,4DAA6G;AAG7G,gDAA+C;AAC/C,uCAAqC;AACrC,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAO,CAAC,CAAC;AAEnC,SAAS,SAAS,CAAC,KAAU;IACzB,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,IAAA,mBAAW,EAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC;AACzF,CAAC;AAED,SAAS,WAAW,CAAC,KAAU;IAC3B,IAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IAChC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzC,CAAC;AAED;IAA4B,0BAAM;IAU9B,gBAAY,UAA2D,EAAE,QAAmB;QAA5F,YACI,iBAAO,SA4DV;QA1DG,IAAI,SAAS,CAAC,UAAU,CAAC,EAAE;YACvB,IAAM,YAAU,GAAG,IAAI,wBAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACzD,IAAA,2BAAc,EAAC,KAAI,EAAE,aAAa,EAAE,cAAM,OAAA,YAAU,EAAV,CAAU,CAAC,CAAC;YACtD,IAAA,2BAAc,EAAC,KAAI,EAAE,SAAS,EAAE,IAAA,6BAAc,EAAC,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAEhE,IAAI,KAAI,CAAC,OAAO,KAAK,IAAA,oBAAU,EAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBACjD,MAAM,CAAC,kBAAkB,CAAC,6BAA6B,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;aACxF;YAED,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE;gBACzB,IAAM,aAAW,GAAG,UAAU,CAAC,QAAQ,CAAC;gBACxC,IAAA,2BAAc,EAAC,KAAI,EAAE,WAAW,EAAE,cAAM,OAAA,CACpC;oBACI,MAAM,EAAE,aAAW,CAAC,MAAM;oBAC1B,IAAI,EAAE,aAAW,CAAC,IAAI,IAAI,oBAAW;oBACrC,MAAM,EAAE,aAAW,CAAC,MAAM,IAAI,IAAI;iBACrC,CACJ,EANuC,CAMvC,CAAC,CAAC;gBACH,IAAM,QAAQ,GAAG,KAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAM,IAAI,GAAG,eAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACnG,IAAI,IAAA,6BAAc,EAAC,IAAI,CAAC,UAAU,CAAC,KAAK,KAAI,CAAC,OAAO,EAAE;oBAClD,MAAM,CAAC,kBAAkB,CAAC,2BAA2B,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;iBACtF;aACJ;iBAAM;gBACH,IAAA,2BAAc,EAAC,KAAI,EAAE,WAAW,EAAE,cAAgB,OAAA,IAAI,EAAJ,CAAI,CAAC,CAAC;aAC3D;SAGJ;aAAM;YACH,IAAI,wBAAU,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;gBACrC,wBAAwB;gBACxB,IAAI,UAAU,CAAC,KAAK,KAAK,WAAW,EAAE;oBAClC,MAAM,CAAC,kBAAkB,CAAC,sCAAsC,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;iBACjG;gBACD,IAAA,2BAAc,EAAC,KAAI,EAAE,aAAa,EAAE,cAAM,OAAa,UAAW,EAAxB,CAAwB,CAAC,CAAC;aAEvE;iBAAM;gBACH,0EAA0E;gBAC1E,IAAI,OAAM,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE;oBACjC,IAAI,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE;wBAC9D,UAAU,GAAG,IAAI,GAAG,UAAU,CAAC;qBAClC;iBACJ;gBAED,IAAM,YAAU,GAAG,IAAI,wBAAU,CAAC,UAAU,CAAC,CAAC;gBAC9C,IAAA,2BAAc,EAAC,KAAI,EAAE,aAAa,EAAE,cAAM,OAAA,YAAU,EAAV,CAAU,CAAC,CAAC;aACzD;YAED,IAAA,2BAAc,EAAC,KAAI,EAAE,WAAW,EAAE,cAAgB,OAAA,IAAI,EAAJ,CAAI,CAAC,CAAC;YACxD,IAAA,2BAAc,EAAC,KAAI,EAAE,SAAS,EAAE,IAAA,6BAAc,EAAC,KAAI,CAAC,SAAS,CAAC,CAAC,CAAC;SACnE;QAED,wBAAwB;QACxB,IAAI,QAAQ,IAAI,CAAC,4BAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC5C,MAAM,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;SACvE;QAED,IAAA,2BAAc,EAAC,KAAI,EAAE,UAAU,EAAE,QAAQ,IAAI,IAAI,CAAC,CAAC;;IACvD,CAAC;IAED,sBAAI,4BAAQ;aAAZ,cAA2B,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;;;OAAA;IACrD,sBAAI,8BAAU;aAAd,cAA2B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;;;OAAA;IAClE,sBAAI,6BAAS;aAAb,cAA0B,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IAEhE,2BAAU,GAAV;QACI,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,wBAAO,GAAP,UAAQ,QAAkB;QACtB,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED,gCAAe,GAAf,UAAgB,WAA+B;QAA/C,iBAYC;QAXG,OAAO,IAAA,8BAAiB,EAAC,WAAW,CAAC,CAAC,IAAI,CAAC,UAAC,EAAE;YAC1C,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE;gBACjB,IAAI,IAAA,oBAAU,EAAC,EAAE,CAAC,IAAI,CAAC,KAAK,KAAI,CAAC,OAAO,EAAE;oBACtC,MAAM,CAAC,kBAAkB,CAAC,mCAAmC,EAAE,kBAAkB,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;iBACxG;gBACD,OAAO,EAAE,CAAC,IAAI,CAAC;aAClB;YAED,IAAM,SAAS,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,IAAA,qBAAS,EAAC,IAAA,wBAAS,EAAsB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/F,OAAO,IAAA,wBAAS,EAAsB,EAAE,EAAE,SAAS,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACP,CAAC;IAEK,4BAAW,GAAjB,UAAkB,OAAuB;;;gBACrC,sBAAO,IAAA,qBAAa,EAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,IAAA,kBAAW,EAAC,OAAO,CAAC,CAAC,CAAC,EAAC;;;KAC7E;IAEK,+BAAc,GAApB,UAAqB,MAAuB,EAAE,KAA4C,EAAE,KAA0B;;;;;;4BAEhG,qBAAM,wBAAiB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAC,IAAY;4BACtF,IAAI,KAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gCACvB,MAAM,CAAC,UAAU,CAAC,6CAA6C,EAAE,eAAM,CAAC,MAAM,CAAC,qBAAqB,EAAE;oCAClG,SAAS,EAAE,aAAa;oCACxB,KAAK,EAAE,IAAI;iCACd,CAAC,CAAC;6BACN;4BACD,OAAO,KAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;wBAC3C,CAAC,CAAC,EAAA;;wBARI,SAAS,GAAG,SAQhB;wBAEF,sBAAO,IAAA,qBAAa,EAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,wBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAC;;;;KACzH;IAED,wBAAO,GAAP,UAAQ,QAAwB,EAAE,OAAa,EAAE,gBAAmC;QAChF,IAAI,OAAM,CAAC,OAAO,CAAC,KAAK,UAAU,IAAI,CAAC,gBAAgB,EAAE;YACrD,gBAAgB,GAAG,OAAO,CAAC;YAC3B,OAAO,GAAG,EAAE,CAAC;SAChB;QAED,IAAI,gBAAgB,IAAI,OAAM,CAAC,gBAAgB,CAAC,KAAK,UAAU,EAAE;YAC7D,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO,GAAG,EAAE,CAAC;SAAE;QAE/B,OAAO,IAAA,8BAAe,EAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;IACtE,CAAC;IAGD;;OAEG;IACI,mBAAY,GAAnB,UAAoB,OAAa;QAC7B,IAAI,OAAO,GAAe,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC;QAE1C,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO,GAAG,EAAG,CAAC;SAAE;QAEhC,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,OAAO,GAAG,IAAA,gBAAQ,EAAC,IAAA,oBAAY,EAAC,IAAA,qBAAS,EAAC,IAAA,cAAM,EAAC,CAAE,OAAO,EAAE,OAAO,CAAC,YAAY,CAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SACjG;QAED,IAAM,QAAQ,GAAG,IAAA,0BAAiB,EAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5D,OAAO,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IACvE,CAAC;IAEM,wBAAiB,GAAxB,UAAyB,IAAY,EAAE,QAAwB,EAAE,gBAAmC;QAChG,OAAO,IAAA,gCAAiB,EAAC,IAAI,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAC,OAAO;YACpE,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,4BAAqB,GAA5B,UAA6B,IAAY,EAAE,QAAwB;QAC/D,OAAO,IAAI,MAAM,CAAC,IAAA,oCAAqB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC7D,CAAC;IAEM,mBAAY,GAAnB,UAAoB,QAAgB,EAAE,IAAa,EAAE,QAAmB;QACpE,IAAI,CAAC,IAAI,EAAE;YAAE,IAAI,GAAG,oBAAW,CAAC;SAAE;QAClC,OAAO,IAAI,MAAM,CAAC,eAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACtF,CAAC;IACL,aAAC;AAAD,CAAC,AApKD,CAA4B,wBAAM,GAoKjC;AApKY,wBAAM;AAsKnB,SAAgB,aAAa,CAAC,OAAuB,EAAE,SAAwB;IAC3E,OAAO,IAAA,6BAAc,EAAC,IAAA,kBAAW,EAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC;AAC3D,CAAC;AAFD,sCAEC;AAED,SAAgB,eAAe,CAAC,MAAuB,EAAE,KAA4C,EAAE,KAA0B,EAAE,SAAwB;IACvJ,OAAO,IAAA,6BAAc,EAAC,wBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;AACnF,CAAC;AAFD,0CAEC"}