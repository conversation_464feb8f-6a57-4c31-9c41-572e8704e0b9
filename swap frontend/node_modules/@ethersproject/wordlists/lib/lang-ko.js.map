{"version": 3, "file": "lang-ko.js", "sourceRoot": "", "sources": ["../src.ts/lang-ko.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;AAEb,kDAAsD;AAEtD,uCAAsC;AAGtC,IAAM,IAAI,GAAG;IACT,MAAM;IACN,y5JAAy5J;IACz5J,8lIAA8lI;IAC9lI,i8BAAi8B;IACj8B,koCAAkoC;IACloC,yaAAya;IACza,gHAAgH;IAChH,+EAA+E;CAClF,CAAA;AAED,IAAM,KAAK,GAAG,wEAAwE,CAAA;AAEtF,SAAS,SAAS,CAAC,IAAY;IAC3B,IAAI,IAAI,IAAI,EAAE,EAAE;QACZ,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;KAC1B;SAAM,IAAI,IAAI,IAAI,EAAE,EAAE;QACnB,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;KACzB;IAED,OAAO,IAAA,sBAAY,EAAC,CAAE,GAAG,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACxE,CAAC;AAED,IAAI,QAAQ,GAAkB,IAAI,CAAC;AAEnC,SAAS,SAAS,CAAC,IAAc;IAC7B,IAAI,QAAQ,IAAI,IAAI,EAAE;QAAE,OAAO;KAAE;IAEjC,QAAQ,GAAG,EAAE,CAAC;IAEd,IAAI,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,MAAM;QACtB,MAAM,IAAI,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,MAAM,EAAE;YAC1C,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC7B,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;aACjD;YACD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACtB;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEhB,qDAAqD;IACrD,wBAAwB;IACxB,IAAI,mBAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,oEAAoE,EAAE;QAC/F,QAAQ,GAAG,IAAI,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;KAC5D;AACL,CAAC;AAGD;IAAqB,0BAAQ;IACzB;eACI,kBAAM,IAAI,CAAC;IACf,CAAC;IAED,wBAAO,GAAP,UAAQ,KAAa;QACjB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChB,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,6BAAY,GAAZ,UAAa,IAAY;QACrB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChB,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IACL,aAAC;AAAD,CAAC,AAdD,CAAqB,mBAAQ,GAc5B;AAED,IAAM,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;AAGnB,wBAAM;AAFf,mBAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC"}