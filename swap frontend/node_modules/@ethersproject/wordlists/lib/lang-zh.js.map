{"version": 3, "file": "lang-zh.js", "sourceRoot": "", "sources": ["../src.ts/lang-zh.ts"], "names": [], "mappings": "AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;AAEb,kDAAsD;AAEtD,uCAAsC;AAGtC,IAAM,IAAI,GAAG,kgMAAkgM,CAAC;AAChhM,IAAM,SAAS,GAAG,6lDAA6lD,CAAC;AAEhnD,qBAAqB;AAErB,IAAM,QAAQ,GAAqC;IAC/C,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;CACd,CAAA;AAED,IAAM,MAAM,GAA8B;IACtC,KAAK,EAAE,oEAAoE;IAC3E,KAAK,EAAE,oEAAoE;CAC9E,CAAA;AAED,IAAM,KAAK,GAAG,kEAAkE,CAAC;AACjF,IAAM,KAAK,GAAG,4BAA4B,CAAA;AAE1C,SAAS,SAAS,CAAC,IAAc;IAC7B,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO;KAAE;IAE/C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IAE3B,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;QAC3B,IAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrC,IAAM,KAAK,GAAG;YACV,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YACd,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YACpC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;SACvC,CAAC;QAEF,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE;YACzB,IAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,KAAK,IAAI,GAAC,GAAG,MAAM,EAAE,GAAC,GAAG,CAAC,EAAE,GAAC,EAAE,EAAE;gBAC7B,KAAK,CAAC,GAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA,CAAC,CAAC,GAAG,CAAC,CAAC;aAC9E;SACJ;QAED,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAA,sBAAY,EAAC,KAAK,CAAC,CAAC,CAAC;KACnD;IAED,qDAAqD;IACrD,wBAAwB;IACxB,IAAI,mBAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAC9C,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC,MAAM,GAAG,mBAAmB,CAAC,CAAC;KAC9E;AACL,CAAC;AAED;IAAqB,0BAAQ;IACzB,gBAAY,OAAe;eACvB,kBAAM,KAAK,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED,wBAAO,GAAP,UAAQ,KAAa;QACjB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChB,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,6BAAY,GAAZ,UAAa,IAAY;QACrB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChB,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,sBAAK,GAAL,UAAM,QAAgB;QAClB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;IACL,aAAC;AAAD,CAAC,AAnBD,CAAqB,mBAAQ,GAmB5B;AAED,IAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;AAOzB,4BAAQ;AANjB,mBAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC5B,mBAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAElC,IAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;AAGf,4BAAQ;AAF3B,mBAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC"}