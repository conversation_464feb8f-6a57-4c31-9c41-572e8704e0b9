{"name": "aes-js", "version": "3.0.0", "bugs": {"url": "http://github.com/ricmoo/aes-js/issues", "email": "<EMAIL>"}, "description": "A pure JavaScript implementation of the AES block cipher and all common modes of operation.", "devDependencies": {"nodeunit": "0.9.1"}, "main": "index.js", "scripts": {"test": "./node_modules/.bin/nodeunit test/index.js"}, "repository": {"type": "git", "url": "git://github.com/ricmoo/aes-js.git"}, "keywords": ["aes", "aes-ctr", "aes-ofb", "aes-ecb", "aes-cbc", "aes-cfb", "encrypt", "decrypt", "block", "cipher"], "author": "<PERSON> <<EMAIL>>", "license": "MIT"}