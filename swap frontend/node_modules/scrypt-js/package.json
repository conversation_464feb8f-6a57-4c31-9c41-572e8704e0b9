{"name": "scrypt-js", "version": "3.0.1", "description": "The scrypt password-based key derivation function with sync and cancellable async.", "main": "scrypt.js", "scripts": {"test": "mocha test/test-scrypt.js"}, "types": "scrypt.d.ts", "devDependencies": {"mocha": "6.2.2"}, "keywords": ["scrypt", "pbkdf", "password", "async", "asynchronous", "stepwise"], "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/ricmoo/scrypt-js.git"}, "license": "MIT"}