import React, { useState } from 'react';
import AuroraBackground from './components/AuroraBackground';
import Header from './components/Header';
import Navigation from './components/Navigation';
import SwapInterface from './components/SwapInterface';
import LiquidityInterface from './components/LiquidityInterface';
import FarmingInterface from './components/FarmingInterface';
import TokenManagement from './components/TokenManagement';
import SecurityInterface from './components/SecurityInterface';
import AnalyticsInterface from './components/AnalyticsInterface';
import GovernanceInterface from './components/GovernanceInterface';
import FaucetInterface from './components/FaucetInterface';
import SettingsInterface from './components/SettingsInterface';

function App() {
  const [activeTab, setActiveTab] = useState('swap');
  const [isAnimationPaused, setIsAnimationPaused] = useState(true); // Start paused for better performance

  const renderContent = () => {
    switch (activeTab) {
      case 'swap':
        return (
          <div className="flex flex-col items-center justify-center min-h-[calc(100vh-300px)]">
            <div className="text-center mb-8">
              <h2 className="text-5xl font-normal text-white mb-4 tracking-wider">
                Trade crypto with confidence
              </h2>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed font-normal tracking-wide">
                Access the best prices across multiple DEXs with minimal slippage and maximum security
              </p>
            </div>
            <SwapInterface />
            <div className="mt-8 text-center">
              <p className="text-gray-400 text-sm font-normal tracking-wide">
                Powered by Quantlink Developer Team
              </p>
            </div>
          </div>
        );
      case 'liquidity':
        return <LiquidityInterface />;
      case 'farming':
        return <FarmingInterface />;
      case 'tokens':
        return <TokenManagement />;
      case 'security':
        return <SecurityInterface />;
      case 'analytics':
        return <AnalyticsInterface />;
      case 'governance':
        return <GovernanceInterface />;
      case 'faucet':
        return <FaucetInterface />;
      case 'settings':
        return <SettingsInterface />;
      default:
        return <SwapInterface />;
    }
  };

  return (
    <div className="relative min-h-screen overflow-hidden">
      <AuroraBackground isPaused={isAnimationPaused} />

      <div className="relative z-10">
        <Header />
        <Navigation activeTab={activeTab} onTabChange={setActiveTab} />

        <main className="container mx-auto px-6 py-8">
          {renderContent()}
        </main>

        {/* Animation Control Button */}
        <button
          onClick={() => setIsAnimationPaused(!isAnimationPaused)}
          className="fixed bottom-4 right-4 z-50 bg-gray-800/80 hover:bg-gray-700/80 text-white p-3 rounded-full shadow-lg transition-all duration-200 backdrop-blur-sm border border-gray-600/50"
          title={isAnimationPaused ? 'Resume Animation' : 'Pause Animation'}
        >
          {isAnimationPaused ? (
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </div>
    </div>
  );
}

export default App;