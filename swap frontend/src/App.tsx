import React, { useState } from 'react';
import Header from './components/Header';
import Navigation from './components/Navigation';
import SwapInterface from './components/SwapInterface';

function App() {
  const [activeTab, setActiveTab] = useState('swap');

  const renderContent = () => {
    if (activeTab === 'swap') {
      return <SwapInterface />;
    }
    return (
      <div className="bg-gray-800 rounded-lg p-6 text-center">
        <p className="text-xl mb-4">{activeTab} interface</p>
        <p className="text-gray-400">
          This section is under development
        </p>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Header />
      <Navigation activeTab={activeTab} onTabChange={setActiveTab} />
      <div className="container mx-auto px-4 py-8">
        {renderContent()}
      </div>
    </div>
  );
}

export default App;