import React from 'react';
import AuroraBackground from './components/AuroraBackground';
import Header from './components/Header';
import SwapInterface from './components/SwapInterface';

function App() {
  return (
    <div className="relative min-h-screen overflow-hidden">
      <AuroraBackground />
      
      <div className="relative z-10">
        <Header />
        
        <main className="container mx-auto px-6 py-8">
          <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)]">
            <div className="text-center mb-8">
              <h2 className="text-5xl font-normal text-white mb-4 tracking-wider">
                Trade crypto with confidence
              </h2>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed font-normal tracking-wide">
                Access the best prices across multiple DEXs with minimal slippage and maximum security
              </p>
            </div>
            
            <SwapInterface />
            
            <div className="mt-8 text-center">
              <p className="text-gray-400 text-sm font-normal tracking-wide">
                Powered by Quantlink Developer Team
              </p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;