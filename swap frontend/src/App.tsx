import React, { useState } from 'react';
import AuroraBackground from './components/AuroraBackground';
import Header from './components/Header';
import Navigation from './components/Navigation';
import SwapInterface from './components/SwapInterface';
import LiquidityInterface from './components/LiquidityInterface';
import FarmingInterface from './components/FarmingInterface';
import TokenManagement from './components/TokenManagement';
import SecurityInterface from './components/SecurityInterface';
import AnalyticsInterface from './components/AnalyticsInterface';
import GovernanceInterface from './components/GovernanceInterface';
import FaucetInterface from './components/FaucetInterface';
import SettingsInterface from './components/SettingsInterface';

function App() {
  const [activeTab, setActiveTab] = useState('swap');

  const renderContent = () => {
    switch (activeTab) {
      case 'swap':
        return (
          <div className="flex flex-col items-center justify-center min-h-[calc(100vh-300px)]">
            <div className="text-center mb-8">
              <h2 className="text-5xl font-normal text-white mb-4 tracking-wider">
                Trade crypto with confidence
              </h2>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed font-normal tracking-wide">
                Access the best prices across multiple DEXs with minimal slippage and maximum security
              </p>
            </div>
            <SwapInterface />
            <div className="mt-8 text-center">
              <p className="text-gray-400 text-sm font-normal tracking-wide">
                Powered by Quantlink Developer Team
              </p>
            </div>
          </div>
        );
      case 'liquidity':
        return <LiquidityInterface />;
      case 'farming':
        return <FarmingInterface />;
      case 'tokens':
        return <TokenManagement />;
      case 'security':
        return <SecurityInterface />;
      case 'analytics':
        return <AnalyticsInterface />;
      case 'governance':
        return <GovernanceInterface />;
      case 'faucet':
        return <FaucetInterface />;
      case 'settings':
        return <SettingsInterface />;
      default:
        return <SwapInterface />;
    }
  };

  return (
    <div className="relative min-h-screen overflow-hidden">
      <AuroraBackground />

      <div className="relative z-10">
        <Header />
        <Navigation activeTab={activeTab} onTabChange={setActiveTab} />

        <main className="container mx-auto px-6 py-8">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}

export default App;