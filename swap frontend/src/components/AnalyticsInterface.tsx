import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Activity,
  Zap,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw
} from 'lucide-react';
import { useWeb3 } from '../hooks/useWeb3';

interface AnalyticsData {
  totalVolume24h: string;
  totalVolumeChange: number;
  totalTVL: string;
  tvlChange: number;
  totalTrades24h: number;
  tradesChange: number;
  uniqueUsers24h: number;
  usersChange: number;
  averageTradeSize: string;
  platformFeesCollected: string;
  topTokens: Array<{
    symbol: string;
    volume24h: string;
    change24h: number;
    price: string;
    logoURI: string;
  }>;
  topPairs: Array<{
    pair: string;
    volume24h: string;
    tvl: string;
    apr: string;
    token0: { symbol: string; logoURI: string };
    token1: { symbol: string; logoURI: string };
  }>;
}

const AnalyticsInterface: React.FC = () => {
  const { network } = useWeb3();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalVolume24h: '2450000',
    totalVolumeChange: 12.5,
    totalTVL: '8750000',
    tvlChange: -2.3,
    totalTrades24h: 1247,
    tradesChange: 8.7,
    uniqueUsers24h: 342,
    usersChange: 15.2,
    averageTradeSize: '1965',
    platformFeesCollected: '7350',
    topTokens: [
      {
        symbol: 'WETH',
        volume24h: '850000',
        change24h: 5.2,
        price: '2450.00',
        logoURI: 'https://tokens.1inch.io/******************************************.png'
      },
      {
        symbol: 'USDC',
        volume24h: '720000',
        change24h: -1.8,
        price: '1.00',
        logoURI: 'https://tokens.1inch.io/******************************************.png'
      }
    ],
    topPairs: [
      {
        pair: 'WETH/USDC',
        volume24h: '450000',
        tvl: '1200000',
        apr: '25.5',
        token0: { symbol: 'WETH', logoURI: 'https://tokens.1inch.io/******************************************.png' },
        token1: { symbol: 'USDC', logoURI: 'https://tokens.1inch.io/******************************************.png' }
      }
    ]
  });
  const [isLoading, setIsLoading] = useState(false);
  const [timeframe, setTimeframe] = useState<'24h' | '7d' | '30d'>('24h');

  const loadAnalytics = async () => {
    setIsLoading(true);
    try {
      // Implementation to load analytics data
      console.log('Loading analytics data...');
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, [timeframe]);

  const formatNumber = (num: string | number) => {
    const value = typeof num === 'string' ? parseFloat(num) : num;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  const formatChange = (change: number) => {
    const isPositive = change >= 0;
    return (
      <div className={`flex items-center space-x-1 ${isPositive ? 'text-green-400' : 'text-red-400'}`}>
        {isPositive ? <ArrowUpRight className="w-4 h-4" /> : <ArrowDownRight className="w-4 h-4" />}
        <span>{Math.abs(change).toFixed(1)}%</span>
      </div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-900 rounded-3xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-black text-white tracking-wider">Analytics Dashboard</h1>
              <p className="text-gray-400">Protocol metrics and performance data</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Timeframe Selector */}
            <div className="flex bg-gray-800 rounded-xl p-1">
              {(['24h', '7d', '30d'] as const).map((period) => (
                <button
                  key={period}
                  onClick={() => setTimeframe(period)}
                  className={`px-4 py-2 rounded-lg transition-all ${
                    timeframe === period
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-400 hover:text-gray-300'
                  }`}
                >
                  {period}
                </button>
              ))}
            </div>
            
            <button
              onClick={loadAnalytics}
              disabled={isLoading}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            {formatChange(analyticsData.totalVolumeChange)}
          </div>
          <div className="text-2xl font-black text-white mb-1">
            {formatNumber(analyticsData.totalVolume24h)}
          </div>
          <div className="text-gray-400 text-sm">Total Volume {timeframe}</div>
        </div>

        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            {formatChange(analyticsData.tvlChange)}
          </div>
          <div className="text-2xl font-black text-white mb-1">
            {formatNumber(analyticsData.totalTVL)}
          </div>
          <div className="text-gray-400 text-sm">Total Value Locked</div>
        </div>

        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
              <Activity className="w-5 h-5 text-white" />
            </div>
            {formatChange(analyticsData.tradesChange)}
          </div>
          <div className="text-2xl font-black text-white mb-1">
            {analyticsData.totalTrades24h.toLocaleString()}
          </div>
          <div className="text-gray-400 text-sm">Total Trades {timeframe}</div>
        </div>

        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-10 h-10 bg-orange-600 rounded-full flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            {formatChange(analyticsData.usersChange)}
          </div>
          <div className="text-2xl font-black text-white mb-1">
            {analyticsData.uniqueUsers24h.toLocaleString()}
          </div>
          <div className="text-gray-400 text-sm">Unique Users {timeframe}</div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-yellow-600 rounded-full flex items-center justify-center">
              <Zap className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="text-white font-bold">Average Trade Size</div>
              <div className="text-gray-400 text-sm">Per transaction</div>
            </div>
          </div>
          <div className="text-2xl font-black text-white">
            {formatNumber(analyticsData.averageTradeSize)}
          </div>
        </div>

        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-pink-600 rounded-full flex items-center justify-center">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="text-white font-bold">Platform Fees</div>
              <div className="text-gray-400 text-sm">Collected {timeframe}</div>
            </div>
          </div>
          <div className="text-2xl font-black text-white">
            {formatNumber(analyticsData.platformFeesCollected)}
          </div>
        </div>

        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center">
              <Clock className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="text-white font-bold">Fee Rate</div>
              <div className="text-gray-400 text-sm">Platform commission</div>
            </div>
          </div>
          <div className="text-2xl font-black text-white">0.3%</div>
        </div>
      </div>

      {/* Top Tokens and Pairs */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Tokens */}
        <div className="bg-gray-900 rounded-3xl p-6">
          <h3 className="text-lg font-black text-white mb-6">Top Tokens by Volume</h3>
          <div className="space-y-4">
            {analyticsData.topTokens.map((token, index) => (
              <div key={token.symbol} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-gray-400 text-sm w-6">{index + 1}</div>
                  <img src={token.logoURI} alt={token.symbol} className="w-8 h-8 rounded-full" />
                  <div>
                    <div className="text-white font-medium">{token.symbol}</div>
                    <div className="text-gray-400 text-sm">{formatNumber(token.price)}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-white font-medium">{formatNumber(token.volume24h)}</div>
                  {formatChange(token.change24h)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Pairs */}
        <div className="bg-gray-900 rounded-3xl p-6">
          <h3 className="text-lg font-black text-white mb-6">Top Trading Pairs</h3>
          <div className="space-y-4">
            {analyticsData.topPairs.map((pair, index) => (
              <div key={pair.pair} className="bg-gray-800 rounded-2xl p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="text-gray-400 text-sm">{index + 1}</div>
                    <div className="flex -space-x-2">
                      <img src={pair.token0.logoURI} alt={pair.token0.symbol} className="w-6 h-6 rounded-full border-2 border-gray-700" />
                      <img src={pair.token1.logoURI} alt={pair.token1.symbol} className="w-6 h-6 rounded-full border-2 border-gray-700" />
                    </div>
                    <div className="text-white font-medium">{pair.pair}</div>
                  </div>
                  <div className="text-green-400 text-sm font-medium">{pair.apr}% APR</div>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Volume:</span>
                    <div className="text-white">{formatNumber(pair.volume24h)}</div>
                  </div>
                  <div>
                    <span className="text-gray-400">TVL:</span>
                    <div className="text-white">{formatNumber(pair.tvl)}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsInterface;
