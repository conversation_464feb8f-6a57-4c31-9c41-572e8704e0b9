import { useEffect, useRef } from 'react';

interface AuroraBackgroundProps {
  isPaused?: boolean;
}

const AuroraBackground: React.FC<AuroraBackgroundProps> = ({ isPaused = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationIdRef = useRef<number>();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    const gl = canvas.getContext('webgl2');

    if (!gl) {
      console.error('WebGL2 not supported');
      return;
    }

    const vsSource = `#version 300 es
    in vec2 aPosition;
    void main() {
        gl_Position = vec4(aPosition, 0.0, 1.0);
    }`;

    const fsSource = `#version 300 es
    precision highp float;

    uniform float uTime;
    uniform vec2 uResolution;
    out vec4 fragColor;

    float hash(vec2 p) {
        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
    }

    float hash21(vec2 p) {
        vec3 p3 = fract(vec3(p.xyx) * 0.1031);
        p3 += dot(p3, p3.yzx + 33.33);
        return fract((p3.x + p3.y) * p3.z);
    }

    float noise(vec2 uv) {
        vec2 i = floor(uv); 
        vec2 f = fract(uv); 
        vec2 u = f * f * (3.0 - 2.0 * f); 
        float a = hash21(i);
        float b = hash21(i + vec2(1.0, 0.0));
        float c = hash21(i + vec2(0.0, 1.0));
        float d = hash21(i + vec2(1.0, 1.0));
        return mix(mix(a, b, u.x), mix(c, d, u.x), u.y);
    }

    float fbm(vec2 uv, int octaves) {
        float value = 0.0;
        float amplitude = 0.5; 
        float frequency = 1.0; 
        for (int i = 0; i < octaves; ++i) { 
            value += amplitude * noise(uv * frequency);
            frequency *= 2.0;   
            amplitude *= 0.5;   
        }
        return value;
    }

    float turbulence(vec2 uv, float time) {
        float t = 0.0;
        t += noise(uv * 2.0 + time * 0.3) * 0.5;
        t += noise(uv * 4.0 + time * 0.7) * 0.25;
        t += noise(uv * 8.0 + time * 1.2) * 0.125; 

        return t;
    }

    float createAuroraBand(vec2 uv, float bandCenter, float bandWidth, float timeOffset, float spikeIntensity) {
        float time = uTime * 0.5 + timeOffset;
        float verticalChaos = turbulence(vec2(uv.x * 3.0, time * 0.4), time) * 0.15;
        float adjustedCenter = bandCenter + verticalChaos;
        float dist = abs(uv.y - adjustedCenter);
        float band = 1.0 - smoothstep(0.0, bandWidth, dist);

        float flowX = uv.x;
        flowX += sin(uv.y * 6.0 + time * 1.3) * 0.2;
        flowX += turbulence(vec2(uv.x * 2.0, uv.y * 1.5), time * 0.8) * 0.3;
        flowX += noise(vec2(uv.x * 8.0, time * 1.7)) * 0.1;

        float flowY = uv.y;
        flowY += sin(uv.x * 4.0 + time * 0.9) * 0.05;
        flowY += turbulence(vec2(uv.x * 3.0, uv.y * 2.0), time * 0.6) * 0.08;
        flowY += noise(vec2(uv.y * 12.0, time * 2.3)) * 0.03;

        float detail1 = fbm(vec2(flowX * 3.0, flowY * 1.5) + time * 0.2, 3); 
        float detail2 = fbm(vec2(flowX * 6.0, flowY * 3.0) + time * 0.5, 2); 
        float detail3 = fbm(vec2(flowX * 12.0, flowY * 6.0) + time * 0.8, 1); 

        float chaos = turbulence(vec2(flowX * 8.0, flowY * 4.0), time * 1.5) * 0.3;
        float texture = detail1 * 0.5 + detail2 * 0.3 + detail3 * 0.15 + chaos * 0.05;

        float intensityNoise = noise(vec2(uv.x * 2.0, time * 0.3));
        texture *= 0.7 + intensityNoise * 0.6;
        texture *= spikeIntensity;

        float powerVariation = 1.2 + noise(vec2(time * 0.1, uv.x * 0.5)) * 0.8;
        texture = pow(max(texture, 0.0), powerVariation);

        return band * texture;
    }

    vec3 getAuroraColor(float intensity, float bandId, vec2 uv, float time) {
        vec3 color = vec3(0.0);

        float colorShift = noise(vec2(uv.x * 1.5, time * 0.2)) * 0.3;
        float adjustedBandId = bandId + colorShift;

        if (adjustedBandId < 0.5) {
            vec3 green = vec3(0.2, 0.8, 0.3);
            vec3 yellow = vec3(0.8, 0.9, 0.4);
            color = mix(green, yellow, smoothstep(0.3, 0.7, intensity));
        } else if (adjustedBandId < 1.5) {
            vec3 purple = vec3(0.4, 0.2, 0.8);
            vec3 pink = vec3(0.8, 0.3, 0.6);
            color = mix(purple, pink, smoothstep(0.3, 0.7, intensity));
        } else {
            vec3 blue = vec3(0.2, 0.4, 0.9);
            vec3 cyan = vec3(0.3, 0.8, 0.9);
            color = mix(blue, cyan, smoothstep(0.3, 0.7, intensity));
        }

        float tempShift = noise(vec2(uv.x * 2.0 + time * 0.1, uv.y * 1.5)) * 0.2;
        color.r += tempShift * 0.1;
        color.b -= tempShift * 0.1;

        return color;
    }

    void main() {
        vec2 uv = gl_FragCoord.xy / uResolution;

        float time = uTime * 0.5;
        float baseCenterY = 0.85; 
        float globalVerticalChaos = turbulence(vec2(time * 0.1, 0.0), time) * 0.05;
        float centerY = baseCenterY + globalVerticalChaos;

        float spike1 = 1.0 + sin(time * 0.3 + 1.0) * 0.15;
        float spike2 = 1.0 + sin(time * 0.4 + 2.0) * 0.2; 
        float spike3 = 1.0 + sin(time * 0.5 + 3.0) * 0.25;
        float spike4 = 1.0 + sin(time * 0.35 + 4.0) * 0.1;
        float spike5 = 1.0 + sin(time * 0.45 + 5.0) * 0.18;

        float randomSpike1 = 1.0 + smoothstep(0.85, 1.0, noise(vec2(time * 0.1, 1.0))) * 0.8;
        float randomSpike2 = 1.0 + smoothstep(0.9, 1.0, noise(vec2(time * 0.15, 2.0))) * 1.0;
        float randomSpike3 = 1.0 + smoothstep(0.88, 1.0, noise(vec2(time * 0.12, 3.0))) * 0.9;

        spike1 *= randomSpike1;
        spike2 *= randomSpike2;
        spike3 *= randomSpike3;

        float band1 = createAuroraBand(uv, centerY + noise(vec2(time * 0.3, 1.0)) * 0.08, 0.5, time * 0.7, spike1);
        float band2 = createAuroraBand(uv, centerY + noise(vec2(time * 0.4, 2.0)) * 0.09, 0.45, time * 0.9 + 1.0, spike2);
        float band3 = createAuroraBand(uv, centerY + noise(vec2(time * 0.5, 3.0)) * 0.07, 0.4, time * 1.1 + 2.0, spike3);

        float band4 = createAuroraBand(uv, centerY + noise(vec2(time * 0.2, 4.0)) * 0.06, 0.35, time * 0.6 + 3.0, spike4) * 0.6;
        float band5 = createAuroraBand(uv, centerY + noise(vec2(time * 0.35, 5.0)) * 0.05, 0.3, time * 0.8 + 4.0, spike5) * 0.4;

        float topExtension = smoothstep(0.5, 1.0, uv.y);
        float extensionNoise = noise(vec2(uv.x * 4.0, time * 0.2)) * 0.3;
        topExtension *= (0.2 + extensionNoise);

        float totalBandIntensity = band1 + band2 + band3 + band4 + band5;
        float extensionBlend = topExtension * smoothstep(0.1, 0.5, totalBandIntensity);

        vec3 color1 = getAuroraColor(band1, 0.0, uv, time) * band1;
        vec3 color2 = getAuroraColor(band2, 1.0, uv, time) * band2;
        vec3 color3 = getAuroraColor(band3, 2.0, uv, time) * band3;
        vec3 color4 = getAuroraColor(band4, 0.5, uv, time) * band4;
        vec3 color5 = getAuroraColor(band5, 1.5, uv, time) * band5;

        vec3 extensionColor = mix(
            getAuroraColor(extensionBlend, 0.5, uv, time),
            getAuroraColor(extensionBlend, 1.5, uv, time),
            noise(vec2(uv.x * 2.0, time * 0.3))
        ) * extensionBlend;

        vec3 finalColor = vec3(0.0);
        finalColor = color1 + color2 + color3 + color4 + color5 + extensionColor;

        float totalIntensity = band1 + band2 + band3 + band4 + band5 + extensionBlend;

        if (totalIntensity > 0.1) {
            vec3 weightedAverage = (color1 * band1 + color2 * band2 + color3 * band3 + color4 * band4 + color5 * band5 + extensionColor * extensionBlend) / max(totalIntensity, 0.001);
            float blendFactor = smoothstep(0.2, 0.8, totalIntensity) * 0.6;
            finalColor = mix(finalColor, weightedAverage, blendFactor);
        }

        vec3 luminance = vec3(0.299, 0.587, 0.114);
        float lum = dot(finalColor, luminance);
        finalColor = mix(vec3(lum), finalColor, 1.3); 

        float vignette = 1.0 - length(uv - 0.5) * 0.6;
        finalColor *= vignette;

        float scattering = noise(vec2(uv.x * 0.5, time * 0.1)) * 0.05;
        finalColor += vec3(scattering * 0.1, scattering * 0.15, scattering * 0.2);

        vec3 skyColor = vec3(0.01, 0.02, 0.05);
        finalColor = mix(skyColor, finalColor, smoothstep(0.0, 0.1, length(finalColor)));

        float alpha = smoothstep(0.0, 0.5, totalIntensity);

        finalColor = finalColor / (finalColor + vec3(1.0));
        finalColor = pow(finalColor, vec3(1.0/2.2));

        fragColor = vec4(finalColor, alpha);
    }`;

    const compileShader = (gl: WebGL2RenderingContext, type: number, source: string) => {
      const shader = gl.createShader(type);
      if (!shader) throw new Error('Failed to create shader');
      gl.shaderSource(shader, source);
      gl.compileShader(shader);
      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('Shader error:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        throw new Error('Shader compile failed');
      }
      return shader;
    };

    const createProgram = (gl: WebGL2RenderingContext, vsSource: string, fsSource: string) => {
      const vs = compileShader(gl, gl.VERTEX_SHADER, vsSource);
      const fs = compileShader(gl, gl.FRAGMENT_SHADER, fsSource);
      const program = gl.createProgram();
      if (!program) throw new Error('Failed to create program');
      gl.attachShader(program, vs);
      gl.attachShader(program, fs);
      gl.linkProgram(program);
      if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('Program error:', gl.getProgramInfoLog(program));
        throw new Error('Program link failed');
      }
      return program;
    };

    const program = createProgram(gl, vsSource, fsSource);
    gl.useProgram(program);

    const quadVerts = new Float32Array([
      -1, -1, 
       1, -1, 
      -1,  1, 
      -1,  1, 
       1, -1, 
       1,  1  
    ]);

    const vao = gl.createVertexArray();
    gl.bindVertexArray(vao);

    const vbo = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, vbo);
    gl.bufferData(gl.ARRAY_BUFFER, quadVerts, gl.STATIC_DRAW);

    const aPosition = gl.getAttribLocation(program, 'aPosition');
    gl.enableVertexAttribArray(aPosition);
    gl.vertexAttribPointer(aPosition, 2, gl.FLOAT, false, 0, 0);

    const uTime = gl.getUniformLocation(program, 'uTime');
    const uResolution = gl.getUniformLocation(program, 'uResolution');

    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

    let animationId: number;

    const draw = (time: number) => {
      if (isPaused) {
        animationIdRef.current = requestAnimationFrame(draw);
        return;
      }

      time *= 0.001;
      gl.viewport(0, 0, canvas.width, canvas.height);
      gl.clear(gl.COLOR_BUFFER_BIT);

      gl.uniform1f(uTime, time);
      gl.uniform2f(uResolution, canvas.width, canvas.height);

      gl.drawArrays(gl.TRIANGLES, 0, 6);
      animationIdRef.current = requestAnimationFrame(draw);
    };

    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    window.addEventListener('resize', handleResize);
    animationIdRef.current = requestAnimationFrame(draw);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
    };
  }, [isPaused]);

  return (
    <canvas 
      ref={canvasRef}
      className="fixed inset-0 w-full h-full"
      style={{ background: '#000' }}
    />
  );
};

export default AuroraBackground;