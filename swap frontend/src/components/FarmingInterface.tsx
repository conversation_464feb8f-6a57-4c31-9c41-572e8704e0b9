import React, { useState, useEffect } from 'react';
import { TrendingUp, Co<PERSON>, Clock, Zap, ExternalLink, Loader2, AlertTriangle } from 'lucide-react';
import { useWeb3 } from '../hooks/useWeb3';
import { TokenInfo } from '../utils/SwapSDK';

interface FarmPool {
  id: number;
  lpToken: TokenInfo;
  token0: TokenInfo;
  token1: TokenInfo;
  rewardToken: TokenInfo;
  apr: string;
  tvl: string;
  userStaked: string;
  userRewards: string;
  allocPoint: number;
  isActive: boolean;
  endBlock: number;
  multiplier: string;
}

const FarmingInterface: React.FC = () => {
  const { account, network, isConnected } = useWeb3();
  const [farms, setFarms] = useState<FarmPool[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'active' | 'finished' | 'my-farms'>('active');
  const [sortBy, setSortBy] = useState<'apr' | 'tvl' | 'multiplier'>('apr');

  const handleStake = async (farmId: number, amount: string) => {
    setIsLoading(true);
    try {
      // Implementation for staking LP tokens
      console.log('Staking:', { farmId, amount });
    } catch (error) {
      console.error('Error staking:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnstake = async (farmId: number, amount: string) => {
    setIsLoading(true);
    try {
      // Implementation for unstaking LP tokens
      console.log('Unstaking:', { farmId, amount });
    } catch (error) {
      console.error('Error unstaking:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleHarvest = async (farmId: number) => {
    setIsLoading(true);
    try {
      // Implementation for harvesting rewards
      console.log('Harvesting:', { farmId });
    } catch (error) {
      console.error('Error harvesting:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredFarms = farms.filter(farm => {
    if (activeTab === 'active') return farm.isActive;
    if (activeTab === 'finished') return !farm.isActive;
    if (activeTab === 'my-farms') return parseFloat(farm.userStaked) > 0;
    return true;
  });

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-900 rounded-3xl p-6 mb-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
            <TrendingUp className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-black text-white tracking-wider">Yield Farming</h1>
            <p className="text-gray-400">Stake LP tokens to earn governance tokens</p>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Coins className="w-5 h-5 text-yellow-400" />
              <span className="text-gray-400 text-sm">Total Value Locked</span>
            </div>
            <div className="text-2xl font-black text-white">$2,450,000</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-green-400" />
              <span className="text-gray-400 text-sm">Average APR</span>
            </div>
            <div className="text-2xl font-black text-green-400">125.5%</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Zap className="w-5 h-5 text-blue-400" />
              <span className="text-gray-400 text-sm">Active Farms</span>
            </div>
            <div className="text-2xl font-black text-white">12</div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-gray-800 rounded-2xl p-1 mb-6">
        {[
          { id: 'active', label: 'Active Farms' },
          { id: 'finished', label: 'Finished' },
          { id: 'my-farms', label: 'My Farms' }
        ].map(({ id, label }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex-1 py-3 px-4 rounded-xl transition-all ${
              activeTab === id
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            <span className="font-normal tracking-wide">{label}</span>
          </button>
        ))}
      </div>

      {/* Sort Options */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <span className="text-gray-400 text-sm">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="bg-gray-800 text-white rounded-xl px-3 py-2 text-sm border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="apr">APR</option>
            <option value="tvl">TVL</option>
            <option value="multiplier">Multiplier</option>
          </select>
        </div>
        <div className="text-gray-400 text-sm">
          {filteredFarms.length} farms found
        </div>
      </div>

      {/* Farm Cards */}
      {!isConnected && activeTab === 'my-farms' ? (
        <div className="bg-gray-900 rounded-3xl p-12 text-center">
          <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-xl font-black text-white mb-2">Connect Your Wallet</h3>
          <p className="text-gray-400">Connect your wallet to view your farming positions</p>
        </div>
      ) : filteredFarms.length === 0 ? (
        <div className="bg-gray-900 rounded-3xl p-12 text-center">
          <TrendingUp className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-black text-white mb-2">No Farms Found</h3>
          <p className="text-gray-400">
            {activeTab === 'my-farms' 
              ? 'You have no active farming positions'
              : 'No farms available in this category'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredFarms.map((farm) => (
            <FarmCard
              key={farm.id}
              farm={farm}
              onStake={handleStake}
              onUnstake={handleUnstake}
              onHarvest={handleHarvest}
              isLoading={isLoading}
            />
          ))}
        </div>
      )}
    </div>
  );
};

interface FarmCardProps {
  farm: FarmPool;
  onStake: (farmId: number, amount: string) => void;
  onUnstake: (farmId: number, amount: string) => void;
  onHarvest: (farmId: number) => void;
  isLoading: boolean;
}

const FarmCard: React.FC<FarmCardProps> = ({ farm, onStake, onUnstake, onHarvest, isLoading }) => {
  const [stakeAmount, setStakeAmount] = useState('');
  const [unstakeAmount, setUnstakeAmount] = useState('');
  const [showStakeModal, setShowStakeModal] = useState(false);
  const [showUnstakeModal, setShowUnstakeModal] = useState(false);
  const { network } = useWeb3();

  return (
    <div className="bg-gray-900 rounded-3xl p-6 space-y-4">
      {/* Farm Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex -space-x-2">
            <img 
              src={farm.token0.logoURI} 
              alt={farm.token0.symbol} 
              className="w-10 h-10 rounded-full border-2 border-gray-700" 
            />
            <img 
              src={farm.token1.logoURI} 
              alt={farm.token1.symbol} 
              className="w-10 h-10 rounded-full border-2 border-gray-700" 
            />
          </div>
          <div>
            <h3 className="text-lg font-black text-white">
              {farm.token0.symbol}/{farm.token1.symbol}
            </h3>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400 text-sm">Earn {farm.rewardToken.symbol}</span>
              {farm.multiplier !== '1x' && (
                <span className="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-bold">
                  {farm.multiplier}
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-black text-green-400">{farm.apr}%</div>
          <div className="text-gray-400 text-sm">APR</div>
        </div>
      </div>

      {/* Farm Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-800 rounded-2xl p-3">
          <div className="text-gray-400 text-sm">TVL</div>
          <div className="text-white font-normal">${farm.tvl}</div>
        </div>
        <div className="bg-gray-800 rounded-2xl p-3">
          <div className="text-gray-400 text-sm">Multiplier</div>
          <div className="text-white font-normal">{farm.multiplier}</div>
        </div>
      </div>

      {/* User Position */}
      {parseFloat(farm.userStaked) > 0 && (
        <div className="bg-gray-800 rounded-2xl p-4 space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-gray-400 text-sm">Your Position</span>
            <Clock className="w-4 h-4 text-gray-400" />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-gray-400 text-sm">Staked</div>
              <div className="text-white font-normal">{farm.userStaked} LP</div>
            </div>
            <div>
              <div className="text-gray-400 text-sm">Rewards</div>
              <div className="text-green-400 font-normal">{farm.userRewards} {farm.rewardToken.symbol}</div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-2">
        {parseFloat(farm.userRewards) > 0 && (
          <button
            onClick={() => onHarvest(farm.id)}
            disabled={isLoading}
            className="w-full py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-400 hover:to-emerald-500 text-white rounded-2xl font-black transition-all tracking-wider"
          >
            {isLoading && <Loader2 className="w-4 h-4 animate-spin inline mr-2" />}
            Harvest {farm.userRewards} {farm.rewardToken.symbol}
          </button>
        )}
        
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => setShowStakeModal(true)}
            className="py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl font-black transition-all tracking-wider"
          >
            Stake
          </button>
          <button
            onClick={() => setShowUnstakeModal(true)}
            disabled={parseFloat(farm.userStaked) === 0}
            className={`py-3 rounded-2xl font-black transition-all tracking-wider ${
              parseFloat(farm.userStaked) > 0
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-gray-700 text-gray-400 cursor-not-allowed'
            }`}
          >
            Unstake
          </button>
        </div>
      </div>

      {/* Farm Details Link */}
      {network && (
        <button
          onClick={() => window.open(`${network.blockExplorer}/address/${farm.lpToken.address}`, '_blank')}
          className="w-full flex items-center justify-center space-x-2 py-2 text-gray-400 hover:text-gray-300 transition-all"
        >
          <span className="text-sm">View LP Token</span>
          <ExternalLink className="w-4 h-4" />
        </button>
      )}

      {/* Stake Modal */}
      {showStakeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-3xl p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-black text-white mb-4">Stake LP Tokens</h3>
            <div className="space-y-4">
              <div className="bg-gray-800 rounded-2xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-400 text-sm">Amount to Stake</span>
                  <span className="text-gray-400 text-sm">Balance: {farm.lpToken.balance || '0'}</span>
                </div>
                <input
                  type="number"
                  placeholder="0.0"
                  value={stakeAmount}
                  onChange={(e) => setStakeAmount(e.target.value)}
                  className="w-full bg-transparent text-xl font-black text-white placeholder-gray-500 outline-none"
                />
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setShowStakeModal(false)}
                  className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-2xl transition-all"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    onStake(farm.id, stakeAmount);
                    setShowStakeModal(false);
                    setStakeAmount('');
                  }}
                  disabled={!stakeAmount || isLoading}
                  className="flex-1 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl transition-all disabled:opacity-50"
                >
                  Stake
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Unstake Modal */}
      {showUnstakeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-3xl p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-black text-white mb-4">Unstake LP Tokens</h3>
            <div className="space-y-4">
              <div className="bg-gray-800 rounded-2xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-400 text-sm">Amount to Unstake</span>
                  <span className="text-gray-400 text-sm">Staked: {farm.userStaked}</span>
                </div>
                <input
                  type="number"
                  placeholder="0.0"
                  value={unstakeAmount}
                  onChange={(e) => setUnstakeAmount(e.target.value)}
                  className="w-full bg-transparent text-xl font-black text-white placeholder-gray-500 outline-none"
                />
              </div>
              <div className="grid grid-cols-4 gap-2">
                {[25, 50, 75, 100].map((percentage) => (
                  <button
                    key={percentage}
                    onClick={() => setUnstakeAmount((parseFloat(farm.userStaked) * percentage / 100).toString())}
                    className="py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-all"
                  >
                    {percentage}%
                  </button>
                ))}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setShowUnstakeModal(false)}
                  className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-2xl transition-all"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    onUnstake(farm.id, unstakeAmount);
                    setShowUnstakeModal(false);
                    setUnstakeAmount('');
                  }}
                  disabled={!unstakeAmount || isLoading}
                  className="flex-1 py-3 bg-red-600 hover:bg-red-700 text-white rounded-2xl transition-all disabled:opacity-50"
                >
                  Unstake
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FarmingInterface;
