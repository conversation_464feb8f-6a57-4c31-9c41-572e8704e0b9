import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  Clock, 
  CheckCircle, 
  <PERSON><PERSON><PERSON>riangle, 
  Loader2,
  ExternalLink,
  Coins,
  Gift,
  Timer,
  RefreshCw
} from 'lucide-react';
import { useWeb3 } from '../hooks/useWeb3';
import { TokenInfo } from '../utils/SwapSDK';

interface FaucetToken {
  address: string;
  symbol: string;
  name: string;
  logoURI: string;
  amount: string;
  cooldown: number;
  lastClaim: number;
  isAvailable: boolean;
  balance: string;
}

const FaucetInterface: React.FC = () => {
  const { account, network, isConnected } = useWeb3();
  const [faucetTokens, setFaucetTokens] = useState<FaucetToken[]>([
    {
      address: '******************************************',
      symbol: 'USDC',
      name: 'USD Coin',
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      amount: '1000',
      cooldown: 24 * 60 * 60, // 24 hours
      lastClaim: 0,
      isAvailable: true,
      balance: '50000'
    },
    {
      address: '******************************************',
      symbol: 'WETH',
      name: 'Wrapped Ethereum',
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      amount: '0.5',
      cooldown: 12 * 60 * 60, // 12 hours
      lastClaim: 0,
      isAvailable: true,
      balance: '25'
    },
    {
      address: '******************************************',
      symbol: 'DAI',
      name: 'Dai Stablecoin',
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      amount: '500',
      cooldown: 24 * 60 * 60, // 24 hours
      lastClaim: 0,
      isAvailable: true,
      balance: '100000'
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [claimHistory, setClaimHistory] = useState<Array<{
    token: string;
    amount: string;
    timestamp: number;
    txHash: string;
  }>>([]);

  useEffect(() => {
    if (isConnected) {
      loadFaucetData();
      loadClaimHistory();
    }
  }, [isConnected, account]);

  const loadFaucetData = async () => {
    try {
      // Implementation to load faucet data
      console.log('Loading faucet data...');
    } catch (error) {
      console.error('Error loading faucet data:', error);
    }
  };

  const loadClaimHistory = async () => {
    try {
      // Implementation to load claim history
      console.log('Loading claim history...');
    } catch (error) {
      console.error('Error loading claim history:', error);
    }
  };

  const handleClaim = async (token: FaucetToken) => {
    if (!isConnected) return;
    
    setIsLoading(true);
    try {
      // Implementation to claim tokens from faucet
      console.log('Claiming tokens:', token);
      
      // Update last claim time
      setFaucetTokens(prev => prev.map(t => 
        t.address === token.address 
          ? { ...t, lastClaim: Date.now() / 1000, isAvailable: false }
          : t
      ));
      
      // Add to claim history
      setClaimHistory(prev => [{
        token: token.symbol,
        amount: token.amount,
        timestamp: Date.now(),
        txHash: '0x' + Math.random().toString(16).substr(2, 64)
      }, ...prev]);
      
    } catch (error) {
      console.error('Error claiming tokens:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTimeUntilNextClaim = (token: FaucetToken) => {
    if (token.lastClaim === 0) return 0;
    const now = Date.now() / 1000;
    const nextClaim = token.lastClaim + token.cooldown;
    return Math.max(0, nextClaim - now);
  };

  const formatTimeRemaining = (seconds: number) => {
    if (seconds === 0) return 'Available now';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) return `${hours}h ${minutes}m`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };

  const isTestnet = network?.chainId === 11155111; // Sepolia

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-900 rounded-3xl p-6 mb-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-black text-white tracking-wider">Token Faucet</h1>
            <p className="text-gray-400">Get free testnet tokens for trading and testing</p>
          </div>
        </div>

        {!isTestnet && (
          <div className="bg-yellow-900 border border-yellow-700 rounded-2xl p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-yellow-400" />
              <span className="text-yellow-300 font-medium">
                Faucet is only available on Sepolia testnet
              </span>
            </div>
          </div>
        )}

        {!isConnected && (
          <div className="bg-blue-900 border border-blue-700 rounded-2xl p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-blue-400" />
              <span className="text-blue-300 font-medium">
                Connect your wallet to claim testnet tokens
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Faucet Stats */}
      {isConnected && isTestnet && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-gray-900 rounded-3xl p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                <Gift className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-white font-bold">Available Tokens</div>
                <div className="text-gray-400 text-sm">Ready to claim</div>
              </div>
            </div>
            <div className="text-2xl font-black text-white">
              {faucetTokens.filter(t => getTimeUntilNextClaim(t) === 0).length}
            </div>
          </div>

          <div className="bg-gray-900 rounded-3xl p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <Timer className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-white font-bold">On Cooldown</div>
                <div className="text-gray-400 text-sm">Waiting period</div>
              </div>
            </div>
            <div className="text-2xl font-black text-white">
              {faucetTokens.filter(t => getTimeUntilNextClaim(t) > 0).length}
            </div>
          </div>

          <div className="bg-gray-900 rounded-3xl p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
                <Coins className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-white font-bold">Total Claims</div>
                <div className="text-gray-400 text-sm">All time</div>
              </div>
            </div>
            <div className="text-2xl font-black text-white">{claimHistory.length}</div>
          </div>
        </div>
      )}

      {/* Token Faucets */}
      {isConnected && isTestnet && (
        <div className="bg-gray-900 rounded-3xl p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-black text-white">Available Tokens</h3>
            <button
              onClick={loadFaucetData}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {faucetTokens.map((token) => {
              const timeRemaining = getTimeUntilNextClaim(token);
              const canClaim = timeRemaining === 0;

              return (
                <div key={token.address} className="bg-gray-800 rounded-2xl p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <img 
                      src={token.logoURI} 
                      alt={token.symbol} 
                      className="w-12 h-12 rounded-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://via.placeholder.com/48x48/6366f1/ffffff?text=${token.symbol}`;
                      }}
                    />
                    <div className="flex-1">
                      <div className="text-white font-bold text-lg">{token.symbol}</div>
                      <div className="text-gray-400 text-sm">{token.name}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-bold">{token.amount}</div>
                      <div className="text-gray-400 text-sm">per claim</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Faucet Balance</span>
                      <span className="text-white">{token.balance} {token.symbol}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Cooldown</span>
                      <span className="text-white">{token.cooldown / 3600}h</span>
                    </div>

                    {!canClaim && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-400">Next claim in</span>
                        <span className="text-yellow-400">{formatTimeRemaining(timeRemaining)}</span>
                      </div>
                    )}
                  </div>

                  <button
                    onClick={() => handleClaim(token)}
                    disabled={!canClaim || isLoading}
                    className={`w-full mt-4 py-3 rounded-2xl font-black transition-all ${
                      canClaim && !isLoading
                        ? 'bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-400 hover:to-blue-500 text-white shadow-lg transform hover:scale-105'
                        : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {isLoading && <Loader2 className="w-4 h-4 animate-spin inline mr-2" />}
                    {canClaim ? `Claim ${token.amount} ${token.symbol}` : `Available in ${formatTimeRemaining(timeRemaining)}`}
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Claim History */}
      {isConnected && isTestnet && claimHistory.length > 0 && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <h3 className="text-lg font-black text-white mb-6">Recent Claims</h3>
          <div className="space-y-4">
            {claimHistory.slice(0, 5).map((claim, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-800 rounded-2xl p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="text-white font-medium">
                      Claimed {claim.amount} {claim.token}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {new Date(claim.timestamp).toLocaleString()}
                    </div>
                  </div>
                </div>
                {network && (
                  <button
                    onClick={() => window.open(`${network.blockExplorer}/tx/${claim.txHash}`, '_blank')}
                    className="p-2 text-gray-400 hover:text-gray-300 transition-all"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      {isConnected && isTestnet && (
        <div className="bg-gray-900 rounded-3xl p-6 mt-6">
          <h3 className="text-lg font-black text-white mb-4">How to Use the Faucet</h3>
          <div className="space-y-3 text-gray-400">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold mt-0.5">1</div>
              <div>
                <div className="text-white font-medium">Connect your wallet</div>
                <div className="text-sm">Make sure you're connected to Sepolia testnet</div>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold mt-0.5">2</div>
              <div>
                <div className="text-white font-medium">Claim tokens</div>
                <div className="text-sm">Click the claim button for any available token</div>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold mt-0.5">3</div>
              <div>
                <div className="text-white font-medium">Wait for cooldown</div>
                <div className="text-sm">Each token has a cooldown period before you can claim again</div>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold mt-0.5">4</div>
              <div>
                <div className="text-white font-medium">Start trading</div>
                <div className="text-sm">Use your testnet tokens to test swaps and liquidity provision</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FaucetInterface;
