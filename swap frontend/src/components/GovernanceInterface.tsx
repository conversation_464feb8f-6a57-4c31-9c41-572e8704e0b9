import React, { useState, useEffect } from 'react';
import { 
  Vote, 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Plus,
  ExternalLink,
  Loader2,
  TrendingUp,
  Calendar,
  Target
} from 'lucide-react';
import { useWeb3 } from '../hooks/useWeb3';

interface Proposal {
  id: number;
  title: string;
  description: string;
  proposer: string;
  status: 'PENDING' | 'ACTIVE' | 'SUCCEEDED' | 'DEFEATED' | 'QUEUED' | 'EXECUTED' | 'CANCELLED';
  startTime: number;
  endTime: number;
  forVotes: string;
  againstVotes: string;
  abstainVotes: string;
  quorum: string;
  userVote?: 'FOR' | 'AGAINST' | 'ABSTAIN';
  userVotingPower: string;
}

interface GovernanceStats {
  totalSupply: string;
  totalDelegated: string;
  userBalance: string;
  userVotingPower: string;
  userDelegate: string;
  activeProposals: number;
  totalProposals: number;
  participationRate: string;
}

const GovernanceInterface: React.FC = () => {
  const { account, isConnected } = useWeb3();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [governanceStats, setGovernanceStats] = useState<GovernanceStats>({
    totalSupply: '1000000',
    totalDelegated: '750000',
    userBalance: '5000',
    userVotingPower: '5000',
    userDelegate: '',
    activeProposals: 3,
    totalProposals: 12,
    participationRate: '65.5'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'proposals' | 'delegate' | 'create'>('proposals');
  const [delegateAddress, setDelegateAddress] = useState('');
  const [newProposal, setNewProposal] = useState({
    title: '',
    description: '',
    targets: [''],
    values: ['0'],
    calldatas: ['0x'],
    description_full: ''
  });

  useEffect(() => {
    loadProposals();
    loadGovernanceStats();
  }, []);

  const loadProposals = async () => {
    setIsLoading(true);
    try {
      // Implementation to load proposals
      console.log('Loading proposals...');
    } catch (error) {
      console.error('Error loading proposals:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadGovernanceStats = async () => {
    try {
      // Implementation to load governance stats
      console.log('Loading governance stats...');
    } catch (error) {
      console.error('Error loading governance stats:', error);
    }
  };

  const handleVote = async (proposalId: number, support: 'FOR' | 'AGAINST' | 'ABSTAIN') => {
    setIsLoading(true);
    try {
      // Implementation to cast vote
      console.log('Voting:', { proposalId, support });
    } catch (error) {
      console.error('Error voting:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelegate = async () => {
    setIsLoading(true);
    try {
      // Implementation to delegate voting power
      console.log('Delegating to:', delegateAddress);
    } catch (error) {
      console.error('Error delegating:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateProposal = async () => {
    setIsLoading(true);
    try {
      // Implementation to create proposal
      console.log('Creating proposal:', newProposal);
    } catch (error) {
      console.error('Error creating proposal:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'text-blue-400 bg-blue-400/10';
      case 'SUCCEEDED': return 'text-green-400 bg-green-400/10';
      case 'DEFEATED': return 'text-red-400 bg-red-400/10';
      case 'EXECUTED': return 'text-purple-400 bg-purple-400/10';
      case 'QUEUED': return 'text-yellow-400 bg-yellow-400/10';
      case 'CANCELLED': return 'text-gray-400 bg-gray-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const formatNumber = (num: string) => {
    const value = parseFloat(num);
    if (value >= 1e6) return `${(value / 1e6).toFixed(1)}M`;
    if (value >= 1e3) return `${(value / 1e3).toFixed(1)}K`;
    return value.toLocaleString();
  };

  const calculateVotePercentage = (votes: string, total: string) => {
    const voteNum = parseFloat(votes);
    const totalNum = parseFloat(total);
    if (totalNum === 0) return 0;
    return (voteNum / totalNum) * 100;
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-900 rounded-3xl p-6 mb-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
            <Vote className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-black text-white tracking-wider">Governance</h1>
            <p className="text-gray-400">Participate in protocol governance and decision making</p>
          </div>
        </div>

        {/* Governance Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="w-5 h-5 text-blue-400" />
              <span className="text-gray-400 text-sm">Your Voting Power</span>
            </div>
            <div className="text-2xl font-black text-white">{formatNumber(governanceStats.userVotingPower)}</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Target className="w-5 h-5 text-green-400" />
              <span className="text-gray-400 text-sm">Active Proposals</span>
            </div>
            <div className="text-2xl font-black text-white">{governanceStats.activeProposals}</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-purple-400" />
              <span className="text-gray-400 text-sm">Participation Rate</span>
            </div>
            <div className="text-2xl font-black text-white">{governanceStats.participationRate}%</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Calendar className="w-5 h-5 text-yellow-400" />
              <span className="text-gray-400 text-sm">Total Proposals</span>
            </div>
            <div className="text-2xl font-black text-white">{governanceStats.totalProposals}</div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-gray-800 rounded-2xl p-1 mb-6">
        {[
          { id: 'proposals', label: 'Proposals', icon: Vote },
          { id: 'delegate', label: 'Delegate', icon: Users },
          { id: 'create', label: 'Create Proposal', icon: Plus }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all ${
              activeTab === id
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span className="font-normal tracking-wide">{label}</span>
          </button>
        ))}
      </div>

      {/* Proposals Tab */}
      {activeTab === 'proposals' && (
        <div className="space-y-6">
          {!isConnected ? (
            <div className="bg-gray-900 rounded-3xl p-12 text-center">
              <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-xl font-black text-white mb-2">Connect Your Wallet</h3>
              <p className="text-gray-400">Connect your wallet to view and vote on proposals</p>
            </div>
          ) : proposals.length === 0 ? (
            <div className="bg-gray-900 rounded-3xl p-12 text-center">
              <Vote className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-black text-white mb-2">No Proposals</h3>
              <p className="text-gray-400">No governance proposals found</p>
            </div>
          ) : (
            proposals.map((proposal) => (
              <div key={proposal.id} className="bg-gray-900 rounded-3xl p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-black text-white">#{proposal.id} {proposal.title}</h3>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(proposal.status)}`}>
                        {proposal.status}
                      </span>
                    </div>
                    <p className="text-gray-400 mb-4">{proposal.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>Proposed by: {proposal.proposer.slice(0, 6)}...{proposal.proposer.slice(-4)}</span>
                      <span>•</span>
                      <span>Ends: {new Date(proposal.endTime).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>

                {/* Voting Results */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-green-400 text-sm">For</span>
                    <span className="text-white font-medium">{formatNumber(proposal.forVotes)} votes</span>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-2">
                    <div 
                      className="bg-green-400 h-2 rounded-full" 
                      style={{ 
                        width: `${calculateVotePercentage(
                          proposal.forVotes, 
                          (parseFloat(proposal.forVotes) + parseFloat(proposal.againstVotes) + parseFloat(proposal.abstainVotes)).toString()
                        )}%` 
                      }}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-red-400 text-sm">Against</span>
                    <span className="text-white font-medium">{formatNumber(proposal.againstVotes)} votes</span>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-2">
                    <div 
                      className="bg-red-400 h-2 rounded-full" 
                      style={{ 
                        width: `${calculateVotePercentage(
                          proposal.againstVotes, 
                          (parseFloat(proposal.forVotes) + parseFloat(proposal.againstVotes) + parseFloat(proposal.abstainVotes)).toString()
                        )}%` 
                      }}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-gray-400 text-sm">Abstain</span>
                    <span className="text-white font-medium">{formatNumber(proposal.abstainVotes)} votes</span>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-2">
                    <div 
                      className="bg-gray-400 h-2 rounded-full" 
                      style={{ 
                        width: `${calculateVotePercentage(
                          proposal.abstainVotes, 
                          (parseFloat(proposal.forVotes) + parseFloat(proposal.againstVotes) + parseFloat(proposal.abstainVotes)).toString()
                        )}%` 
                      }}
                    />
                  </div>
                </div>

                {/* Voting Buttons */}
                {proposal.status === 'ACTIVE' && !proposal.userVote && (
                  <div className="flex space-x-3">
                    <button
                      onClick={() => handleVote(proposal.id, 'FOR')}
                      disabled={isLoading}
                      className="flex-1 py-3 bg-green-600 hover:bg-green-700 text-white rounded-2xl font-black transition-all"
                    >
                      Vote For
                    </button>
                    <button
                      onClick={() => handleVote(proposal.id, 'AGAINST')}
                      disabled={isLoading}
                      className="flex-1 py-3 bg-red-600 hover:bg-red-700 text-white rounded-2xl font-black transition-all"
                    >
                      Vote Against
                    </button>
                    <button
                      onClick={() => handleVote(proposal.id, 'ABSTAIN')}
                      disabled={isLoading}
                      className="flex-1 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-2xl font-black transition-all"
                    >
                      Abstain
                    </button>
                  </div>
                )}

                {proposal.userVote && (
                  <div className="bg-gray-800 rounded-2xl p-4">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-5 h-5 text-green-400" />
                      <span className="text-white">You voted: <strong>{proposal.userVote}</strong></span>
                      <span className="text-gray-400">({formatNumber(proposal.userVotingPower)} votes)</span>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      )}

      {/* Delegate Tab */}
      {activeTab === 'delegate' && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <h3 className="text-lg font-black text-white mb-6">Delegate Voting Power</h3>
          
          <div className="space-y-6">
            <div className="bg-gray-800 rounded-2xl p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Your Balance</span>
                  <div className="text-white font-medium">{formatNumber(governanceStats.userBalance)} tokens</div>
                </div>
                <div>
                  <span className="text-gray-400">Current Delegate</span>
                  <div className="text-white font-medium">
                    {governanceStats.userDelegate || 'Self-delegated'}
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-gray-400 text-sm mb-2">Delegate Address</label>
              <input
                type="text"
                placeholder="0x... or ENS name"
                value={delegateAddress}
                onChange={(e) => setDelegateAddress(e.target.value)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-gray-500 text-sm mt-2">
                Delegate your voting power to another address. Leave empty to delegate to yourself.
              </p>
            </div>

            <button
              onClick={handleDelegate}
              disabled={isLoading}
              className="w-full py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl font-black transition-all disabled:opacity-50"
            >
              {isLoading && <Loader2 className="w-4 h-4 animate-spin inline mr-2" />}
              {delegateAddress ? 'Delegate Votes' : 'Self Delegate'}
            </button>
          </div>
        </div>
      )}

      {/* Create Proposal Tab */}
      {activeTab === 'create' && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <h3 className="text-lg font-black text-white mb-6">Create New Proposal</h3>
          
          <div className="space-y-6">
            <div>
              <label className="block text-gray-400 text-sm mb-2">Title</label>
              <input
                type="text"
                placeholder="Proposal title"
                value={newProposal.title}
                onChange={(e) => setNewProposal({...newProposal, title: e.target.value})}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-gray-400 text-sm mb-2">Description</label>
              <textarea
                placeholder="Detailed description of the proposal"
                value={newProposal.description}
                onChange={(e) => setNewProposal({...newProposal, description: e.target.value})}
                rows={4}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="bg-yellow-900 border border-yellow-700 rounded-2xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="w-5 h-5 text-yellow-400" />
                <span className="text-yellow-300 font-medium">Requirements</span>
              </div>
              <div className="text-yellow-200 text-sm space-y-1">
                <div>• Minimum 100,000 tokens required to create a proposal</div>
                <div>• Your current voting power: {formatNumber(governanceStats.userVotingPower)}</div>
                <div>• Proposals require 4% quorum to pass</div>
              </div>
            </div>

            <button
              onClick={handleCreateProposal}
              disabled={isLoading || parseFloat(governanceStats.userVotingPower) < 100000}
              className="w-full py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl font-black transition-all disabled:opacity-50"
            >
              {isLoading && <Loader2 className="w-4 h-4 animate-spin inline mr-2" />}
              Create Proposal
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GovernanceInterface;
