import React from 'react';
import { Activity, Menu, Wallet } from 'lucide-react';

const Header: React.FC = () => {
  return (
    <header className="relative z-10 w-full">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-3">
              <h1 className="text-lg font-normal text-white tracking-wider">QLK Swap</h1>
            </div>
          </div>
          
          <nav className="hidden md:flex items-center space-x-8 absolute left-1/2 transform -translate-x-1/2">
            <a href="#" className="text-white hover:text-blue-400 transition-colors font-normal tracking-wide">
              Swap
            </a>
            <a href="#" className="text-gray-300 hover:text-white transition-colors font-normal tracking-wide">
              Pool
            </a>
            <a href="#" className="text-gray-300 hover:text-white transition-colors font-normal tracking-wide">
              Portfolio
            </a>
            <a href="#" className="text-gray-300 hover:text-white transition-colors font-normal tracking-wide">
              Analytics
            </a>
          </nav>
          
          <div className="flex items-center space-x-4">
            <button className="flex items-center space-x-2 px-6 py-3 bg-black border border-gray-700 rounded-lg hover:bg-gray-900 hover:border-gray-600 transition-all">
              <span className="text-white font-normal tracking-wide text-sm">Connect Wallet</span>
            </button>
            
            <button className="md:hidden p-2 hover:bg-gray-800 rounded-xl transition-colors">
              <Menu className="w-6 h-6 text-gray-400" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;