import React, { useState, useEffect } from 'react';
import { Plus, Minus, Droplets, TrendingUp, AlertTriangle, ExternalLink, Loader2 } from 'lucide-react';
import { useWeb3 } from '../hooks/useWeb3';
import { useSwap } from '../hooks/useSwap';
import { useLiquidity } from '../hooks/useLiquidity';
import { TokenInfo, LiquidityPool } from '../utils/SwapSDK';
import TokenSelector from './TokenSelector';

const LiquidityInterface: React.FC = () => {
  const { account, network, isConnected } = useWeb3();
  const {
    supportedTokens,
    isTestnet,
    requestTestTokens,
    refreshBalances
  } = useSwap();

  const {
    pools,
    userPools,
    isLoading,
    error,
    addLiquidity,
    removeLiquidity,
    loadAllPools,
    loadUserPools
  } = useLiquidity();

  const [activeTab, setActiveTab] = useState<'add' | 'remove' | 'pools'>('add');
  const [token0, setToken0] = useState<TokenInfo | null>(null);
  const [token1, setToken1] = useState<TokenInfo | null>(null);
  const [amount0, setAmount0] = useState('');
  const [amount1, setAmount1] = useState('');
  const [slippage, setSlippage] = useState(0.5);
  const [isToken0SelectorOpen, setIsToken0SelectorOpen] = useState(false);
  const [isToken1SelectorOpen, setIsToken1SelectorOpen] = useState(false);

  // Initialize default tokens
  useEffect(() => {
    if (supportedTokens.length > 0) {
      const eth = supportedTokens.find(t => t.symbol === 'ETH');
      const usdc = supportedTokens.find(t => t.symbol === 'USDC');

      if (!token0 && eth) setToken0(eth);
      if (!token1 && usdc) setToken1(usdc);
    }
  }, [supportedTokens, token0, token1]);

  const handleAddLiquidity = async () => {
    if (!token0 || !token1 || !amount0 || !amount1) {
      console.log('❌ Missing required data for adding liquidity');
      return;
    }

    try {
      const success = await addLiquidity(token0, token1, amount0, amount1, slippage);

      if (success) {
        // Clear amounts on success
        setAmount0('');
        setAmount1('');
        console.log('✅ Liquidity added successfully!');
      }
    } catch (error) {
      console.error('❌ Error adding liquidity:', error);
    }
  };

  const handleRemoveLiquidity = async (pool: LiquidityPool, percentage: number) => {
    try {
      const success = await removeLiquidity(pool, percentage, slippage);

      if (success) {
        console.log('✅ Liquidity removed successfully!');
      }
    } catch (error) {
      console.error('❌ Error removing liquidity:', error);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* Tab Navigation */}
      <div className="flex bg-gray-800 rounded-2xl p-1 mb-6">
        {[
          { id: 'add', label: 'Add Liquidity', icon: Plus },
          { id: 'remove', label: 'Remove Liquidity', icon: Minus },
          { id: 'pools', label: 'My Pools', icon: Droplets }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all ${
              activeTab === id
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span className="font-normal tracking-wide">{label}</span>
          </button>
        ))}
      </div>

      {/* Add Liquidity */}
      {activeTab === 'add' && (
        <div className="bg-gray-900 rounded-3xl p-6 space-y-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <Plus className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-black text-white tracking-wider">Add Liquidity</h2>
              <p className="text-gray-400 text-sm">Provide liquidity to earn trading fees</p>
            </div>
          </div>

          {/* Token 0 Input */}
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-gray-400 text-sm font-normal tracking-wide">First Token</span>
              {token0 && (
                <span className="text-gray-400 text-sm">
                  Balance: {token0.balance || '0'}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <input
                type="number"
                placeholder="0.0"
                value={amount0}
                onChange={(e) => setAmount0(e.target.value)}
                className="flex-1 bg-transparent text-2xl font-black text-white placeholder-gray-500 outline-none tracking-wider"
              />
              <button
                onClick={() => setIsToken0SelectorOpen(true)}
                className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-xl transition-all"
              >
                {token0 ? (
                  <>
                    <img src={token0.logoURI} alt={token0.symbol} className="w-6 h-6 rounded-full" />
                    <span className="text-white font-normal tracking-wide">{token0.symbol}</span>
                  </>
                ) : (
                  <span className="text-gray-400 font-normal tracking-wide">Select Token</span>
                )}
              </button>
            </div>
          </div>

          {/* Plus Icon */}
          <div className="flex justify-center">
            <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
              <Plus className="w-4 h-4 text-gray-400" />
            </div>
          </div>

          {/* Token 1 Input */}
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-gray-400 text-sm font-normal tracking-wide">Second Token</span>
              {token1 && (
                <span className="text-gray-400 text-sm">
                  Balance: {token1.balance || '0'}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <input
                type="number"
                placeholder="0.0"
                value={amount1}
                onChange={(e) => setAmount1(e.target.value)}
                className="flex-1 bg-transparent text-2xl font-black text-white placeholder-gray-500 outline-none tracking-wider"
              />
              <button
                onClick={() => setIsToken1SelectorOpen(true)}
                className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-xl transition-all"
              >
                {token1 ? (
                  <>
                    <img src={token1.logoURI} alt={token1.symbol} className="w-6 h-6 rounded-full" />
                    <span className="text-white font-normal tracking-wide">{token1.symbol}</span>
                  </>
                ) : (
                  <span className="text-gray-400 font-normal tracking-wide">Select Token</span>
                )}
              </button>
            </div>
          </div>

          {/* Pool Info */}
          {token0 && token1 && (
            <div className="bg-gray-800 rounded-2xl p-4 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm font-normal tracking-wide">Pool Information</span>
                <TrendingUp className="w-4 h-4 text-green-400" />
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Pool Share</span>
                  <div className="text-white font-normal">0.01%</div>
                </div>
                <div>
                  <span className="text-gray-400">APR</span>
                  <div className="text-green-400 font-normal">12.5%</div>
                </div>
              </div>
            </div>
          )}

          {/* Add Liquidity Button */}
          <button
            onClick={handleAddLiquidity}
            disabled={!token0 || !token1 || !amount0 || !amount1 || isLoading}
            className={`w-full py-4 rounded-2xl font-black text-lg transition-all tracking-wider ${
              token0 && token1 && amount0 && amount1 && !isLoading
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-400 hover:to-purple-500 text-white shadow-lg transform hover:scale-105'
                : 'bg-gray-700 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isLoading && <Loader2 className="w-5 h-5 animate-spin inline mr-2" />}
            {isLoading ? 'Adding Liquidity...' : 'Add Liquidity'}
          </button>
        </div>
      )}

      {/* Remove Liquidity */}
      {activeTab === 'remove' && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
              <Minus className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-black text-white tracking-wider">Remove Liquidity</h2>
              <p className="text-gray-400 text-sm">Remove your liquidity from pools</p>
            </div>
          </div>

          {userPools.length === 0 ? (
            <div className="text-center py-12">
              <Droplets className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">No liquidity positions found</p>
              <p className="text-gray-500 text-sm">Add liquidity to start earning fees</p>
            </div>
          ) : (
            <div className="space-y-4">
              {userPools.map((pool, index) => (
                <div key={index} className="bg-gray-800 rounded-2xl p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex -space-x-2">
                        <img src={pool.token0.logoURI} alt={pool.token0.symbol} className="w-8 h-8 rounded-full border-2 border-gray-700" />
                        <img src={pool.token1.logoURI} alt={pool.token1.symbol} className="w-8 h-8 rounded-full border-2 border-gray-700" />
                      </div>
                      <div>
                        <div className="text-white font-normal">{pool.token0.symbol}/{pool.token1.symbol}</div>
                        <div className="text-gray-400 text-sm">APR: {pool.apr}%</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-normal">${pool.userLiquidity}</div>
                      <div className="text-gray-400 text-sm">{pool.userShare}% of pool</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-4 gap-2">
                    {[25, 50, 75, 100].map((percentage) => (
                      <button
                        key={percentage}
                        onClick={() => handleRemoveLiquidity(pool, percentage)}
                        className="py-2 px-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-white text-sm transition-all"
                      >
                        {percentage}%
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* My Pools */}
      {activeTab === 'pools' && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
              <Droplets className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-black text-white tracking-wider">My Liquidity Pools</h2>
              <p className="text-gray-400 text-sm">Manage your liquidity positions</p>
            </div>
          </div>

          {!isConnected ? (
            <div className="text-center py-12">
              <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">Connect your wallet</p>
              <p className="text-gray-500 text-sm">to view your liquidity positions</p>
            </div>
          ) : userPools.length === 0 ? (
            <div className="text-center py-12">
              <Droplets className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">No liquidity positions</p>
              <button
                onClick={() => setActiveTab('add')}
                className="mt-4 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all"
              >
                Add Liquidity
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {userPools.map((pool, index) => (
                <div key={index} className="bg-gray-800 rounded-2xl p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex -space-x-2">
                        <img src={pool.token0.logoURI} alt={pool.token0.symbol} className="w-10 h-10 rounded-full border-2 border-gray-700" />
                        <img src={pool.token1.logoURI} alt={pool.token1.symbol} className="w-10 h-10 rounded-full border-2 border-gray-700" />
                      </div>
                      <div>
                        <div className="text-white font-normal text-lg">{pool.token0.symbol}/{pool.token1.symbol}</div>
                        <div className="text-gray-400 text-sm">Pool Share: {pool.userShare}%</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-normal text-lg">${pool.userLiquidity}</div>
                      <div className="text-green-400 text-sm">APR: {pool.apr}%</div>
                    </div>
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-gray-700 grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Your {pool.token0.symbol}</span>
                      <div className="text-white">{pool.reserve0}</div>
                    </div>
                    <div>
                      <span className="text-gray-400">Your {pool.token1.symbol}</span>
                      <div className="text-white">{pool.reserve1}</div>
                    </div>
                  </div>

                  <div className="mt-4 flex space-x-2">
                    <button
                      onClick={() => setActiveTab('add')}
                      className="flex-1 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all text-sm"
                    >
                      Add More
                    </button>
                    <button
                      onClick={() => setActiveTab('remove')}
                      className="flex-1 py-2 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all text-sm"
                    >
                      Remove
                    </button>
                    {network && (
                      <button
                        onClick={() => window.open(`${network.blockExplorer}/address/${pool.address}`, '_blank')}
                        className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-xl transition-all"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Token Selectors */}
      <TokenSelector
        isOpen={isToken0SelectorOpen}
        onClose={() => setIsToken0SelectorOpen(false)}
        onSelectToken={setToken0}
        selectedToken={token0}
        tokens={supportedTokens}
        isTestnet={isTestnet}
        onRequestTestTokens={requestTestTokens}
        onRefreshBalances={refreshBalances}
      />

      <TokenSelector
        isOpen={isToken1SelectorOpen}
        onClose={() => setIsToken1SelectorOpen(false)}
        onSelectToken={setToken1}
        selectedToken={token1}
        tokens={supportedTokens}
        isTestnet={isTestnet}
        onRequestTestTokens={requestTestTokens}
        onRefreshBalances={refreshBalances}
      />
    </div>
  );
};

export default LiquidityInterface;
