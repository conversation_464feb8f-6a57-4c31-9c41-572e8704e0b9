import React from 'react';
import { 
  ArrowLeftRight, 
  Droplets, 
  TrendingUp, 
  Co<PERSON>, 
  Shield, 
  Settings,
  BarChart3,
  Vote,
  Zap
} from 'lucide-react';

interface NavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const Navigation: React.FC<NavigationProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'swap', label: 'Swap', icon: ArrowLeftRight },
    { id: 'liquidity', label: 'Liquidity', icon: Droplets },
    { id: 'farming', label: 'Farming', icon: TrendingUp },
    { id: 'tokens', label: 'Tokens', icon: Coins },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'governance', label: 'Governance', icon: Vote },
    { id: 'faucet', label: 'Faucet', icon: Zap },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className="bg-gray-900 border-b border-gray-800">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex space-x-1 overflow-x-auto scrollbar-hide">
          {tabs.map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => onTabChange(id)}
              className={`flex items-center space-x-2 px-4 py-3 rounded-t-lg transition-all whitespace-nowrap ${
                activeTab === id
                  ? 'bg-gray-800 text-blue-400 border-b-2 border-blue-400'
                  : 'text-gray-400 hover:text-gray-300 hover:bg-gray-800/50'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="font-normal tracking-wide">{label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Navigation;
