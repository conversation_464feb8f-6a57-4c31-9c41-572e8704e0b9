import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Users, 
  Activity,
  Settings,
  Pause,
  Play,
  Lock,
  Unlock,
  Eye,
  EyeOff,
  Loader2
} from 'lucide-react';
import { useWeb3 } from '../hooks/useWeb3';

interface SecurityStatus {
  isEmergencyActive: boolean;
  emergencyTimeLeft: number;
  circuitBreakerTriggered: boolean;
  mevProtectionEnabled: boolean;
  rateLimitEnabled: boolean;
  guardianCount: number;
  requiredGuardians: number;
  totalVolumeToday: string;
  suspiciousTransactions: number;
  blockedAddresses: number;
}

interface SecurityEvent {
  id: string;
  type: 'MEV_PROTECTION' | 'RATE_LIMIT' | 'CIRCUIT_BREAKER' | 'EMERGENCY' | 'GUARDIAN_ACTION';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  timestamp: number;
  user?: string;
  txHash?: string;
}

const SecurityInterface: React.FC = () => {
  const { account, network, isConnected } = useWeb3();
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus>({
    isEmergencyActive: false,
    emergencyTimeLeft: 0,
    circuitBreakerTriggered: false,
    mevProtectionEnabled: true,
    rateLimitEnabled: true,
    guardianCount: 3,
    requiredGuardians: 2,
    totalVolumeToday: '2450000',
    suspiciousTransactions: 12,
    blockedAddresses: 5
  });
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'events' | 'settings' | 'guardians'>('overview');
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    loadSecurityStatus();
    loadSecurityEvents();
  }, []);

  const loadSecurityStatus = async () => {
    setIsLoading(true);
    try {
      // Implementation to load security status from SecurityManager
      console.log('Loading security status...');
    } catch (error) {
      console.error('Error loading security status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSecurityEvents = async () => {
    try {
      // Implementation to load security events
      console.log('Loading security events...');
    } catch (error) {
      console.error('Error loading security events:', error);
    }
  };

  const handleEmergencyToggle = async () => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    try {
      // Implementation to toggle emergency mode
      console.log('Toggling emergency mode...');
    } catch (error) {
      console.error('Error toggling emergency mode:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCircuitBreakerToggle = async () => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    try {
      // Implementation to toggle circuit breaker
      console.log('Toggling circuit breaker...');
    } catch (error) {
      console.error('Error toggling circuit breaker:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMEVProtectionToggle = async () => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    try {
      // Implementation to toggle MEV protection
      console.log('Toggling MEV protection...');
    } catch (error) {
      console.error('Error toggling MEV protection:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimeLeft = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW': return 'text-green-400 bg-green-400/10';
      case 'MEDIUM': return 'text-yellow-400 bg-yellow-400/10';
      case 'HIGH': return 'text-orange-400 bg-orange-400/10';
      case 'CRITICAL': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-900 rounded-3xl p-6 mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-600 rounded-full flex items-center justify-center">
            <Shield className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-black text-white tracking-wider">Security Center</h1>
            <p className="text-gray-400">Monitor and manage protocol security</p>
          </div>
        </div>

        {/* Emergency Status */}
        {securityStatus.isEmergencyActive && (
          <div className="mt-6 bg-red-900 border border-red-700 rounded-2xl p-4">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="w-6 h-6 text-red-400" />
              <div>
                <div className="text-red-300 font-bold">EMERGENCY MODE ACTIVE</div>
                <div className="text-red-400 text-sm">
                  Time remaining: {formatTimeLeft(securityStatus.emergencyTimeLeft)}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-gray-800 rounded-2xl p-1 mb-6">
        {[
          { id: 'overview', label: 'Overview', icon: Shield },
          { id: 'events', label: 'Security Events', icon: Activity },
          { id: 'settings', label: 'Settings', icon: Settings },
          { id: 'guardians', label: 'Guardians', icon: Users }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all ${
              activeTab === id
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span className="font-normal tracking-wide">{label}</span>
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Security Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gray-900 rounded-3xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  securityStatus.mevProtectionEnabled ? 'bg-green-600' : 'bg-red-600'
                }`}>
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-white font-bold">MEV Protection</div>
                  <div className={`text-sm ${
                    securityStatus.mevProtectionEnabled ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {securityStatus.mevProtectionEnabled ? 'Active' : 'Inactive'}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-900 rounded-3xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  securityStatus.circuitBreakerTriggered ? 'bg-red-600' : 'bg-green-600'
                }`}>
                  {securityStatus.circuitBreakerTriggered ? 
                    <Pause className="w-5 h-5 text-white" /> : 
                    <Play className="w-5 h-5 text-white" />
                  }
                </div>
                <div>
                  <div className="text-white font-bold">Circuit Breaker</div>
                  <div className={`text-sm ${
                    securityStatus.circuitBreakerTriggered ? 'text-red-400' : 'text-green-400'
                  }`}>
                    {securityStatus.circuitBreakerTriggered ? 'Triggered' : 'Normal'}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-900 rounded-3xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-white font-bold">Guardians</div>
                  <div className="text-blue-400 text-sm">
                    {securityStatus.guardianCount}/{securityStatus.requiredGuardians} Required
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-900 rounded-3xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
                  <Activity className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-white font-bold">24h Volume</div>
                  <div className="text-purple-400 text-sm">
                    ${parseFloat(securityStatus.totalVolumeToday).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Security Status Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-900 rounded-3xl p-6">
              <h3 className="text-lg font-black text-white mb-4">Threat Detection</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Suspicious Transactions</span>
                  <span className="text-yellow-400 font-bold">{securityStatus.suspiciousTransactions}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Blocked Addresses</span>
                  <span className="text-red-400 font-bold">{securityStatus.blockedAddresses}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Rate Limit Violations</span>
                  <span className="text-orange-400 font-bold">3</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-900 rounded-3xl p-6">
              <h3 className="text-lg font-black text-white mb-4">System Health</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Protocol Status</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-green-400 font-bold">Operational</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Last Security Check</span>
                  <span className="text-gray-300">2 minutes ago</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-400">Uptime</span>
                  <span className="text-green-400 font-bold">99.9%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Events Tab */}
      {activeTab === 'events' && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-black text-white">Recent Security Events</h3>
            <button
              onClick={loadSecurityEvents}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all"
            >
              Refresh
            </button>
          </div>

          {securityEvents.length === 0 ? (
            <div className="text-center py-12">
              <Shield className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">No security events</p>
              <p className="text-gray-500 text-sm">All systems operating normally</p>
            </div>
          ) : (
            <div className="space-y-4">
              {securityEvents.map((event) => (
                <div key={event.id} className="bg-gray-800 rounded-2xl p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getSeverityColor(event.severity)}`}>
                        {event.type === 'MEV_PROTECTION' && <Shield className="w-4 h-4" />}
                        {event.type === 'RATE_LIMIT' && <Clock className="w-4 h-4" />}
                        {event.type === 'CIRCUIT_BREAKER' && <Pause className="w-4 h-4" />}
                        {event.type === 'EMERGENCY' && <AlertTriangle className="w-4 h-4" />}
                        {event.type === 'GUARDIAN_ACTION' && <Users className="w-4 h-4" />}
                      </div>
                      <div>
                        <div className="text-white font-medium">{event.message}</div>
                        <div className="text-gray-400 text-sm">
                          {new Date(event.timestamp).toLocaleString()}
                        </div>
                        {event.user && (
                          <div className="text-gray-500 text-sm">User: {event.user}</div>
                        )}
                      </div>
                    </div>
                    <div className={`px-2 py-1 rounded-lg text-xs font-medium ${getSeverityColor(event.severity)}`}>
                      {event.severity}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Settings Tab */}
      {activeTab === 'settings' && (
        <div className="space-y-6">
          {!isAdmin ? (
            <div className="bg-gray-900 rounded-3xl p-12 text-center">
              <Lock className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-black text-white mb-2">Admin Access Required</h3>
              <p className="text-gray-400">Only administrators can modify security settings</p>
            </div>
          ) : (
            <>
              {/* Emergency Controls */}
              <div className="bg-gray-900 rounded-3xl p-6">
                <h3 className="text-lg font-black text-white mb-6">Emergency Controls</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium">Emergency Mode</div>
                      <div className="text-gray-400 text-sm">Pause all protocol operations</div>
                    </div>
                    <button
                      onClick={handleEmergencyToggle}
                      disabled={isLoading}
                      className={`px-6 py-3 rounded-xl font-bold transition-all ${
                        securityStatus.isEmergencyActive
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'bg-red-600 hover:bg-red-700 text-white'
                      }`}
                    >
                      {isLoading && <Loader2 className="w-4 h-4 animate-spin inline mr-2" />}
                      {securityStatus.isEmergencyActive ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium">Circuit Breaker</div>
                      <div className="text-gray-400 text-sm">Automatic trading halt on anomalies</div>
                    </div>
                    <button
                      onClick={handleCircuitBreakerToggle}
                      disabled={isLoading}
                      className={`px-6 py-3 rounded-xl font-bold transition-all ${
                        securityStatus.circuitBreakerTriggered
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'bg-red-600 hover:bg-red-700 text-white'
                      }`}
                    >
                      {securityStatus.circuitBreakerTriggered ? 'Reset' : 'Trigger'}
                    </button>
                  </div>
                </div>
              </div>

              {/* Protection Settings */}
              <div className="bg-gray-900 rounded-3xl p-6">
                <h3 className="text-lg font-black text-white mb-6">Protection Settings</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium">MEV Protection</div>
                      <div className="text-gray-400 text-sm">Protect against MEV attacks</div>
                    </div>
                    <button
                      onClick={handleMEVProtectionToggle}
                      disabled={isLoading}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        securityStatus.mevProtectionEnabled ? 'bg-blue-600' : 'bg-gray-600'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          securityStatus.mevProtectionEnabled ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium">Rate Limiting</div>
                      <div className="text-gray-400 text-sm">Limit transaction frequency per user</div>
                    </div>
                    <button
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        securityStatus.rateLimitEnabled ? 'bg-blue-600' : 'bg-gray-600'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          securityStatus.rateLimitEnabled ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {/* Guardians Tab */}
      {activeTab === 'guardians' && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-black text-white">Guardian Management</h3>
            {isAdmin && (
              <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all">
                Add Guardian
              </button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-800 rounded-2xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-white font-medium">Guardian 1</div>
                  <div className="text-gray-400 text-sm">0x1234...5678</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-2xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-white font-medium">Guardian 2</div>
                  <div className="text-gray-400 text-sm">0x9876...4321</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-2xl p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="text-white font-medium">Guardian 3</div>
                  <div className="text-gray-400 text-sm">0xabcd...efgh</div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 bg-gray-800 rounded-2xl p-4">
            <div className="text-white font-medium mb-2">Guardian Requirements</div>
            <div className="text-gray-400 text-sm space-y-1">
              <div>• Minimum {securityStatus.requiredGuardians} guardians required for emergency actions</div>
              <div>• Current active guardians: {securityStatus.guardianCount}</div>
              <div>• Guardians can pause protocol in emergency situations</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityInterface;
