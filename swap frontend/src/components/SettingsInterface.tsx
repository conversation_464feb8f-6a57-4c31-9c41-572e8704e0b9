import React, { useState, useEffect } from 'react';
import { 
  <PERSON>ting<PERSON>, 
  <PERSON>lide<PERSON>, 
  <PERSON>, 
  <PERSON>, 
  Bell,
  Moon,
  Sun,
  Globe,
  Zap,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Upload,
  Trash2
} from 'lucide-react';
import { useWeb3 } from '../hooks/useWeb3';

interface UserSettings {
  slippageTolerance: number;
  transactionDeadline: number;
  gasPrice: 'slow' | 'standard' | 'fast' | 'custom';
  customGasPrice: number;
  autoSlippage: boolean;
  expertMode: boolean;
  darkMode: boolean;
  notifications: {
    transactions: boolean;
    priceAlerts: boolean;
    governance: boolean;
    farming: boolean;
  };
  language: string;
  currency: string;
  rpcEndpoint: string;
  customTokens: Array<{
    address: string;
    symbol: string;
    name: string;
    decimals: number;
  }>;
}

const SettingsInterface: React.FC = () => {
  const { account, network, isConnected } = useWeb3();
  const [settings, setSettings] = useState<UserSettings>({
    slippageTolerance: 0.5,
    transactionDeadline: 20,
    gasPrice: 'standard',
    customGasPrice: 20,
    autoSlippage: true,
    expertMode: false,
    darkMode: true,
    notifications: {
      transactions: true,
      priceAlerts: false,
      governance: true,
      farming: true
    },
    language: 'en',
    currency: 'USD',
    rpcEndpoint: '',
    customTokens: []
  });
  const [activeTab, setActiveTab] = useState<'trading' | 'interface' | 'notifications' | 'advanced'>('trading');
  const [isLoading, setIsLoading] = useState(false);
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  useEffect(() => {
    loadSettings();
  }, [account]);

  const loadSettings = async () => {
    try {
      // Load settings from localStorage or backend
      const savedSettings = localStorage.getItem(`settings_${account}`);
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const saveSettings = async (newSettings: UserSettings) => {
    try {
      setSettings(newSettings);
      localStorage.setItem(`settings_${account}`, JSON.stringify(newSettings));
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  const handleSlippageChange = (value: number) => {
    saveSettings({ ...settings, slippageTolerance: value });
  };

  const handleDeadlineChange = (value: number) => {
    saveSettings({ ...settings, transactionDeadline: value });
  };

  const handleGasPriceChange = (type: 'slow' | 'standard' | 'fast' | 'custom') => {
    saveSettings({ ...settings, gasPrice: type });
  };

  const handleToggleSetting = (key: keyof UserSettings, value?: any) => {
    if (typeof settings[key] === 'boolean') {
      saveSettings({ ...settings, [key]: !settings[key] });
    } else {
      saveSettings({ ...settings, [key]: value });
    }
  };

  const handleNotificationToggle = (type: keyof UserSettings['notifications']) => {
    saveSettings({
      ...settings,
      notifications: {
        ...settings.notifications,
        [type]: !settings.notifications[type]
      }
    });
  };

  const handleResetSettings = () => {
    const defaultSettings: UserSettings = {
      slippageTolerance: 0.5,
      transactionDeadline: 20,
      gasPrice: 'standard',
      customGasPrice: 20,
      autoSlippage: true,
      expertMode: false,
      darkMode: true,
      notifications: {
        transactions: true,
        priceAlerts: false,
        governance: true,
        farming: true
      },
      language: 'en',
      currency: 'USD',
      rpcEndpoint: '',
      customTokens: []
    };
    saveSettings(defaultSettings);
    setShowResetConfirm(false);
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `dex-settings-${account?.slice(0, 6)}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedSettings = JSON.parse(e.target?.result as string);
        saveSettings(importedSettings);
      } catch (error) {
        console.error('Error importing settings:', error);
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-900 rounded-3xl p-6 mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-r from-gray-500 to-gray-700 rounded-full flex items-center justify-center">
            <Settings className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-black text-white tracking-wider">Settings</h1>
            <p className="text-gray-400">Customize your trading experience</p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-gray-800 rounded-2xl p-1 mb-6">
        {[
          { id: 'trading', label: 'Trading', icon: Sliders },
          { id: 'interface', label: 'Interface', icon: Moon },
          { id: 'notifications', label: 'Notifications', icon: Bell },
          { id: 'advanced', label: 'Advanced', icon: Shield }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl transition-all ${
              activeTab === id
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            <Icon className="w-4 h-4" />
            <span className="font-normal tracking-wide">{label}</span>
          </button>
        ))}
      </div>

      {/* Trading Settings */}
      {activeTab === 'trading' && (
        <div className="space-y-6">
          <div className="bg-gray-900 rounded-3xl p-6">
            <h3 className="text-lg font-black text-white mb-6">Trading Preferences</h3>
            
            <div className="space-y-6">
              {/* Slippage Tolerance */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="text-white font-medium">Slippage Tolerance</label>
                  <span className="text-gray-400 text-sm">{settings.slippageTolerance}%</span>
                </div>
                <div className="flex space-x-2 mb-3">
                  {[0.1, 0.5, 1.0].map((value) => (
                    <button
                      key={value}
                      onClick={() => handleSlippageChange(value)}
                      className={`px-4 py-2 rounded-xl transition-all ${
                        settings.slippageTolerance === value
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-800 text-gray-400 hover:text-gray-300'
                      }`}
                    >
                      {value}%
                    </button>
                  ))}
                  <input
                    type="number"
                    step="0.1"
                    min="0.1"
                    max="50"
                    value={settings.slippageTolerance}
                    onChange={(e) => handleSlippageChange(parseFloat(e.target.value) || 0.5)}
                    className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-xl text-white text-center w-20"
                  />
                </div>
                <p className="text-gray-500 text-sm">
                  Your transaction will revert if the price changes unfavorably by more than this percentage.
                </p>
              </div>

              {/* Transaction Deadline */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="text-white font-medium">Transaction Deadline</label>
                  <span className="text-gray-400 text-sm">{settings.transactionDeadline} minutes</span>
                </div>
                <input
                  type="range"
                  min="1"
                  max="60"
                  value={settings.transactionDeadline}
                  onChange={(e) => handleDeadlineChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <p className="text-gray-500 text-sm mt-2">
                  Your transaction will revert if it is pending for more than this long.
                </p>
              </div>

              {/* Auto Slippage */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-white font-medium">Auto Slippage</div>
                  <div className="text-gray-400 text-sm">Automatically adjust slippage based on market conditions</div>
                </div>
                <button
                  onClick={() => handleToggleSetting('autoSlippage')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.autoSlippage ? 'bg-blue-600' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.autoSlippage ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Expert Mode */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-white font-medium flex items-center space-x-2">
                    <span>Expert Mode</span>
                    <AlertTriangle className="w-4 h-4 text-yellow-400" />
                  </div>
                  <div className="text-gray-400 text-sm">Allow high price impact trades and skip confirmation</div>
                </div>
                <button
                  onClick={() => handleToggleSetting('expertMode')}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.expertMode ? 'bg-red-600' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.expertMode ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Gas Settings */}
          <div className="bg-gray-900 rounded-3xl p-6">
            <h3 className="text-lg font-black text-white mb-6">Gas Settings</h3>
            
            <div className="space-y-4">
              <div className="grid grid-cols-4 gap-2">
                {[
                  { key: 'slow', label: 'Slow', desc: '~30s' },
                  { key: 'standard', label: 'Standard', desc: '~15s' },
                  { key: 'fast', label: 'Fast', desc: '~5s' },
                  { key: 'custom', label: 'Custom', desc: 'Set your own' }
                ].map(({ key, label, desc }) => (
                  <button
                    key={key}
                    onClick={() => handleGasPriceChange(key as any)}
                    className={`p-3 rounded-xl transition-all text-center ${
                      settings.gasPrice === key
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-800 text-gray-400 hover:text-gray-300'
                    }`}
                  >
                    <div className="font-medium">{label}</div>
                    <div className="text-xs opacity-75">{desc}</div>
                  </button>
                ))}
              </div>

              {settings.gasPrice === 'custom' && (
                <div>
                  <label className="block text-gray-400 text-sm mb-2">Custom Gas Price (Gwei)</label>
                  <input
                    type="number"
                    value={settings.customGasPrice}
                    onChange={(e) => saveSettings({ ...settings, customGasPrice: parseInt(e.target.value) || 20 })}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Interface Settings */}
      {activeTab === 'interface' && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <h3 className="text-lg font-black text-white mb-6">Interface Preferences</h3>
          
          <div className="space-y-6">
            {/* Dark Mode */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {settings.darkMode ? <Moon className="w-5 h-5 text-blue-400" /> : <Sun className="w-5 h-5 text-yellow-400" />}
                <div>
                  <div className="text-white font-medium">Dark Mode</div>
                  <div className="text-gray-400 text-sm">Use dark theme for better night viewing</div>
                </div>
              </div>
              <button
                onClick={() => handleToggleSetting('darkMode')}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.darkMode ? 'bg-blue-600' : 'bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.darkMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Language */}
            <div>
              <label className="block text-white font-medium mb-3">Language</label>
              <select
                value={settings.language}
                onChange={(e) => handleToggleSetting('language', e.target.value)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white"
              >
                <option value="en">English</option>
                <option value="tr">Türkçe</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
                <option value="de">Deutsch</option>
                <option value="zh">中文</option>
                <option value="ja">日本語</option>
              </select>
            </div>

            {/* Currency */}
            <div>
              <label className="block text-white font-medium mb-3">Display Currency</label>
              <select
                value={settings.currency}
                onChange={(e) => handleToggleSetting('currency', e.target.value)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white"
              >
                <option value="USD">USD ($)</option>
                <option value="EUR">EUR (€)</option>
                <option value="GBP">GBP (£)</option>
                <option value="JPY">JPY (¥)</option>
                <option value="TRY">TRY (₺)</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Notifications */}
      {activeTab === 'notifications' && (
        <div className="bg-gray-900 rounded-3xl p-6">
          <h3 className="text-lg font-black text-white mb-6">Notification Preferences</h3>
          
          <div className="space-y-6">
            {Object.entries(settings.notifications).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between">
                <div>
                  <div className="text-white font-medium capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {key === 'transactions' && 'Get notified about transaction status'}
                    {key === 'priceAlerts' && 'Receive alerts for significant price changes'}
                    {key === 'governance' && 'Updates about governance proposals and voting'}
                    {key === 'farming' && 'Notifications about farming rewards and opportunities'}
                  </div>
                </div>
                <button
                  onClick={() => handleNotificationToggle(key as keyof UserSettings['notifications'])}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    value ? 'bg-blue-600' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      value ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Advanced Settings */}
      {activeTab === 'advanced' && (
        <div className="space-y-6">
          <div className="bg-gray-900 rounded-3xl p-6">
            <h3 className="text-lg font-black text-white mb-6">Advanced Settings</h3>
            
            <div className="space-y-6">
              {/* Custom RPC */}
              <div>
                <label className="block text-white font-medium mb-3">Custom RPC Endpoint</label>
                <input
                  type="url"
                  placeholder="https://your-rpc-endpoint.com"
                  value={settings.rpcEndpoint}
                  onChange={(e) => handleToggleSetting('rpcEndpoint', e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400"
                />
                <p className="text-gray-500 text-sm mt-2">
                  Use a custom RPC endpoint for better performance or privacy
                </p>
              </div>
            </div>
          </div>

          {/* Data Management */}
          <div className="bg-gray-900 rounded-3xl p-6">
            <h3 className="text-lg font-black text-white mb-6">Data Management</h3>
            
            <div className="space-y-4">
              <div className="flex space-x-4">
                <button
                  onClick={exportSettings}
                  className="flex-1 flex items-center justify-center space-x-2 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all"
                >
                  <Download className="w-4 h-4" />
                  <span>Export Settings</span>
                </button>
                
                <label className="flex-1 flex items-center justify-center space-x-2 py-3 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-all cursor-pointer">
                  <Upload className="w-4 h-4" />
                  <span>Import Settings</span>
                  <input
                    type="file"
                    accept=".json"
                    onChange={importSettings}
                    className="hidden"
                  />
                </label>
              </div>

              <button
                onClick={() => setShowResetConfirm(true)}
                className="w-full flex items-center justify-center space-x-2 py-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all"
              >
                <Trash2 className="w-4 h-4" />
                <span>Reset to Defaults</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reset Confirmation Modal */}
      {showResetConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-3xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-red-400" />
              <h3 className="text-xl font-black text-white">Reset Settings</h3>
            </div>
            <p className="text-gray-400 mb-6">
              Are you sure you want to reset all settings to their default values? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowResetConfirm(false)}
                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-xl transition-all"
              >
                Cancel
              </button>
              <button
                onClick={handleResetSettings}
                className="flex-1 py-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all"
              >
                Reset
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsInterface;
