import React, { useState } from 'react';
import { ChevronDown, ArrowUpDown, Settings } from 'lucide-react';
import TokenSelector from './TokenSelector';

interface Token {
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  logoURI: string;
  balance?: string;
}

const defaultTokens = {
  from: {
    symbol: 'ETH',
    name: 'Ethereum',
    address: '******************************************',
    decimals: 18,
    logoURI: 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png',
    balance: '1.2345'
  },
  to: {
    symbol: 'USDC',
    name: 'USD Coin',
    address: '******************************************',
    decimals: 6,
    logoURI: 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png',
    balance: '1,234.56'
  }
};

const SwapInterface: React.FC = () => {
  const [fromToken, setFromToken] = useState<Token>(defaultTokens.from);
  const [toToken, setToToken] = useState<Token>(defaultTokens.to);
  const [fromAmount, setFromAmount] = useState('');
  const [toAmount, setToAmount] = useState('');
  const [isFromTokenSelectorOpen, setIsFromTokenSelectorOpen] = useState(false);
  const [isToTokenSelectorOpen, setIsToTokenSelectorOpen] = useState(false);
  const [slippage, setSlippage] = useState('0.5');

  const handleSwapTokens = () => {
    const tempToken = fromToken;
    setFromToken(toToken);
    setToToken(tempToken);
    
    const tempAmount = fromAmount;
    setFromAmount(toAmount);
    setToAmount(tempAmount);
  };

  const handleFromAmountChange = (value: string) => {
    setFromAmount(value);
    // Mock exchange rate calculation
    if (value) {
      const exchangeRate = 2450; // ETH to USDC
      const calculatedAmount = (parseFloat(value) * exchangeRate).toFixed(2);
      setToAmount(calculatedAmount);
    } else {
      setToAmount('');
    }
  };

  const handleMaxClick = () => {
    const maxAmount = fromToken.balance || '0';
    setFromAmount(maxAmount);
    handleFromAmountChange(maxAmount);
  };

  return (
    <div className="w-full max-w-lg mx-auto bg-gray-900 rounded-3xl border border-gray-700 shadow-2xl">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-normal text-white tracking-wider">Swap</h2>
          <button className="p-2 hover:bg-gray-800 rounded-full transition-colors">
            <Settings className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        <div className="space-y-4">
          {/* From Token Section */}
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-gray-400 text-sm font-normal tracking-wide">From</span>
              <span className="text-gray-400 text-sm">
                Balance: {fromToken.balance || '0.00'}
              </span>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsFromTokenSelectorOpen(true)}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-xl hover:bg-gray-600 transition-colors"
              >
                <img
                  src={fromToken.logoURI}
                  alt={fromToken.symbol}
                  className="w-6 h-6 rounded-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://via.placeholder.com/24x24/6366f1/ffffff?text=${fromToken.symbol}`;
                  }}
                />
                <span className="text-white font-normal tracking-wide">{fromToken.symbol}</span>
                <ChevronDown className="w-4 h-4 text-gray-400" />
              </button>
              
              <div className="flex-1 relative pr-16">
                <input
                  type="number"
                  value={fromAmount}
                  onChange={(e) => handleFromAmountChange(e.target.value)}
                  placeholder="0.0"
                  className="w-full text-right text-2xl font-normal text-white bg-transparent border-none outline-none placeholder-gray-500 tracking-wide pr-2"
                />
                <button
                  onClick={handleMaxClick}
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 px-2 py-1 text-xs font-normal text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded transition-all tracking-wider"
                >
                  MAX
                </button>
              </div>
            </div>
          </div>

          {/* Swap Button */}
          <div className="flex justify-center">
            <button
              onClick={handleSwapTokens}
              className="p-3 bg-gray-700 hover:bg-gray-600 rounded-xl transition-colors"
            >
              <ArrowUpDown className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          {/* To Token Section */}
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-gray-400 text-sm font-normal tracking-wide">To</span>
              <span className="text-gray-400 text-sm">
                Balance: {toToken.balance || '0.00'}
              </span>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsToTokenSelectorOpen(true)}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-xl hover:bg-gray-600 transition-colors"
              >
                <img
                  src={toToken.logoURI}
                  alt={toToken.symbol}
                  className="w-6 h-6 rounded-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://via.placeholder.com/24x24/6366f1/ffffff?text=${toToken.symbol}`;
                  }}
                />
                <span className="text-white font-normal tracking-wide">{toToken.symbol}</span>
                <ChevronDown className="w-4 h-4 text-gray-400" />
              </button>
              
              <div className="flex-1">
                <input
                  type="number"
                  value={toAmount}
                  readOnly
                  placeholder="0.0"
                  className="w-full text-right text-2xl font-normal text-white bg-transparent border-none outline-none placeholder-gray-500 tracking-wide"
                />
              </div>
            </div>
          </div>

          {/* Price Info */}
          {fromAmount && toAmount && (
            <div className="bg-gray-800 rounded-2xl p-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400 font-normal tracking-wide">Exchange Rate</span>
                <span className="text-white font-normal tracking-wide">1 {fromToken.symbol} = 2,450 {toToken.symbol}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400 font-normal tracking-wide">Price Impact</span>
                <span className="text-green-400 font-normal tracking-wide">0.05%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400 font-normal tracking-wide">Maximum Slippage</span>
                <span className="text-white font-normal tracking-wide">{slippage}%</span>
              </div>
            </div>
          )}

          {/* Swap Button */}
          <button
            disabled={!fromAmount || !toAmount}
            className={`w-full py-4 rounded-2xl font-black text-lg transition-all tracking-wider ${
              fromAmount && toAmount
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-400 hover:to-purple-500 text-white shadow-lg transform hover:scale-105'
                : 'bg-gray-700 text-gray-400 cursor-not-allowed'
            }`}
          >
            {fromAmount && toAmount ? 'Swap' : 'Enter Amount'}
          </button>
        </div>
      </div>

      {/* Token Selectors */}
      <TokenSelector
        isOpen={isFromTokenSelectorOpen}
        onClose={() => setIsFromTokenSelectorOpen(false)}
        onSelectToken={setFromToken}
        selectedToken={fromToken}
      />
      
      <TokenSelector
        isOpen={isToTokenSelectorOpen}
        onClose={() => setIsToTokenSelectorOpen(false)}
        onSelectToken={setToToken}
        selectedToken={toToken}
      />
    </div>
  );
};

export default SwapInterface;