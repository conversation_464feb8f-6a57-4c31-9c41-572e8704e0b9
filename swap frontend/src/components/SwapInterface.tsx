import React, { useState, useEffect } from 'react';
import { ChevronDown, ArrowUpDown, Settings, AlertTriangle, Loader2, ExternalLink, Zap } from 'lucide-react';
import TokenSelector from './TokenSelector';
import { useSwap } from '../hooks/useSwap';
import { useWeb3 } from '../hooks/useWeb3';
import { TokenInfo } from '../utils/SwapSDK';

const SwapInterface: React.FC = () => {
  const { isConnected, network } = useWeb3();
  const {
    fromToken,
    toToken,
    fromAmount,
    toAmount,
    quote,
    isLoading,
    isSwapping,
    error,
    slippage,
    supportedTokens,
    canSwap,
    maxBalance,
    isTestnet,
    setFromToken,
    setToToken,
    swapTokens,
    setFromAmount,
    setSlippage,
    setMaxAmount,
    executeSwap,
    requestTestTokens,
    refreshBalances,
  } = useSwap();

  const [isFromTokenSelectorOpen, setIsFromTokenSelectorOpen] = useState(false);
  const [isToTokenSelectorOpen, setIsToTokenSelectorOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [customSlippage, setCustomSlippage] = useState('');
  const [showQuoteDetails, setShowQuoteDetails] = useState(false);
  const [txHash, setTxHash] = useState<string | null>(null);

  // Update quote when amount changes
  useEffect(() => {
    if (fromAmount && fromToken && toToken) {
      setFromAmount(fromAmount);
    }
  }, [fromAmount, fromToken, toToken, setFromAmount]);

  const handleFromAmountChange = (value: string) => {
    // Only allow numbers and decimal point
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setFromAmount(value);
    }
  };

  const handleSlippageChange = (value: string) => {
    if (value === 'custom') {
      setCustomSlippage('');
    } else {
      setSlippage(parseFloat(value));
      setCustomSlippage('');
    }
  };

  const handleCustomSlippageChange = (value: string) => {
    setCustomSlippage(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue >= 0 && numValue <= 50) {
      setSlippage(numValue);
    }
  };

  const handleSwap = async () => {
    try {
      const success = await executeSwap();
      if (success) {
        setTxHash('success'); // In real implementation, get actual tx hash
        setShowQuoteDetails(false);
      }
    } catch (error) {
      console.error('Swap failed:', error);
    }
  };

  const handleRequestTestTokens = async (token: TokenInfo) => {
    try {
      await requestTestTokens(token.address, '1000');
    } catch (error) {
      console.error('Failed to request test tokens:', error);
    }
  };

  const formatBalance = (balance: string | undefined) => {
    if (!balance) return '0';
    const num = parseFloat(balance);
    if (num === 0) return '0';
    if (num < 0.001) return '<0.001';
    if (num < 1) return num.toFixed(6);
    if (num < 1000) return num.toFixed(4);
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };

  const getPriceImpactColor = (impact: string) => {
    const num = parseFloat(impact);
    if (num < 1) return 'text-green-400';
    if (num < 3) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getSwapButtonText = () => {
    if (!isConnected) return 'Connect Wallet';
    if (!fromToken || !toToken) return 'Select Tokens';
    if (!fromAmount || parseFloat(fromAmount) === 0) return 'Enter Amount';
    if (parseFloat(fromAmount) > parseFloat(maxBalance)) return 'Insufficient Balance';
    if (isLoading) return 'Getting Quote...';
    if (isSwapping) return 'Swapping...';
    return 'Swap';
  };

  const isSwapDisabled = () => {
    return !canSwap || isLoading || isSwapping;
  };



  return (
    <div className="w-full max-w-lg mx-auto bg-gray-900 rounded-3xl border border-gray-700 shadow-2xl">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-normal text-white tracking-wider">Swap</h2>
          <div className="flex items-center space-x-2">
            {isTestnet && (
              <span className="px-2 py-1 bg-yellow-600 text-yellow-100 text-xs rounded-full">
                Testnet
              </span>
            )}
            <button
              onClick={() => setIsSettingsOpen(!isSettingsOpen)}
              className="p-2 hover:bg-gray-800 rounded-full transition-colors"
            >
              <Settings className="w-5 h-5 text-gray-400" />
            </button>
          </div>
        </div>

        {/* Settings Panel */}
        {isSettingsOpen && (
          <div className="mb-6 bg-gray-800 rounded-2xl p-4">
            <h3 className="text-white font-medium mb-3">Slippage Tolerance</h3>
            <div className="flex items-center space-x-2 mb-3">
              {['0.1', '0.5', '1.0'].map((value) => (
                <button
                  key={value}
                  onClick={() => handleSlippageChange(value)}
                  className={`px-3 py-2 rounded-lg text-sm transition-all ${
                    slippage === parseFloat(value)
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {value}%
                </button>
              ))}
              <button
                onClick={() => handleSlippageChange('custom')}
                className={`px-3 py-2 rounded-lg text-sm transition-all ${
                  customSlippage
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Custom
              </button>
            </div>
            {customSlippage !== '' && (
              <input
                type="number"
                value={customSlippage}
                onChange={(e) => handleCustomSlippageChange(e.target.value)}
                placeholder="0.50"
                className="w-full px-3 py-2 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 outline-none"
              />
            )}
          </div>
        )}

        <div className="space-y-4">
          {/* From Token Section */}
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-gray-400 text-sm font-normal tracking-wide">From</span>
              <div className="flex items-center space-x-2">
                {fromToken && (
                  <span className="text-gray-400 text-sm">
                    Balance: {formatBalance(fromToken.balance)}
                  </span>
                )}
                {isTestnet && fromToken && (
                  <button
                    onClick={() => handleRequestTestTokens(fromToken)}
                    className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-all"
                  >
                    <Zap className="w-3 h-3 inline mr-1" />
                    Faucet
                  </button>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsFromTokenSelectorOpen(true)}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-xl hover:bg-gray-600 transition-colors"
              >
                {fromToken ? (
                  <>
                    <img
                      src={fromToken.logoURI}
                      alt={fromToken.symbol}
                      className="w-6 h-6 rounded-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://via.placeholder.com/24x24/6366f1/ffffff?text=${fromToken.symbol}`;
                      }}
                    />
                    <span className="text-white font-normal tracking-wide">{fromToken.symbol}</span>
                  </>
                ) : (
                  <span className="text-gray-400 font-normal tracking-wide">Select Token</span>
                )}
                <ChevronDown className="w-4 h-4 text-gray-400" />
              </button>

              <div className="flex-1 relative pr-16">
                <input
                  type="text"
                  value={fromAmount}
                  onChange={(e) => handleFromAmountChange(e.target.value)}
                  placeholder="0.0"
                  className="w-full text-right text-2xl font-normal text-white bg-transparent border-none outline-none placeholder-gray-500 tracking-wide pr-2"
                />
                <button
                  onClick={setMaxAmount}
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 px-2 py-1 text-xs font-normal text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 rounded transition-all tracking-wider"
                >
                  MAX
                </button>
              </div>
            </div>
          </div>

          {/* Swap Button */}
          <div className="flex justify-center">
            <button
              onClick={swapTokens}
              className="p-3 bg-gray-700 hover:bg-gray-600 rounded-xl transition-colors"
            >
              <ArrowUpDown className="w-5 h-5 text-gray-400" />
            </button>
          </div>

          {/* To Token Section */}
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-gray-400 text-sm font-normal tracking-wide">To</span>
              <div className="flex items-center space-x-2">
                {toToken && (
                  <span className="text-gray-400 text-sm">
                    Balance: {formatBalance(toToken.balance)}
                  </span>
                )}
                {isTestnet && toToken && (
                  <button
                    onClick={() => handleRequestTestTokens(toToken)}
                    className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-all"
                  >
                    <Zap className="w-3 h-3 inline mr-1" />
                    Faucet
                  </button>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsToTokenSelectorOpen(true)}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-xl hover:bg-gray-600 transition-colors"
              >
                {toToken ? (
                  <>
                    <img
                      src={toToken.logoURI}
                      alt={toToken.symbol}
                      className="w-6 h-6 rounded-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://via.placeholder.com/24x24/6366f1/ffffff?text=${toToken.symbol}`;
                      }}
                    />
                    <span className="text-white font-normal tracking-wide">{toToken.symbol}</span>
                  </>
                ) : (
                  <span className="text-gray-400 font-normal tracking-wide">Select Token</span>
                )}
                <ChevronDown className="w-4 h-4 text-gray-400" />
              </button>

              <div className="flex-1 relative">
                <input
                  type="text"
                  value={toAmount}
                  readOnly
                  placeholder="0.0"
                  className="w-full text-right text-2xl font-normal text-white bg-transparent border-none outline-none placeholder-gray-500 tracking-wide"
                />
                {isLoading && (
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Quote Details */}
          {quote && fromToken && toToken && (
            <div className="bg-gray-800 rounded-2xl p-4 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm font-normal tracking-wide">Quote Details</span>
                <button
                  onClick={() => setShowQuoteDetails(!showQuoteDetails)}
                  className="text-blue-400 hover:text-blue-300 text-sm"
                >
                  {showQuoteDetails ? 'Hide' : 'Show'}
                </button>
              </div>

              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400 font-normal tracking-wide">Exchange Rate</span>
                <span className="text-white font-normal tracking-wide">
                  1 {fromToken.symbol} = {(parseFloat(toAmount) / parseFloat(fromAmount)).toFixed(6)} {toToken.symbol}
                </span>
              </div>

              {showQuoteDetails && (
                <>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400 font-normal tracking-wide">Price Impact</span>
                    <span className={`font-normal tracking-wide ${getPriceImpactColor(quote.priceImpact)}`}>
                      {quote.priceImpact}%
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400 font-normal tracking-wide">Platform Fee</span>
                    <span className="text-white font-normal tracking-wide">
                      {quote.platformFee} {fromToken.symbol}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400 font-normal tracking-wide">Minimum Received</span>
                    <span className="text-white font-normal tracking-wide">
                      {quote.minimumReceived} {toToken.symbol}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400 font-normal tracking-wide">Slippage Tolerance</span>
                    <span className="text-white font-normal tracking-wide">{slippage}%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400 font-normal tracking-wide">Gas Estimate</span>
                    <span className="text-white font-normal tracking-wide">
                      {parseInt(quote.gasEstimate).toLocaleString()}
                    </span>
                  </div>
                  {quote.path.length > 2 && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400 font-normal tracking-wide">Route</span>
                      <span className="text-white font-normal tracking-wide text-right">
                        {quote.path.map((address: string, index: number) => {
                          const token = supportedTokens.find((t: TokenInfo) => t.address === address);
                          return (
                            <span key={address}>
                              {token?.symbol || address.slice(0, 6)}
                              {index < quote.path.length - 1 && ' → '}
                            </span>
                          );
                        })}
                      </span>
                    </div>
                  )}
                </>
              )}
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-900 border border-red-700 rounded-2xl p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-red-400" />
                <span className="text-red-300 text-sm">{error}</span>
              </div>
            </div>
          )}

          {/* Success Message */}
          {txHash && (
            <div className="bg-green-900 border border-green-700 rounded-2xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                  <span className="text-green-300 text-sm">Swap Successful!</span>
                </div>
                {network && (
                  <button
                    onClick={() => window.open(`${network.blockExplorer}/tx/${txHash}`, '_blank')}
                    className="flex items-center space-x-1 text-green-400 hover:text-green-300 text-sm"
                  >
                    <ExternalLink className="w-4 h-4" />
                    <span>View</span>
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Swap Button */}
          <button
            onClick={handleSwap}
            disabled={isSwapDisabled()}
            className={`w-full py-4 rounded-2xl font-black text-lg transition-all tracking-wider ${
              canSwap && !isLoading && !isSwapping
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-400 hover:to-purple-500 text-white shadow-lg transform hover:scale-105'
                : 'bg-gray-700 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isSwapping && <Loader2 className="w-5 h-5 animate-spin inline mr-2" />}
            {getSwapButtonText()}
          </button>
        </div>
      </div>

      {/* Token Selectors */}
      <TokenSelector
        isOpen={isFromTokenSelectorOpen}
        onClose={() => setIsFromTokenSelectorOpen(false)}
        onSelectToken={setFromToken}
        selectedToken={fromToken}
        tokens={supportedTokens}
        isTestnet={isTestnet}
        onRequestTestTokens={handleRequestTestTokens}
        onRefreshBalances={refreshBalances}
      />

      <TokenSelector
        isOpen={isToTokenSelectorOpen}
        onClose={() => setIsToTokenSelectorOpen(false)}
        onSelectToken={setToToken}
        selectedToken={toToken}
        tokens={supportedTokens}
        isTestnet={isTestnet}
        onRequestTestTokens={handleRequestTestTokens}
        onRefreshBalances={refreshBalances}
      />
    </div>
  );
};

export default SwapInterface;