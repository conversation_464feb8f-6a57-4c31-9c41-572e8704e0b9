import React, { useState, useEffect } from 'react';
import { 
  Coins, 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  Search, 
  Filter,
  ExternalLink,
  AlertTriangle,
  Loader2,
  Star,
  TrendingUp
} from 'lucide-react';
import { useWeb3 } from '../hooks/useWeb3';
import { TokenInfo } from '../utils/SwapSDK';

interface TokenManagementProps {
  isAdmin?: boolean;
}

const TokenManagement: React.FC<TokenManagementProps> = ({ isAdmin = false }) => {
  const { account, network, isConnected } = useWeb3();
  const [tokens, setTokens] = useState<TokenInfo[]>([]);
  const [filteredTokens, setFilteredTokens] = useState<TokenInfo[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBy, setFilterBy] = useState<'all' | 'verified' | 'unverified' | 'active' | 'inactive'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'symbol' | 'marketCap' | 'balance'>('marketCap');
  const [isLoading, setIsLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingToken, setEditingToken] = useState<TokenInfo | null>(null);

  // New token form state
  const [newToken, setNewToken] = useState({
    address: '',
    symbol: '',
    name: '',
    decimals: 18,
    logoURI: '',
    website: '',
    marketCap: '',
    isActive: true,
    isVerified: false
  });

  useEffect(() => {
    loadTokens();
  }, []);

  useEffect(() => {
    filterTokens();
  }, [tokens, searchTerm, filterBy, sortBy]);

  const loadTokens = async () => {
    setIsLoading(true);
    try {
      // Implementation to load tokens from TokenRegistry
      console.log('Loading tokens...');
    } catch (error) {
      console.error('Error loading tokens:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterTokens = () => {
    let filtered = tokens.filter(token => {
      const matchesSearch = 
        token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.address.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesFilter = 
        filterBy === 'all' ||
        (filterBy === 'verified' && token.isVerified) ||
        (filterBy === 'unverified' && !token.isVerified) ||
        (filterBy === 'active' && token.isActive) ||
        (filterBy === 'inactive' && !token.isActive);

      return matchesSearch && matchesFilter;
    });

    // Sort tokens
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'symbol':
          return a.symbol.localeCompare(b.symbol);
        case 'marketCap':
          return parseFloat(b.marketCap) - parseFloat(a.marketCap);
        case 'balance':
          return parseFloat(b.balance || '0') - parseFloat(a.balance || '0');
        default:
          return 0;
      }
    });

    setFilteredTokens(filtered);
  };

  const handleAddToken = async () => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    try {
      // Implementation to add token to registry
      console.log('Adding token:', newToken);
      setShowAddModal(false);
      setNewToken({
        address: '',
        symbol: '',
        name: '',
        decimals: 18,
        logoURI: '',
        website: '',
        marketCap: '',
        isActive: true,
        isVerified: false
      });
      await loadTokens();
    } catch (error) {
      console.error('Error adding token:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateToken = async (token: TokenInfo) => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    try {
      // Implementation to update token
      console.log('Updating token:', token);
      setEditingToken(null);
      await loadTokens();
    } catch (error) {
      console.error('Error updating token:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (token: TokenInfo) => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    try {
      // Implementation to toggle token active status
      console.log('Toggling active status:', token);
      await loadTokens();
    } catch (error) {
      console.error('Error toggling token status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleVerified = async (token: TokenInfo) => {
    if (!isAdmin) return;
    
    setIsLoading(true);
    try {
      // Implementation to toggle token verified status
      console.log('Toggling verified status:', token);
      await loadTokens();
    } catch (error) {
      console.error('Error toggling verification:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatMarketCap = (marketCap: string) => {
    const value = parseFloat(marketCap);
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="bg-gray-900 rounded-3xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
              <Coins className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-black text-white tracking-wider">Token Management</h1>
              <p className="text-gray-400">Manage supported tokens and their properties</p>
            </div>
          </div>
          {isAdmin && (
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all"
            >
              <Plus className="w-4 h-4" />
              <span>Add Token</span>
            </button>
          )}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Coins className="w-5 h-5 text-blue-400" />
              <span className="text-gray-400 text-sm">Total Tokens</span>
            </div>
            <div className="text-2xl font-black text-white">{tokens.length}</div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-gray-400 text-sm">Verified</span>
            </div>
            <div className="text-2xl font-black text-green-400">
              {tokens.filter(t => t.isVerified).length}
            </div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Star className="w-5 h-5 text-yellow-400" />
              <span className="text-gray-400 text-sm">Active</span>
            </div>
            <div className="text-2xl font-black text-white">
              {tokens.filter(t => t.isActive).length}
            </div>
          </div>
          <div className="bg-gray-800 rounded-2xl p-4">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-5 h-5 text-purple-400" />
              <span className="text-gray-400 text-sm">Total Market Cap</span>
            </div>
            <div className="text-2xl font-black text-white">
              {formatMarketCap(tokens.reduce((sum, t) => sum + parseFloat(t.marketCap), 0).toString())}
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-gray-900 rounded-3xl p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search tokens..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value as any)}
                className="bg-gray-800 text-white rounded-xl px-3 py-2 text-sm border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Tokens</option>
                <option value="verified">Verified Only</option>
                <option value="unverified">Unverified Only</option>
                <option value="active">Active Only</option>
                <option value="inactive">Inactive Only</option>
              </select>
            </div>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="bg-gray-800 text-white rounded-xl px-3 py-2 text-sm border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="marketCap">Market Cap</option>
              <option value="name">Name</option>
              <option value="symbol">Symbol</option>
              <option value="balance">Balance</option>
            </select>
          </div>
        </div>
      </div>

      {/* Token List */}
      {isLoading ? (
        <div className="bg-gray-900 rounded-3xl p-12 text-center">
          <Loader2 className="w-12 h-12 text-blue-400 animate-spin mx-auto mb-4" />
          <p className="text-gray-400">Loading tokens...</p>
        </div>
      ) : filteredTokens.length === 0 ? (
        <div className="bg-gray-900 rounded-3xl p-12 text-center">
          <Coins className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-black text-white mb-2">No Tokens Found</h3>
          <p className="text-gray-400">
            {searchTerm ? 'Try adjusting your search criteria' : 'No tokens match the current filter'}
          </p>
        </div>
      ) : (
        <div className="bg-gray-900 rounded-3xl overflow-hidden">
          {/* Table Header */}
          <div className="bg-gray-800 px-6 py-4 grid grid-cols-12 gap-4 text-sm font-medium text-gray-400">
            <div className="col-span-4">Token</div>
            <div className="col-span-2">Market Cap</div>
            <div className="col-span-2">Balance</div>
            <div className="col-span-2">Status</div>
            <div className="col-span-2">Actions</div>
          </div>

          {/* Token Rows */}
          <div className="divide-y divide-gray-800">
            {filteredTokens.map((token) => (
              <div key={token.address} className="px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-800/50 transition-all">
                {/* Token Info */}
                <div className="col-span-4 flex items-center space-x-3">
                  <img
                    src={token.logoURI}
                    alt={token.symbol}
                    className="w-10 h-10 rounded-full"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://via.placeholder.com/40x40/6366f1/ffffff?text=${token.symbol}`;
                    }}
                  />
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="text-white font-medium">{token.symbol}</span>
                      {token.isVerified && (
                        <CheckCircle className="w-4 h-4 text-green-400" />
                      )}
                    </div>
                    <div className="text-gray-400 text-sm">{token.name}</div>
                  </div>
                </div>

                {/* Market Cap */}
                <div className="col-span-2">
                  <div className="text-white font-medium">{formatMarketCap(token.marketCap)}</div>
                </div>

                {/* Balance */}
                <div className="col-span-2">
                  <div className="text-white font-medium">
                    {token.balance ? parseFloat(token.balance).toLocaleString() : '0'}
                  </div>
                </div>

                {/* Status */}
                <div className="col-span-2">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${token.isActive ? 'bg-green-400' : 'bg-red-400'}`} />
                    <span className={`text-sm ${token.isActive ? 'text-green-400' : 'text-red-400'}`}>
                      {token.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="col-span-2 flex items-center space-x-2">
                  {network && (
                    <button
                      onClick={() => window.open(`${network.blockExplorer}/address/${token.address}`, '_blank')}
                      className="p-2 text-gray-400 hover:text-gray-300 transition-all"
                      title="View on Explorer"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </button>
                  )}
                  
                  {isAdmin && (
                    <>
                      <button
                        onClick={() => setEditingToken(token)}
                        className="p-2 text-blue-400 hover:text-blue-300 transition-all"
                        title="Edit Token"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => handleToggleActive(token)}
                        className={`p-2 transition-all ${
                          token.isActive 
                            ? 'text-red-400 hover:text-red-300' 
                            : 'text-green-400 hover:text-green-300'
                        }`}
                        title={token.isActive ? 'Deactivate' : 'Activate'}
                      >
                        {token.isActive ? <XCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                      </button>
                      
                      <button
                        onClick={() => handleToggleVerified(token)}
                        className={`p-2 transition-all ${
                          token.isVerified 
                            ? 'text-yellow-400 hover:text-yellow-300' 
                            : 'text-green-400 hover:text-green-300'
                        }`}
                        title={token.isVerified ? 'Unverify' : 'Verify'}
                      >
                        <Star className={`w-4 h-4 ${token.isVerified ? 'fill-current' : ''}`} />
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add Token Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-3xl p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-black text-white mb-6">Add New Token</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-400 text-sm mb-2">Contract Address</label>
                <input
                  type="text"
                  placeholder="0x..."
                  value={newToken.address}
                  onChange={(e) => setNewToken({...newToken, address: e.target.value})}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-400 text-sm mb-2">Symbol</label>
                  <input
                    type="text"
                    placeholder="ETH"
                    value={newToken.symbol}
                    onChange={(e) => setNewToken({...newToken, symbol: e.target.value})}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-400 text-sm mb-2">Decimals</label>
                  <input
                    type="number"
                    placeholder="18"
                    value={newToken.decimals}
                    onChange={(e) => setNewToken({...newToken, decimals: parseInt(e.target.value) || 18})}
                    className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-gray-400 text-sm mb-2">Name</label>
                <input
                  type="text"
                  placeholder="Ethereum"
                  value={newToken.name}
                  onChange={(e) => setNewToken({...newToken, name: e.target.value})}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-400 text-sm mb-2">Logo URI</label>
                <input
                  type="url"
                  placeholder="https://..."
                  value={newToken.logoURI}
                  onChange={(e) => setNewToken({...newToken, logoURI: e.target.value})}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-400 text-sm mb-2">Website</label>
                <input
                  type="url"
                  placeholder="https://..."
                  value={newToken.website}
                  onChange={(e) => setNewToken({...newToken, website: e.target.value})}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-400 text-sm mb-2">Market Cap (USD)</label>
                <input
                  type="number"
                  placeholder="1000000"
                  value={newToken.marketCap}
                  onChange={(e) => setNewToken({...newToken, marketCap: e.target.value})}
                  className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={newToken.isActive}
                    onChange={(e) => setNewToken({...newToken, isActive: e.target.checked})}
                    className="w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"
                  />
                  <span className="text-gray-400 text-sm">Active</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={newToken.isVerified}
                    onChange={(e) => setNewToken({...newToken, isVerified: e.target.checked})}
                    className="w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"
                  />
                  <span className="text-gray-400 text-sm">Verified</span>
                </label>
              </div>
            </div>
            
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowAddModal(false)}
                className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-2xl transition-all"
              >
                Cancel
              </button>
              <button
                onClick={handleAddToken}
                disabled={!newToken.address || !newToken.symbol || !newToken.name || isLoading}
                className="flex-1 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl transition-all disabled:opacity-50"
              >
                {isLoading && <Loader2 className="w-4 h-4 animate-spin inline mr-2" />}
                Add Token
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TokenManagement;
