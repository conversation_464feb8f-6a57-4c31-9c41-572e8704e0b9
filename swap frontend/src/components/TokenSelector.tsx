import React, { useState } from 'react';
import { Search, X } from 'lucide-react';

interface Token {
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  logoURI: string;
  balance?: string;
}

interface TokenSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectToken: (token: Token) => void;
  selectedToken?: Token;
}

const popularTokens: Token[] = [
  {
    symbol: 'ETH',
    name: 'Ethereum',
    address: '******************************************',
    decimals: 18,
    logoURI: 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png',
    balance: '1.2345'
  },
  {
    symbol: 'USDC',
    name: 'USD Coin',
    address: '******************************************',
    decimals: 6,
    logoURI: 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png',
    balance: '1,234.56'
  },
  {
    symbol: 'USDT',
    name: 'Tether',
    address: '******************************************',
    decimals: 6,
    logoURI: 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png',
    balance: '567.89'
  },
  {
    symbol: 'WBTC',
    name: 'Wrapped Bitcoin',
    address: '******************************************',
    decimals: 8,
    logoURI: 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png',
    balance: '0.0123'
  },
  {
    symbol: 'DAI',
    name: 'Dai Stablecoin',
    address: '******************************************',
    decimals: 18,
    logoURI: 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png',
    balance: '890.12'
  },
  {
    symbol: 'UNI',
    name: 'Uniswap',
    address: '******************************************',
    decimals: 18,
    logoURI: 'https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png',
    balance: '45.67'
  },
];

const TokenSelector: React.FC<TokenSelectorProps> = ({
  isOpen,
  onClose,
  onSelectToken,
  selectedToken
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTokens, setFilteredTokens] = useState(popularTokens);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    const filtered = popularTokens.filter(token =>
      token.symbol.toLowerCase().includes(term.toLowerCase()) ||
      token.name.toLowerCase().includes(term.toLowerCase())
    );
    setFilteredTokens(filtered);
  };

  const handleTokenSelect = (token: Token) => {
    onSelectToken(token);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="relative w-full max-w-md mx-4 bg-gray-900 rounded-3xl border border-gray-700 shadow-2xl">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-normal text-white tracking-wider">Select Token</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-800 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-400" />
            </button>
          </div>
          
          <div className="relative mb-6">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search tokens"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-12 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 font-normal tracking-wide"
            />
          </div>

          <div className="space-y-1 max-h-96 overflow-y-auto">
            {filteredTokens.map((token) => (
              <button
                key={token.address}
                onClick={() => handleTokenSelect(token)}
                className={`w-full flex items-center justify-between p-4 rounded-xl transition-all hover:bg-gray-800 ${
                  selectedToken?.address === token.address ? 'bg-gray-800' : ''
                }`}
              >
                <div className="flex items-center space-x-3">
                  <img
                    src={token.logoURI}
                    alt={token.symbol}
                    className="w-10 h-10 rounded-full"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://via.placeholder.com/40x40/6366f1/ffffff?text=${token.symbol}`;
                    }}
                  />
                  <div className="text-left">
                    <div className="text-white font-normal tracking-wide">{token.symbol}</div>
                    <div className="text-gray-400 text-sm font-normal tracking-wide">{token.name}</div>
                  </div>
                </div>
                {token.balance && (
                  <div className="text-right">
                    <div className="text-white font-normal tracking-wide">{token.balance}</div>
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TokenSelector;