import React, { useState, useMemo } from 'react';
import { Search, X, Zap, RefreshCw } from 'lucide-react';
import { TokenInfo } from '../utils/SwapSDK';

interface TokenSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectToken: (token: TokenInfo) => void;
  selectedToken?: TokenInfo | null;
  tokens: TokenInfo[];
  isTestnet?: boolean;
  onRequestTestTokens?: (token: TokenInfo) => Promise<void>;
  onRefreshBalances?: () => Promise<void>;
}

const TokenSelector: React.FC<TokenSelectorProps> = ({
  isOpen,
  onClose,
  onSelectToken,
  selectedToken,
  tokens,
  isTestnet = false,
  onRequestTestTokens,
  onRefreshBalances
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState<'all' | 'verified' | 'popular'>('all');

  // Default tokens for testing
  const defaultTokens: TokenInfo[] = isTestnet ? [
    {
      address: '******************************************',
      symbol: 'ETH',
      name: 'Sepolia ETH',
      decimals: 18,
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      balance: '0.0',
      isVerified: true,
      isActive: true,
      marketCap: '500000000000',
      website: 'https://ethereum.org'
    },
    {
      address: '******************************************',
      symbol: 'WETH',
      name: 'Wrapped Ethereum',
      decimals: 18,
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      balance: '0.0',
      isVerified: true,
      isActive: true,
      marketCap: '500000000000',
      website: 'https://weth.io'
    },
    {
      address: '******************************************',
      symbol: 'USDC',
      name: 'USD Coin (Sepolia)',
      decimals: 6,
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      balance: '0.0',
      isVerified: true,
      isActive: true,
      marketCap: '25000000000',
      website: 'https://centre.io'
    },
    {
      address: '******************************************',
      symbol: 'DAI',
      name: 'Dai Stablecoin (Sepolia)',
      decimals: 18,
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      balance: '0.0',
      isVerified: true,
      isActive: true,
      marketCap: '5000000000',
      website: 'https://makerdao.com'
    }
  ] : [
    {
      address: '******************************************',
      symbol: 'ETH',
      name: 'Ethereum',
      decimals: 18,
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      balance: '0.0',
      isVerified: true,
      isActive: true,
      marketCap: '500000000000',
      website: 'https://ethereum.org'
    },
    {
      address: '******************************************',
      symbol: 'WETH',
      name: 'Wrapped Ethereum',
      decimals: 18,
      logoURI: 'https://tokens.1inch.io/******************************************.png',
      balance: '0.0',
      isVerified: true,
      isActive: true,
      marketCap: '500000000000',
      website: 'https://weth.io'
    }
  ];

  // Use provided tokens or default tokens
  const availableTokens = tokens.length > 0 ? tokens : defaultTokens;

  console.log('🎯 TokenSelector - Available tokens:', availableTokens.map(t => `${t.symbol}: ${t.balance}`));
  console.log('🎯 TokenSelector - Provided tokens count:', tokens.length);
  console.log('🎯 TokenSelector - Default tokens count:', defaultTokens.length);

  // Filter tokens based on search and category
  const filteredTokens = useMemo(() => {
    let filtered = availableTokens;

    // Filter by category
    if (category === 'verified') {
      filtered = filtered.filter(token => token.isVerified);
    } else if (category === 'popular') {
      filtered = filtered.filter(token => parseFloat(token.marketCap) > 1000000000); // > 1B market cap
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(token =>
        token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.address.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [availableTokens, category, searchTerm]);

  const handleTokenSelect = (token: TokenInfo) => {
    onSelectToken(token);
    onClose();
  };

  const formatBalance = (balance: string | undefined) => {
    console.log('🎯 Formatting balance:', balance);
    if (!balance || balance === '0' || balance === '0.0') return '0';
    const num = parseFloat(balance);
    if (isNaN(num) || num === 0) return '0';
    if (num < 0.001) return '<0.001';
    if (num < 1) return num.toFixed(6);
    if (num < 1000) return num.toFixed(4);
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };

  const handleRequestTokens = async (token: TokenInfo) => {
    if (onRequestTestTokens) {
      try {
        await onRequestTestTokens(token);
      } catch (error) {
        console.error('Failed to request test tokens:', error);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      <div className="relative w-full max-w-md mx-4 bg-gray-900 rounded-3xl border border-gray-700 shadow-2xl">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-normal text-white tracking-wider">Select Token</h2>
            <div className="flex items-center space-x-2">
              {onRefreshBalances && (
                <button
                  onClick={onRefreshBalances}
                  className="p-2 hover:bg-gray-800 rounded-full transition-colors"
                  title="Refresh Balances"
                >
                  <RefreshCw className="w-4 h-4 text-gray-400" />
                </button>
              )}
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-800 rounded-full transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>
          </div>
          
          <div className="relative mb-6">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search tokens"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 font-normal tracking-wide"
            />
          </div>

          {/* Category Filters */}
          <div className="flex space-x-2 mb-4">
            {[
              { key: 'all', label: 'All' },
              { key: 'verified', label: 'Verified' },
              { key: 'popular', label: 'Popular' }
            ].map(({ key, label }) => (
              <button
                key={key}
                onClick={() => setCategory(key as any)}
                className={`px-4 py-2 rounded-lg text-sm transition-all ${
                  category === key
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {label}
              </button>
            ))}
          </div>

          <div className="space-y-1 max-h-96 overflow-y-auto">
            {filteredTokens.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-400">No tokens found</p>
              </div>
            ) : (
              filteredTokens.map((token) => (
                <button
                  key={token.address}
                  onClick={() => handleTokenSelect(token)}
                  className={`w-full flex items-center justify-between p-4 rounded-xl transition-all hover:bg-gray-800 ${
                    selectedToken?.address === token.address ? 'bg-blue-600' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <img
                      src={token.logoURI}
                      alt={token.symbol}
                      className="w-10 h-10 rounded-full"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `data:image/svg+xml;base64,${btoa(`
                          <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                            <rect width="40" height="40" fill="#6366f1"/>
                            <text x="20" y="25" font-family="Arial" font-size="12" fill="white" text-anchor="middle">${token.symbol}</text>
                          </svg>
                        `)}`;
                      }}
                    />
                    <div className="text-left">
                      <div className="flex items-center space-x-2">
                        <span className="text-white font-normal tracking-wide">{token.symbol}</span>
                        {token.isVerified && (
                          <div className="w-2 h-2 bg-green-400 rounded-full" />
                        )}
                      </div>
                      <div className="text-gray-400 text-sm font-normal tracking-wide">{token.name}</div>
                    </div>
                  </div>
                  <div className="text-right flex items-center space-x-2">
                    <div>
                      <div className="text-white font-normal tracking-wide">
                        {formatBalance(token.balance)}
                      </div>
                      {isTestnet && (
                        <div
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRequestTokens(token);
                          }}
                          className="text-xs text-blue-400 hover:text-blue-300 flex items-center space-x-1 mt-1 cursor-pointer"
                        >
                          <Zap className="w-3 h-3" />
                          <span>Faucet</span>
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TokenSelector;