// Contract addresses configuration
// This file will be auto-updated by deployment scripts

export const PLATFORM_FEE_RECIPIENT = '0x100c7c6dAB862FdF668030081219F40F3EE3AcB2';

// Network configurations
export interface NetworkConfig {
  chainId: number;
  name: string;
  rpcUrl: string;
  blockExplorer: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  contracts: {
    router: string;
    factory: string;
    tokenRegistry: string;
    securityManager: string;
    priceOracle: string;
    smartRouter: string;
    liquidityMining: string;
    governanceToken: string;
    testTokenDeployer?: string;
  };
}

export const NETWORKS: Record<number, NetworkConfig> = {
  // Ethereum Mainnet
  1: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    rpcUrl: 'https://mainnet.infura.io/v3/********************************',
    blockExplorer: 'https://etherscan.io',
    nativeCurrency: {
      name: 'Ethereum',
      symbol: 'ETH',
      decimals: 18,
    },
    contracts: {
      router: '******************************************', // Will be updated after deployment
      factory: '******************************************',
      tokenRegistry: '******************************************',
      securityManager: '******************************************',
      priceOracle: '******************************************',
      smartRouter: '******************************************',
      liquidityMining: '******************************************',
      governanceToken: '******************************************',
    },
  },
  // Sepolia Testnet
  11155111: {
    chainId: 11155111,
    name: 'Sepolia Testnet',
    rpcUrl: 'https://sepolia.infura.io/v3/********************************',
    blockExplorer: 'https://sepolia.etherscan.io',
    nativeCurrency: {
      name: 'Sepolia ETH',
      symbol: 'SEP',
      decimals: 18,
    },
    contracts: {
      router: '******************************************', // Will be updated after deployment
      factory: '******************************************',
      tokenRegistry: '******************************************',
      securityManager: '******************************************',
      priceOracle: '******************************************',
      smartRouter: '******************************************',
      liquidityMining: '******************************************',
      governanceToken: '******************************************',
      testTokenDeployer: '******************************************',
    },
  },
};

// Helper functions
export const getNetworkConfig = (chainId: number): NetworkConfig | null => {
  return NETWORKS[chainId] || null;
};

export const getSupportedChainIds = (): number[] => {
  return Object.keys(NETWORKS).map(Number);
};

export const isMainnet = (chainId: number): boolean => {
  return chainId === 1;
};

export const isTestnet = (chainId: number): boolean => {
  return chainId === 11155111;
};

// Contract ABIs (essential functions only)
export const ROUTER_ABI = [
  'function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
  'function swapTokensForExactTokens(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
  'function swapExactETHForTokens(uint amountOutMin, address[] calldata path, address to, uint deadline) external payable returns (uint[] memory amounts)',
  'function swapTokensForExactETH(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
  'function swapExactTokensForETH(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
  'function swapETHForExactTokens(uint amountOut, address[] calldata path, address to, uint deadline) external payable returns (uint[] memory amounts)',
  'function swapETHForWETH() external payable',
  'function swapWETHForETH(uint256 amountIn) external',
  'function addLiquidity(address tokenA, address tokenB, uint amountADesired, uint amountBDesired, uint amountAMin, uint amountBMin, address to, uint deadline) external returns (uint amountA, uint amountB, uint liquidity)',
  'function removeLiquidity(address tokenA, address tokenB, uint liquidity, uint amountAMin, uint amountBMin, address to, uint deadline) external returns (uint amountA, uint amountB)',
  'function getAmountsOut(uint amountIn, address[] memory path) external view returns (uint[] memory amounts)',
  'function getAmountsIn(uint amountOut, address[] memory path) external view returns (uint[] memory amounts)',
  'function getOptimalSwapPath(address tokenIn, address tokenOut) external view returns (address[] memory path)',
  'function getSupportedTokens() external view returns (address[] memory)',
  'function getVerifiedTokens() external view returns (address[] memory)',
  'function getPlatformFeeRate() external view returns (uint256)',
  'function getPlatformFeeRecipient() external pure returns (address)',
  'function isTokenSupported(address token) external view returns (bool)',
  'event Swap(address indexed sender, uint256 amount0In, uint256 amount1In, uint256 amount0Out, uint256 amount1Out, address indexed to)',
  'event PlatformFeeCollected(address indexed token, uint256 amount, address indexed recipient)'
];

export const TOKEN_REGISTRY_ABI = [
  'function getActiveTokens() external view returns (address[] memory)',
  'function getVerifiedTokens() external view returns (address[] memory)',
  'function getTokenInfo(address token) external view returns (tuple(string symbol, string name, uint8 decimals, bool isActive, bool isVerified, string logoURI, string website, uint256 marketCap))',
  'function getTokensByMarketCap(uint256 limit) external view returns (address[] memory)',
  'function isTokenSupported(address token) external view returns (bool)',
  'function getTotalTokens() external view returns (uint256)'
];

export const PRICE_ORACLE_ABI = [
  'function getSpotPrice(address pair) external view returns (uint256 price0, uint256 price1)',
  'function getTWAPPrice(address pair, uint256 period) external view returns (uint256 price0, uint256 price1)',
  'function getCombinedPrice(address tokenA, address tokenB) external view returns (uint256 price, bool reliable)',
  'function getSafePrice(address tokenA, address tokenB) external view returns (uint256 price, bool safe)',
  'function isChainlinkAvailable(address token) external view returns (bool)',
  'function isTWAPAvailable(address pair) external view returns (bool)'
];

export const SMART_ROUTER_ABI = [
  'function findOptimalPath(address tokenIn, address tokenOut, uint256 amountIn) external view returns (tuple(address[] tokens, uint256[] amounts, uint256 expectedOutput, uint256 priceImpact, uint256 gasEstimate, bool isValid))',
  'function getIntermediateTokens() external view returns (address[] memory)',
  'function isIntermediateToken(address token) external view returns (bool)',
  'function estimateGasCost(address[] memory path) external view returns (uint256)'
];

export const ERC20_ABI = [
  'function balanceOf(address owner) external view returns (uint256)',
  'function decimals() external view returns (uint8)',
  'function symbol() external view returns (string)',
  'function name() external view returns (string)',
  'function approve(address spender, uint256 amount) external returns (bool)',
  'function allowance(address owner, address spender) external view returns (uint256)',
  'function transfer(address to, uint256 amount) external returns (bool)',
  'function transferFrom(address from, address to, uint256 amount) external returns (bool)',
  'event Transfer(address indexed from, address indexed to, uint256 value)',
  'event Approval(address indexed owner, address indexed spender, uint256 value)'
];

export const TEST_TOKEN_ABI = [
  ...ERC20_ABI,
  'function faucet(uint256 amount) external',
  'function mint(address to, uint256 amount) external'
];

// Known token addresses
export const KNOWN_TOKENS: Record<number, Record<string, string>> = {
  // Mainnet
  1: {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    WBTC: '******************************************',
    UNI: '******************************************',
    AAVE: '******************************************',
    LINK: '******************************************',
  },
  // Sepolia
  11155111: {
    WETH: '******************************************',
    // Test tokens will be added after deployment
  },
};

export default NETWORKS;
