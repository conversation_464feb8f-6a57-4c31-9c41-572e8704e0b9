import { useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers';
import { useWeb3 } from './useWeb3';
import { useSwap } from './useSwap';
import { LiquidityPool, AddLiquidityParams, RemoveLiquidityParams, TokenInfo } from '../utils/SwapSDK';

interface LiquidityState {
  pools: LiquidityPool[];
  userPools: LiquidityPool[];
  isLoading: boolean;
  error: string | null;
}

export const useLiquidity = () => {
  const { account, signer, isConnected } = useWeb3();
  const { sdk } = useSwap();
  
  const [state, setState] = useState<LiquidityState>({
    pools: [],
    userPools: [],
    isLoading: false,
    error: null,
  });

  const updateState = useCallback((updates: Partial<LiquidityState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Load all pools
  const loadAllPools = useCallback(async () => {
    if (!sdk) return;

    try {
      updateState({ isLoading: true, error: null });
      console.log('🔄 Loading all liquidity pools...');
      
      const pools = await sdk.getAllPairs();
      console.log(`✅ Loaded ${pools.length} pools`);
      
      updateState({ pools, isLoading: false });
    } catch (error: any) {
      console.error('❌ Error loading pools:', error);
      updateState({ error: error.message, isLoading: false });
    }
  }, [sdk, updateState]);

  // Load user pools
  const loadUserPools = useCallback(async () => {
    if (!sdk || !account) return;

    try {
      console.log('🔄 Loading user liquidity pools...');
      
      const userPools = await sdk.getUserLiquidityPools(account);
      console.log(`✅ Loaded ${userPools.length} user pools`);
      
      updateState({ userPools });
    } catch (error: any) {
      console.error('❌ Error loading user pools:', error);
      updateState({ error: error.message });
    }
  }, [sdk, account, updateState]);

  // Add liquidity
  const addLiquidity = useCallback(async (
    tokenA: TokenInfo,
    tokenB: TokenInfo,
    amountA: string,
    amountB: string,
    slippage: number = 0.5
  ): Promise<boolean> => {
    if (!sdk || !signer || !account) {
      console.log('❌ Missing required data for adding liquidity');
      return false;
    }

    try {
      updateState({ isLoading: true, error: null });
      console.log('💧 Adding liquidity:', { tokenA: tokenA.symbol, tokenB: tokenB.symbol, amountA, amountB });

      const slippageMultiplier = (100 - slippage) / 100;
      const amountAMin = (parseFloat(amountA) * slippageMultiplier).toString();
      const amountBMin = (parseFloat(amountB) * slippageMultiplier).toString();
      const deadline = Math.floor(Date.now() / 1000) + 1200; // 20 minutes

      const params: AddLiquidityParams = {
        tokenA: tokenA.address,
        tokenB: tokenB.address,
        amountADesired: amountA,
        amountBDesired: amountB,
        amountAMin,
        amountBMin,
        to: account,
        deadline
      };

      const tx = await sdk.addLiquidity(params, signer);
      console.log('📝 Transaction sent:', tx.hash);
      
      await tx.wait();
      console.log('✅ Liquidity added successfully!');

      // Refresh pools
      await Promise.all([loadAllPools(), loadUserPools()]);
      
      updateState({ isLoading: false });
      return true;
    } catch (error: any) {
      console.error('❌ Error adding liquidity:', error);
      updateState({ error: error.message, isLoading: false });
      return false;
    }
  }, [sdk, signer, account, updateState, loadAllPools, loadUserPools]);

  // Remove liquidity
  const removeLiquidity = useCallback(async (
    pool: LiquidityPool,
    percentage: number,
    slippage: number = 0.5
  ): Promise<boolean> => {
    if (!sdk || !signer || !account) {
      console.log('❌ Missing required data for removing liquidity');
      return false;
    }

    try {
      updateState({ isLoading: true, error: null });
      console.log('💧 Removing liquidity:', { pool: pool.address, percentage });

      const liquidityToRemove = (parseFloat(pool.userLiquidity) * percentage / 100).toString();
      const slippageMultiplier = (100 - slippage) / 100;
      
      const expectedAmountA = parseFloat(pool.reserve0) * percentage / 100;
      const expectedAmountB = parseFloat(pool.reserve1) * percentage / 100;
      
      const amountAMin = (expectedAmountA * slippageMultiplier).toString();
      const amountBMin = (expectedAmountB * slippageMultiplier).toString();
      const deadline = Math.floor(Date.now() / 1000) + 1200; // 20 minutes

      const params: RemoveLiquidityParams = {
        tokenA: pool.token0.address,
        tokenB: pool.token1.address,
        liquidity: liquidityToRemove,
        amountAMin,
        amountBMin,
        to: account,
        deadline
      };

      const tx = await sdk.removeLiquidity(params, signer);
      console.log('📝 Transaction sent:', tx.hash);
      
      await tx.wait();
      console.log('✅ Liquidity removed successfully!');

      // Refresh pools
      await Promise.all([loadAllPools(), loadUserPools()]);
      
      updateState({ isLoading: false });
      return true;
    } catch (error: any) {
      console.error('❌ Error removing liquidity:', error);
      updateState({ error: error.message, isLoading: false });
      return false;
    }
  }, [sdk, signer, account, updateState, loadAllPools, loadUserPools]);

  // Get pair address
  const getPairAddress = useCallback(async (tokenA: string, tokenB: string): Promise<string> => {
    if (!sdk) return ethers.constants.AddressZero;
    return await sdk.getPairAddress(tokenA, tokenB);
  }, [sdk]);

  // Load pools on mount and when dependencies change
  useEffect(() => {
    if (sdk) {
      loadAllPools();
    }
  }, [sdk, loadAllPools]);

  useEffect(() => {
    if (sdk && account && isConnected) {
      loadUserPools();
    }
  }, [sdk, account, isConnected, loadUserPools]);

  return {
    // State
    ...state,
    
    // Actions
    addLiquidity,
    removeLiquidity,
    loadAllPools,
    loadUserPools,
    getPairAddress,
  };
};
