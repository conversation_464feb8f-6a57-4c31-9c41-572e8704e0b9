import { useState, useCallback, useEffect } from 'react';
import { ethers } from 'ethers';
import { SwapSDK, TokenInfo, SwapQuote } from '../utils/SwapSDK';
import { useWeb3 } from './useWeb3';

export interface SwapState {
  fromToken: TokenInfo | null;
  toToken: TokenInfo | null;
  fromAmount: string;
  toAmount: string;
  quote: SwapQuote | null;
  isLoading: boolean;
  isSwapping: boolean;
  error: string | null;
  slippage: number;
  deadline: number;
}

export const useSwap = () => {
  const { provider, signer, account, network, isConnected } = useWeb3();
  const [sdk, setSdk] = useState<SwapSDK | null>(null);
  const [supportedTokens, setSupportedTokens] = useState<TokenInfo[]>([]);
  const [state, setState] = useState<SwapState>({
    fromToken: null,
    toToken: null,
    fromAmount: '',
    toAmount: '',
    quote: null,
    isLoading: false,
    isSwapping: false,
    error: null,
    slippage: 0.5,
    deadline: 30, // minutes
  });

  // Initialize SDK when provider and network are available
  useEffect(() => {
    if (provider && network) {
      const newSdk = new SwapSDK(provider, network);
      setSdk(newSdk);
    } else {
      setSdk(null);
    }
  }, [provider, network]);

  // Load supported tokens when SDK is ready
  useEffect(() => {
    const loadTokens = async () => {
      if (!sdk) return;

      try {
        setState(prev => ({ ...prev, isLoading: true, error: null }));
        const tokens = await sdk.getSupportedTokens();
        setSupportedTokens(tokens);

        // Set default tokens if none selected
        if (!state.fromToken && tokens.length > 0) {
          const weth = tokens.find(t => t.symbol === 'WETH');
          const usdc = tokens.find(t => t.symbol === 'USDC' || t.symbol === 'TUSDC');
          
          setState(prev => ({
            ...prev,
            fromToken: weth || tokens[0],
            toToken: usdc || tokens[1],
          }));
        }
      } catch (error: any) {
        setState(prev => ({ ...prev, error: error.message }));
      } finally {
        setState(prev => ({ ...prev, isLoading: false }));
      }
    };

    loadTokens();
  }, [sdk]);

  // Update token balances when account or SDK changes
  useEffect(() => {
    const updateBalances = async () => {
      if (!sdk || !account) {
        console.log('SDK or account not available for balance update');
        return;
      }

      if (supportedTokens.length === 0) {
        console.log('No supported tokens to update balances for');
        return;
      }

      console.log('🔄 Updating balances for', supportedTokens.length, 'tokens, account:', account);

      try {
        const updatedTokens = await Promise.all(
          supportedTokens.map(async (token) => {
            try {
              console.log(`📊 Getting balance for ${token.symbol} (${token.address})`);
              const balance = await sdk.getTokenBalance(token.address, account);
              console.log(`✅ Balance for ${token.symbol}:`, balance);
              return { ...token, balance };
            } catch (error) {
              console.error(`❌ Error getting balance for ${token.symbol}:`, error);
              return { ...token, balance: '0' };
            }
          })
        );

        console.log('🎯 Setting updated tokens:', updatedTokens.map(t => `${t.symbol}: ${t.balance}`));
        setSupportedTokens(updatedTokens);

        // Update selected tokens with balances
        if (state.fromToken) {
          const updatedFromToken = updatedTokens.find(t => t.address === state.fromToken!.address);
          if (updatedFromToken) {
            console.log(`🔄 Updating fromToken balance: ${updatedFromToken.symbol} = ${updatedFromToken.balance}`);
            setState(prev => ({ ...prev, fromToken: updatedFromToken }));
          }
        }

        if (state.toToken) {
          const updatedToToken = updatedTokens.find(t => t.address === state.toToken!.address);
          if (updatedToToken) {
            console.log(`🔄 Updating toToken balance: ${updatedToToken.symbol} = ${updatedToToken.balance}`);
            setState(prev => ({ ...prev, toToken: updatedToToken }));
          }
        }
      } catch (error) {
        console.error('❌ Error updating balances:', error);
      }
    };

    // Immediate update when dependencies change
    updateBalances();
  }, [sdk, account, supportedTokens.length]);

  // Force balance update every 10 seconds when connected
  useEffect(() => {
    if (!sdk || !account || supportedTokens.length === 0) return;

    const interval = setInterval(async () => {
      console.log('⏰ Periodic balance update triggered');

      try {
        const updatedTokens = await Promise.all(
          supportedTokens.map(async (token) => {
            try {
              const balance = await sdk.getTokenBalance(token.address, account);
              return { ...token, balance };
            } catch (error) {
              console.error(`Error in periodic balance update for ${token.symbol}:`, error);
              return { ...token, balance: '0' };
            }
          })
        );
        setSupportedTokens(updatedTokens);
      } catch (error) {
        console.error('Error in periodic balance update:', error);
      }
    }, 10000); // Every 10 seconds

    return () => clearInterval(interval);
  }, [sdk, account, supportedTokens.length]);

  const updateState = useCallback((updates: Partial<SwapState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const setFromToken = useCallback((token: TokenInfo) => {
    updateState({ fromToken: token, quote: null, toAmount: '' });
  }, [updateState]);

  const setToToken = useCallback((token: TokenInfo) => {
    updateState({ toToken: token, quote: null, toAmount: '' });
  }, [updateState]);

  const swapTokens = useCallback(() => {
    setState(prev => ({
      ...prev,
      fromToken: prev.toToken,
      toToken: prev.fromToken,
      fromAmount: prev.toAmount,
      toAmount: prev.fromAmount,
      quote: null,
    }));
  }, []);

  const setFromAmount = useCallback(async (amount: string) => {
    updateState({ fromAmount: amount, isLoading: true, error: null });

    if (!amount || !state.fromToken || !state.toToken || !sdk) {
      updateState({ toAmount: '', quote: null, isLoading: false });
      return;
    }

    try {
      const quote = await sdk.getSwapQuote(
        state.fromToken.address,
        state.toToken.address,
        amount,
        state.slippage
      );

      if (quote) {
        updateState({
          toAmount: quote.amountOut,
          quote,
          isLoading: false,
        });
      } else {
        updateState({
          toAmount: '',
          quote: null,
          error: 'Unable to get quote',
          isLoading: false,
        });
      }
    } catch (error: any) {
      updateState({
        toAmount: '',
        quote: null,
        error: error.message,
        isLoading: false,
      });
    }
  }, [sdk, state.fromToken, state.toToken, state.slippage, updateState]);

  const setSlippage = useCallback((slippage: number) => {
    updateState({ slippage });
    // Re-calculate quote with new slippage if we have an amount
    if (state.fromAmount) {
      setFromAmount(state.fromAmount);
    }
  }, [state.fromAmount, setFromAmount, updateState]);

  const checkAllowance = useCallback(async (): Promise<boolean> => {
    if (!sdk || !account || !state.fromToken || !network) {
      return false;
    }

    try {
      const allowance = await sdk.getAllowance(
        state.fromToken.address,
        account,
        network.contracts.router
      );

      return parseFloat(allowance) >= parseFloat(state.fromAmount);
    } catch (error) {
      console.error('Error checking allowance:', error);
      return false;
    }
  }, [sdk, account, state.fromToken, state.fromAmount, network]);

  const approveToken = useCallback(async (): Promise<boolean> => {
    if (!sdk || !signer || !state.fromToken || !network) {
      return false;
    }

    try {
      updateState({ isLoading: true, error: null });

      const tx = await sdk.approveToken(
        state.fromToken.address,
        network.contracts.router,
        state.fromAmount,
        signer
      );

      await tx.wait();
      return true;
    } catch (error: any) {
      updateState({ error: error.message });
      return false;
    } finally {
      updateState({ isLoading: false });
    }
  }, [sdk, signer, state.fromToken, state.fromAmount, network, updateState]);

  const executeSwap = useCallback(async (): Promise<boolean> => {
    if (!sdk || !signer || !state.fromToken || !state.toToken || !state.quote) {
      return false;
    }

    try {
      updateState({ isSwapping: true, error: null });

      // Check if approval is needed (for non-ETH tokens)
      if (state.fromToken.address !== ethers.constants.AddressZero) {
        const hasAllowance = await checkAllowance();
        if (!hasAllowance) {
          const approved = await approveToken();
          if (!approved) {
            return false;
          }
        }
      }

      const deadline = Math.floor(Date.now() / 1000) + (state.deadline * 60);
      
      const tx = await sdk.executeSwap(
        state.fromToken.address,
        state.toToken.address,
        state.fromAmount,
        state.quote.minimumReceived,
        signer,
        deadline
      );

      await tx.wait();
      
      // Reset form after successful swap
      updateState({
        fromAmount: '',
        toAmount: '',
        quote: null,
      });

      return true;
    } catch (error: any) {
      updateState({ error: error.message });
      return false;
    } finally {
      updateState({ isSwapping: false });
    }
  }, [
    sdk,
    signer,
    state.fromToken,
    state.toToken,
    state.quote,
    state.fromAmount,
    state.deadline,
    checkAllowance,
    approveToken,
    updateState,
  ]);

  const requestTestTokens = useCallback(async (token: TokenInfo | string, amount: string = '1000'): Promise<boolean> => {
    if (!sdk || !signer || network?.chainId !== ********) {
      return false;
    }

    const tokenAddress = typeof token === 'string' ? token : token.address;

    try {
      updateState({ isLoading: true, error: null });

      const tx = await sdk.requestTestTokens(tokenAddress, amount, signer);
      await tx.wait();

      return true;
    } catch (error: any) {
      updateState({ error: error.message });
      return false;
    } finally {
      updateState({ isLoading: false });
    }
  }, [sdk, signer, network, updateState]);

  const getMaxBalance = useCallback((): string => {
    if (!state.fromToken?.balance) return '0';
    
    // For ETH, leave some for gas
    if (state.fromToken.address === ethers.constants.AddressZero) {
      const balance = parseFloat(state.fromToken.balance);
      return Math.max(0, balance - 0.01).toString(); // Leave 0.01 ETH for gas
    }
    
    return state.fromToken.balance;
  }, [state.fromToken]);

  const setMaxAmount = useCallback(() => {
    const maxBalance = getMaxBalance();
    setFromAmount(maxBalance);
  }, [getMaxBalance, setFromAmount]);

  const canSwap = useCallback((): boolean => {
    return !!(
      isConnected &&
      state.fromToken &&
      state.toToken &&
      state.fromAmount &&
      state.toAmount &&
      state.quote &&
      !state.isLoading &&
      !state.isSwapping &&
      parseFloat(state.fromAmount) > 0 &&
      parseFloat(state.fromAmount) <= parseFloat(getMaxBalance())
    );
  }, [
    isConnected,
    state.fromToken,
    state.toToken,
    state.fromAmount,
    state.toAmount,
    state.quote,
    state.isLoading,
    state.isSwapping,
    getMaxBalance,
  ]);

  const refreshBalances = useCallback(async () => {
    if (!sdk || !account || supportedTokens.length === 0) return;

    console.log('Manual refresh balances triggered');

    try {
      const updatedTokens = await Promise.all(
        supportedTokens.map(async (token) => {
          try {
            const balance = await sdk.getTokenBalance(token.address, account);
            console.log(`Refreshed balance for ${token.symbol}:`, balance);
            return { ...token, balance };
          } catch (error) {
            console.error(`Error refreshing balance for ${token.symbol}:`, error);
            return { ...token, balance: '0' };
          }
        })
      );
      setSupportedTokens(updatedTokens);

      // Update selected tokens with balances
      if (state.fromToken) {
        const updatedFromToken = updatedTokens.find(t => t.address === state.fromToken!.address);
        if (updatedFromToken) {
          setState(prev => ({ ...prev, fromToken: updatedFromToken }));
        }
      }

      if (state.toToken) {
        const updatedToToken = updatedTokens.find(t => t.address === state.toToken!.address);
        if (updatedToToken) {
          setState(prev => ({ ...prev, toToken: updatedToToken }));
        }
      }
    } catch (error) {
      console.error('Error refreshing balances:', error);
    }
  }, [sdk, account, supportedTokens, state.fromToken?.address, state.toToken?.address]);

  // Wrapper for requestTestTokens that also refreshes balances
  const requestTestTokensWithRefresh = useCallback(async (token: TokenInfo | string, amount: string = '1000'): Promise<boolean> => {
    const result = await requestTestTokens(token, amount);
    if (result) {
      // Refresh balances after successful token request
      await refreshBalances();
    }
    return result;
  }, [requestTestTokens, refreshBalances]);

  return {
    // State
    ...state,
    supportedTokens,
    canSwap: canSwap(),
    maxBalance: getMaxBalance(),
    
    // Actions
    setFromToken,
    setToToken,
    swapTokens,
    setFromAmount,
    setSlippage,
    setMaxAmount,
    executeSwap,
    requestTestTokens: requestTestTokensWithRefresh,
    refreshBalances,

    // Utils
    sdk,
    isTestnet: network?.chainId === ********,
  };
};
