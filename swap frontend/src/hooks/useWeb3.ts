import { useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers';
import { NETWORKS, NetworkConfig } from '../config/contracts';



export interface Web3State {
  provider: ethers.providers.Web3Provider | null;
  signer: ethers.Signer | null;
  account: string | null;
  chainId: number | null;
  network: NetworkConfig | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

export const useWeb3 = () => {
  const [state, setState] = useState<Web3State>({
    provider: null,
    signer: null,
    account: null,
    chainId: null,
    network: null,
    isConnected: false,
    isConnecting: false,
    error: null,
  });

  const updateState = useCallback((updates: Partial<Web3State>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const connectWallet = useCallback(async () => {
    if (!window.ethereum) {
      updateState({ error: 'MetaMask not found. Please install MetaMask.' });
      return;
    }

    try {
      updateState({ isConnecting: true, error: null });

      // Request account access
      await window.ethereum.request({ method: 'eth_requestAccounts' });

      const provider = new ethers.providers.Web3Provider(window.ethereum);
      const signer = provider.getSigner();
      const account = await signer.getAddress();
      const network = await provider.getNetwork();
      const chainId = network.chainId;

      const networkConfig = NETWORKS[chainId];

      updateState({
        provider,
        signer,
        account,
        chainId,
        network: networkConfig || null,
        isConnected: true,
        isConnecting: false,
        error: networkConfig ? null : `Unsupported network: ${network.name}`,
      });

    } catch (error: any) {
      console.error('Wallet connection error:', error);
      updateState({
        isConnecting: false,
        error: error.message || 'Failed to connect wallet',
      });
    }
  }, [updateState]);

  const disconnectWallet = useCallback(() => {
    updateState({
      provider: null,
      signer: null,
      account: null,
      chainId: null,
      network: null,
      isConnected: false,
      isConnecting: false,
      error: null,
    });
  }, [updateState]);

  const switchNetwork = useCallback(async (targetChainId: number) => {
    if (!window.ethereum) {
      updateState({ error: 'MetaMask not found' });
      return;
    }

    const networkConfig = NETWORKS[targetChainId];
    if (!networkConfig) {
      updateState({ error: `Unsupported network: ${targetChainId}` });
      return;
    }

    try {
      // Try to switch to the network
      await window.ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId: `0x${targetChainId.toString(16)}` }],
      });
    } catch (switchError: any) {
      // If network doesn't exist, add it
      if (switchError.code === 4902) {
        try {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [{
              chainId: `0x${targetChainId.toString(16)}`,
              chainName: networkConfig.name,
              nativeCurrency: networkConfig.nativeCurrency,
              rpcUrls: [networkConfig.rpcUrl],
              blockExplorerUrls: [networkConfig.blockExplorer],
            }],
          });
        } catch (addError: any) {
          updateState({ error: `Failed to add network: ${addError.message}` });
        }
      } else {
        updateState({ error: `Failed to switch network: ${switchError.message}` });
      }
    }
  }, [updateState]);

  const addToken = useCallback(async (tokenAddress: string, symbol: string, decimals: number, image?: string) => {
    if (!window.ethereum) {
      updateState({ error: 'MetaMask not found' });
      return;
    }

    try {
      await window.ethereum.request({
        method: 'wallet_watchAsset',
        params: {
          type: 'ERC20',
          options: {
            address: tokenAddress,
            symbol,
            decimals,
            image,
          },
        },
      });
    } catch (error: any) {
      console.error('Add token error:', error);
      updateState({ error: `Failed to add token: ${error.message}` });
    }
  }, [updateState]);

  // Listen for account and network changes
  useEffect(() => {
    if (!window.ethereum) return;

    const handleAccountsChanged = (accounts: string[]) => {
      if (accounts.length === 0) {
        disconnectWallet();
      } else if (accounts[0] !== state.account) {
        connectWallet();
      }
    };

    const handleChainChanged = (chainId: string) => {
      const newChainId = parseInt(chainId, 16);
      const networkConfig = NETWORKS[newChainId];
      
      updateState({
        chainId: newChainId,
        network: networkConfig || null,
        error: networkConfig ? null : `Unsupported network: ${newChainId}`,
      });
    };

    window.ethereum.on('accountsChanged', handleAccountsChanged);
    window.ethereum.on('chainChanged', handleChainChanged);

    return () => {
      window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
      window.ethereum.removeListener('chainChanged', handleChainChanged);
    };
  }, [state.account, connectWallet, disconnectWallet, updateState]);

  // Auto-connect if previously connected
  useEffect(() => {
    const autoConnect = async () => {
      if (!window.ethereum) {
        console.log('🔌 No MetaMask detected');
        return;
      }

      try {
        console.log('🔄 Checking for existing MetaMask connection...');
        // Check if already connected without requesting permissions
        const accounts = await window.ethereum.request({ method: 'eth_accounts' });
        console.log('👛 Found accounts:', accounts);

        if (accounts.length > 0) {
          console.log('✅ Auto-connecting to account:', accounts[0]);

          // Create provider and signer for auto-connected account
          const provider = new ethers.providers.Web3Provider(window.ethereum);
          const signer = provider.getSigner();
          const network = await provider.getNetwork();
          const chainId = network.chainId;
          const networkConfig = NETWORKS[chainId];

          console.log('🌐 Connected to network:', networkConfig?.name || 'Unknown', `(${chainId})`);

          setState(prev => ({
            ...prev,
            provider,
            signer,
            account: accounts[0],
            chainId,
            network: networkConfig || null,
            isConnected: true,
            error: networkConfig ? null : `Unsupported network: ${network.name}`,
          }));
        } else {
          console.log('👛 No accounts found, user needs to connect manually');
        }
      } catch (error) {
        // Silently fail auto-connect, user can manually connect
        console.log('⚠️ Auto-connect not available:', error);
      }
    };

    // Delay auto-connect to avoid conflicts
    setTimeout(autoConnect, 1000);
  }, []);

  return {
    ...state,
    connectWallet,
    disconnectWallet,
    switchNetwork,
    addToken,
    supportedNetworks: Object.values(NETWORKS),
  };
};

// Extend Window interface for TypeScript
declare global {
  interface Window {
    ethereum?: any;
  }
}
