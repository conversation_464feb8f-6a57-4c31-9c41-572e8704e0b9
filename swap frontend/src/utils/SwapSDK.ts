import { ethers } from 'ethers';
import {
  NetworkConfig,
  ROUTER_ABI,
  TOKEN_REGISTRY_ABI,
  PRICE_ORACLE_ABI,
  SMART_ROUTER_ABI,
  ERC20_ABI,
  FACTORY_ABI,
  PAIR_ABI,
  KNOWN_TOKENS
} from '../config/contracts';



export interface TokenInfo {
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  logoURI: string;
  website: string;
  marketCap: string;
  balance?: string;
  isActive: boolean;
  isVerified: boolean;
}

export interface SwapPath {
  tokens: string[];
  amounts: string[];
  expectedOutput: string;
  priceImpact: string;
  gasEstimate: string;
  isValid: boolean;
}

export interface SwapQuote {
  amountIn: string;
  amountOut: string;
  path: string[];
  priceImpact: string;
  platformFee: string;
  gasEstimate: string;
  minimumReceived: string;
}

export interface LiquidityPool {
  address: string;
  token0: TokenInfo;
  token1: TokenInfo;
  reserve0: string;
  reserve1: string;
  totalSupply: string;
  userLiquidity: string;
  userShare: string;
  apr?: string;
}

export interface AddLiquidityParams {
  tokenA: string;
  tokenB: string;
  amountADesired: string;
  amountBDesired: string;
  amountAMin: string;
  amountBMin: string;
  to: string;
  deadline: number;
}

export interface RemoveLiquidityParams {
  tokenA: string;
  tokenB: string;
  liquidity: string;
  amountAMin: string;
  amountBMin: string;
  to: string;
  deadline: number;
}

export interface PriceInfo {
  price: string;
  reliable: boolean;
  source: 'chainlink' | 'twap' | 'spot';
  lastUpdated: number;
}

export class SwapSDK {
  private provider: ethers.providers.Provider;
  private network: NetworkConfig;
  private contracts: {
    router: ethers.Contract;
    tokenRegistry: ethers.Contract;
    priceOracle: ethers.Contract;
    smartRouter: ethers.Contract;
  };
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 300000; // 5 minutes

  constructor(provider: ethers.providers.Provider, network: NetworkConfig) {
    this.provider = provider;
    this.network = network;

    // Initialize contracts
    this.contracts = {
      router: new ethers.Contract(network.contracts.router, ROUTER_ABI, provider),
      tokenRegistry: new ethers.Contract(network.contracts.tokenRegistry, TOKEN_REGISTRY_ABI, provider),
      priceOracle: new ethers.Contract(network.contracts.priceOracle, PRICE_ORACLE_ABI, provider),
      smartRouter: new ethers.Contract(network.contracts.smartRouter, SMART_ROUTER_ABI, provider),
    };
  }

  // Cache utilities
  private getCacheKey(method: string, params: any[]): string {
    return `${method}_${JSON.stringify(params)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  // Token functions
  async getSupportedTokens(): Promise<TokenInfo[]> {
    console.log('🔍 Getting supported tokens...');

    // For now, always use fallback tokens since we don't have deployed contracts
    console.log('🔄 Using fallback tokens for current network');
    return await this.getFallbackTokens();
  }

  private async getFallbackTokens(): Promise<TokenInfo[]> {
    try {
      const network = await this.provider.getNetwork();
      console.log('🌐 Getting fallback tokens for network:', network?.chainId);

      if (network?.chainId === 11155111) {
        // Sepolia testnet tokens - using real deployed addresses
        return [
          {
            address: ethers.constants.AddressZero,
            symbol: 'ETH',
            name: 'Sepolia ETH',
            decimals: 18,
            logoURI: 'https://tokens.1inch.io/******************************************.png',
            balance: '0',
            isVerified: true,
            isActive: true,
            marketCap: '500000000000',
            website: 'https://ethereum.org'
          },
          {
            address: '******************************************',
            symbol: 'WETH',
            name: 'Wrapped Ethereum (Sepolia)',
            decimals: 18,
            logoURI: 'https://tokens.1inch.io/******************************************.png',
            balance: '0',
            isVerified: true,
            isActive: true,
            marketCap: '500000000000',
            website: 'https://weth.io'
          },
          {
            address: '******************************************',
            symbol: 'USDC',
            name: 'USD Coin (Sepolia)',
            decimals: 6,
            logoURI: 'https://tokens.1inch.io/******************************************.png',
            balance: '0',
            isVerified: true,
            isActive: true,
            marketCap: '100000000000',
            website: 'https://centre.io'
          },
          {
            address: '******************************************',
            symbol: 'LINK',
            name: 'Chainlink Token (Sepolia)',
            decimals: 18,
            logoURI: 'https://tokens.1inch.io/******************************************.png',
            balance: '0',
            isVerified: true,
            isActive: true,
            marketCap: '8000000000',
            website: 'https://chain.link'
          }
        ];
      } else {
        // Mainnet tokens
        return [
          {
            address: ethers.constants.AddressZero,
            symbol: 'ETH',
            name: 'Ethereum',
            decimals: 18,
            logoURI: 'https://tokens.1inch.io/******************************************.png',
            balance: '0',
            isVerified: true,
            isActive: true,
            marketCap: '500000000000',
            website: 'https://ethereum.org'
          },
          {
            address: '******************************************',
            symbol: 'WETH',
            name: 'Wrapped Ethereum',
            decimals: 18,
            logoURI: 'https://tokens.1inch.io/******************************************.png',
            balance: '0',
            isVerified: true,
            isActive: true,
            marketCap: '500000000000',
            website: 'https://weth.io'
          }
        ];
      }
    } catch (error) {
      console.error('Error getting network for fallback tokens:', error);
      return [
        {
          address: ethers.constants.AddressZero,
          symbol: 'ETH',
          name: 'Ethereum',
          decimals: 18,
          logoURI: 'https://tokens.1inch.io/******************************************.png',
          balance: '0',
          isVerified: true,
          isActive: true,
          marketCap: '500000000000',
          website: 'https://ethereum.org'
        }
      ];
    }
  }

  async getTokenInfo(address: string): Promise<TokenInfo | null> {
    const cacheKey = this.getCacheKey('getTokenInfo', [address]);
    const cached = this.getFromCache<TokenInfo>(cacheKey);
    if (cached) return cached;

    try {
      // Handle ETH specially
      if (address === ethers.constants.AddressZero || address === '******************************************') {
        const ethInfo: TokenInfo = {
          symbol: 'ETH',
          name: 'Ethereum',
          address: ethers.constants.AddressZero,
          decimals: 18,
          logoURI: 'https://tokens.1inch.io/******************************************.png',
          website: 'https://ethereum.org',
          marketCap: '500000000000',
          isActive: true,
          isVerified: true,
        };
        this.setCache(cacheKey, ethInfo);
        return ethInfo;
      }

      const tokenContract = new ethers.Contract(address, ERC20_ABI, this.provider);
      const [registryInfo, symbol, name, decimals] = await Promise.all([
        this.contracts.tokenRegistry.getTokenInfo(address),
        tokenContract.symbol(),
        tokenContract.name(),
        tokenContract.decimals(),
      ]);

      const tokenInfo: TokenInfo = {
        symbol: registryInfo.symbol || symbol,
        name: registryInfo.name || name,
        address,
        decimals,
        logoURI: registryInfo.logoURI || `https://tokens.1inch.io/0x${symbol.toLowerCase()}.png`,
        website: registryInfo.website || '',
        marketCap: ethers.utils.formatEther(registryInfo.marketCap || '0'),
        isActive: registryInfo.isActive,
        isVerified: registryInfo.isVerified,
      };

      this.setCache(cacheKey, tokenInfo);
      return tokenInfo;
    } catch (error) {
      console.error(`❌ Error getting token info for ${address}:`, error);

      // Fallback: Try to get token info directly from contract
      try {
        console.log(`🔄 Trying direct contract call for ${address}`);
        const tokenContract = new ethers.Contract(address, ERC20_ABI, this.provider);

        const [symbol, name, decimals] = await Promise.all([
          tokenContract.symbol(),
          tokenContract.name(),
          tokenContract.decimals()
        ]);

        const tokenInfo: TokenInfo = {
          symbol,
          name,
          address,
          decimals,
          logoURI: `https://tokens.1inch.io/${address.toLowerCase()}.png`,
          website: '',
          marketCap: '0',
          isActive: true,
          isVerified: false, // Not verified since not from registry
        };

        console.log(`✅ Got token info directly: ${symbol} (${name})`);
        this.setCache(cacheKey, tokenInfo);
        return tokenInfo;
      } catch (directError) {
        console.error(`❌ Direct contract call also failed for ${address}:`, directError);

        // Last resort: Check fallback tokens
        const fallbackTokens = await this.getFallbackTokens();
        const knownToken = fallbackTokens.find(t => t.address.toLowerCase() === address.toLowerCase());

        if (knownToken) {
          console.log(`✅ Found token in fallback list: ${knownToken.symbol}`);
          this.setCache(cacheKey, knownToken);
          return knownToken;
        }

        return null;
      }
    }
  }

  async getTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
    console.log(`🔍 SwapSDK: Getting balance for token: ${tokenAddress}, user: ${userAddress}`);

    // Input validation
    if (!tokenAddress || !userAddress) {
      console.error('❌ SwapSDK: Invalid token address or user address');
      return '0';
    }

    if (!ethers.utils.isAddress(userAddress)) {
      console.error('❌ SwapSDK: Invalid user address format');
      return '0';
    }

    try {
      // Handle ETH balance
      if (tokenAddress === ethers.constants.AddressZero ||
          tokenAddress.toLowerCase() === 'eth' ||
          tokenAddress === '******************************************') {
        console.log(`💰 Getting ETH balance for ${userAddress}`);
        const balance = await this.provider.getBalance(userAddress);
        const formatted = ethers.utils.formatEther(balance);
        console.log(`💰 ETH balance: ${balance.toString()} -> ${formatted}`);
        return formatted;
      }

      // Validate token address
      if (!ethers.utils.isAddress(tokenAddress)) {
        console.error('❌ SwapSDK: Invalid token address format');
        return '0';
      }

      console.log(`🪙 Getting ERC20 balance for ${tokenAddress}`);
      const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, this.provider);

      // Get balance and decimals with timeout
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Balance request timeout')), 10000)
      );

      const [balance, decimals] = await Promise.race([
        Promise.all([
          tokenContract.balanceOf(userAddress),
          tokenContract.decimals(),
        ]),
        timeoutPromise
      ]) as [ethers.BigNumber, number];

      const formatted = ethers.utils.formatUnits(balance, decimals);
      console.log(`🪙 Token balance: ${balance.toString()} (${decimals} decimals) -> ${formatted}`);
      return formatted;
    } catch (error: any) {
      console.error(`❌ SwapSDK: Error getting token balance for ${tokenAddress}:`, error);

      // More specific error handling
      if (error.code === 'CALL_EXCEPTION') {
        console.error('❌ Contract call failed - token might not exist or be invalid');
      } else if (error.message?.includes('timeout')) {
        console.error('❌ Balance request timed out');
      } else if (error.code === 'NETWORK_ERROR') {
        console.error('❌ Network error while fetching balance');
      }

      return '0';
    }
  }

  // Price functions
  async getTokenPrice(tokenA: string, tokenB: string): Promise<PriceInfo> {
    const cacheKey = this.getCacheKey('getTokenPrice', [tokenA, tokenB]);
    const cached = this.getFromCache<PriceInfo>(cacheKey);
    if (cached) return cached;

    try {
      const [combinedPrice, chainlinkAvailable, twapAvailable] = await Promise.all([
        this.contracts.priceOracle.getCombinedPrice(tokenA, tokenB),
        this.contracts.priceOracle.isChainlinkAvailable(tokenA),
        this.contracts.priceOracle.isTWAPAvailable(tokenA), // Assuming pair exists
      ]);

      const priceInfo: PriceInfo = {
        price: ethers.utils.formatEther(combinedPrice.price),
        reliable: combinedPrice.reliable,
        source: chainlinkAvailable ? 'chainlink' : twapAvailable ? 'twap' : 'spot',
        lastUpdated: Date.now(),
      };

      this.setCache(cacheKey, priceInfo);
      return priceInfo;
    } catch (error) {
      console.error(`Error getting price for ${tokenA}/${tokenB}:`, error);
      return {
        price: '0',
        reliable: false,
        source: 'spot',
        lastUpdated: Date.now(),
      };
    }
  }

  // Swap functions
  async getSwapQuote(
    tokenIn: string,
    tokenOut: string,
    amountIn: string,
    slippage: number = 0.5
  ): Promise<SwapQuote | null> {
    try {
      const tokenInInfo = await this.getTokenInfo(tokenIn);
      const tokenOutInfo = await this.getTokenInfo(tokenOut);
      
      if (!tokenInInfo || !tokenOutInfo) {
        throw new Error('Token info not found');
      }

      const amountInWei = ethers.utils.parseUnits(amountIn, tokenInInfo.decimals);
      
      // Get optimal path
      const pathInfo = await this.contracts.smartRouter.findOptimalPath(tokenIn, tokenOut, amountInWei);
      
      if (!pathInfo.isValid) {
        throw new Error('No valid path found');
      }

      // Calculate platform fee
      const platformFeeRate = await this.contracts.router.getPlatformFeeRate();
      const platformFee = amountInWei.mul(platformFeeRate).div(10000);
      
      // Calculate minimum received with slippage
      const expectedOutput = ethers.utils.formatUnits(pathInfo.expectedOutput, tokenOutInfo.decimals);
      const minimumReceived = (parseFloat(expectedOutput) * (100 - slippage) / 100).toFixed(6);

      return {
        amountIn,
        amountOut: expectedOutput,
        path: pathInfo.tokens,
        priceImpact: ethers.utils.formatUnits(pathInfo.priceImpact, 2), // Assuming 2 decimals for percentage
        platformFee: ethers.utils.formatUnits(platformFee, tokenInInfo.decimals),
        gasEstimate: pathInfo.gasEstimate.toString(),
        minimumReceived,
      };
    } catch (error) {
      console.error('Error getting swap quote:', error);
      return null;
    }
  }

  async executeSwap(
    tokenIn: string,
    tokenOut: string,
    amountIn: string,
    minimumAmountOut: string,
    signer: ethers.Signer,
    deadline?: number
  ): Promise<ethers.ContractTransaction> {
    try {
      const routerWithSigner = this.contracts.router.connect(signer);
      const userAddress = await signer.getAddress();
      const swapDeadline = deadline || Math.floor(Date.now() / 1000) + 1800; // 30 minutes

      const tokenInInfo = await this.getTokenInfo(tokenIn);
      const tokenOutInfo = await this.getTokenInfo(tokenOut);
      
      if (!tokenInInfo || !tokenOutInfo) {
        throw new Error('Token info not found');
      }

      const amountInWei = ethers.utils.parseUnits(amountIn, tokenInInfo.decimals);
      const minimumAmountOutWei = ethers.utils.parseUnits(minimumAmountOut, tokenOutInfo.decimals);

      // Get optimal path
      const path = await this.contracts.router.getOptimalSwapPath(tokenIn, tokenOut);
      
      if (path.length === 0) {
        throw new Error('No swap path found');
      }

      // Handle ETH swaps
      const wethAddress = KNOWN_TOKENS[this.network.chainId]?.WETH || KNOWN_TOKENS[1].WETH;

      // ETH to WETH
      if (tokenIn === ethers.constants.AddressZero && tokenOut === wethAddress) {
        return await routerWithSigner.swapETHForWETH({ value: amountInWei });
      }

      // WETH to ETH
      if (tokenIn === wethAddress && tokenOut === ethers.constants.AddressZero) {
        return await routerWithSigner.swapWETHForETH(amountInWei);
      }

      // ETH to Token
      if (tokenIn === ethers.constants.AddressZero) {
        return await routerWithSigner.swapExactETHForTokens(
          minimumAmountOutWei,
          path,
          userAddress,
          swapDeadline,
          { value: amountInWei }
        );
      }

      // Token to ETH
      if (tokenOut === ethers.constants.AddressZero) {
        return await routerWithSigner.swapExactTokensForETH(
          amountInWei,
          minimumAmountOutWei,
          path,
          userAddress,
          swapDeadline
        );
      }

      // Token to Token
      return await routerWithSigner.swapExactTokensForTokens(
        amountInWei,
        minimumAmountOutWei,
        path,
        userAddress,
        swapDeadline
      );
    } catch (error) {
      console.error('Error executing swap:', error);
      throw error;
    }
  }

  // Utility functions
  async approveToken(
    tokenAddress: string,
    spenderAddress: string,
    amount: string,
    signer: ethers.Signer
  ): Promise<ethers.ContractTransaction> {
    const tokenInfo = await this.getTokenInfo(tokenAddress);
    if (!tokenInfo) {
      throw new Error('Token info not found');
    }

    const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, signer);
    const amountWei = ethers.utils.parseUnits(amount, tokenInfo.decimals);
    
    return await tokenContract.approve(spenderAddress, amountWei);
  }

  async getAllowance(
    tokenAddress: string,
    ownerAddress: string,
    spenderAddress: string
  ): Promise<string> {
    try {
      const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, this.provider);
      const [allowance, decimals] = await Promise.all([
        tokenContract.allowance(ownerAddress, spenderAddress),
        tokenContract.decimals(),
      ]);

      return ethers.utils.formatUnits(allowance, decimals);
    } catch (error) {
      console.error('Error getting allowance:', error);
      return '0';
    }
  }

  // Test token functions (Sepolia only)
  async getTestTokens(): Promise<TokenInfo[]> {
    if (this.network.chainId !== 11155111) {
      return [];
    }

    // Test token addresses will be populated after deployment
    const testTokenAddresses: string[] = [
      // These will be updated with actual deployed addresses
    ];

    const tokens: TokenInfo[] = [];
    for (const address of testTokenAddresses) {
      const tokenInfo = await this.getTokenInfo(address);
      if (tokenInfo) {
        tokens.push(tokenInfo);
      }
    }

    return tokens;
  }

  async requestTestTokens(
    tokenAddress: string,
    userAddress: string,
    amount: string = '1000'
  ): Promise<ethers.ContractTransaction> {
    if (this.network.chainId !== 11155111) {
      throw new Error('Test tokens only available on Sepolia testnet');
    }

    console.log(`🪙 Requesting test tokens: ${amount} of ${tokenAddress} for ${userAddress}`);

    // For Sepolia, we'll use a mock faucet that mints tokens directly
    // In a real implementation, this would call actual faucet contracts

    try {
      // Get token info to determine decimals
      const tokenInfo = await this.getTokenInfo(tokenAddress);
      if (!tokenInfo) {
        throw new Error('Token info not found');
      }

      // Create a mock transaction for demonstration
      // In reality, you'd call the actual faucet contract
      const mockTx = {
        hash: '0x' + Math.random().toString(16).substring(2, 66),
        wait: async () => {
          console.log(`✅ Mock faucet: Minted ${amount} ${tokenInfo.symbol} to ${userAddress}`);
          return {
            status: 1,
            transactionHash: '0x' + Math.random().toString(16).substring(2, 66),
            blockNumber: Math.floor(Math.random() * 1000000),
            gasUsed: ethers.BigNumber.from('21000')
          };
        }
      };

      return mockTx as ethers.ContractTransaction;
    } catch (error) {
      console.error('❌ Error requesting test tokens:', error);
      throw new Error(`Failed to request test tokens: ${error}`);
    }
  }

  // Platform info
  async getPlatformInfo() {
    try {
      const [feeRate, feeRecipient] = await Promise.all([
        this.contracts.router.getPlatformFeeRate(),
        this.contracts.router.getPlatformFeeRecipient(),
      ]);

      return {
        feeRate: feeRate.toNumber() / 100, // Convert basis points to percentage
        feeRecipient,
        network: this.network.name,
        chainId: this.network.chainId,
      };
    } catch (error) {
      console.error('Error getting platform info:', error);
      return {
        feeRate: 0.3,
        feeRecipient: '******************************************',
        network: this.network.name,
        chainId: this.network.chainId,
      };
    }
  }

  // Liquidity Pool Functions
  async getAllPairs(): Promise<LiquidityPool[]> {
    try {
      console.log('🔍 Getting all liquidity pairs...');

      const factoryContract = new ethers.Contract(
        this.network.contracts.factory,
        FACTORY_ABI,
        this.provider
      );

      const pairsLength = await factoryContract.allPairsLength();
      console.log(`📊 Found ${pairsLength} pairs`);

      const pools: LiquidityPool[] = [];

      for (let i = 0; i < Math.min(pairsLength, 20); i++) { // Limit to first 20 pairs
        try {
          const pairAddress = await factoryContract.allPairs(i);
          const pool = await this.getPairInfo(pairAddress);
          if (pool) {
            pools.push(pool);
          }
        } catch (error) {
          console.error(`Error getting pair ${i}:`, error);
        }
      }

      return pools.sort((a, b) => parseFloat(b.reserve0) - parseFloat(a.reserve0));
    } catch (error) {
      console.error('❌ Error getting all pairs:', error);
      return [];
    }
  }

  async getPairInfo(pairAddress: string): Promise<LiquidityPool | null> {
    try {
      const pairContract = new ethers.Contract(pairAddress, PAIR_ABI, this.provider);

      const [token0Address, token1Address, reserves, totalSupply] = await Promise.all([
        pairContract.token0(),
        pairContract.token1(),
        pairContract.getReserves(),
        pairContract.totalSupply()
      ]);

      const [token0Info, token1Info] = await Promise.all([
        this.getTokenInfo(token0Address),
        this.getTokenInfo(token1Address)
      ]);

      if (!token0Info || !token1Info) {
        return null;
      }

      const reserve0 = ethers.utils.formatUnits(reserves.reserve0, token0Info.decimals);
      const reserve1 = ethers.utils.formatUnits(reserves.reserve1, token1Info.decimals);
      const totalSupplyFormatted = ethers.utils.formatEther(totalSupply);

      return {
        address: pairAddress,
        token0: token0Info,
        token1: token1Info,
        reserve0,
        reserve1,
        totalSupply: totalSupplyFormatted,
        userLiquidity: '0',
        userShare: '0',
        apr: (Math.random() * 20 + 5).toFixed(1) // Mock APR
      };
    } catch (error) {
      console.error(`❌ Error getting pair info for ${pairAddress}:`, error);
      return null;
    }
  }

  async getUserLiquidityPools(userAddress: string): Promise<LiquidityPool[]> {
    try {
      console.log('👤 Getting user liquidity pools for:', userAddress);

      const allPools = await this.getAllPairs();
      const userPools: LiquidityPool[] = [];

      for (const pool of allPools) {
        try {
          const pairContract = new ethers.Contract(pool.address, PAIR_ABI, this.provider);
          const userBalance = await pairContract.balanceOf(userAddress);

          if (userBalance.gt(0)) {
            const userLiquidity = ethers.utils.formatEther(userBalance);
            const userShare = ((parseFloat(userLiquidity) / parseFloat(pool.totalSupply)) * 100).toFixed(4);

            userPools.push({
              ...pool,
              userLiquidity,
              userShare
            });
          }
        } catch (error) {
          console.error(`Error checking user balance for pool ${pool.address}:`, error);
        }
      }

      return userPools;
    } catch (error) {
      console.error('❌ Error getting user liquidity pools:', error);
      return [];
    }
  }

  async addLiquidity(
    params: AddLiquidityParams,
    signer: ethers.Signer
  ): Promise<ethers.ContractTransaction> {
    console.log('💧 Adding liquidity:', params);

    const routerWithSigner = new ethers.Contract(
      this.network.contracts.router,
      ROUTER_ABI,
      signer
    );

    const amountADesired = ethers.utils.parseUnits(params.amountADesired, 18);
    const amountBDesired = ethers.utils.parseUnits(params.amountBDesired, 18);
    const amountAMin = ethers.utils.parseUnits(params.amountAMin, 18);
    const amountBMin = ethers.utils.parseUnits(params.amountBMin, 18);

    return await routerWithSigner.addLiquidity(
      params.tokenA,
      params.tokenB,
      amountADesired,
      amountBDesired,
      amountAMin,
      amountBMin,
      params.to,
      params.deadline
    );
  }

  async removeLiquidity(
    params: RemoveLiquidityParams,
    signer: ethers.Signer
  ): Promise<ethers.ContractTransaction> {
    console.log('💧 Removing liquidity:', params);

    const routerWithSigner = new ethers.Contract(
      this.network.contracts.router,
      ROUTER_ABI,
      signer
    );

    const liquidity = ethers.utils.parseEther(params.liquidity);
    const amountAMin = ethers.utils.parseUnits(params.amountAMin, 18);
    const amountBMin = ethers.utils.parseUnits(params.amountBMin, 18);

    return await routerWithSigner.removeLiquidity(
      params.tokenA,
      params.tokenB,
      liquidity,
      amountAMin,
      amountBMin,
      params.to,
      params.deadline
    );
  }

  async getPairAddress(tokenA: string, tokenB: string): Promise<string> {
    try {
      const factoryContract = new ethers.Contract(
        this.network.contracts.factory,
        FACTORY_ABI,
        this.provider
      );

      return await factoryContract.getPair(tokenA, tokenB);
    } catch (error) {
      console.error('❌ Error getting pair address:', error);
      return ethers.constants.AddressZero;
    }
  }
}
