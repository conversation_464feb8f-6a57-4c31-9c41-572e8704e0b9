import { ethers } from 'ethers';
import {
  NetworkConfig,
  ROUTER_ABI,
  TOKEN_REGISTRY_ABI,
  PRICE_ORACLE_ABI,
  SMART_ROUTER_ABI,
  ERC20_ABI,
  TEST_TOKEN_ABI,
  KNOWN_TOKENS
} from '../config/contracts';



export interface TokenInfo {
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  logoURI: string;
  website: string;
  marketCap: string;
  balance?: string;
  isActive: boolean;
  isVerified: boolean;
}

export interface SwapPath {
  tokens: string[];
  amounts: string[];
  expectedOutput: string;
  priceImpact: string;
  gasEstimate: string;
  isValid: boolean;
}

export interface SwapQuote {
  amountIn: string;
  amountOut: string;
  path: string[];
  priceImpact: string;
  platformFee: string;
  gasEstimate: string;
  minimumReceived: string;
}

export interface PriceInfo {
  price: string;
  reliable: boolean;
  source: 'chainlink' | 'twap' | 'spot';
  lastUpdated: number;
}

export class SwapSDK {
  private provider: ethers.providers.Provider;
  private network: NetworkConfig;
  private contracts: {
    router: ethers.Contract;
    tokenRegistry: ethers.Contract;
    priceOracle: ethers.Contract;
    smartRouter: ethers.Contract;
  };
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 300000; // 5 minutes

  constructor(provider: ethers.providers.Provider, network: NetworkConfig) {
    this.provider = provider;
    this.network = network;

    // Initialize contracts
    this.contracts = {
      router: new ethers.Contract(network.contracts.router, ROUTER_ABI, provider),
      tokenRegistry: new ethers.Contract(network.contracts.tokenRegistry, TOKEN_REGISTRY_ABI, provider),
      priceOracle: new ethers.Contract(network.contracts.priceOracle, PRICE_ORACLE_ABI, provider),
      smartRouter: new ethers.Contract(network.contracts.smartRouter, SMART_ROUTER_ABI, provider),
    };
  }

  // Cache utilities
  private getCacheKey(method: string, params: any[]): string {
    return `${method}_${JSON.stringify(params)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  // Token functions
  async getSupportedTokens(): Promise<TokenInfo[]> {
    const cacheKey = this.getCacheKey('getSupportedTokens', []);
    const cached = this.getFromCache<TokenInfo[]>(cacheKey);
    if (cached) return cached;

    try {
      const tokenAddresses = await this.contracts.tokenRegistry.getActiveTokens();
      const tokens: TokenInfo[] = [];

      for (const address of tokenAddresses) {
        const tokenInfo = await this.getTokenInfo(address);
        if (tokenInfo) {
          tokens.push(tokenInfo);
        }
      }

      // Sort by market cap
      tokens.sort((a, b) => parseFloat(b.marketCap) - parseFloat(a.marketCap));

      this.setCache(cacheKey, tokens);
      return tokens;
    } catch (error) {
      console.error('Error getting supported tokens:', error);
      return [];
    }
  }

  async getTokenInfo(address: string): Promise<TokenInfo | null> {
    const cacheKey = this.getCacheKey('getTokenInfo', [address]);
    const cached = this.getFromCache<TokenInfo>(cacheKey);
    if (cached) return cached;

    try {
      // Handle ETH specially
      if (address === ethers.constants.AddressZero || address === '******************************************') {
        const ethInfo: TokenInfo = {
          symbol: 'ETH',
          name: 'Ethereum',
          address: ethers.constants.AddressZero,
          decimals: 18,
          logoURI: 'https://tokens.1inch.io/******************************************.png',
          website: 'https://ethereum.org',
          marketCap: '500000000000',
          isActive: true,
          isVerified: true,
        };
        this.setCache(cacheKey, ethInfo);
        return ethInfo;
      }

      const tokenContract = new ethers.Contract(address, ERC20_ABI, this.provider);
      const [registryInfo, symbol, name, decimals] = await Promise.all([
        this.contracts.tokenRegistry.getTokenInfo(address),
        tokenContract.symbol(),
        tokenContract.name(),
        tokenContract.decimals(),
      ]);

      const tokenInfo: TokenInfo = {
        symbol: registryInfo.symbol || symbol,
        name: registryInfo.name || name,
        address,
        decimals,
        logoURI: registryInfo.logoURI || `https://tokens.1inch.io/0x${symbol.toLowerCase()}.png`,
        website: registryInfo.website || '',
        marketCap: ethers.utils.formatEther(registryInfo.marketCap || '0'),
        isActive: registryInfo.isActive,
        isVerified: registryInfo.isVerified,
      };

      this.setCache(cacheKey, tokenInfo);
      return tokenInfo;
    } catch (error) {
      console.error(`Error getting token info for ${address}:`, error);
      return null;
    }
  }

  async getTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
    try {
      if (tokenAddress === ethers.constants.AddressZero || tokenAddress.toLowerCase() === 'eth') {
        const balance = await this.provider.getBalance(userAddress);
        return ethers.utils.formatEther(balance);
      }

      const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, this.provider);
      const [balance, decimals] = await Promise.all([
        tokenContract.balanceOf(userAddress),
        tokenContract.decimals(),
      ]);

      return ethers.utils.formatUnits(balance, decimals);
    } catch (error) {
      console.error(`Error getting token balance for ${tokenAddress}:`, error);
      return '0';
    }
  }

  // Price functions
  async getTokenPrice(tokenA: string, tokenB: string): Promise<PriceInfo> {
    const cacheKey = this.getCacheKey('getTokenPrice', [tokenA, tokenB]);
    const cached = this.getFromCache<PriceInfo>(cacheKey);
    if (cached) return cached;

    try {
      const [combinedPrice, chainlinkAvailable, twapAvailable] = await Promise.all([
        this.contracts.priceOracle.getCombinedPrice(tokenA, tokenB),
        this.contracts.priceOracle.isChainlinkAvailable(tokenA),
        this.contracts.priceOracle.isTWAPAvailable(tokenA), // Assuming pair exists
      ]);

      const priceInfo: PriceInfo = {
        price: ethers.utils.formatEther(combinedPrice.price),
        reliable: combinedPrice.reliable,
        source: chainlinkAvailable ? 'chainlink' : twapAvailable ? 'twap' : 'spot',
        lastUpdated: Date.now(),
      };

      this.setCache(cacheKey, priceInfo);
      return priceInfo;
    } catch (error) {
      console.error(`Error getting price for ${tokenA}/${tokenB}:`, error);
      return {
        price: '0',
        reliable: false,
        source: 'spot',
        lastUpdated: Date.now(),
      };
    }
  }

  // Swap functions
  async getSwapQuote(
    tokenIn: string,
    tokenOut: string,
    amountIn: string,
    slippage: number = 0.5
  ): Promise<SwapQuote | null> {
    try {
      const tokenInInfo = await this.getTokenInfo(tokenIn);
      const tokenOutInfo = await this.getTokenInfo(tokenOut);
      
      if (!tokenInInfo || !tokenOutInfo) {
        throw new Error('Token info not found');
      }

      const amountInWei = ethers.utils.parseUnits(amountIn, tokenInInfo.decimals);
      
      // Get optimal path
      const pathInfo = await this.contracts.smartRouter.findOptimalPath(tokenIn, tokenOut, amountInWei);
      
      if (!pathInfo.isValid) {
        throw new Error('No valid path found');
      }

      // Calculate platform fee
      const platformFeeRate = await this.contracts.router.getPlatformFeeRate();
      const platformFee = amountInWei.mul(platformFeeRate).div(10000);
      
      // Calculate minimum received with slippage
      const expectedOutput = ethers.utils.formatUnits(pathInfo.expectedOutput, tokenOutInfo.decimals);
      const minimumReceived = (parseFloat(expectedOutput) * (100 - slippage) / 100).toFixed(6);

      return {
        amountIn,
        amountOut: expectedOutput,
        path: pathInfo.tokens,
        priceImpact: ethers.utils.formatUnits(pathInfo.priceImpact, 2), // Assuming 2 decimals for percentage
        platformFee: ethers.utils.formatUnits(platformFee, tokenInInfo.decimals),
        gasEstimate: pathInfo.gasEstimate.toString(),
        minimumReceived,
      };
    } catch (error) {
      console.error('Error getting swap quote:', error);
      return null;
    }
  }

  async executeSwap(
    tokenIn: string,
    tokenOut: string,
    amountIn: string,
    minimumAmountOut: string,
    signer: ethers.Signer,
    deadline?: number
  ): Promise<ethers.ContractTransaction> {
    try {
      const routerWithSigner = this.contracts.router.connect(signer);
      const userAddress = await signer.getAddress();
      const swapDeadline = deadline || Math.floor(Date.now() / 1000) + 1800; // 30 minutes

      const tokenInInfo = await this.getTokenInfo(tokenIn);
      const tokenOutInfo = await this.getTokenInfo(tokenOut);
      
      if (!tokenInInfo || !tokenOutInfo) {
        throw new Error('Token info not found');
      }

      const amountInWei = ethers.utils.parseUnits(amountIn, tokenInInfo.decimals);
      const minimumAmountOutWei = ethers.utils.parseUnits(minimumAmountOut, tokenOutInfo.decimals);

      // Get optimal path
      const path = await this.contracts.router.getOptimalSwapPath(tokenIn, tokenOut);
      
      if (path.length === 0) {
        throw new Error('No swap path found');
      }

      // Handle ETH swaps
      const wethAddress = KNOWN_TOKENS[this.network.chainId]?.WETH || KNOWN_TOKENS[1].WETH;

      // ETH to WETH
      if (tokenIn === ethers.constants.AddressZero && tokenOut === wethAddress) {
        return await routerWithSigner.swapETHForWETH({ value: amountInWei });
      }

      // WETH to ETH
      if (tokenIn === wethAddress && tokenOut === ethers.constants.AddressZero) {
        return await routerWithSigner.swapWETHForETH(amountInWei);
      }

      // ETH to Token
      if (tokenIn === ethers.constants.AddressZero) {
        return await routerWithSigner.swapExactETHForTokens(
          minimumAmountOutWei,
          path,
          userAddress,
          swapDeadline,
          { value: amountInWei }
        );
      }

      // Token to ETH
      if (tokenOut === ethers.constants.AddressZero) {
        return await routerWithSigner.swapExactTokensForETH(
          amountInWei,
          minimumAmountOutWei,
          path,
          userAddress,
          swapDeadline
        );
      }

      // Token to Token
      return await routerWithSigner.swapExactTokensForTokens(
        amountInWei,
        minimumAmountOutWei,
        path,
        userAddress,
        swapDeadline
      );
    } catch (error) {
      console.error('Error executing swap:', error);
      throw error;
    }
  }

  // Utility functions
  async approveToken(
    tokenAddress: string,
    spenderAddress: string,
    amount: string,
    signer: ethers.Signer
  ): Promise<ethers.ContractTransaction> {
    const tokenInfo = await this.getTokenInfo(tokenAddress);
    if (!tokenInfo) {
      throw new Error('Token info not found');
    }

    const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, signer);
    const amountWei = ethers.utils.parseUnits(amount, tokenInfo.decimals);
    
    return await tokenContract.approve(spenderAddress, amountWei);
  }

  async getAllowance(
    tokenAddress: string,
    ownerAddress: string,
    spenderAddress: string
  ): Promise<string> {
    try {
      const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, this.provider);
      const [allowance, decimals] = await Promise.all([
        tokenContract.allowance(ownerAddress, spenderAddress),
        tokenContract.decimals(),
      ]);

      return ethers.utils.formatUnits(allowance, decimals);
    } catch (error) {
      console.error('Error getting allowance:', error);
      return '0';
    }
  }

  // Test token functions (Sepolia only)
  async getTestTokens(): Promise<TokenInfo[]> {
    if (this.network.chainId !== 11155111) {
      return [];
    }

    // Test token addresses will be populated after deployment
    const testTokenAddresses: string[] = [
      // These will be updated with actual deployed addresses
    ];

    const tokens: TokenInfo[] = [];
    for (const address of testTokenAddresses) {
      const tokenInfo = await this.getTokenInfo(address);
      if (tokenInfo) {
        tokens.push(tokenInfo);
      }
    }

    return tokens;
  }

  async requestTestTokens(
    tokenAddress: string,
    amount: string,
    signer: ethers.Signer
  ): Promise<ethers.ContractTransaction> {
    if (this.network.chainId !== 11155111) {
      throw new Error('Test tokens only available on Sepolia');
    }

    const tokenInfo = await this.getTokenInfo(tokenAddress);
    if (!tokenInfo) {
      throw new Error('Token info not found');
    }

    const tokenContract = new ethers.Contract(tokenAddress, TEST_TOKEN_ABI, signer);
    const amountWei = ethers.utils.parseUnits(amount, tokenInfo.decimals);
    
    return await tokenContract.faucet(amountWei);
  }

  // Platform info
  async getPlatformInfo() {
    try {
      const [feeRate, feeRecipient] = await Promise.all([
        this.contracts.router.getPlatformFeeRate(),
        this.contracts.router.getPlatformFeeRecipient(),
      ]);

      return {
        feeRate: feeRate.toNumber() / 100, // Convert basis points to percentage
        feeRecipient,
        network: this.network.name,
        chainId: this.network.chainId,
      };
    } catch (error) {
      console.error('Error getting platform info:', error);
      return {
        feeRate: 0.3,
        feeRecipient: '******************************************',
        network: this.network.name,
        chainId: this.network.chainId,
      };
    }
  }
}
