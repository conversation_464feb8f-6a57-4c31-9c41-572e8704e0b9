const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("GovernanceToken", function () {
    let governanceToken;
    let owner, minter, user1, user2;

    beforeEach(async function () {
        [owner, minter, user1, user2] = await ethers.getSigners();

        // Deploy GovernanceToken
        const GovernanceToken = await ethers.getContractFactory("GovernanceToken");
        governanceToken = await GovernanceToken.deploy();
        await governanceToken.deployed();
    });

    describe("Deployment", function () {
        it("Should set correct initial parameters", async function () {
            expect(await governanceToken.name()).to.equal("DEX Governance Token");
            expect(await governanceToken.symbol()).to.equal("DEXGOV");
            expect(await governanceToken.decimals()).to.equal(18);
            
            const initialSupply = ethers.utils.parseEther("100000000"); // 100M
            expect(await governanceToken.totalSupply()).to.equal(initialSupply);
            expect(await governanceToken.balanceOf(owner.address)).to.equal(initialSupply);
        });

        it("Should set deployer as initial minter", async function () {
            expect(await governanceToken.minters(owner.address)).to.be.true;
        });

        it("Should set correct minting cap", async function () {
            const mintingCap = ethers.utils.parseEther("50000000"); // 50M
            expect(await governanceToken.mintingCap()).to.equal(mintingCap);
        });
    });

    describe("Minting", function () {
        beforeEach(async function () {
            await governanceToken.addMinter(minter.address);
        });

        it("Should allow minter to mint tokens", async function () {
            const mintAmount = ethers.utils.parseEther("1000");
            
            await expect(
                governanceToken.connect(minter).mint(user1.address, mintAmount)
            ).to.emit(governanceToken, "Transfer")
             .withArgs(ethers.constants.AddressZero, user1.address, mintAmount);

            expect(await governanceToken.balanceOf(user1.address)).to.equal(mintAmount);
        });

        it("Should not allow non-minter to mint", async function () {
            const mintAmount = ethers.utils.parseEther("1000");
            
            await expect(
                governanceToken.connect(user1).mint(user2.address, mintAmount)
            ).to.be.revertedWith("GovernanceToken: NOT_MINTER");
        });

        it("Should enforce annual minting cap", async function () {
            const mintingCap = await governanceToken.mintingCap();
            
            await expect(
                governanceToken.connect(minter).mint(user1.address, mintingCap.add(1))
            ).to.be.revertedWith("GovernanceToken: EXCEEDS_MINTING_CAP");
        });

        it("Should enforce max supply", async function () {
            const maxSupply = ethers.utils.parseEther("1000000000"); // 1B
            const currentSupply = await governanceToken.totalSupply();
            const remainingSupply = maxSupply.sub(currentSupply);
            
            await expect(
                governanceToken.connect(minter).mint(user1.address, remainingSupply.add(1))
            ).to.be.revertedWith("GovernanceToken: EXCEEDS_MAX_SUPPLY");
        });

        it("Should reset minting cap after one year", async function () {
            const mintingCap = await governanceToken.mintingCap();
            
            // Mint up to cap
            await governanceToken.connect(minter).mint(user1.address, mintingCap);
            
            // Should fail to mint more
            await expect(
                governanceToken.connect(minter).mint(user1.address, 1)
            ).to.be.revertedWith("GovernanceToken: EXCEEDS_MINTING_CAP");
            
            // Fast forward one year
            await ethers.provider.send("evm_increaseTime", [365 * 24 * 60 * 60]);
            await ethers.provider.send("evm_mine");
            
            // Should be able to mint again
            await expect(
                governanceToken.connect(minter).mint(user1.address, ethers.utils.parseEther("1000"))
            ).to.not.be.reverted;
        });

        it("Should track current year minted correctly", async function () {
            const mintAmount = ethers.utils.parseEther("1000");
            
            expect(await governanceToken.currentYearMinted()).to.equal(0);
            
            await governanceToken.connect(minter).mint(user1.address, mintAmount);
            
            expect(await governanceToken.currentYearMinted()).to.equal(mintAmount);
        });
    });

    describe("Minter Management", function () {
        it("Should allow owner to add minter", async function () {
            await expect(
                governanceToken.addMinter(minter.address)
            ).to.emit(governanceToken, "MinterAdded")
             .withArgs(minter.address);

            expect(await governanceToken.minters(minter.address)).to.be.true;
        });

        it("Should allow owner to remove minter", async function () {
            await governanceToken.addMinter(minter.address);
            
            await expect(
                governanceToken.removeMinter(minter.address)
            ).to.emit(governanceToken, "MinterRemoved")
             .withArgs(minter.address);

            expect(await governanceToken.minters(minter.address)).to.be.false;
        });

        it("Should not allow non-owner to manage minters", async function () {
            await expect(
                governanceToken.connect(user1).addMinter(minter.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");

            await expect(
                governanceToken.connect(user1).removeMinter(owner.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });

        it("Should not allow adding duplicate minter", async function () {
            await governanceToken.addMinter(minter.address);
            
            await expect(
                governanceToken.addMinter(minter.address)
            ).to.be.revertedWith("GovernanceToken: ALREADY_MINTER");
        });

        it("Should not allow removing non-minter", async function () {
            await expect(
                governanceToken.removeMinter(minter.address)
            ).to.be.revertedWith("GovernanceToken: NOT_MINTER");
        });
    });

    describe("Vesting", function () {
        const vestingAmount = ethers.utils.parseEther("10000");
        const vestingDuration = 365 * 24 * 60 * 60; // 1 year
        const cliffDuration = 90 * 24 * 60 * 60; // 3 months

        it("Should create vesting schedule", async function () {
            const startTime = (await ethers.provider.getBlock("latest")).timestamp + 100;
            
            await expect(
                governanceToken.createVestingSchedule(
                    user1.address,
                    vestingAmount,
                    startTime,
                    vestingDuration,
                    cliffDuration,
                    true // revocable
                )
            ).to.emit(governanceToken, "VestingScheduleCreated")
             .withArgs(user1.address, 0, vestingAmount, vestingDuration);

            expect(await governanceToken.getVestingScheduleCount(user1.address)).to.equal(1);
            expect(await governanceToken.totalVestedAmount(user1.address)).to.equal(vestingAmount);
        });

        it("Should not allow vesting before cliff", async function () {
            const startTime = (await ethers.provider.getBlock("latest")).timestamp + 100;
            
            await governanceToken.createVestingSchedule(
                user1.address,
                vestingAmount,
                startTime,
                vestingDuration,
                cliffDuration,
                true
            );

            // Fast forward to just before cliff
            await ethers.provider.send("evm_setNextBlockTimestamp", [startTime + cliffDuration - 1]);
            await ethers.provider.send("evm_mine");

            const vestedAmount = await governanceToken.getVestedAmount(user1.address, 0);
            expect(vestedAmount).to.equal(0);
        });

        it("Should allow vesting after cliff", async function () {
            const startTime = (await ethers.provider.getBlock("latest")).timestamp + 100;
            
            await governanceToken.createVestingSchedule(
                user1.address,
                vestingAmount,
                startTime,
                vestingDuration,
                cliffDuration,
                true
            );

            // Fast forward to after cliff (halfway through vesting)
            await ethers.provider.send("evm_setNextBlockTimestamp", [startTime + vestingDuration / 2]);
            await ethers.provider.send("evm_mine");

            const vestedAmount = await governanceToken.getVestedAmount(user1.address, 0);
            expect(vestedAmount).to.be.closeTo(vestingAmount.div(2), ethers.utils.parseEther("100"));
        });

        it("Should allow releasing vested tokens", async function () {
            const startTime = (await ethers.provider.getBlock("latest")).timestamp + 100;
            
            await governanceToken.createVestingSchedule(
                user1.address,
                vestingAmount,
                startTime,
                vestingDuration,
                cliffDuration,
                true
            );

            // Fast forward to after cliff
            await ethers.provider.send("evm_setNextBlockTimestamp", [startTime + vestingDuration / 2]);
            await ethers.provider.send("evm_mine");

            const initialBalance = await governanceToken.balanceOf(user1.address);
            
            await expect(
                governanceToken.releaseVestedTokens(user1.address, 0)
            ).to.emit(governanceToken, "TokensVested");

            const finalBalance = await governanceToken.balanceOf(user1.address);
            expect(finalBalance).to.be.gt(initialBalance);
        });

        it("Should allow revoking revocable vesting schedule", async function () {
            const startTime = (await ethers.provider.getBlock("latest")).timestamp + 100;
            
            await governanceToken.createVestingSchedule(
                user1.address,
                vestingAmount,
                startTime,
                vestingDuration,
                cliffDuration,
                true // revocable
            );

            // Fast forward to halfway through vesting
            await ethers.provider.send("evm_setNextBlockTimestamp", [startTime + vestingDuration / 2]);
            await ethers.provider.send("evm_mine");

            await expect(
                governanceToken.revokeVestingSchedule(user1.address, 0)
            ).to.emit(governanceToken, "VestingScheduleRevoked")
             .withArgs(user1.address, 0);
        });

        it("Should not allow revoking non-revocable vesting schedule", async function () {
            const startTime = (await ethers.provider.getBlock("latest")).timestamp + 100;
            
            await governanceToken.createVestingSchedule(
                user1.address,
                vestingAmount,
                startTime,
                vestingDuration,
                cliffDuration,
                false // not revocable
            );

            await expect(
                governanceToken.revokeVestingSchedule(user1.address, 0)
            ).to.be.revertedWith("GovernanceToken: NOT_REVOCABLE");
        });
    });

    describe("Governance Features", function () {
        it("Should support voting delegation", async function () {
            const amount = ethers.utils.parseEther("1000");
            await governanceToken.transfer(user1.address, amount);

            // Delegate votes to user2
            await governanceToken.connect(user1).delegate(user2.address);

            expect(await governanceToken.getVotes(user2.address)).to.equal(amount);
            expect(await governanceToken.getVotes(user1.address)).to.equal(0);
        });

        it("Should track voting power correctly", async function () {
            const amount = ethers.utils.parseEther("1000");
            await governanceToken.transfer(user1.address, amount);

            // Self-delegate to activate voting power
            await governanceToken.connect(user1).delegate(user1.address);

            expect(await governanceToken.getVotes(user1.address)).to.equal(amount);
        });

        it("Should support permit functionality", async function () {
            const amount = ethers.utils.parseEther("1000");
            const deadline = Math.floor(Date.now() / 1000) + 3600;

            // This is a simplified test - in practice, you'd need to sign the permit
            // For now, just check that the function exists and has correct interface
            expect(governanceToken.permit).to.be.a('function');
        });
    });

    describe("Pause Functionality", function () {
        it("Should allow owner to pause/unpause", async function () {
            await governanceToken.pause();
            expect(await governanceToken.paused()).to.be.true;

            await governanceToken.unpause();
            expect(await governanceToken.paused()).to.be.false;
        });

        it("Should block transfers when paused", async function () {
            const amount = ethers.utils.parseEther("1000");
            
            await governanceToken.pause();
            
            await expect(
                governanceToken.transfer(user1.address, amount)
            ).to.be.revertedWith("Pausable: paused");
        });

        it("Should not allow non-owner to pause", async function () {
            await expect(
                governanceToken.connect(user1).pause()
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });
    });

    describe("View Functions", function () {
        it("Should return remaining minting capacity", async function () {
            const remainingCapacity = await governanceToken.getRemainingMintingCapacity();
            expect(remainingCapacity).to.equal(await governanceToken.mintingCap());
        });

        it("Should return correct releasable amount", async function () {
            const startTime = (await ethers.provider.getBlock("latest")).timestamp + 100;
            const vestingAmount = ethers.utils.parseEther("10000");
            
            await governanceToken.createVestingSchedule(
                user1.address,
                vestingAmount,
                startTime,
                365 * 24 * 60 * 60,
                90 * 24 * 60 * 60,
                true
            );

            // Before cliff
            let releasableAmount = await governanceToken.getReleasableAmount(user1.address, 0);
            expect(releasableAmount).to.equal(0);

            // After cliff
            await ethers.provider.send("evm_setNextBlockTimestamp", [startTime + 180 * 24 * 60 * 60]);
            await ethers.provider.send("evm_mine");

            releasableAmount = await governanceToken.getReleasableAmount(user1.address, 0);
            expect(releasableAmount).to.be.gt(0);
        });
    });

    describe("Edge Cases", function () {
        it("Should handle zero address checks", async function () {
            await expect(
                governanceToken.addMinter(ethers.constants.AddressZero)
            ).to.be.revertedWith("GovernanceToken: ZERO_ADDRESS");

            await expect(
                governanceToken.mint(ethers.constants.AddressZero, ethers.utils.parseEther("1000"))
            ).to.be.revertedWith("GovernanceToken: ZERO_ADDRESS");
        });

        it("Should handle zero amount minting", async function () {
            await governanceToken.addMinter(minter.address);
            
            await expect(
                governanceToken.connect(minter).mint(user1.address, 0)
            ).to.be.revertedWith("GovernanceToken: ZERO_AMOUNT");
        });

        it("Should handle invalid vesting parameters", async function () {
            const startTime = (await ethers.provider.getBlock("latest")).timestamp + 100;
            
            // Invalid cliff duration
            await expect(
                governanceToken.createVestingSchedule(
                    user1.address,
                    ethers.utils.parseEther("1000"),
                    startTime,
                    365 * 24 * 60 * 60,
                    400 * 24 * 60 * 60, // Cliff longer than duration
                    true
                )
            ).to.be.revertedWith("GovernanceToken: INVALID_CLIFF");

            // Invalid start time
            await expect(
                governanceToken.createVestingSchedule(
                    user1.address,
                    ethers.utils.parseEther("1000"),
                    startTime - 200, // Past time
                    365 * 24 * 60 * 60,
                    90 * 24 * 60 * 60,
                    true
                )
            ).to.be.revertedWith("GovernanceToken: INVALID_START_TIME");
        });
    });
});
