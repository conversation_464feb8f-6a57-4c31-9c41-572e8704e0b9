const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Integration Tests", function () {
    let factory, router, tokenRegistry, securityManager, priceOracle, smartRouter;
    let weth, tokenA, tokenB;
    let owner, user1, user2;

    beforeEach(async function () {
        [owner, user1, user2] = await ethers.getSigners();

        // Deploy WETH
        const WETH9 = await ethers.getContractFactory("WETH9");
        weth = await WETH9.deploy();
        await weth.deployed();

        // Deploy Factory
        const SwapFactory = await ethers.getContractFactory("SwapFactory");
        factory = await SwapFactory.deploy(owner.address);
        await factory.deployed();

        // Deploy TokenRegistry
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        tokenRegistry = await TokenRegistry.deploy();
        await tokenRegistry.deployed();

        // Deploy SecurityManager
        const SecurityManager = await ethers.getContractFactory("SecurityManager");
        securityManager = await SecurityManager.deploy();
        await securityManager.deployed();

        // Deploy PriceOracle
        const PriceOracle = await ethers.getContractFactory("PriceOracle");
        priceOracle = await PriceOracle.deploy(factory.address, weth.address);
        await priceOracle.deployed();

        // Deploy SmartRouter
        const SmartRouter = await ethers.getContractFactory("SmartRouter");
        smartRouter = await SmartRouter.deploy(
            factory.address,
            weth.address,
            tokenRegistry.address,
            priceOracle.address
        );
        await smartRouter.deployed();

        // Deploy SwapRouter
        const SwapRouter = await ethers.getContractFactory("SwapRouter");
        router = await SwapRouter.deploy(
            factory.address,
            weth.address,
            tokenRegistry.address,
            securityManager.address
        );
        await router.deployed();

        // Deploy test tokens
        const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
        tokenA = await ERC20Mock.deploy("Token A", "TKA", ethers.utils.parseEther("1000000"));
        tokenB = await ERC20Mock.deploy("Token B", "TKB", ethers.utils.parseEther("1000000"));
        
        await tokenA.deployed();
        await tokenB.deployed();

        // Add tokens to registry
        await tokenRegistry.addToken(
            tokenA.address, "TKA", "Token A", 18, "", "", 1000000
        );
        await tokenRegistry.addToken(
            tokenB.address, "TKB", "Token B", 18, "", "", 1000000
        );

        // Create pair
        await factory.createPair(tokenA.address, tokenB.address);
        const pairAddress = await factory.getPair(tokenA.address, tokenB.address);

        // Add liquidity
        await tokenA.transfer(pairAddress, ethers.utils.parseEther("1000"));
        await tokenB.transfer(pairAddress, ethers.utils.parseEther("1000"));
        
        const SwapPair = await ethers.getContractFactory("SwapPair");
        const pair = SwapPair.attach(pairAddress);
        await pair.mint(owner.address);

        // Transfer tokens to users
        await tokenA.transfer(user1.address, ethers.utils.parseEther("1000"));
        await tokenB.transfer(user1.address, ethers.utils.parseEther("1000"));
        await tokenA.transfer(user2.address, ethers.utils.parseEther("1000"));
        await tokenB.transfer(user2.address, ethers.utils.parseEther("1000"));
    });

    describe("End-to-End Swap Flow", function () {
        it("Should complete a full swap with all security checks", async function () {
            const swapAmount = ethers.utils.parseEther("10");
            const minAmountOut = ethers.utils.parseEther("9");

            // Approve tokens
            await tokenA.connect(user1).approve(router.address, swapAmount);

            // Get initial balances
            const initialBalanceA = await tokenA.balanceOf(user1.address);
            const initialBalanceB = await tokenB.balanceOf(user1.address);

            // Perform swap
            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await router.connect(user1).swapExactTokensForTokens(
                swapAmount,
                minAmountOut,
                [tokenA.address, tokenB.address],
                user1.address,
                deadline
            );

            // Check final balances
            const finalBalanceA = await tokenA.balanceOf(user1.address);
            const finalBalanceB = await tokenB.balanceOf(user1.address);

            expect(finalBalanceA).to.equal(initialBalanceA.sub(swapAmount));
            expect(finalBalanceB).to.be.gt(initialBalanceB);
            expect(finalBalanceB.sub(initialBalanceB)).to.be.gte(minAmountOut);
        });

        it("Should block swap when security limits are exceeded", async function () {
            const largeSwapAmount = ethers.utils.parseEther("1001"); // Exceeds circuit breaker limit

            await tokenA.connect(user1).approve(router.address, largeSwapAmount);

            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await expect(
                router.connect(user1).swapExactTokensForTokens(
                    largeSwapAmount,
                    0,
                    [tokenA.address, tokenB.address],
                    user1.address,
                    deadline
                )
            ).to.be.revertedWith("SecurityManager: CIRCUIT_BREAKER_VOLUME_BLOCK");
        });

        it("Should block rapid consecutive swaps (MEV protection)", async function () {
            const swapAmount = ethers.utils.parseEther("10");

            await tokenA.connect(user1).approve(router.address, swapAmount.mul(2));

            const deadline = Math.floor(Date.now() / 1000) + 3600;

            // First swap should succeed
            await router.connect(user1).swapExactTokensForTokens(
                swapAmount,
                0,
                [tokenA.address, tokenB.address],
                user1.address,
                deadline
            );

            // Second swap in same block should fail
            await expect(
                router.connect(user1).swapExactTokensForTokens(
                    swapAmount,
                    0,
                    [tokenA.address, tokenB.address],
                    user1.address,
                    deadline
                )
            ).to.be.revertedWith("SecurityManager: MEV_PROTECTION_BLOCK_DELAY");
        });

        it("Should reject unsupported tokens", async function () {
            // Deploy unsupported token
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            const unsupportedToken = await ERC20Mock.deploy("Unsupported", "UNS", ethers.utils.parseEther("1000"));
            await unsupportedToken.deployed();

            await unsupportedToken.transfer(user1.address, ethers.utils.parseEther("100"));
            await unsupportedToken.connect(user1).approve(router.address, ethers.utils.parseEther("100"));

            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await expect(
                router.connect(user1).swapExactTokensForTokens(
                    ethers.utils.parseEther("10"),
                    0,
                    [unsupportedToken.address, tokenB.address],
                    user1.address,
                    deadline
                )
            ).to.be.revertedWith("SwapRouter: UNSUPPORTED_TOKEN");
        });
    });

    describe("Smart Routing Integration", function () {
        beforeEach(async function () {
            // Create additional pairs for multi-hop routing
            await factory.createPair(tokenA.address, weth.address);
            await factory.createPair(tokenB.address, weth.address);

            // Add liquidity to WETH pairs
            const pairAWETH = await factory.getPair(tokenA.address, weth.address);
            const pairBWETH = await factory.getPair(tokenB.address, weth.address);

            await tokenA.transfer(pairAWETH, ethers.utils.parseEther("1000"));
            await weth.deposit({ value: ethers.utils.parseEther("1000") });
            await weth.transfer(pairAWETH, ethers.utils.parseEther("1000"));

            await tokenB.transfer(pairBWETH, ethers.utils.parseEther("1000"));
            await weth.deposit({ value: ethers.utils.parseEther("1000") });
            await weth.transfer(pairBWETH, ethers.utils.parseEther("1000"));

            const SwapPair = await ethers.getContractFactory("SwapPair");
            await SwapPair.attach(pairAWETH).mint(owner.address);
            await SwapPair.attach(pairBWETH).mint(owner.address);
        });

        it("Should find optimal path using SmartRouter", async function () {
            const path = await smartRouter.findOptimalPath(
                tokenA.address,
                tokenB.address,
                ethers.utils.parseEther("10")
            );

            expect(path.isValid).to.be.true;
            expect(path.expectedOutput).to.be.gt(0);
            expect(path.priceImpact).to.be.lte(1000); // Max 10%
        });

        it("Should provide multiple routing options", async function () {
            const pathOptions = await smartRouter.getPathOptions(
                tokenA.address,
                tokenB.address,
                ethers.utils.parseEther("10"),
                3
            );

            expect(pathOptions.length).to.be.gte(1);
            
            // Should have direct path and potentially multi-hop paths
            const directPath = pathOptions.find(p => p.tokens.length === 2);
            expect(directPath).to.not.be.undefined;
        });

        it("Should calculate route details accurately", async function () {
            const route = await smartRouter.getRouteDetails(
                tokenA.address,
                tokenB.address,
                ethers.utils.parseEther("10")
            );

            expect(route.totalAmountIn).to.equal(ethers.utils.parseEther("10"));
            expect(route.totalAmountOut).to.be.gt(0);
            expect(route.totalFee).to.be.gt(0);
            expect(route.steps.length).to.be.gte(1);

            // Verify step consistency
            let totalFee = ethers.BigNumber.from(0);
            for (const step of route.steps) {
                expect(step.amountIn).to.be.gt(0);
                expect(step.amountOut).to.be.gt(0);
                expect(step.fee).to.be.gt(0);
                totalFee = totalFee.add(step.fee);
            }
            expect(totalFee).to.equal(route.totalFee);
        });
    });

    describe("Price Oracle Integration", function () {
        beforeEach(async function () {
            const pairAddress = await factory.getPair(tokenA.address, tokenB.address);
            
            // Configure TWAP for the pair
            await priceOracle.configureTWAP(pairAddress, 1800, 8, true); // 30 min period, 8 observations
        });

        it("Should get spot price from pair", async function () {
            const pairAddress = await factory.getPair(tokenA.address, tokenB.address);
            const [price0, price1] = await priceOracle.getSpotPrice(pairAddress);

            expect(price0).to.be.gt(0);
            expect(price1).to.be.gt(0);
        });

        it("Should update TWAP observations", async function () {
            const pairAddress = await factory.getPair(tokenA.address, tokenB.address);
            
            // Update TWAP
            await priceOracle.update(pairAddress);
            
            // Check if TWAP is available
            const isAvailable = await priceOracle.isTWAPAvailable(pairAddress);
            expect(isAvailable).to.be.true;
        });

        it("Should get combined price with reliability check", async function () {
            const [price, reliable] = await priceOracle.getCombinedPrice(tokenA.address, tokenB.address);
            
            expect(price).to.be.gt(0);
            // Reliability depends on available price sources
        });
    });

    describe("Emergency Scenarios", function () {
        it("Should handle emergency mode activation", async function () {
            // Add guardian
            await securityManager.addGuardian(user2.address);
            
            // Activate emergency mode
            await securityManager.connect(user2).activateEmergencyMode("Test emergency");
            
            // Swaps should be blocked
            await tokenA.connect(user1).approve(router.address, ethers.utils.parseEther("10"));
            
            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await expect(
                router.connect(user1).swapExactTokensForTokens(
                    ethers.utils.parseEther("10"),
                    0,
                    [tokenA.address, tokenB.address],
                    user1.address,
                    deadline
                )
            ).to.be.revertedWith("Pausable: paused");
        });

        it("Should handle factory pause", async function () {
            // Pause factory
            await factory.setPaused(true);
            
            // New pair creation should be blocked
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            const newToken = await ERC20Mock.deploy("New Token", "NEW", ethers.utils.parseEther("1000"));
            await newToken.deployed();
            
            await expect(
                factory.createPair(tokenA.address, newToken.address)
            ).to.be.revertedWith("SwapFactory: PAUSED");
        });
    });

    describe("Gas Optimization", function () {
        it("Should use reasonable gas for simple swaps", async function () {
            const swapAmount = ethers.utils.parseEther("10");
            
            await tokenA.connect(user1).approve(router.address, swapAmount);
            
            const deadline = Math.floor(Date.now() / 1000) + 3600;
            const tx = await router.connect(user1).swapExactTokensForTokens(
                swapAmount,
                0,
                [tokenA.address, tokenB.address],
                user1.address,
                deadline
            );
            
            const receipt = await tx.wait();
            console.log(`Gas used for simple swap: ${receipt.gasUsed.toString()}`);
            
            // Should use reasonable amount of gas (adjust based on actual measurements)
            expect(receipt.gasUsed).to.be.lt(200000);
        });

        it("Should estimate gas correctly for different path lengths", async function () {
            const directGas = await smartRouter.estimateGasCost([tokenA.address, tokenB.address]);
            const multiHopGas = await smartRouter.estimateGasCost([tokenA.address, weth.address, tokenB.address]);
            
            expect(multiHopGas).to.be.gt(directGas);
            expect(multiHopGas).to.equal(directGas.mul(2)); // Should be exactly 2x for 2 hops vs 1 hop
        });
    });

    describe("Token Registry Integration", function () {
        it("Should use only supported tokens in routing", async function () {
            const supportedTokens = await router.getSupportedTokens();
            expect(supportedTokens.length).to.be.gt(20); // Should have 25+ pre-configured tokens
            expect(supportedTokens).to.include(tokenA.address);
            expect(supportedTokens).to.include(tokenB.address);
        });

        it("Should get verified tokens for premium features", async function () {
            const verifiedTokens = await router.getVerifiedTokens();
            expect(verifiedTokens.length).to.be.gt(20); // Pre-configured tokens are verified
        });

        it("Should provide token information", async function () {
            const tokenInfo = await router.getTokenInfo(tokenA.address);
            expect(tokenInfo.symbol).to.equal("TKA");
            expect(tokenInfo.name).to.equal("Token A");
            expect(tokenInfo.isActive).to.be.true;
        });
    });
});
