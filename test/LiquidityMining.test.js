const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("LiquidityMining", function () {
    let liquidityMining, governanceToken, factory, tokenA, tokenB, lpToken;
    let owner, user1, user2, user3;

    beforeEach(async function () {
        [owner, user1, user2, user3] = await ethers.getSigners();

        // Deploy GovernanceToken
        const GovernanceToken = await ethers.getContractFactory("GovernanceToken");
        governanceToken = await GovernanceToken.deploy();
        await governanceToken.deployed();

        // Deploy Factory for LP tokens
        const SwapFactory = await ethers.getContractFactory("SwapFactory");
        factory = await SwapFactory.deploy(owner.address);
        await factory.deployed();

        // Deploy test tokens
        const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
        tokenA = await ERC20Mock.deploy("Token A", "TKA", ethers.utils.parseEther("1000000"));
        tokenB = await ERC20Mock.deploy("Token B", "TKB", ethers.utils.parseEther("1000000"));
        await tokenA.deployed();
        await tokenB.deployed();

        // Create pair and get LP token
        await factory.createPair(tokenA.address, tokenB.address);
        const pairAddress = await factory.getPair(tokenA.address, tokenB.address);
        
        const SwapPair = await ethers.getContractFactory("SwapPair");
        lpToken = SwapPair.attach(pairAddress);

        // Add liquidity to create LP tokens
        await tokenA.transfer(pairAddress, ethers.utils.parseEther("1000"));
        await tokenB.transfer(pairAddress, ethers.utils.parseEther("1000"));
        await lpToken.mint(owner.address);

        // Deploy LiquidityMining
        const LiquidityMining = await ethers.getContractFactory("LiquidityMining");
        const rewardPerBlock = ethers.utils.parseEther("10");
        const startBlock = await ethers.provider.getBlockNumber() + 10;
        const endBlock = startBlock + 1000;

        liquidityMining = await LiquidityMining.deploy(
            governanceToken.address,
            rewardPerBlock,
            startBlock,
            endBlock
        );
        await liquidityMining.deployed();

        // Add LiquidityMining as minter
        await governanceToken.addMinter(liquidityMining.address);

        // Transfer LP tokens to users
        const lpBalance = await lpToken.balanceOf(owner.address);
        await lpToken.transfer(user1.address, lpBalance.div(4));
        await lpToken.transfer(user2.address, lpBalance.div(4));
        await lpToken.transfer(user3.address, lpBalance.div(4));
    });

    describe("Deployment", function () {
        it("Should set correct initial parameters", async function () {
            expect(await liquidityMining.rewardToken()).to.equal(governanceToken.address);
            expect(await liquidityMining.rewardPerBlock()).to.equal(ethers.utils.parseEther("10"));
            expect(await liquidityMining.totalAllocPoint()).to.equal(0);
        });

        it("Should initialize boost multipliers", async function () {
            expect(await liquidityMining.boostMultipliers(0)).to.equal(10000); // 1x
            expect(await liquidityMining.boostMultipliers(1)).to.equal(11000); // 1.1x
            expect(await liquidityMining.boostMultipliers(4)).to.equal(20000); // 2x
        });
    });

    describe("Pool Management", function () {
        it("Should allow owner to add pool", async function () {
            await expect(
                liquidityMining.addPool(
                    100, // allocPoint
                    lpToken.address,
                    0, // minStakingPeriod
                    100, // withdrawalFee (1%)
                    "LP Pool",
                    false
                )
            ).to.emit(liquidityMining, "PoolAdded")
             .withArgs(0, lpToken.address, 100);

            const poolInfo = await liquidityMining.getPoolInfo(0);
            expect(poolInfo.lpToken).to.equal(lpToken.address);
            expect(poolInfo.allocPoint).to.equal(100);
            expect(poolInfo.name).to.equal("LP Pool");
            expect(poolInfo.isActive).to.be.true;
        });

        it("Should not allow non-owner to add pool", async function () {
            await expect(
                liquidityMining.connect(user1).addPool(
                    100,
                    lpToken.address,
                    0,
                    100,
                    "LP Pool",
                    false
                )
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });

        it("Should allow owner to update pool", async function () {
            await liquidityMining.addPool(100, lpToken.address, 0, 100, "LP Pool", false);

            await expect(
                liquidityMining.updatePool(0, 200, false)
            ).to.emit(liquidityMining, "PoolUpdated")
             .withArgs(0, 200);

            const poolInfo = await liquidityMining.getPoolInfo(0);
            expect(poolInfo.allocPoint).to.equal(200);
        });

        it("Should allow owner to set pool active status", async function () {
            await liquidityMining.addPool(100, lpToken.address, 0, 100, "LP Pool", false);
            
            await liquidityMining.setPoolActive(0, false);
            const poolInfo = await liquidityMining.getPoolInfo(0);
            expect(poolInfo.isActive).to.be.false;
        });

        it("Should reject invalid withdrawal fee", async function () {
            await expect(
                liquidityMining.addPool(
                    100,
                    lpToken.address,
                    0,
                    1001, // > 10%
                    "LP Pool",
                    false
                )
            ).to.be.revertedWith("LiquidityMining: INVALID_FEE");
        });
    });

    describe("Staking", function () {
        beforeEach(async function () {
            await liquidityMining.addPool(100, lpToken.address, 0, 100, "LP Pool", false);
        });

        it("Should allow users to deposit LP tokens", async function () {
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);

            await expect(
                liquidityMining.connect(user1).deposit(0, depositAmount, ethers.constants.AddressZero)
            ).to.emit(liquidityMining, "Deposit")
             .withArgs(user1.address, 0, depositAmount);

            const userInfo = await liquidityMining.getUserInfo(0, user1.address);
            expect(userInfo.amount).to.equal(depositAmount);
        });

        it("Should set referrer on first deposit", async function () {
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);

            await liquidityMining.connect(user1).deposit(0, depositAmount, user2.address);
            
            expect(await liquidityMining.referrers(user1.address)).to.equal(user2.address);
        });

        it("Should not allow deposit to inactive pool", async function () {
            await liquidityMining.setPoolActive(0, false);
            
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);

            await expect(
                liquidityMining.connect(user1).deposit(0, depositAmount, ethers.constants.AddressZero)
            ).to.be.revertedWith("LiquidityMining: POOL_INACTIVE");
        });

        it("Should allow users to withdraw LP tokens", async function () {
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);
            await liquidityMining.connect(user1).deposit(0, depositAmount, ethers.constants.AddressZero);

            // Fast forward to pass minimum staking period
            await ethers.provider.send("evm_mine");

            await expect(
                liquidityMining.connect(user1).withdraw(0, depositAmount)
            ).to.emit(liquidityMining, "Withdraw")
             .withArgs(user1.address, 0, depositAmount);

            const userInfo = await liquidityMining.getUserInfo(0, user1.address);
            expect(userInfo.amount).to.equal(0);
        });

        it("Should apply withdrawal fee", async function () {
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);
            await liquidityMining.connect(user1).deposit(0, depositAmount, ethers.constants.AddressZero);

            const initialBalance = await lpToken.balanceOf(user1.address);
            
            // Fast forward to pass minimum staking period
            await ethers.provider.send("evm_mine");
            
            await liquidityMining.connect(user1).withdraw(0, depositAmount);
            
            const finalBalance = await lpToken.balanceOf(user1.address);
            const expectedFee = depositAmount.mul(100).div(10000); // 1% fee
            const expectedReceived = depositAmount.sub(expectedFee);
            
            expect(finalBalance.sub(initialBalance)).to.equal(expectedReceived);
        });

        it("Should allow emergency withdraw", async function () {
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);
            await liquidityMining.connect(user1).deposit(0, depositAmount, ethers.constants.AddressZero);

            const initialBalance = await lpToken.balanceOf(user1.address);

            await expect(
                liquidityMining.connect(user1).emergencyWithdraw(0)
            ).to.emit(liquidityMining, "EmergencyWithdraw")
             .withArgs(user1.address, 0, depositAmount);

            const finalBalance = await lpToken.balanceOf(user1.address);
            const emergencyFee = depositAmount.mul(500).div(10000); // 5% emergency fee
            const expectedReceived = depositAmount.sub(emergencyFee);
            
            expect(finalBalance.sub(initialBalance)).to.equal(expectedReceived);

            const userInfo = await liquidityMining.getUserInfo(0, user1.address);
            expect(userInfo.amount).to.equal(0);
        });
    });

    describe("Rewards", function () {
        beforeEach(async function () {
            await liquidityMining.addPool(100, lpToken.address, 0, 100, "LP Pool", false);
            
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);
            await liquidityMining.connect(user1).deposit(0, depositAmount, ethers.constants.AddressZero);
        });

        it("Should calculate pending rewards correctly", async function () {
            // Mine some blocks to generate rewards
            for (let i = 0; i < 10; i++) {
                await ethers.provider.send("evm_mine");
            }

            const [primaryReward] = await liquidityMining.pendingRewards(0, user1.address);
            expect(primaryReward).to.be.gt(0);
        });

        it("Should allow users to claim rewards", async function () {
            // Mine some blocks to generate rewards
            for (let i = 0; i < 10; i++) {
                await ethers.provider.send("evm_mine");
            }

            const initialBalance = await governanceToken.balanceOf(user1.address);
            
            await expect(
                liquidityMining.connect(user1).claimRewards(0)
            ).to.emit(liquidityMining, "RewardClaimed");

            const finalBalance = await governanceToken.balanceOf(user1.address);
            expect(finalBalance).to.be.gt(initialBalance);
        });

        it("Should pay referral commission", async function () {
            // Set up referral
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user2).approve(liquidityMining.address, depositAmount);
            await liquidityMining.connect(user2).deposit(0, depositAmount, user3.address);

            // Mine some blocks
            for (let i = 0; i < 10; i++) {
                await ethers.provider.send("evm_mine");
            }

            const initialReferrerBalance = await governanceToken.balanceOf(user3.address);
            
            await liquidityMining.connect(user2).claimRewards(0);
            
            const finalReferrerBalance = await governanceToken.balanceOf(user3.address);
            expect(finalReferrerBalance).to.be.gt(initialReferrerBalance);
        });

        it("Should apply boost multiplier", async function () {
            // Set boost level for user
            await liquidityMining.setUserBoostLevel(user1.address, 2); // 1.25x multiplier

            // Mine some blocks
            for (let i = 0; i < 10; i++) {
                await ethers.provider.send("evm_mine");
            }

            const [pendingReward] = await liquidityMining.pendingRewards(0, user1.address);
            
            // Claim rewards
            const initialBalance = await governanceToken.balanceOf(user1.address);
            await liquidityMining.connect(user1).claimRewards(0);
            const finalBalance = await governanceToken.balanceOf(user1.address);
            
            const received = finalBalance.sub(initialBalance);
            // Should receive boosted amount (difficult to test exact amount due to block timing)
            expect(received).to.be.gt(0);
        });
    });

    describe("Bonus Rewards", function () {
        let bonusToken;

        beforeEach(async function () {
            await liquidityMining.addPool(100, lpToken.address, 0, 100, "LP Pool", false);

            // Deploy bonus token
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            bonusToken = await ERC20Mock.deploy("Bonus Token", "BONUS", ethers.utils.parseEther("1000000"));
            await bonusToken.deployed();

            // Transfer bonus tokens to contract
            await bonusToken.transfer(liquidityMining.address, ethers.utils.parseEther("10000"));
        });

        it("Should allow owner to add bonus reward", async function () {
            const currentBlock = await ethers.provider.getBlockNumber();
            
            await expect(
                liquidityMining.addBonusReward(
                    0,
                    bonusToken.address,
                    ethers.utils.parseEther("1"), // 1 token per block
                    currentBlock + 1,
                    currentBlock + 100
                )
            ).to.emit(liquidityMining, "BonusRewardAdded")
             .withArgs(0, bonusToken.address, ethers.utils.parseEther("1"));
        });

        it("Should distribute bonus rewards", async function () {
            const currentBlock = await ethers.provider.getBlockNumber();
            await liquidityMining.addBonusReward(
                0,
                bonusToken.address,
                ethers.utils.parseEther("1"),
                currentBlock + 1,
                currentBlock + 100
            );

            // User deposits
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);
            await liquidityMining.connect(user1).deposit(0, depositAmount, ethers.constants.AddressZero);

            // Mine some blocks
            for (let i = 0; i < 10; i++) {
                await ethers.provider.send("evm_mine");
            }

            const initialBonusBalance = await bonusToken.balanceOf(user1.address);
            await liquidityMining.connect(user1).claimRewards(0);
            const finalBonusBalance = await bonusToken.balanceOf(user1.address);

            expect(finalBonusBalance).to.be.gt(initialBonusBalance);
        });
    });

    describe("Admin Functions", function () {
        it("Should allow owner to update reward per block", async function () {
            const newRewardPerBlock = ethers.utils.parseEther("20");
            await liquidityMining.updateRewardPerBlock(newRewardPerBlock);
            expect(await liquidityMining.rewardPerBlock()).to.equal(newRewardPerBlock);
        });

        it("Should allow owner to update boost multiplier", async function () {
            await liquidityMining.updateBoostMultiplier(1, 12000); // 1.2x
            expect(await liquidityMining.boostMultipliers(1)).to.equal(12000);
        });

        it("Should allow owner to update referral commission", async function () {
            await liquidityMining.updateReferralCommission(200); // 2%
            expect(await liquidityMining.referralCommission()).to.equal(200);
        });

        it("Should allow owner to pause/unpause", async function () {
            await liquidityMining.pause();
            expect(await liquidityMining.paused()).to.be.true;

            await liquidityMining.unpause();
            expect(await liquidityMining.paused()).to.be.false;
        });

        it("Should not allow non-owner admin functions", async function () {
            await expect(
                liquidityMining.connect(user1).updateRewardPerBlock(ethers.utils.parseEther("20"))
            ).to.be.revertedWith("Ownable: caller is not the owner");

            await expect(
                liquidityMining.connect(user1).pause()
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });
    });

    describe("View Functions", function () {
        beforeEach(async function () {
            await liquidityMining.addPool(100, lpToken.address, 0, 100, "LP Pool", false);
        });

        it("Should return correct pool length", async function () {
            expect(await liquidityMining.poolLength()).to.equal(1);
        });

        it("Should return pool APR", async function () {
            const apr = await liquidityMining.getPoolAPR(0);
            expect(apr).to.be.gt(0);
        });

        it("Should return user total staked", async function () {
            const depositAmount = ethers.utils.parseEther("10");
            await lpToken.connect(user1).approve(liquidityMining.address, depositAmount);
            await liquidityMining.connect(user1).deposit(0, depositAmount, ethers.constants.AddressZero);

            const totalStaked = await liquidityMining.getUserTotalStaked(user1.address);
            expect(totalStaked).to.equal(depositAmount);
        });
    });
});
