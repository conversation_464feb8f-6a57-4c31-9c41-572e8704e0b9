const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Platform Fee Tests", function () {
    let factory, router, tokenA, tokenB, weth;
    let owner, user1, feeRecipient;
    
    const PLATFORM_FEE_RECIPIENT = "******************************************";

    beforeEach(async function () {
        [owner, user1, feeRecipient] = await ethers.getSigners();

        // Deploy WETH
        const WETH9 = await ethers.getContractFactory("WETH9");
        weth = await WETH9.deploy();
        await weth.deployed();

        // Deploy Factory
        const SwapFactory = await ethers.getContractFactory("SwapFactory");
        factory = await SwapFactory.deploy(owner.address);
        await factory.deployed();

        // Deploy TokenRegistry
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        const tokenRegistry = await TokenRegistry.deploy();
        await tokenRegistry.deployed();

        // Deploy SecurityManager
        const SecurityManager = await ethers.getContractFactory("SecurityManager");
        const securityManager = await SecurityManager.deploy();
        await securityManager.deployed();

        // Deploy SwapRouter
        const SwapRouter = await ethers.getContractFactory("SwapRouter");
        router = await SwapRouter.deploy(
            factory.address,
            weth.address,
            tokenRegistry.address,
            securityManager.address
        );
        await router.deployed();

        // Deploy test tokens
        const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
        tokenA = await ERC20Mock.deploy("Token A", "TKA", ethers.utils.parseEther("1000000"));
        tokenB = await ERC20Mock.deploy("Token B", "TKB", ethers.utils.parseEther("1000000"));
        await tokenA.deployed();
        await tokenB.deployed();

        // Add tokens to registry
        await tokenRegistry.addToken(
            tokenA.address, "TKA", "Token A", 18, "", "", 1000000
        );
        await tokenRegistry.addToken(
            tokenB.address, "TKB", "Token B", 18, "", "", 1000000
        );

        // Create pair and add liquidity
        await factory.createPair(tokenA.address, tokenB.address);
        const pairAddress = await factory.getPair(tokenA.address, tokenB.address);

        await tokenA.transfer(pairAddress, ethers.utils.parseEther("1000"));
        await tokenB.transfer(pairAddress, ethers.utils.parseEther("1000"));

        const SwapPair = await ethers.getContractFactory("SwapPair");
        const pair = SwapPair.attach(pairAddress);
        await pair.mint(owner.address);

        // Transfer tokens to user
        await tokenA.transfer(user1.address, ethers.utils.parseEther("100"));
        await tokenB.transfer(user1.address, ethers.utils.parseEther("100"));
    });

    describe("Platform Fee Configuration", function () {
        it("Should have correct platform fee recipient", async function () {
            expect(await router.getPlatformFeeRecipient()).to.equal(PLATFORM_FEE_RECIPIENT);
        });

        it("Should have default platform fee rate", async function () {
            expect(await router.getPlatformFeeRate()).to.equal(30); // 0.3%
        });

        it("Should allow owner to update platform fee rate", async function () {
            await expect(
                router.updatePlatformFeeRate(50) // 0.5%
            ).to.emit(router, "PlatformFeeRateUpdated")
             .withArgs(30, 50);

            expect(await router.getPlatformFeeRate()).to.equal(50);
        });

        it("Should not allow non-owner to update platform fee rate", async function () {
            await expect(
                router.connect(user1).updatePlatformFeeRate(50)
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });

        it("Should not allow platform fee rate above maximum", async function () {
            await expect(
                router.updatePlatformFeeRate(101) // > 1%
            ).to.be.revertedWith("SwapRouter: FEE_TOO_HIGH");
        });
    });

    describe("Token Swap Platform Fees", function () {
        it("Should collect platform fee on token swaps", async function () {
            const swapAmount = ethers.utils.parseEther("10");
            const expectedFee = swapAmount.mul(30).div(10000); // 0.3%

            // Approve tokens
            await tokenA.connect(user1).approve(router.address, swapAmount);

            // Get initial balance of fee recipient
            const initialFeeRecipientBalance = await tokenA.balanceOf(PLATFORM_FEE_RECIPIENT);

            // Perform swap
            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await expect(
                router.connect(user1).swapExactTokensForTokens(
                    swapAmount,
                    0,
                    [tokenA.address, tokenB.address],
                    user1.address,
                    deadline
                )
            ).to.emit(router, "PlatformFeeCollected")
             .withArgs(tokenA.address, expectedFee, PLATFORM_FEE_RECIPIENT);

            // Check fee recipient balance
            const finalFeeRecipientBalance = await tokenA.balanceOf(PLATFORM_FEE_RECIPIENT);
            expect(finalFeeRecipientBalance.sub(initialFeeRecipientBalance)).to.equal(expectedFee);
        });

        it("Should not collect fee when rate is zero", async function () {
            // Set fee rate to 0
            await router.updatePlatformFeeRate(0);

            const swapAmount = ethers.utils.parseEther("10");
            await tokenA.connect(user1).approve(router.address, swapAmount);

            const initialFeeRecipientBalance = await tokenA.balanceOf(PLATFORM_FEE_RECIPIENT);

            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await router.connect(user1).swapExactTokensForTokens(
                swapAmount,
                0,
                [tokenA.address, tokenB.address],
                user1.address,
                deadline
            );

            const finalFeeRecipientBalance = await tokenA.balanceOf(PLATFORM_FEE_RECIPIENT);
            expect(finalFeeRecipientBalance).to.equal(initialFeeRecipientBalance);
        });

        it("Should calculate correct output amount after fee deduction", async function () {
            const swapAmount = ethers.utils.parseEther("10");
            const expectedFee = swapAmount.mul(30).div(10000); // 0.3%
            const amountAfterFee = swapAmount.sub(expectedFee);

            await tokenA.connect(user1).approve(router.address, swapAmount);

            const initialUserBalanceA = await tokenA.balanceOf(user1.address);
            const initialUserBalanceB = await tokenB.balanceOf(user1.address);

            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await router.connect(user1).swapExactTokensForTokens(
                swapAmount,
                0,
                [tokenA.address, tokenB.address],
                user1.address,
                deadline
            );

            const finalUserBalanceA = await tokenA.balanceOf(user1.address);
            const finalUserBalanceB = await tokenB.balanceOf(user1.address);

            // User should have spent the full swap amount
            expect(initialUserBalanceA.sub(finalUserBalanceA)).to.equal(swapAmount);
            
            // User should have received tokens based on amount after fee
            expect(finalUserBalanceB).to.be.gt(initialUserBalanceB);
        });
    });

    describe("ETH-WETH Swap Platform Fees", function () {
        it("Should collect platform fee on ETH to WETH swap", async function () {
            const ethAmount = ethers.utils.parseEther("1");
            const expectedFee = ethAmount.mul(30).div(10000); // 0.3%
            const expectedWETH = ethAmount.sub(expectedFee);

            const initialFeeRecipientBalance = await ethers.provider.getBalance(PLATFORM_FEE_RECIPIENT);
            const initialUserWETHBalance = await weth.balanceOf(user1.address);

            await expect(
                router.connect(user1).swapETHForWETH({ value: ethAmount })
            ).to.emit(router, "PlatformFeeCollected")
             .withArgs(ethers.constants.AddressZero, expectedFee, PLATFORM_FEE_RECIPIENT);

            // Check fee recipient received ETH
            const finalFeeRecipientBalance = await ethers.provider.getBalance(PLATFORM_FEE_RECIPIENT);
            expect(finalFeeRecipientBalance.sub(initialFeeRecipientBalance)).to.equal(expectedFee);

            // Check user received WETH (minus fee)
            const finalUserWETHBalance = await weth.balanceOf(user1.address);
            expect(finalUserWETHBalance.sub(initialUserWETHBalance)).to.equal(expectedWETH);
        });

        it("Should collect platform fee on WETH to ETH swap", async function () {
            // First wrap some ETH
            const wrapAmount = ethers.utils.parseEther("1");
            await weth.connect(user1).deposit({ value: wrapAmount });

            const swapAmount = ethers.utils.parseEther("0.5");
            const expectedFee = swapAmount.mul(30).div(10000); // 0.3%
            const expectedETH = swapAmount.sub(expectedFee);

            await weth.connect(user1).approve(router.address, swapAmount);

            const initialFeeRecipientWETHBalance = await weth.balanceOf(PLATFORM_FEE_RECIPIENT);
            const initialUserETHBalance = await ethers.provider.getBalance(user1.address);

            const tx = await router.connect(user1).swapWETHForETH(swapAmount);
            const receipt = await tx.wait();
            const gasUsed = receipt.gasUsed.mul(receipt.effectiveGasPrice);

            // Check fee recipient received WETH
            const finalFeeRecipientWETHBalance = await weth.balanceOf(PLATFORM_FEE_RECIPIENT);
            expect(finalFeeRecipientWETHBalance.sub(initialFeeRecipientWETHBalance)).to.equal(expectedFee);

            // Check user received ETH (minus fee and gas)
            const finalUserETHBalance = await ethers.provider.getBalance(user1.address);
            const expectedUserBalance = initialUserETHBalance.add(expectedETH).sub(gasUsed);
            expect(finalUserETHBalance).to.be.closeTo(expectedUserBalance, ethers.utils.parseEther("0.001"));
        });
    });

    describe("Platform Fee Edge Cases", function () {
        it("Should handle very small amounts correctly", async function () {
            const swapAmount = ethers.utils.parseUnits("1", 6); // 1 token with 6 decimals
            const expectedFee = swapAmount.mul(30).div(10000); // Should be 0 due to rounding

            // Deploy 6-decimal token
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            const token6 = await ERC20Mock.deploy("Token6", "TK6", ethers.utils.parseUnits("1000000", 6), 6);
            await token6.deployed();

            // Add to registry and create pair
            await tokenRegistry.addToken(token6.address, "TK6", "Token6", 6, "", "", 1000000);
            await factory.createPair(token6.address, tokenB.address);
            
            const pairAddress = await factory.getPair(token6.address, tokenB.address);
            await token6.transfer(pairAddress, ethers.utils.parseUnits("1000", 6));
            await tokenB.transfer(pairAddress, ethers.utils.parseEther("1000"));
            
            const SwapPair = await ethers.getContractFactory("SwapPair");
            const pair = SwapPair.attach(pairAddress);
            await pair.mint(owner.address);

            // Transfer to user and test
            await token6.transfer(user1.address, ethers.utils.parseUnits("100", 6));
            await token6.connect(user1).approve(router.address, swapAmount);

            const deadline = Math.floor(Date.now() / 1000) + 3600;
            
            if (expectedFee.gt(0)) {
                await expect(
                    router.connect(user1).swapExactTokensForTokens(
                        swapAmount,
                        0,
                        [token6.address, tokenB.address],
                        user1.address,
                        deadline
                    )
                ).to.emit(router, "PlatformFeeCollected");
            } else {
                // Should not emit fee event for zero fee
                const tx = await router.connect(user1).swapExactTokensForTokens(
                    swapAmount,
                    0,
                    [token6.address, tokenB.address],
                    user1.address,
                    deadline
                );
                const receipt = await tx.wait();
                const feeEvents = receipt.events?.filter(e => e.event === "PlatformFeeCollected") || [];
                expect(feeEvents.length).to.equal(0);
            }
        });

        it("Should handle maximum fee rate correctly", async function () {
            // Set maximum fee rate (1%)
            await router.updatePlatformFeeRate(100);

            const swapAmount = ethers.utils.parseEther("10");
            const expectedFee = swapAmount.mul(100).div(10000); // 1%

            await tokenA.connect(user1).approve(router.address, swapAmount);

            const initialFeeRecipientBalance = await tokenA.balanceOf(PLATFORM_FEE_RECIPIENT);

            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await router.connect(user1).swapExactTokensForTokens(
                swapAmount,
                0,
                [tokenA.address, tokenB.address],
                user1.address,
                deadline
            );

            const finalFeeRecipientBalance = await tokenA.balanceOf(PLATFORM_FEE_RECIPIENT);
            expect(finalFeeRecipientBalance.sub(initialFeeRecipientBalance)).to.equal(expectedFee);
        });
    });

    describe("Platform Fee Integration", function () {
        it("Should work with multi-hop swaps", async function () {
            // Create A -> WETH -> B path
            await factory.createPair(tokenA.address, weth.address);
            await factory.createPair(weth.address, tokenB.address);

            // Add liquidity to new pairs
            const pairAWETH = await factory.getPair(tokenA.address, weth.address);
            const pairWETHB = await factory.getPair(weth.address, tokenB.address);

            await tokenA.transfer(pairAWETH, ethers.utils.parseEther("1000"));
            await weth.deposit({ value: ethers.utils.parseEther("1000") });
            await weth.transfer(pairAWETH, ethers.utils.parseEther("1000"));

            await weth.deposit({ value: ethers.utils.parseEther("1000") });
            await weth.transfer(pairWETHB, ethers.utils.parseEther("1000"));
            await tokenB.transfer(pairWETHB, ethers.utils.parseEther("1000"));

            const SwapPair = await ethers.getContractFactory("SwapPair");
            await SwapPair.attach(pairAWETH).mint(owner.address);
            await SwapPair.attach(pairWETHB).mint(owner.address);

            // Test multi-hop swap with fee
            const swapAmount = ethers.utils.parseEther("10");
            const expectedFee = swapAmount.mul(30).div(10000);

            await tokenA.connect(user1).approve(router.address, swapAmount);

            const initialFeeRecipientBalance = await tokenA.balanceOf(PLATFORM_FEE_RECIPIENT);

            const deadline = Math.floor(Date.now() / 1000) + 3600;
            await router.connect(user1).swapExactTokensForTokens(
                swapAmount,
                0,
                [tokenA.address, weth.address, tokenB.address],
                user1.address,
                deadline
            );

            const finalFeeRecipientBalance = await tokenA.balanceOf(PLATFORM_FEE_RECIPIENT);
            expect(finalFeeRecipientBalance.sub(initialFeeRecipientBalance)).to.equal(expectedFee);
        });
    });
});
