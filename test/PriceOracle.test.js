const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("PriceOracle", function () {
    let priceOracle, factory, weth, tokenA, tokenB, pair;
    let owner, user1;

    beforeEach(async function () {
        [owner, user1] = await ethers.getSigners();

        // Deploy WETH
        const WETH9 = await ethers.getContractFactory("WETH9");
        weth = await WETH9.deploy();
        await weth.deployed();

        // Deploy Factory
        const SwapFactory = await ethers.getContractFactory("SwapFactory");
        factory = await SwapFactory.deploy(owner.address);
        await factory.deployed();

        // Deploy PriceOracle
        const PriceOracle = await ethers.getContractFactory("PriceOracle");
        priceOracle = await PriceOracle.deploy(factory.address, weth.address);
        await priceOracle.deployed();

        // Deploy test tokens
        const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
        tokenA = await ERC20Mock.deploy("Token A", "TKA", ethers.utils.parseEther("1000000"));
        tokenB = await ERC20Mock.deploy("Token B", "TKB", ethers.utils.parseEther("1000000"));
        await tokenA.deployed();
        await tokenB.deployed();

        // Create pair
        await factory.createPair(tokenA.address, tokenB.address);
        const pairAddress = await factory.getPair(tokenA.address, tokenB.address);
        
        const SwapPair = await ethers.getContractFactory("SwapPair");
        pair = SwapPair.attach(pairAddress);

        // Add liquidity to pair
        await tokenA.transfer(pairAddress, ethers.utils.parseEther("1000"));
        await tokenB.transfer(pairAddress, ethers.utils.parseEther("2000")); // 1:2 ratio
        await pair.mint(owner.address);
    });

    describe("Deployment", function () {
        it("Should set correct factory and WETH addresses", async function () {
            expect(await priceOracle.factory()).to.equal(factory.address);
            expect(await priceOracle.WETH()).to.equal(weth.address);
        });

        it("Should set correct constants", async function () {
            expect(await priceOracle.DEFAULT_PERIOD()).to.equal(1800); // 30 minutes
            expect(await priceOracle.DEFAULT_GRANULARITY()).to.equal(8);
            expect(await priceOracle.PRICE_PRECISION()).to.equal(ethers.utils.parseEther("1"));
            expect(await priceOracle.MAX_PRICE_DEVIATION()).to.equal(1000); // 10%
        });
    });

    describe("TWAP Configuration", function () {
        it("Should allow owner to configure TWAP", async function () {
            await expect(
                priceOracle.configureTWAP(pair.address, 3600, 12, true)
            ).to.emit(priceOracle, "TWAPConfigUpdated")
             .withArgs(pair.address, 3600, 12, true);

            const [period, granularity, enabled] = await priceOracle.getTWAPConfig(pair.address);
            expect(period).to.equal(3600);
            expect(granularity).to.equal(12);
            expect(enabled).to.be.true;
        });

        it("Should not allow non-owner to configure TWAP", async function () {
            await expect(
                priceOracle.connect(user1).configureTWAP(pair.address, 3600, 12, true)
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });

        it("Should validate TWAP configuration parameters", async function () {
            // Invalid period
            await expect(
                priceOracle.configureTWAP(pair.address, 0, 12, true)
            ).to.be.revertedWith("PriceOracle: INVALID_PERIOD");

            // Invalid granularity
            await expect(
                priceOracle.configureTWAP(pair.address, 3600, 0, true)
            ).to.be.revertedWith("PriceOracle: INVALID_GRANULARITY");

            await expect(
                priceOracle.configureTWAP(pair.address, 3600, 256, true)
            ).to.be.revertedWith("PriceOracle: INVALID_GRANULARITY");
        });
    });

    describe("Spot Price", function () {
        it("Should return correct spot price", async function () {
            const [price0, price1] = await priceOracle.getSpotPrice(pair.address);
            
            // With 1000 tokenA and 2000 tokenB, price0 should be 2 and price1 should be 0.5
            expect(price0).to.equal(ethers.utils.parseEther("2"));
            expect(price1).to.equal(ethers.utils.parseEther("0.5"));
        });

        it("Should revert for non-existent pair", async function () {
            await expect(
                priceOracle.getSpotPrice(ethers.constants.AddressZero)
            ).to.be.revertedWith("PriceOracle: ZERO_ADDRESS");
        });

        it("Should revert for pair with no liquidity", async function () {
            // Create empty pair
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            const tokenC = await ERC20Mock.deploy("Token C", "TKC", ethers.utils.parseEther("1000000"));
            const tokenD = await ERC20Mock.deploy("Token D", "TKD", ethers.utils.parseEther("1000000"));
            
            await factory.createPair(tokenC.address, tokenD.address);
            const emptyPairAddress = await factory.getPair(tokenC.address, tokenD.address);

            await expect(
                priceOracle.getSpotPrice(emptyPairAddress)
            ).to.be.revertedWith("PriceOracle: INSUFFICIENT_LIQUIDITY");
        });
    });

    describe("TWAP Updates", function () {
        beforeEach(async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, true);
        });

        it("Should update TWAP observations", async function () {
            await expect(
                priceOracle.update(pair.address)
            ).to.emit(priceOracle, "PriceUpdated");

            expect(await priceOracle.isTWAPAvailable(pair.address)).to.be.true;
            expect(await priceOracle.getObservationCount(pair.address)).to.equal(8);
        });

        it("Should not update if TWAP not enabled", async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, false);
            
            // Should not revert but also not update
            await priceOracle.update(pair.address);
            expect(await priceOracle.isTWAPAvailable(pair.address)).to.be.false;
        });

        it("Should batch update multiple pairs", async function () {
            // Create another pair
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            const tokenC = await ERC20Mock.deploy("Token C", "TKC", ethers.utils.parseEther("1000000"));
            
            await factory.createPair(tokenA.address, tokenC.address);
            const pair2Address = await factory.getPair(tokenA.address, tokenC.address);
            
            // Add liquidity to second pair
            await tokenA.transfer(pair2Address, ethers.utils.parseEther("500"));
            await tokenC.transfer(pair2Address, ethers.utils.parseEther("1000"));
            const SwapPair = await ethers.getContractFactory("SwapPair");
            const pair2 = SwapPair.attach(pair2Address);
            await pair2.mint(owner.address);

            // Configure TWAP for second pair
            await priceOracle.configureTWAP(pair2Address, 1800, 8, true);

            // Batch update
            await priceOracle.batchUpdate([pair.address, pair2Address]);

            expect(await priceOracle.isTWAPAvailable(pair.address)).to.be.true;
            expect(await priceOracle.isTWAPAvailable(pair2Address)).to.be.true;
        });
    });

    describe("TWAP Price Calculation", function () {
        beforeEach(async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, true);
            await priceOracle.update(pair.address);
            
            // Mine some blocks and update again to have price history
            for (let i = 0; i < 10; i++) {
                await ethers.provider.send("evm_mine");
            }
            await priceOracle.update(pair.address);
        });

        it("Should calculate TWAP price", async function () {
            const [price0, price1] = await priceOracle.getTWAPPrice(pair.address, 300); // 5 minutes
            
            expect(price0).to.be.gt(0);
            expect(price1).to.be.gt(0);
        });

        it("Should revert for invalid period", async function () {
            await expect(
                priceOracle.getTWAPPrice(pair.address, 0)
            ).to.be.revertedWith("PriceOracle: INVALID_PERIOD");
        });

        it("Should revert if TWAP not enabled", async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, false);
            
            await expect(
                priceOracle.getTWAPPrice(pair.address, 300)
            ).to.be.revertedWith("PriceOracle: TWAP_NOT_ENABLED");
        });
    });

    describe("Chainlink Integration", function () {
        let mockChainlinkFeed;

        beforeEach(async function () {
            // Deploy mock Chainlink feed
            const MockChainlinkFeed = await ethers.getContractFactory("MockChainlinkFeed");
            mockChainlinkFeed = await MockChainlinkFeed.deploy(
                ethers.utils.parseUnits("2000", 8), // $2000 with 8 decimals
                8
            );
            await mockChainlinkFeed.deployed();
        });

        it("Should allow owner to set Chainlink feed", async function () {
            await expect(
                priceOracle.setChainlinkFeed(tokenA.address, mockChainlinkFeed.address, true)
            ).to.emit(priceOracle, "ChainlinkFeedUpdated")
             .withArgs(tokenA.address, mockChainlinkFeed.address, true);

            expect(await priceOracle.isChainlinkAvailable(tokenA.address)).to.be.true;
        });

        it("Should get price from Chainlink feed", async function () {
            await priceOracle.setChainlinkFeed(tokenA.address, mockChainlinkFeed.address, true);
            
            const [price, updatedAt] = await priceOracle.getChainlinkPrice(tokenA.address);
            
            expect(price).to.equal(ethers.utils.parseEther("2000"));
            expect(updatedAt).to.be.gt(0);
        });

        it("Should revert if Chainlink not enabled", async function () {
            await expect(
                priceOracle.getChainlinkPrice(tokenA.address)
            ).to.be.revertedWith("PriceOracle: CHAINLINK_NOT_ENABLED");
        });
    });

    describe("Combined Price", function () {
        beforeEach(async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, true);
            await priceOracle.update(pair.address);
        });

        it("Should get combined price with TWAP only", async function () {
            // Mine blocks to generate TWAP data
            for (let i = 0; i < 10; i++) {
                await ethers.provider.send("evm_mine");
            }
            await priceOracle.update(pair.address);

            const [price, reliable] = await priceOracle.getCombinedPrice(tokenA.address, tokenB.address);
            
            expect(price).to.be.gt(0);
            expect(reliable).to.be.true;
        });

        it("Should fallback to spot price when no TWAP", async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, false);
            
            const [price, reliable] = await priceOracle.getCombinedPrice(tokenA.address, tokenB.address);
            
            expect(price).to.be.gt(0);
            expect(reliable).to.be.false; // Spot price is less reliable
        });

        it("Should detect price deviation", async function () {
            // Set up TWAP
            for (let i = 0; i < 10; i++) {
                await ethers.provider.send("evm_mine");
            }
            await priceOracle.update(pair.address);

            // Set up Chainlink with very different price
            const MockChainlinkFeed = await ethers.getContractFactory("MockChainlinkFeed");
            const feedA = await MockChainlinkFeed.deploy(ethers.utils.parseUnits("1000", 8), 8);
            const feedB = await MockChainlinkFeed.deploy(ethers.utils.parseUnits("100", 8), 8); // 10x difference
            
            await priceOracle.setChainlinkFeed(tokenA.address, feedA.address, true);
            await priceOracle.setChainlinkFeed(tokenB.address, feedB.address, true);

            // Should emit price deviation event
            await expect(
                priceOracle.getCombinedPrice(tokenA.address, tokenB.address)
            ).to.emit(priceOracle, "PriceDeviation");
        });
    });

    describe("Safe Price", function () {
        beforeEach(async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, true);
            await priceOracle.update(pair.address);
        });

        it("Should return safe price for normal conditions", async function () {
            const [price, safe] = await priceOracle.getSafePrice(tokenA.address, tokenB.address);
            
            expect(price).to.be.gt(0);
            expect(safe).to.be.true;
        });

        it("Should mark price as unsafe for extreme values", async function () {
            // This test would require manipulating the price to extreme values
            // For now, just test the function exists and returns values
            const [price, safe] = await priceOracle.getSafePrice(tokenA.address, tokenB.address);
            expect(price).to.be.a('object'); // BigNumber
            expect(safe).to.be.a('boolean');
        });
    });

    describe("Emergency Functions", function () {
        beforeEach(async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, true);
        });

        it("Should allow owner to emergency disable TWAP", async function () {
            await expect(
                priceOracle.emergencyDisableTWAP(pair.address)
            ).to.emit(priceOracle, "TWAPConfigUpdated")
             .withArgs(pair.address, 0, 0, false);

            const [, , enabled] = await priceOracle.getTWAPConfig(pair.address);
            expect(enabled).to.be.false;
        });

        it("Should allow owner to emergency disable Chainlink", async function () {
            const MockChainlinkFeed = await ethers.getContractFactory("MockChainlinkFeed");
            const mockFeed = await MockChainlinkFeed.deploy(ethers.utils.parseUnits("2000", 8), 8);
            
            await priceOracle.setChainlinkFeed(tokenA.address, mockFeed.address, true);
            
            await expect(
                priceOracle.emergencyDisableChainlink(tokenA.address)
            ).to.emit(priceOracle, "ChainlinkFeedUpdated")
             .withArgs(tokenA.address, ethers.constants.AddressZero, false);

            expect(await priceOracle.isChainlinkAvailable(tokenA.address)).to.be.false;
        });

        it("Should not allow non-owner emergency functions", async function () {
            await expect(
                priceOracle.connect(user1).emergencyDisableTWAP(pair.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");

            await expect(
                priceOracle.connect(user1).emergencyDisableChainlink(tokenA.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });
    });

    describe("View Functions", function () {
        beforeEach(async function () {
            await priceOracle.configureTWAP(pair.address, 1800, 8, true);
            await priceOracle.update(pair.address);
        });

        it("Should return latest observation", async function () {
            const [timestamp, price0Cumulative, price1Cumulative] = await priceOracle.getLatestObservation(pair.address);
            
            expect(timestamp).to.be.gt(0);
            expect(price0Cumulative).to.be.a('object'); // BigNumber
            expect(price1Cumulative).to.be.a('object'); // BigNumber
        });

        it("Should return observation count", async function () {
            const count = await priceOracle.getObservationCount(pair.address);
            expect(count).to.equal(8); // Default granularity
        });

        it("Should check TWAP availability", async function () {
            expect(await priceOracle.isTWAPAvailable(pair.address)).to.be.true;
            
            await priceOracle.configureTWAP(pair.address, 1800, 8, false);
            expect(await priceOracle.isTWAPAvailable(pair.address)).to.be.false;
        });
    });
});
