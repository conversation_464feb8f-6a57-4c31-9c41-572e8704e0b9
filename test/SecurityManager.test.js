const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("SecurityManager", function () {
    let securityManager;
    let owner, guardian1, guardian2, user1, user2;

    beforeEach(async function () {
        [owner, guardian1, guardian2, user1, user2] = await ethers.getSigners();

        // Deploy SecurityManager
        const SecurityManager = await ethers.getContractFactory("SecurityManager");
        securityManager = await SecurityManager.deploy();
        await securityManager.deployed();
    });

    describe("Deployment", function () {
        it("Should initialize with default configurations", async function () {
            const circuitBreaker = await securityManager.circuitBreaker();
            expect(circuitBreaker.enabled).to.be.true;
            expect(circuitBreaker.maxVolumePerBlock).to.equal(ethers.utils.parseEther("1000"));
            expect(circuitBreaker.maxVolumePerHour).to.equal(ethers.utils.parseEther("10000"));

            const mevProtection = await securityManager.mevProtection();
            expect(mevProtection.enabled).to.be.true;
            expect(mevProtection.minBlockDelay).to.equal(2);
        });

        it("Should set deployer as initial guardian", async function () {
            expect(await securityManager.guardians(owner.address)).to.be.true;
            expect(await securityManager.guardianCount()).to.equal(1);
            expect(await securityManager.requiredGuardianSignatures()).to.equal(1);
        });

        it("Should not be in emergency mode initially", async function () {
            expect(await securityManager.emergencyMode()).to.be.false;
            expect(await securityManager.paused()).to.be.false;
        });
    });

    describe("Circuit Breaker", function () {
        it("Should allow trades within volume limits", async function () {
            const result = await securityManager.checkTradeAllowed(
                user1.address,
                ethers.utils.parseEther("100"), // 100 ETH
                500 // 5% price impact
            );
            expect(result).to.be.true;
        });

        it("Should block trades exceeding volume per block", async function () {
            await expect(
                securityManager.checkTradeAllowed(
                    user1.address,
                    ethers.utils.parseEther("1001"), // Exceeds 1000 ETH limit
                    500
                )
            ).to.be.revertedWith("SecurityManager: CIRCUIT_BREAKER_VOLUME_BLOCK");
        });

        it("Should block trades exceeding price impact", async function () {
            await expect(
                securityManager.checkTradeAllowed(
                    user1.address,
                    ethers.utils.parseEther("100"),
                    1001 // Exceeds 10% limit
                )
            ).to.be.revertedWith("SecurityManager: CIRCUIT_BREAKER_PRICE_IMPACT");
        });

        it("Should allow owner to update circuit breaker config", async function () {
            await expect(
                securityManager.updateCircuitBreakerConfig(
                    ethers.utils.parseEther("2000"), // maxVolumePerBlock
                    ethers.utils.parseEther("20000"), // maxVolumePerHour
                    1500, // maxPriceImpact (15%)
                    1000, // maxSlippage (10%)
                    true // enabled
                )
            ).to.emit(securityManager, "SecurityConfigUpdated")
             .withArgs("CIRCUIT_BREAKER");

            const config = await securityManager.circuitBreaker();
            expect(config.maxVolumePerBlock).to.equal(ethers.utils.parseEther("2000"));
            expect(config.maxPriceImpact).to.equal(1500);
        });
    });

    describe("MEV Protection", function () {
        it("Should allow first trade from user", async function () {
            const result = await securityManager.checkTradeAllowed(
                user1.address,
                ethers.utils.parseEther("100"),
                500
            );
            expect(result).to.be.true;
        });

        it("Should block rapid consecutive trades", async function () {
            // First trade should succeed
            await securityManager.checkTradeAllowed(
                user1.address,
                ethers.utils.parseEther("100"),
                500
            );

            // Second trade in same block should fail
            await expect(
                securityManager.checkTradeAllowed(
                    user1.address,
                    ethers.utils.parseEther("100"),
                    500
                )
            ).to.be.revertedWith("SecurityManager: MEV_PROTECTION_BLOCK_DELAY");
        });

        it("Should allow owner to update MEV protection config", async function () {
            await expect(
                securityManager.updateMEVProtectionConfig(
                    3, // minBlockDelay
                    200, // maxFrontRunProtection
                    300, // sandwichProtection
                    true // enabled
                )
            ).to.emit(securityManager, "SecurityConfigUpdated")
             .withArgs("MEV_PROTECTION");

            const config = await securityManager.mevProtection();
            expect(config.minBlockDelay).to.equal(3);
            expect(config.maxFrontRunProtection).to.equal(200);
        });
    });

    describe("Flash Loan Protection", function () {
        it("Should allow flash loans within limits", async function () {
            const result = await securityManager.checkFlashLoanAllowed(
                user1.address,
                ethers.utils.parseEther("1000")
            );
            expect(result).to.be.true;
        });

        it("Should block flash loans exceeding limits", async function () {
            await expect(
                securityManager.checkFlashLoanAllowed(
                    user1.address,
                    ethers.utils.parseEther("10001") // Exceeds 10000 ETH limit
                )
            ).to.be.revertedWith("SecurityManager: FLASH_LOAN_AMOUNT_EXCEEDED");
        });

        it("Should block all flash loans when disabled", async function () {
            await securityManager.updateFlashLoanProtectionConfig(
                ethers.utils.parseEther("10000"),
                30,
                true // blockFlashLoans = true
            );

            await expect(
                securityManager.checkFlashLoanAllowed(
                    user1.address,
                    ethers.utils.parseEther("1000")
                )
            ).to.be.revertedWith("SecurityManager: FLASH_LOANS_BLOCKED");
        });

        it("Should allow whitelisted flash loaners", async function () {
            await securityManager.updateFlashLoanProtectionConfig(
                ethers.utils.parseEther("10000"),
                30,
                true // blockFlashLoans = true
            );

            await securityManager.whitelistFlashLoaner(user1.address, true);

            const result = await securityManager.checkFlashLoanAllowed(
                user1.address,
                ethers.utils.parseEther("1000")
            );
            expect(result).to.be.true;
        });
    });

    describe("Emergency Mode", function () {
        beforeEach(async function () {
            await securityManager.addGuardian(guardian1.address);
        });

        it("Should allow guardian to activate emergency mode", async function () {
            await expect(
                securityManager.connect(guardian1).activateEmergencyMode("Test emergency")
            ).to.emit(securityManager, "EmergencyModeActivated")
             .withArgs(guardian1.address, "Test emergency");

            expect(await securityManager.emergencyMode()).to.be.true;
            expect(await securityManager.paused()).to.be.true;
        });

        it("Should not allow non-guardian to activate emergency mode", async function () {
            await expect(
                securityManager.connect(user1).activateEmergencyMode("Test emergency")
            ).to.be.revertedWith("SecurityManager: NOT_GUARDIAN");
        });

        it("Should block trades during emergency mode", async function () {
            await securityManager.connect(guardian1).activateEmergencyMode("Test emergency");

            await expect(
                securityManager.checkTradeAllowed(
                    user1.address,
                    ethers.utils.parseEther("100"),
                    500
                )
            ).to.be.revertedWith("Pausable: paused");
        });

        it("Should allow guardian to deactivate emergency mode", async function () {
            await securityManager.connect(guardian1).activateEmergencyMode("Test emergency");
            
            // Fast forward time to pass emergency duration
            await ethers.provider.send("evm_increaseTime", [24 * 60 * 60 + 1]); // 24 hours + 1 second
            await ethers.provider.send("evm_mine");

            await expect(
                securityManager.connect(guardian1).deactivateEmergencyMode()
            ).to.emit(securityManager, "EmergencyModeDeactivated")
             .withArgs(guardian1.address);

            expect(await securityManager.emergencyMode()).to.be.false;
            expect(await securityManager.paused()).to.be.false;
        });
    });

    describe("Guardian Management", function () {
        it("Should allow owner to add guardian", async function () {
            await expect(
                securityManager.addGuardian(guardian1.address)
            ).to.emit(securityManager, "GuardianAdded")
             .withArgs(guardian1.address);

            expect(await securityManager.guardians(guardian1.address)).to.be.true;
            expect(await securityManager.guardianCount()).to.equal(2);
        });

        it("Should not allow adding duplicate guardian", async function () {
            await securityManager.addGuardian(guardian1.address);
            
            await expect(
                securityManager.addGuardian(guardian1.address)
            ).to.be.revertedWith("SecurityManager: ALREADY_GUARDIAN");
        });

        it("Should allow owner to remove guardian", async function () {
            await securityManager.addGuardian(guardian1.address);
            await securityManager.addGuardian(guardian2.address);

            await expect(
                securityManager.removeGuardian(guardian1.address)
            ).to.emit(securityManager, "GuardianRemoved")
             .withArgs(guardian1.address);

            expect(await securityManager.guardians(guardian1.address)).to.be.false;
            expect(await securityManager.guardianCount()).to.equal(2); // owner + guardian2
        });

        it("Should not allow removing last guardian", async function () {
            await expect(
                securityManager.removeGuardian(owner.address)
            ).to.be.revertedWith("SecurityManager: CANNOT_REMOVE_LAST_GUARDIAN");
        });

        it("Should not allow non-owner to manage guardians", async function () {
            await expect(
                securityManager.connect(user1).addGuardian(guardian1.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");

            await expect(
                securityManager.connect(user1).removeGuardian(owner.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });
    });

    describe("Rate Limiting", function () {
        it("Should allow actions within rate limit", async function () {
            for (let i = 0; i < 10; i++) {
                const result = await securityManager.checkRateLimit(user1.address);
                expect(result).to.be.true;
            }
        });

        it("Should block actions exceeding rate limit", async function () {
            // Use up the rate limit
            for (let i = 0; i < 10; i++) {
                await securityManager.checkRateLimit(user1.address);
            }

            // 11th action should fail
            await expect(
                securityManager.checkRateLimit(user1.address)
            ).to.be.revertedWith("SecurityManager: RATE_LIMIT_EXCEEDED");
        });

        it("Should reset rate limit after time window", async function () {
            // Use up the rate limit
            for (let i = 0; i < 10; i++) {
                await securityManager.checkRateLimit(user1.address);
            }

            // Fast forward past rate limit window
            await ethers.provider.send("evm_increaseTime", [61]); // 61 seconds
            await ethers.provider.send("evm_mine");

            // Should be able to perform actions again
            const result = await securityManager.checkRateLimit(user1.address);
            expect(result).to.be.true;
        });
    });

    describe("View Functions", function () {
        it("Should return current block volume", async function () {
            await securityManager.checkTradeAllowed(
                user1.address,
                ethers.utils.parseEther("100"),
                500
            );

            const volume = await securityManager.getCurrentBlockVolume();
            expect(volume).to.equal(ethers.utils.parseEther("100"));
        });

        it("Should return user trade information", async function () {
            await securityManager.checkTradeAllowed(
                user1.address,
                ethers.utils.parseEther("100"),
                500
            );

            const lastTradeBlock = await securityManager.getUserLastTradeBlock(user1.address);
            const tradeCount = await securityManager.getUserTradeCount(user1.address);

            expect(lastTradeBlock).to.be.gt(0);
            expect(tradeCount).to.equal(1);
        });

        it("Should return emergency mode status", async function () {
            expect(await securityManager.isInEmergencyMode()).to.be.false;
            
            await securityManager.addGuardian(guardian1.address);
            await securityManager.connect(guardian1).activateEmergencyMode("Test");
            
            expect(await securityManager.isInEmergencyMode()).to.be.true;
        });
    });
});
