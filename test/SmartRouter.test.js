const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("SmartRouter", function () {
    let smartRouter, factory, tokenRegistry, priceOracle;
    let weth, tokenA, tokenB, tokenC;
    let owner, user1;

    beforeEach(async function () {
        [owner, user1] = await ethers.getSigners();

        // Deploy WETH
        const WETH9 = await ethers.getContractFactory("WETH9");
        weth = await WETH9.deploy();
        await weth.deployed();

        // Deploy Factory
        const SwapFactory = await ethers.getContractFactory("SwapFactory");
        factory = await SwapFactory.deploy(owner.address);
        await factory.deployed();

        // Deploy TokenRegistry
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        tokenRegistry = await TokenRegistry.deploy();
        await tokenRegistry.deployed();

        // Deploy PriceOracle
        const PriceOracle = await ethers.getContractFactory("PriceOracle");
        priceOracle = await PriceOracle.deploy(factory.address, weth.address);
        await priceOracle.deployed();

        // Deploy SmartRouter
        const SmartRouter = await ethers.getContractFactory("SmartRouter");
        smartRouter = await SmartRouter.deploy(
            factory.address,
            weth.address,
            tokenRegistry.address,
            priceOracle.address
        );
        await smartRouter.deployed();

        // Deploy test tokens
        const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
        tokenA = await ERC20Mock.deploy("Token A", "TKA", ethers.utils.parseEther("1000000"));
        tokenB = await ERC20Mock.deploy("Token B", "TKB", ethers.utils.parseEther("1000000"));
        tokenC = await ERC20Mock.deploy("Token C", "TKC", ethers.utils.parseEther("1000000"));
        
        await tokenA.deployed();
        await tokenB.deployed();
        await tokenC.deployed();

        // Add tokens to registry
        await tokenRegistry.addToken(
            tokenA.address, "TKA", "Token A", 18, "", "", 1000000
        );
        await tokenRegistry.addToken(
            tokenB.address, "TKB", "Token B", 18, "", "", 1000000
        );
        await tokenRegistry.addToken(
            tokenC.address, "TKC", "Token C", 18, "", "", 1000000
        );

        // Create pairs
        await factory.createPair(tokenA.address, tokenB.address);
        await factory.createPair(tokenA.address, weth.address);
        await factory.createPair(tokenB.address, weth.address);
        await factory.createPair(tokenC.address, weth.address);

        // Add liquidity to pairs
        const pairAB = await factory.getPair(tokenA.address, tokenB.address);
        const pairAWETH = await factory.getPair(tokenA.address, weth.address);
        const pairBWETH = await factory.getPair(tokenB.address, weth.address);
        const pairCWETH = await factory.getPair(tokenC.address, weth.address);

        // Transfer tokens to pairs and mint liquidity
        await tokenA.transfer(pairAB, ethers.utils.parseEther("1000"));
        await tokenB.transfer(pairAB, ethers.utils.parseEther("1000"));
        const SwapPair = await ethers.getContractFactory("SwapPair");
        const pairABContract = SwapPair.attach(pairAB);
        await pairABContract.mint(owner.address);

        await tokenA.transfer(pairAWETH, ethers.utils.parseEther("1000"));
        await weth.deposit({ value: ethers.utils.parseEther("1000") });
        await weth.transfer(pairAWETH, ethers.utils.parseEther("1000"));
        const pairAWETHContract = SwapPair.attach(pairAWETH);
        await pairAWETHContract.mint(owner.address);

        await tokenB.transfer(pairBWETH, ethers.utils.parseEther("1000"));
        await weth.deposit({ value: ethers.utils.parseEther("1000") });
        await weth.transfer(pairBWETH, ethers.utils.parseEther("1000"));
        const pairBWETHContract = SwapPair.attach(pairBWETH);
        await pairBWETHContract.mint(owner.address);

        await tokenC.transfer(pairCWETH, ethers.utils.parseEther("1000"));
        await weth.deposit({ value: ethers.utils.parseEther("1000") });
        await weth.transfer(pairCWETH, ethers.utils.parseEther("1000"));
        const pairCWETHContract = SwapPair.attach(pairCWETH);
        await pairCWETHContract.mint(owner.address);
    });

    describe("Deployment", function () {
        it("Should set correct addresses", async function () {
            expect(await smartRouter.factory()).to.equal(factory.address);
            expect(await smartRouter.WETH()).to.equal(weth.address);
            expect(await smartRouter.tokenRegistry()).to.equal(tokenRegistry.address);
            expect(await smartRouter.priceOracle()).to.equal(priceOracle.address);
        });

        it("Should initialize with WETH as intermediate token", async function () {
            const intermediateTokens = await smartRouter.getIntermediateTokens();
            expect(intermediateTokens).to.include(weth.address);
            expect(await smartRouter.isIntermediateToken(weth.address)).to.be.true;
        });
    });

    describe("Path Finding", function () {
        it("Should find direct path when available", async function () {
            const path = await smartRouter.findOptimalPath(
                tokenA.address,
                tokenB.address,
                ethers.utils.parseEther("10")
            );

            expect(path.isValid).to.be.true;
            expect(path.tokens.length).to.equal(2);
            expect(path.tokens[0]).to.equal(tokenA.address);
            expect(path.tokens[1]).to.equal(tokenB.address);
            expect(path.expectedOutput).to.be.gt(0);
        });

        it("Should find single-hop path through WETH", async function () {
            const path = await smartRouter.findOptimalPath(
                tokenA.address,
                tokenC.address,
                ethers.utils.parseEther("10")
            );

            expect(path.isValid).to.be.true;
            expect(path.tokens.length).to.equal(3);
            expect(path.tokens[0]).to.equal(tokenA.address);
            expect(path.tokens[1]).to.equal(weth.address);
            expect(path.tokens[2]).to.equal(tokenC.address);
        });

        it("Should return invalid path when no route exists", async function () {
            // Deploy a token without any pairs
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            const isolatedToken = await ERC20Mock.deploy("Isolated", "ISO", ethers.utils.parseEther("1000"));
            await isolatedToken.deployed();

            await tokenRegistry.addToken(
                isolatedToken.address, "ISO", "Isolated", 18, "", "", 1000000
            );

            const path = await smartRouter.findOptimalPath(
                tokenA.address,
                isolatedToken.address,
                ethers.utils.parseEther("10")
            );

            expect(path.isValid).to.be.false;
        });

        it("Should reject identical tokens", async function () {
            await expect(
                smartRouter.findOptimalPath(
                    tokenA.address,
                    tokenA.address,
                    ethers.utils.parseEther("10")
                )
            ).to.be.revertedWith("SmartRouter: IDENTICAL_TOKENS");
        });

        it("Should reject zero amount", async function () {
            await expect(
                smartRouter.findOptimalPath(
                    tokenA.address,
                    tokenB.address,
                    0
                )
            ).to.be.revertedWith("SmartRouter: INVALID_AMOUNT");
        });
    });

    describe("Intermediate Token Management", function () {
        it("Should allow owner to add intermediate token", async function () {
            await expect(
                smartRouter.addIntermediateToken(tokenA.address)
            ).to.emit(smartRouter, "IntermediateTokenAdded")
             .withArgs(tokenA.address);

            expect(await smartRouter.isIntermediateToken(tokenA.address)).to.be.true;
            
            const intermediateTokens = await smartRouter.getIntermediateTokens();
            expect(intermediateTokens).to.include(tokenA.address);
        });

        it("Should not allow non-owner to add intermediate token", async function () {
            await expect(
                smartRouter.connect(user1).addIntermediateToken(tokenA.address)
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });

        it("Should not allow adding duplicate intermediate token", async function () {
            await smartRouter.addIntermediateToken(tokenA.address);
            
            await expect(
                smartRouter.addIntermediateToken(tokenA.address)
            ).to.be.revertedWith("SmartRouter: ALREADY_INTERMEDIATE");
        });

        it("Should allow owner to remove intermediate token", async function () {
            await smartRouter.addIntermediateToken(tokenA.address);
            
            await expect(
                smartRouter.removeIntermediateToken(tokenA.address)
            ).to.emit(smartRouter, "IntermediateTokenRemoved")
             .withArgs(tokenA.address);

            expect(await smartRouter.isIntermediateToken(tokenA.address)).to.be.false;
        });

        it("Should not allow removing WETH", async function () {
            await expect(
                smartRouter.removeIntermediateToken(weth.address)
            ).to.be.revertedWith("SmartRouter: CANNOT_REMOVE_WETH");
        });

        it("Should not allow removing non-intermediate token", async function () {
            await expect(
                smartRouter.removeIntermediateToken(tokenA.address)
            ).to.be.revertedWith("SmartRouter: NOT_INTERMEDIATE");
        });
    });

    describe("Path Options", function () {
        beforeEach(async function () {
            await smartRouter.addIntermediateToken(tokenA.address);
            await smartRouter.addIntermediateToken(tokenB.address);
        });

        it("Should return multiple path options", async function () {
            const pathOptions = await smartRouter.getPathOptions(
                tokenA.address,
                tokenB.address,
                ethers.utils.parseEther("10"),
                5
            );

            expect(pathOptions.length).to.be.gt(0);
            expect(pathOptions.length).to.be.lte(5);
            
            // All paths should be valid
            for (const path of pathOptions) {
                expect(path.isValid).to.be.true;
                expect(path.expectedOutput).to.be.gt(0);
            }
        });

        it("Should limit number of returned paths", async function () {
            const pathOptions = await smartRouter.getPathOptions(
                tokenA.address,
                tokenB.address,
                ethers.utils.parseEther("10"),
                2
            );

            expect(pathOptions.length).to.be.lte(2);
        });

        it("Should reject invalid max paths", async function () {
            await expect(
                smartRouter.getPathOptions(
                    tokenA.address,
                    tokenB.address,
                    ethers.utils.parseEther("10"),
                    0
                )
            ).to.be.revertedWith("SmartRouter: INVALID_MAX_PATHS");

            await expect(
                smartRouter.getPathOptions(
                    tokenA.address,
                    tokenB.address,
                    ethers.utils.parseEther("10"),
                    11
                )
            ).to.be.revertedWith("SmartRouter: INVALID_MAX_PATHS");
        });
    });

    describe("Route Details", function () {
        it("Should return detailed route information", async function () {
            const route = await smartRouter.getRouteDetails(
                tokenA.address,
                tokenB.address,
                ethers.utils.parseEther("10")
            );

            expect(route.totalAmountIn).to.equal(ethers.utils.parseEther("10"));
            expect(route.totalAmountOut).to.be.gt(0);
            expect(route.gasEstimate).to.be.gt(0);
            expect(route.steps.length).to.be.gt(0);

            // Check first step
            const firstStep = route.steps[0];
            expect(firstStep.tokenIn).to.equal(tokenA.address);
            expect(firstStep.amountIn).to.equal(ethers.utils.parseEther("10"));
            expect(firstStep.amountOut).to.be.gt(0);
            expect(firstStep.fee).to.be.gt(0);
        });

        it("Should revert for invalid path", async function () {
            // Deploy isolated token
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            const isolatedToken = await ERC20Mock.deploy("Isolated", "ISO", ethers.utils.parseEther("1000"));
            await isolatedToken.deployed();

            await tokenRegistry.addToken(
                isolatedToken.address, "ISO", "Isolated", 18, "", "", 1000000
            );

            await expect(
                smartRouter.getRouteDetails(
                    tokenA.address,
                    isolatedToken.address,
                    ethers.utils.parseEther("10")
                )
            ).to.be.revertedWith("SmartRouter: NO_VALID_PATH");
        });
    });

    describe("Utility Functions", function () {
        it("Should check if path exists", async function () {
            const exists = await smartRouter.pathExists(tokenA.address, tokenB.address);
            expect(exists).to.be.true;

            // Deploy isolated token
            const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
            const isolatedToken = await ERC20Mock.deploy("Isolated", "ISO", ethers.utils.parseEther("1000"));
            await isolatedToken.deployed();

            await tokenRegistry.addToken(
                isolatedToken.address, "ISO", "Isolated", 18, "", "", 1000000
            );

            const notExists = await smartRouter.pathExists(tokenA.address, isolatedToken.address);
            expect(notExists).to.be.false;
        });

        it("Should estimate gas cost correctly", async function () {
            const directPath = [tokenA.address, tokenB.address];
            const directGas = await smartRouter.estimateGasCost(directPath);
            expect(directGas).to.equal(60000); // 1 hop * 60000

            const multiHopPath = [tokenA.address, weth.address, tokenC.address];
            const multiHopGas = await smartRouter.estimateGasCost(multiHopPath);
            expect(multiHopGas).to.equal(120000); // 2 hops * 60000
        });

        it("Should check profitability", async function () {
            const gasPrice = ethers.utils.parseUnits("20", "gwei");
            
            const [profitable, netOutput] = await smartRouter.isProfitable(
                tokenA.address,
                tokenB.address,
                ethers.utils.parseEther("10"),
                gasPrice
            );

            expect(profitable).to.be.true;
            expect(netOutput).to.be.gt(0);
        });

        it("Should return intermediate token count", async function () {
            const initialCount = await smartRouter.getIntermediateTokenCount();
            expect(initialCount).to.equal(1); // Only WETH initially

            await smartRouter.addIntermediateToken(tokenA.address);
            
            const newCount = await smartRouter.getIntermediateTokenCount();
            expect(newCount).to.equal(2);
        });
    });
});
