const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("TokenRegistry", function () {
    let tokenRegistry;
    let owner, user1, user2;
    let mockToken;

    beforeEach(async function () {
        [owner, user1, user2] = await ethers.getSigners();

        // Deploy TokenRegistry
        const TokenRegistry = await ethers.getContractFactory("TokenRegistry");
        tokenRegistry = await TokenRegistry.deploy();
        await tokenRegistry.deployed();

        // Deploy mock token for testing
        const ERC20Mock = await ethers.getContractFactory("ERC20Mock");
        mockToken = await ERC20Mock.deploy("Mock Token", "MOCK", ethers.utils.parseEther("1000000"));
        await mockToken.deployed();
    });

    describe("Deployment", function () {
        it("Should initialize with pre-configured tokens", async function () {
            const totalTokens = await tokenRegistry.getTotalTokens();
            expect(totalTokens).to.be.gt(20); // Should have 25+ tokens
        });

        it("Should set correct owner", async function () {
            expect(await tokenRegistry.owner()).to.equal(owner.address);
        });

        it("Should have WETH as supported token", async function () {
            const wethAddress = "******************************************";
            const isSupported = await tokenRegistry.isTokenSupported(wethAddress);
            expect(isSupported).to.be.true;
        });
    });

    describe("Token Management", function () {
        it("Should allow owner to add new token", async function () {
            await expect(
                tokenRegistry.addToken(
                    mockToken.address,
                    "MOCK",
                    "Mock Token",
                    18,
                    "https://mock.com/logo.png",
                    "https://mock.com",
                    1000000
                )
            ).to.emit(tokenRegistry, "TokenAdded")
             .withArgs(mockToken.address, "MOCK", "Mock Token");

            const tokenInfo = await tokenRegistry.getTokenInfo(mockToken.address);
            expect(tokenInfo.symbol).to.equal("MOCK");
            expect(tokenInfo.name).to.equal("Mock Token");
            expect(tokenInfo.isActive).to.be.true;
        });

        it("Should not allow non-owner to add token", async function () {
            await expect(
                tokenRegistry.connect(user1).addToken(
                    mockToken.address,
                    "MOCK",
                    "Mock Token",
                    18,
                    "https://mock.com/logo.png",
                    "https://mock.com",
                    1000000
                )
            ).to.be.revertedWith("Ownable: caller is not the owner");
        });

        it("Should not allow adding duplicate token", async function () {
            await tokenRegistry.addToken(
                mockToken.address,
                "MOCK",
                "Mock Token",
                18,
                "https://mock.com/logo.png",
                "https://mock.com",
                1000000
            );

            await expect(
                tokenRegistry.addToken(
                    mockToken.address,
                    "MOCK2",
                    "Mock Token 2",
                    18,
                    "https://mock.com/logo.png",
                    "https://mock.com",
                    1000000
                )
            ).to.be.revertedWith("TokenRegistry: TOKEN_EXISTS");
        });

        it("Should allow owner to update token status", async function () {
            await tokenRegistry.addToken(
                mockToken.address,
                "MOCK",
                "Mock Token",
                18,
                "https://mock.com/logo.png",
                "https://mock.com",
                1000000
            );

            await expect(
                tokenRegistry.setTokenStatus(mockToken.address, false, true)
            ).to.emit(tokenRegistry, "TokenStatusChanged")
             .withArgs(mockToken.address, false, true);

            const tokenInfo = await tokenRegistry.getTokenInfo(mockToken.address);
            expect(tokenInfo.isActive).to.be.false;
            expect(tokenInfo.isVerified).to.be.true;
        });
    });

    describe("Token Queries", function () {
        beforeEach(async function () {
            await tokenRegistry.addToken(
                mockToken.address,
                "MOCK",
                "Mock Token",
                18,
                "https://mock.com/logo.png",
                "https://mock.com",
                1000000
            );
        });

        it("Should return token by symbol", async function () {
            const tokenAddress = await tokenRegistry.getTokenBySymbol("MOCK");
            expect(tokenAddress).to.equal(mockToken.address);
        });

        it("Should return active tokens", async function () {
            const activeTokens = await tokenRegistry.getActiveTokens();
            expect(activeTokens.length).to.be.gt(20); // Pre-configured + mock token
            expect(activeTokens).to.include(mockToken.address);
        });

        it("Should return verified tokens", async function () {
            const verifiedTokens = await tokenRegistry.getVerifiedTokens();
            expect(verifiedTokens.length).to.be.gt(20); // Pre-configured tokens are verified
        });

        it("Should return tokens sorted by market cap", async function () {
            const topTokens = await tokenRegistry.getTokensByMarketCap(5);
            expect(topTokens.length).to.equal(5);
        });

        it("Should return paginated tokens", async function () {
            const totalTokens = await tokenRegistry.getTotalTokens();
            const firstPage = await tokenRegistry.getTokensPaginated(0, 10);
            const secondPage = await tokenRegistry.getTokensPaginated(10, 10);
            
            expect(firstPage.length).to.equal(10);
            expect(secondPage.length).to.be.lte(10);
            
            // Should not have duplicates
            const intersection = firstPage.filter(token => secondPage.includes(token));
            expect(intersection.length).to.equal(0);
        });
    });

    describe("Token Support Check", function () {
        it("Should return true for supported active token", async function () {
            await tokenRegistry.addToken(
                mockToken.address,
                "MOCK",
                "Mock Token",
                18,
                "https://mock.com/logo.png",
                "https://mock.com",
                1000000
            );

            const isSupported = await tokenRegistry.isTokenSupported(mockToken.address);
            expect(isSupported).to.be.true;
        });

        it("Should return false for inactive token", async function () {
            await tokenRegistry.addToken(
                mockToken.address,
                "MOCK",
                "Mock Token",
                18,
                "https://mock.com/logo.png",
                "https://mock.com",
                1000000
            );

            await tokenRegistry.setTokenStatus(mockToken.address, false, true);
            
            const isSupported = await tokenRegistry.isTokenSupported(mockToken.address);
            expect(isSupported).to.be.false;
        });

        it("Should return false for non-existent token", async function () {
            const isSupported = await tokenRegistry.isTokenSupported(user1.address);
            expect(isSupported).to.be.false;
        });
    });

    describe("Edge Cases", function () {
        it("Should handle zero address checks", async function () {
            await expect(
                tokenRegistry.addToken(
                    ethers.constants.AddressZero,
                    "ZERO",
                    "Zero Token",
                    18,
                    "https://zero.com/logo.png",
                    "https://zero.com",
                    1000000
                )
            ).to.be.revertedWith("TokenRegistry: ZERO_ADDRESS");
        });

        it("Should handle empty symbol", async function () {
            await expect(
                tokenRegistry.addToken(
                    mockToken.address,
                    "",
                    "Mock Token",
                    18,
                    "https://mock.com/logo.png",
                    "https://mock.com",
                    1000000
                )
            ).to.be.revertedWith("TokenRegistry: EMPTY_SYMBOL");
        });

        it("Should handle empty name", async function () {
            await expect(
                tokenRegistry.addToken(
                    mockToken.address,
                    "MOCK",
                    "",
                    18,
                    "https://mock.com/logo.png",
                    "https://mock.com",
                    1000000
                )
            ).to.be.revertedWith("TokenRegistry: EMPTY_NAME");
        });

        it("Should handle pagination edge cases", async function () {
            const totalTokens = await tokenRegistry.getTotalTokens();
            
            // Test offset beyond bounds
            await expect(
                tokenRegistry.getTokensPaginated(totalTokens.toNumber(), 10)
            ).to.be.revertedWith("TokenRegistry: OFFSET_OUT_OF_BOUNDS");
            
            // Test limit exceeding remaining tokens
            const lastPage = await tokenRegistry.getTokensPaginated(totalTokens.sub(5), 10);
            expect(lastPage.length).to.equal(5);
        });
    });
});
